# src/core/command_processor.py
print("[DEBUG] THIS IS THE ACTIVE COMMAND_PROCESSOR.PY FILE")

import os
import sys
import json
import re
import time
import traceback
import subprocess
import logging
from src.core.commands import get_command, discover_commands
# Import models directly instead of the deprecated login module
from typing import Optional


class CommandProcessor:
    """
    Handles execution of system commands.
    If the command is recognized as one of the dynamically imported command modules,
    it delegates the execution to that module. Otherwise, it executes the command via the shell.
    """

    def __init__(self):
        self.logger = logging.getLogger('athena.command')
        if not self.logger.handlers:
            self.logger.setLevel(logging.INFO)

            # Console handler for INFO+
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)

            # File handler for DEBUG+
            try:
                file_handler = logging.FileHandler('logs/command_processor.log')
                file_handler.setLevel(logging.DEBUG)
                file_handler.setFormatter(formatter)
                self.logger.addHandler(file_handler)
            except Exception as e:
                self.logger.warning(f"Could not set up file logging: {e}")

    def process(self, command: str) -> str:
        """
        1) Strip "athena" prefix if present.
        2) Two‑stage normalization:
             a) Look for a known module immediately after filler words ("on", "with", "in").
             b) If none, scan tokens backwards so the last recognized service wins.
        3) Smart‑swap to allow both "play spotify" and "spotify play".
        4) Underscore‑split single‑token commands like "play_spotify".
        5) Dispatch to module.execute(...) or fallback to shell.
        """
        print(f"[DEBUG] Entered CommandProcessor.process with command: '{command}'")

        # --- Early exit ---
        if not command:
            print("[DEBUG] No command given.")
            return "No command to execute"

        # --- 1) Strip "athena" prefix ---
        tokens = command.strip().split()
        if tokens and tokens[0].lower() == "athena":
            tokens = tokens[1:]
            command = " ".join(tokens)
            if not command:
                print("[DEBUG] Stripped to empty after 'athena'")
                return "No command to execute"

        # --- Prepare for normalization ---
        all_modules = set(discover_commands().keys())
        filler_words = {
            "on", "with", "in",   # indicates module may follow
            "music", "song",       # semantic fluff
            "the", "to", "at", "for", "a", "an", "of"
        }
        tokens = command.strip().split()

        # --- 2a) Stage 1: look for module right after filler word ---
        found_mod = None
        mod_idx = -1
        for i, t in enumerate(tokens):
            if i > 0 and tokens[i - 1].lower() in {"on", "with", "in"}:
                if t.lower() in all_modules:
                    found_mod = t.lower()
                    mod_idx = i
                    break

        # --- 2b) Stage 2: if none, scan backwards so last module wins ---
        if not found_mod:
            for i in range(len(tokens) - 1, -1, -1):
                if tokens[i].lower() in all_modules:
                    found_mod = tokens[i].lower()
                    mod_idx = i
                    break

        # --- Rebuild command if we found a module anywhere ---
        if found_mod:
            # remove that token and any filler words around it
            filtered = [
                tok for idx, tok in enumerate(tokens)
                if idx != mod_idx and tok.lower() not in filler_words
            ]

            # determine action + params
            if mod_idx == len(tokens) - 1 and filtered:
                # module was last: first filtered is action
                action = filtered[0]
                params = filtered[1:]
            elif mod_idx == 0 and filtered:
                # module was first
                action = filtered[0]
                params = filtered[1:]
            else:
                # default
                action = filtered[0] if filtered else ""
                params = filtered[1:] if len(filtered) > 1 else []

            # canonical form: "<module> <action> [params...]"
            command = " ".join([found_mod, action] + params) if action else found_mod
            tokens = command.split()
            print(f"[DEBUG] Normalized to: '{command}'")

        # --- 3) Smart‑swap: if first token not a module but second is, swap ---
        if len(tokens) >= 2:
            first, second = tokens[0].lower(), tokens[1].lower()
            if not get_command(first) and get_command(second):
                tokens = [second, first] + tokens[2:]
                command = " ".join(tokens)
                print(f"[DEBUG] Swapped to: '{command}'")

        # --- 4) Handle single‑token underscores like "play_spotify" ---
        if " " not in command and "_" in command:
            parts = command.split("_")
            for p in parts:
                if get_command(p.lower()):
                    others = [x for x in parts if x.lower() != p.lower()]
                    command = p + " " + " ".join(others)
                    tokens = command.split()
                    print(f"[DEBUG] Re-split underscored to: '{command}'")
                    break

        # --- Final tokenization check ---
        tokens = command.strip().split()
        if not tokens:
            print("[DEBUG] Nothing left after all normalization.")
            return "No command to execute"

        # --- 5) Dispatch to module or shell fallback ---
        module_name = tokens[0].lower()
        module_cls = get_command(module_name)
        if module_cls:
            # parse action + params
            action = tokens[1].lower() if len(tokens) > 1 else ""
            params = {}
            idx = 2
            while idx < len(tokens):
                tok = tokens[idx]
                if tok.startswith("--") and idx + 1 < len(tokens):
                    params[tok[2:]] = tokens[idx + 1]
                    idx += 2
                else:
                    params.setdefault("args", []).append(tok)
                    idx += 1

            self.logger.info(f"Executing module '{module_name}' action '{action}'")
            self.logger.debug(f"Params: {params}")
            print(f"[DEBUG] Calling execute on '{module_name}' → action '{action}', params {params}")
            result = module_cls.execute(action, params)
            print(f"[DEBUG] Module result: {result}")
            return result

        # --- Shell fallback ---
        try:
            self.logger.info(f"Shell exec: {command[:50]}…")
            proc = subprocess.run(
                command, shell=True,
                capture_output=True, text=True,
                encoding="utf-8", errors="replace"
            )
            if proc.returncode == 0:
                out = proc.stdout.strip() or "Command executed successfully"
                print(f"[DEBUG] Shell output: {out}")
                return out
            else:
                err = proc.stderr.strip()
                print(f"[DEBUG] Shell error: {err}")
                return f"Error: {err}"
        except Exception as e:
            self.logger.error(f"Shell exception: {e}")
            print(f"[DEBUG] Exception during shell exec: {e}")
            return f"Error executing command: {e}"

    def process_kb_command(self, conversation_id: str, input_text: str) -> Optional[str]:
        """
        Process knowledge base commands (/kb, /search, /knowledge)
        Returns the result of the KB command if it's a KB command, None otherwise.
        """
        from src.core.kb_search import handle_kb_search_command
        
        input_lower = input_text.lower().strip()
        
        # Check if this is a KB command
        if (input_lower.startswith("/kb") or 
            input_lower.startswith("/search") or 
            input_lower.startswith("/knowledge")):
            
            try:
                # Pass only the conversation ID and text - we'll handle the athena instance inside kb_search
                self.logger.info(f"Processing KB command: {input_text}")
                result = handle_kb_search_command(None, conversation_id, input_text)
                
                if result:
                    self.logger.info("KB command processed successfully")
                    return result
                else:
                    self.logger.warning("KB command returned no results")
                    return "No matching documents found in your knowledge base."
            except Exception as e:
                self.logger.error(f"Error processing KB command: {e}")
                import traceback
                self.logger.error(traceback.format_exc())
                return f"Error processing knowledge base command: {str(e)}"
                
        return None
