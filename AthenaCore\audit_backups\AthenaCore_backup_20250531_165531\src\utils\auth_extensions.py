"""
Authentication extensions.

This module contains authentication-related extensions like Flask-Login.
"""

from flask_login import LoginManager

# Initialize login manager
login_manager = LoginManager()
login_manager.login_message = "Please log in to access this page."
login_manager.login_message_category = "info"

@login_manager.user_loader
def load_user(user_id):
    """
    Load user by ID for Flask-Login.
    
    Args:
        user_id: User ID to load
        
    Returns:
        User object or None
    """
    from src.models.user import User
    return User.query.get(int(user_id))
