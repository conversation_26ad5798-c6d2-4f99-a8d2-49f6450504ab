<!-- AI Provider Connections Settings Page -->
<div class="settings-section"
     data-section="connections"
     id="connections-section">
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h2>AI Provider Connections</h2>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>Database-Driven Configuration:</strong> All API keys are now managed through this web interface and stored securely in the database. 
                Environment variables and config files are no longer used for API key management.
            </div>
            
            <!-- Quick Setup Cards -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <h4>Quick Setup</h4>
                    <p class="text-muted">Choose your AI provider to get started quickly:</p>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card provider-card" onclick="openProviderModal('openai')">
                        <div class="card-body text-center">
                            <i class="fas fa-robot fa-3x text-primary mb-3"></i>
                            <h5>OpenAI</h5>
                            <p class="text-muted">GPT-4, GPT-3.5, DALL-E</p>
                            <small class="text-success">Most Popular</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card provider-card" onclick="openProviderModal('anthropic')">
                        <div class="card-body text-center">
                            <i class="fas fa-brain fa-3x text-warning mb-3"></i>
                            <h5>Anthropic</h5>
                            <p class="text-muted">Claude 3, Claude 2</p>
                            <small class="text-info">Advanced Reasoning</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card provider-card" onclick="openProviderModal('google')">
                        <div class="card-body text-center">
                            <i class="fab fa-google fa-3x text-danger mb-3"></i>
                            <h5>Google</h5>
                            <p class="text-muted">Gemini, PaLM</p>
                            <small class="text-primary">Multimodal</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card provider-card" onclick="openProviderModal('azure')">
                        <div class="card-body text-center">
                            <i class="fab fa-microsoft fa-3x text-info mb-3"></i>
                            <h5>Azure OpenAI</h5>
                            <p class="text-muted">Enterprise OpenAI</p>
                            <small class="text-warning">Enterprise</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card provider-card" onclick="openProviderModal('smithery')">
                        <div class="card-body text-center">
                            <i class="fas fa-tools fa-3x text-secondary mb-3"></i>
                            <h5>Smithery</h5>
                            <p class="text-muted">MCP Registry</p>
                            <small class="text-secondary">Tools & Plugins</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card provider-card" onclick="openConnectionModal()">
                        <div class="card-body text-center">
                            <i class="fas fa-plus-circle fa-3x text-success mb-3"></i>
                            <h5>Custom</h5>
                            <p class="text-muted">Other Providers</p>
                            <small class="text-success">Advanced</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- DirectConnections Management -->
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">Your API Connections</h5>
                        <p class="text-muted mb-0">Manage your configured API connections</p>
                    </div>
                    <button class="btn btn-outline-primary" onclick="openConnectionModal()">
                        <i class="fas fa-plus"></i> Add Custom Connection
                    </button>
                </div>
                <div class="card-body">
                    <!-- Connections List -->
                    <div id="connectionsList">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                            <p>Loading connections...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Provider-Specific Modals -->
<div id="providerModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h4 id="providerTitle">Setup Provider</h4>
            <span class="close" onclick="closeProviderModal()">&times;</span>
        </div>
        <div class="modal-body">
            <div id="providerContent">
                <!-- Provider-specific content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Generic Connection Modal -->
<div id="connectionFormModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h4 id="formTitle">Add Connection</h4>
            <span class="close" onclick="closeConnectionModal()">&times;</span>
        </div>
        <div class="modal-body">
            <form id="connectionForm" onsubmit="saveConnection(event)">
                <div class="form-group">
                    <label for="connectionProvider">Provider Type:</label>
                    <select id="connectionProvider" class="form-control" onchange="updateConnectionForm()">
                        <option value="">Select Provider</option>
                        <option value="openai">OpenAI</option>
                        <option value="anthropic">Anthropic</option>
                        <option value="google">Google</option>
                        <option value="azure">Azure OpenAI</option>
                        <option value="smithery">Smithery</option>
                        <option value="custom">Custom</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="connectionName">Connection Name:</label>
                    <input type="text" id="connectionName" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="connectionUrl">API URL:</label>
                    <input type="url" id="connectionUrl" class="form-control" required>
                    <small class="form-text text-muted" id="urlHelp"></small>
                </div>
                
                <div class="form-group">
                    <label for="connectionKey">API Key:</label>
                    <input type="password" id="connectionKey" class="form-control" required>
                    <small class="form-text text-muted" id="keyHelp"></small>
                </div>
                
                <div class="form-group" id="prefixGroup">
                    <label for="connectionPrefix">Prefix:</label>
                    <input type="text" id="connectionPrefix" class="form-control">
                    <small class="form-text text-muted">Optional prefix for model names</small>
                </div>
                
                <div class="form-group" id="embeddingGroup">
                    <label for="connectionEmbeddingModel">Embedding Model:</label>
                    <input type="text" id="connectionEmbeddingModel" class="form-control">
                    <small class="form-text text-muted">Model for text embeddings</small>
                </div>
                
                <div class="form-group" id="modelsGroup">
                    <label for="connectionModels">Models (comma-separated):</label>
                    <input type="text" id="connectionModels" class="form-control">
                    <small class="form-text text-muted">Leave empty to auto-fetch available models</small>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">Save Connection</button>
                    <button type="button" class="btn btn-secondary" onclick="closeConnectionModal()">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>
</div>

<style>
.provider-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.provider-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-color: #007bff;
}

.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 15px;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
}

.close:hover {
    color: #000;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #495057;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.form-control:focus {
    border-color: #007bff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.form-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    margin-right: 10px;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn-outline-primary {
    border: 1px solid #007bff;
    color: #007bff;
    background-color: transparent;
}

.btn-outline-primary:hover {
    background-color: #007bff;
    color: white;
}

.connection-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    background-color: #f8f9fa;
}

.connection-item.enabled {
    border-color: #28a745;
    background-color: #d4edda;
}

.connection-item.disabled {
    border-color: #dc3545;
    background-color: #f8d7da;
}

.connection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.connection-name {
    font-size: 18px;
    font-weight: 600;
    color: #495057;
}

.connection-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-enabled {
    background-color: #28a745;
    color: white;
}

.status-disabled {
    background-color: #dc3545;
    color: white;
}

.connection-details {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 15px;
}

.connection-actions {
    display: flex;
    gap: 10px;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.provider-setup {
    padding: 20px;
}

.setup-step {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background-color: #f8f9fa;
}

.step-number {
    display: inline-block;
    width: 25px;
    height: 25px;
    background-color: #007bff;
    color: white;
    border-radius: 50%;
    text-align: center;
    line-height: 25px;
    font-weight: bold;
    margin-right: 10px;
}

.code-snippet {
    background-color: #f1f3f4;
    padding: 10px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 13px;
    margin: 10px 0;
}
</style>

<script>
// Initialize connections page when this section is loaded
(function() {
    function initConnectionsSection() {
        console.log('[Connections] Initializing connections section');

        // Check if we have the connections list element
        var connectionsList = document.getElementById('connectionsList');
        if (connectionsList && typeof window.loadConnections === 'function') {
            console.log('[Connections] Loading connections data');
            window.loadConnections();
        }

        // Initialize provider modals if they exist
        if (typeof window.initNewConnectionsPage === 'function') {
            window.initNewConnectionsPage();
        }
    }

    // Initialize immediately if DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initConnectionsSection);
    } else {
        initConnectionsSection();
    }

    // Also initialize when section becomes visible
    setTimeout(function() {
        var connectionsSection = document.getElementById('connections-section');
        if (connectionsSection) {
            var observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                        if (connectionsSection.style.display !== 'none') {
                            initConnectionsSection();
                        }
                    }
                });
            });
            observer.observe(connectionsSection, { attributes: true });
        }
    }, 100);
})();
</script>
