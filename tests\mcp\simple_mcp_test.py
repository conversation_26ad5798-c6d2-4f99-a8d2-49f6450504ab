import os
import json
import sys
import asyncio

# Try to import required packages
try:
    import smithery
    import mcp
    from mcp.client.websocket import websocket_client
except ImportError:
    print("Installing required packages...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "smithery", "mcp"])
    import smithery
    import mcp
    from mcp.client.websocket import websocket_client

async def test_mcp_connection():
    # Load the config file
    config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                             'AthenaCore', 'config.json')
    
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    smithery_api_key = config.get("smithery_api_key")
    if not smithery_api_key:
        print("No Smithery API key found in config.json")
        return
    
    print(f"Using Smithery API key: {smithery_api_key[:6]}...{smithery_api_key[-4:]}")
    
    # Try connecting directly using the standard library approach
    model_name = "smithery-ai/claude-3-haiku"  # Example model, change as needed
    
    # Simple direct WebSocket URL as per MCP spec
    url = f"wss://server.smithery.ai/api/ws/{model_name}?api_key={smithery_api_key}"
    
    print(f"Connecting to {model_name} at {url[:40]}...")
    
    try:
        async with websocket_client(url) as streams:
            async with mcp.ClientSession(*streams) as session:
                # List available tools
                tools_result = await session.list_tools()
                tools = [t.name for t in tools_result]
                print(f"Available tools: {', '.join(tools) if tools else 'None'}")
                
                # Send a test message
                test_message = "Hello, what can you tell me about the weather?"
                print(f"Sending test message: {test_message}")
                
                result = await session.chat(test_message)
                print(f"\nResponse received!")
                print("-" * 50)
                print(result)
                print("-" * 50)
    except Exception as e:
        print(f"Error connecting to MCP server: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Run the async function
    asyncio.run(test_mcp_connection())
