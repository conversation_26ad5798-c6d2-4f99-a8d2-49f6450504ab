from datetime import datetime
import secrets
import time
from flask import Blueprint, request, jsonify, current_app, url_for
from flask_login import login_required, current_user, login_user
from werkzeug.security import generate_password_hash

from src.models import DirectConnection
from src.core.athena import Athena

import secrets
import time
import uuid
from functools import wraps

import requests  # For fetching remote models
import src.core as core
# Import models directly instead of the deprecated login module

# Import configuration and models via login
from src.models import db
from src.models import User
from src.models import APIKey
from src.models import UserLog
from src.models import LLMProviderSetting
from src.models import CommandToggle

api_v1_bp = Blueprint("api_v1", __name__, url_prefix="/api/v1")

athena = Athena()


def require_api_key(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Allow access if user is already authenticated through Flask-Login
        if (
            current_user
            and hasattr(current_user, "is_authenticated")
            and current_user.is_authenticated
        ):
            return f(*args, **kwargs)

        # For API clients, check for API key in the Authorization header
        api_key_header = request.headers.get("Authorization")
        if not api_key_header:
            return jsonify({"error": "API key required"}), 401

        # Extract key from "Bearer <key>" format if needed
        if api_key_header.startswith("Bearer "):
            api_key_header = api_key_header[7:]

        # Look up API key in database
        api_key = APIKey.query.get(api_key_header)
        if not api_key:
            return jsonify({"error": "Invalid API key"}), 403

        # Update last used timestamp
        api_key.last_used = datetime.utcnow()
        db.session.commit()

        # Log in the user associated with this API key
        login_user(api_key.user, remember=False)

        return f(*args, **kwargs)

    return decorated_function


### Modified Agent Sign-In Endpoint (Step 1: sign in only)
@api_v1_bp.route("/agent/signin", methods=["POST"])
def agent_signin():
    """
    Handles agent sign-in:
    - Accepts JSON payload: { "username": "...", "password": "..." }
    - Validates credentials.
    - Logs in the user.
    - Automatically generates an API key for this device using the default name "Agent Device".
    - Returns the API key.
    """
    data = request.json or {}
    username = data.get("username")
    password = data.get("password")

    if not username or not password:
        return jsonify({"error": "Username and password required"}), 400

    # Find the user by username
    user = User.query.filter_by(username=username).first()
    if not user or not user.check_password(password):
        return jsonify({"error": "Sign in failed, check your credentials."}), 403

    # Log in the user
    login_user(user, remember=False)

    # Automatically generate an API key using the default device name.
    new_key_id = f"ath-{secrets.token_hex(16)}"
    api_key = APIKey(
        id=new_key_id,
        name="Agent Device",
        user_id=user.id,
        created_at=datetime.utcnow(),
    )
    db.session.add(api_key)
    db.session.commit()

    return jsonify(
        {"api_key": new_key_id, "message": "Sign in successful. API key generated."}
    )


### New Endpoint: Update Device Name (Step 2)
@api_v1_bp.route("/agent/name_device", methods=["POST"])
@require_api_key
def agent_name_device():
    """
    Allows the agent to name the device after signing in.
    Expects JSON payload: { "device_name": "Your Device Name" }
    Updates the API key record to use the provided device name.
    The API key is read from the Authorization header.
    """
    data = request.json or {}
    device_name = data.get("device_name")
    if not device_name:
        return jsonify({"error": "Device name is required."}), 400

    # Get the API key from the Authorization header
    auth_header = request.headers.get("Authorization", "")
    if auth_header.startswith("Bearer "):
        current_api_key = auth_header[7:]
    else:
        current_api_key = auth_header

    key_record = APIKey.query.get(current_api_key)
    if not key_record:
        return jsonify({"error": "API key not found."}), 404

    key_record.name = device_name
    db.session.commit()

    return jsonify(
        {"message": "Device name updated successfully.", "api_key": current_api_key}
    ), 200


# New Endpoint: Agent Profile (fetch user & Athena images)
@api_v1_bp.route("/agent/profile", methods=["GET"])
@require_api_key
def agent_profile():
    """
    Fetch the signed-in agent's profile, including user profile picture and Athena logo.
    """
    # Determine user profile picture URL (fallback to default)
    if current_user.profile_picture:
        pic = current_user.profile_picture
        if pic.startswith("http"):
            profile_pic = pic
        elif pic.startswith("uploads/"):
            profile_pic = url_for('static', filename=pic, _external=True)
        else:
            profile_pic = url_for('static', filename=f'img/{pic}', _external=True)
    else:
        profile_pic = url_for('static', filename='img/user-avatar.png', _external=True)
    # Athena logo URL (static asset)
    athena_logo = url_for('static', filename='img/AthenaLogoHQ.png', _external=True)
    return jsonify({
        'profile_picture_url': profile_pic,
        'athena_logo_url': athena_logo
    })


@api_v1_bp.route("/chat", methods=["POST"])
@require_api_key
def api_chat():
    data = request.json
    if not data or "message" not in data:
        return jsonify({"error": "Message is required"}), 400

    user_input = data.get("message")
    conversation_id = data.get(
        "conversation_id", datetime.now().strftime("%Y%m%d%H%M%S")
    )

    start_time = datetime.now()
    # <-- NEW: Extract model from payload (if provided)
    model = data.get("model")
    if not model:
        return jsonify({"error": "Model is required"}), 400
    result = athena.get_response(user_input, model=model)
    response_text, command, python_code = result[:3]

    output = None
    status = "success"
    message_type = "chat"

    if command:
        output = athena.execute_command(command)
        message_type = "command"
    elif python_code:
        output = athena.execute_python(python_code)
        message_type = "python"

    execution_time = (datetime.now() - start_time).total_seconds() * 1000

    log_entry = {
        "timestamp": datetime.now().isoformat(),
        "conversation_id": conversation_id,
        "message_type": message_type,
        "content": user_input,
        "response": response_text,
        "code": command if command else (python_code if python_code else None),
        "output": output if output else None,
        "execution_time": execution_time,
        "status": status,
        "api": True,
    }

    core.save_log(log_entry)
    athena.update_history("user", user_input)
    athena.update_history("assistant", response_text)
    athena.state_manager.add_memory(user_input, response_text)

    return jsonify(
        {
            "response": response_text,
            "output": output,
            "code": python_code if python_code else command,
            "message_type": message_type,
            "conversation_id": conversation_id,
            "timestamp": datetime.now().isoformat(),
        }
    )


@api_v1_bp.route("/vector/search", methods=["GET"])
@require_api_key
def vector_search():
    query = request.args.get("query")
    if not query:
        return jsonify({"error": "Query parameter is required"}), 400

    results = athena.state_manager.retrieve_memory(query, top_k=5)
    for result in results:
        if "distance" in result and result["distance"] is not None:
            result["distance"] = float(result["distance"])

    return jsonify({"results": results})


@api_v1_bp.route("/models", methods=["GET", "OPTIONS"])
def list_models():
    """
    Returns available models from DirectConnections only.
    No hardcoded models at all - only returns what's in the database.
    """
    # Handle OPTIONS request for CORS
    if request.method == "OPTIONS":
        response = jsonify({})
        response.headers["Access-Control-Allow-Origin"] = "*"
        response.headers["Access-Control-Allow-Methods"] = "GET, OPTIONS"
        response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
        return response

    # Add CORS headers - these are critical
    cors_headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Pragma": "no-cache",
        "Expires": "0",
    }

    print("==== API: /models endpoint called ====")

    # Start with an empty result set - no hardcoded models
    result = []

    # Add models from direct connections - these are the only models we'll show
    try:
        connections = DirectConnection.query.filter_by(enabled=True, user_id=current_user.id).all()
        print(f"API: Found {len(connections)} enabled connections")

        for conn in connections:
            if conn.model_ids and conn.model_ids.strip():
                model_ids = [m.strip() for m in conn.model_ids.split(",") if m.strip()]
                print(
                    f"API: Adding {len(model_ids)} models from connection {conn.id}: {model_ids}"
                )

                for model_id in model_ids:
                    if not any(m["id"] == model_id for m in result):
                        result.append(
                            {
                                "id": model_id,
                                "object": "model",
                                "owned_by": f"connection:{conn.id}",
                            }
                        )
    except Exception as e:
        print(f"API: Error loading DirectConnection models: {e}")

    # Sort the models alphabetically
    result.sort(key=lambda x: x["id"])

    print(f"API: Returning {len(result)} total models: {[m['id'] for m in result]}")

    # Create and return the final response with CORS headers
    response = jsonify({"object": "list", "data": result})
    for header, value in cors_headers.items():
        response.headers[header] = value

    return response


@api_v1_bp.route("/completions", methods=["POST"])
@require_api_key
def create_completion():
    data = request.json or {}
    prompt = data.get("prompt", "")
    model = data.get("model")
    max_tokens = data.get("max_tokens", 150)
    temperature = data.get("temperature", 0.7)

    # Validate required parameters
    if not prompt:
        return jsonify({"error": "Prompt is required"}), 400

    if not model:
        return jsonify({"error": "Model is required"}), 400

    # Track execution time
    start_time = time.time()

    # Check if this is a direct connection model
    is_direct_connection = False
    conn_id = None

    if ":" in model:
        # Format is expected to be CONN_ID:MODEL_ID
        parts = model.split(":", 1)
        if len(parts) == 2:
            conn_prefix, model_name = parts
            # Find the connection with this prefix
            try:
                conn = DirectConnection.query.filter_by(prefix=conn_prefix, user_id=current_user.id).first()
                if not conn:
                    # Try by conn{id}
                    if conn_prefix.startswith("conn") and conn_prefix[4:].isdigit():
                        conn_id = int(conn_prefix[4:])
                        conn = DirectConnection.query.get(conn_id)
                if conn and conn.enabled:
                    is_direct_connection = True
                    conn_id = conn.id
                    # Use the actual model name part for the request
                    model = model_name
            except Exception as e:
                print(f"Error parsing connection model: {e}")
    else:
        # This might be a raw model ID from an enabled connection
        # Check if it appears in any connection's model_ids
        try:
            conns = DirectConnection.query.filter_by(enabled=True, user_id=current_user.id).all()
            for conn in conns:
                if conn.model_ids and model in [
                    m.strip() for m in conn.model_ids.split(",")
                ]:
                    is_direct_connection = True
                    conn_id = conn.id
                    break
        except Exception as e:
            print(f"Error checking model in connections: {e}")
            # Continue with normal processing

    # Process the request
    try:
        if is_direct_connection:
            # Get the connection details
            conn = DirectConnection.query.get(conn_id)
            if not conn or not conn.enabled:
                return jsonify(
                    {"error": f"Connection {conn_id} not found or disabled"}
                ), 404

            # Forward to the direct connection
            response_text = athena.direct_request(
                conn,
                "completion",
                {
                    "prompt": prompt,
                    "model": model,  # Use the parsed model ID
                    "max_tokens": max_tokens,
                    "temperature": temperature,
                },
            )
        else:
            # Fall back to the configured provider
            response_text = athena.generate_response(prompt)

        execution_time = time.time() - start_time

        # Log the request
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "message_type": "completion",
            "content": prompt,
            "response": response_text,
            "execution_time": execution_time,
            "status": "success",
            "api": True,
        }
        athena.state_manager.save_log(log_entry)

        completion_id = f"cmpl-{secrets.token_hex(12)}"
        created_timestamp = int(datetime.now().timestamp())

        return jsonify(
            {
                "id": completion_id,
                "object": "text_completion",
                "created": created_timestamp,
                "model": model,
                "choices": [
                    {
                        "text": response_text,
                        "index": 0,
                        "logprobs": None,
                        "finish_reason": "stop",
                    }
                ],
                "usage": {
                    "prompt_tokens": len(prompt) // 4,
                    "completion_tokens": len(response_text) // 4,
                    "total_tokens": (len(prompt) + len(response_text)) // 4,
                },
            }
        )
    except Exception as e:
        import traceback

        print(f"Error in completion: {e}")
        print(traceback.format_exc())
        return jsonify({"error": str(e)}), 500


@api_v1_bp.route("/chat/completions", methods=["POST"])
@require_api_key
def create_chat_completion():
    data = request.json or {}

    messages = data.get("messages", [])
    model = data.get("model")
    temperature = data.get("temperature", 0.7)
    max_tokens = data.get("max_tokens", 150)

    if not messages:
        return jsonify({"error": "Messages are required"}), 400

    conversation = []
    for msg in messages:
        role = msg.get("role", "user")
        content = msg.get("content", "")
        conversation.append(f"{role}: {content}")

    prompt = "\n".join(conversation)
    start_time = datetime.now()
    # Here model is passed so that the selected model is used
    result = athena.get_response(prompt, model=model)
    response_text, command, python_code = result[:3]

    output = None
    status = "success"
    message_type = "chat_completion"

    if command:
        output = athena.execute_command(command)
        message_type = "command"
    elif python_code:
        output = athena.execute_python(python_code)
        message_type = "python"

    execution_time = (datetime.now() - start_time).total_seconds() * 1000

    log_entry = {
        "timestamp": datetime.now().isoformat(),
        "message_type": message_type,
        "content": prompt,
        "response": response_text,
        "code": command if command else (python_code if python_code else None),
        "output": output if output else None,
        "execution_time": execution_time,
        "status": status,
        "api": True,
    }
    athena.state_manager.save_log(log_entry)

    completion_id = f"chatcmpl-{secrets.token_hex(12)}"
    created_timestamp = int(datetime.now().timestamp())

    return jsonify(
        {
            "id": completion_id,
            "object": "chat.completion",
            "created": created_timestamp,
            "model": model,
            "choices": [
                {
                    "index": 0,
                    "message": {"role": "assistant", "content": response_text},
                    "finish_reason": "stop",
                }
            ],
            "usage": {
                "prompt_tokens": len(prompt) // 4,
                "completion_tokens": len(response_text) // 4,
                "total_tokens": (len(prompt) + len(response_text)) // 4,
            },
        }
    )


@api_v1_bp.route("/keys", methods=["GET"])
@require_api_key
def list_api_keys():
    if current_user and current_user.is_authenticated:
        user_id = current_user.id
    else:
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            key_id = auth_header[7:]
            api_key = APIKey.query.get(key_id)
            if not api_key:
                return jsonify({"error": "Invalid API key"}), 403
            user_id = api_key.user_id
        else:
            return jsonify({"error": "Authentication required"}), 401

    keys = APIKey.query.filter_by(user_id=user_id).all()
    keys_data = [key.to_dict() for key in keys]
    return jsonify({"object": "list", "data": keys_data})


@api_v1_bp.route("/keys", methods=["POST"])
@require_api_key
def create_api_key():
    if not current_user.is_authenticated:
        return jsonify({"error": "Authentication required"}), 401

    data = request.json or {}
    key_name = data.get("name")

    if not key_name:
        count = APIKey.query.filter_by(user_id=current_user.id).count() + 1
        key_name = f"API Key {count}"

    new_key_id = f"ath-{secrets.token_hex(16)}"
    api_key = APIKey(
        id=new_key_id,
        name=key_name,
        user_id=current_user.id,
        created_at=datetime.utcnow(),
    )

    db.session.add(api_key)
    db.session.commit()

    return jsonify(
        {
            "id": api_key.id,
            "name": api_key.name,
            "created": int(api_key.created_at.timestamp()),
        }
    )


@api_v1_bp.route("/keys/<key_id>", methods=["DELETE"])
@require_api_key
def delete_api_key(key_id):
    if not current_user.is_authenticated:
        return jsonify({"error": "Authentication required"}), 401

    api_key = APIKey.query.get(key_id)
    if not api_key:
        return jsonify({"error": "Key not found"}), 404

    if api_key.user_id != current_user.id:
        return jsonify({"error": "Not authorized to delete this key"}), 403

    db.session.delete(api_key)
    db.session.commit()

    return jsonify({"id": key_id, "deleted": True})


@api_v1_bp.route("/test", methods=["GET"])
def test_api():
    return jsonify(
        {
            "status": "success",
            "message": "API is working correctly",
            "version": "v1",
            "timestamp": datetime.now().isoformat(),
        }
    )


# ----------------------------------------------------
# Direct Connections CRUD + Model Fetch Endpoints
# ----------------------------------------------------


@api_v1_bp.route("/connections", methods=["GET"])
@require_api_key
def direct_get_connections():
    """
    List all DirectConnection entries for the authenticated user.
    """
    if not current_user.is_authenticated:
        return jsonify({"error": "Authentication required"}), 401

    # Use a modified query that doesn't select the non-existent api_type column
    all_conns = DirectConnection.query.with_entities(
        DirectConnection.id, DirectConnection.name, DirectConnection.url,
        DirectConnection.api_key, DirectConnection.prefix, DirectConnection.model_ids,
        DirectConnection.embedding_model, DirectConnection.created_at,
        DirectConnection.enabled, DirectConnection.user_id
    ).filter_by(user_id=current_user.id).all()
    
    # Manually create dictionaries without depending on to_dict()
    results = [{
        "id": conn[0],
        "name": conn[1],
        "url": conn[2],
        "api_key": "***" if conn[3] else None,  # Mask the actual API key
        "prefix": conn[4],
        "model_ids": conn[5],
        "embedding_model": conn[6],
        "api_type": "openai" if "openai.com" in (conn[2] or "").lower() else 
                   "groq" if "groq.com" in (conn[2] or "").lower() else
                   "anthropic" if "anthropic.com" in (conn[2] or "").lower() else "openai",
        "created_at": int(conn[7].timestamp()) if conn[7] else None,
        "enabled": conn[8]
    } for conn in all_conns]
    return jsonify(results)


@api_v1_bp.route("/connections", methods=["POST"])
@require_api_key
def create_connection():
    """
    Create a new DirectConnection entry.
    Expects JSON:
    {
      "name": "...",
      "url": "...",
      "api_key": "...",
      "prefix": "...",
      "model_ids": "...",
      "enabled": true/false
    }
    If model_ids is empty or not provided, automatically fetch from <url>/v1/models.
    """
    if not current_user.is_authenticated:
        return jsonify({"error": "Authentication required"}), 401

    data = request.json or {}
    url = data.get("url")
    if not url:
        return jsonify({"error": "url is required"}), 400

    # Embedding model tied to connection, default to MiniLM
    embedding_model = data.get("embedding_model", "").strip()
    if not embedding_model:
        embedding_model = "sentence-transformers/all-MiniLM-L6-v2"

    name = data.get("name") or ""
    api_key = data.get("api_key") or ""
    prefix = data.get("prefix") or ""

    # Normalize model_ids to comma-separated string
    raw_model_ids = data.get("model_ids", "")
    if isinstance(raw_model_ids, list):
        model_ids = ",".join(raw_model_ids)
    else:
        model_ids = raw_model_ids.strip()
    enabled       = data.get("enabled", True)

    # Auto-fetch model_ids only for OpenAI endpoints
    if not model_ids and "api.openai.com" in url:
        url_clean = url.rstrip("/")
        if url_clean.endswith("/v1"):
            endpoint = url_clean + "/models"
        else:
            endpoint = url_clean + "/v1/models"
        headers = {}
        if api_key:
            headers["Authorization"] = f"Bearer {api_key}"
        try:
            resp = requests.get(endpoint, headers=headers, timeout=10)
            if resp.status_code == 200:
                resp_data = resp.json()
                fetched_models = []
                for mobj in resp_data.get("data", []):
                    mid = mobj.get("id")
                    if mid:
                        fetched_models.append(mid)
                model_ids = ",".join(fetched_models)
            else:
                print(
                    f"Warning: Could not fetch models from {endpoint} (HTTP {resp.status_code})"
                )
                model_ids = ""
        except Exception as e:
            print(f"Error fetching models from {endpoint}: {str(e)}")
            model_ids = ""

    if isinstance(model_ids, (list, tuple)):
        model_ids = ",".join(model_ids)

    new_conn = DirectConnection(
        name=name,
        url=url.strip(),
        api_key=api_key,
        prefix=prefix,
        model_ids=model_ids,
        embedding_model=embedding_model,
        enabled=enabled,
        user_id=current_user.id,
    )
    db.session.add(new_conn)
    db.session.commit()

    return jsonify(new_conn.to_dict())


@api_v1_bp.route("/connections/<int:conn_id>", methods=["PUT"])
@require_api_key
def update_connection(conn_id):
    """
    Update an existing DirectConnection entry.
    Expects JSON with any of the fields:
    {
      "name": "...",
      "url": "...",
      "api_key": "...",
      "prefix": "...",
      "model_ids": "...",
      "enabled": true/false
    }
    If model_ids is explicitly empty, automatically fetch from <url>/v1/models.
    """
    if not current_user.is_authenticated:
        return jsonify({"error": "Authentication required"}), 401

    conn = DirectConnection.query.get(conn_id)
    if not conn:
        return jsonify({"error": "Connection not found"}), 404
    if conn.user_id != current_user.id:
        return jsonify({"error": "Not authorized"}), 403

    data = request.json or {}
    if "url" in data and not data["url"]:
        return jsonify({"error": "url cannot be empty"}), 400

    # Update fields if provided
    if "name" in data:
        conn.name = data["name"]
    if "url" in data:
        conn.url = data["url"].strip()
    if "api_key" in data:
        conn.api_key = data["api_key"]
    if "prefix" in data:
        conn.prefix = data["prefix"]
    if "enabled" in data:
        conn.enabled = bool(data["enabled"])

    # Normalize updated model_ids input
    if "model_ids" in data:
        raw_input_model_ids = data["model_ids"]
        if isinstance(raw_input_model_ids, list):
            new_model_ids = ",".join(raw_input_model_ids)
        else:
            new_model_ids = raw_input_model_ids.strip()
        if not new_model_ids:
            # Only fetch for OpenAI endpoints
            if "api.openai.com" in conn.url:
                url_clean = conn.url.rstrip("/")
                if url_clean.endswith("/v1"):
                    endpoint = url_clean + "/models"
                else:
                    endpoint = url_clean + "/v1/models"
                headers = {}
                if conn.api_key:
                    headers["Authorization"] = f"Bearer {conn.api_key}"
                try:
                    resp = requests.get(endpoint, headers=headers, timeout=10)
                    if resp.status_code == 200:
                        resp_data = resp.json()
                        fetched_models = []
                        for mobj in resp_data.get("data", []):
                            mid = mobj.get("id")
                            if mid:
                                fetched_models.append(mid)
                        conn.model_ids = ",".join(fetched_models)
                    else:
                        print(
                            f"Warning: Could not fetch models from {endpoint} (HTTP {resp.status_code})"
                        )
                        conn.model_ids = ""
                except Exception as e:
                    print(f"Error fetching models from {endpoint}: {str(e)}")
                    conn.model_ids = ""
            else:
                conn.model_ids = new_model_ids
        else:
            if isinstance(new_model_ids, (list, tuple)):
                new_model_ids = ",".join(new_model_ids)
            conn.model_ids = new_model_ids

    # Update embedding_model if provided
    if "embedding_model" in data:
        conn.embedding_model = data["embedding_model"].strip()

    db.session.commit()

    return jsonify(conn.to_dict())


@api_v1_bp.route("/connections/<int:conn_id>/toggle", methods=["POST"])
@require_api_key
def toggle_connection(conn_id):
    """
    Toggle the enabled status of an existing DirectConnection entry.
    This allows disabling a connection without deleting it.

    Expects JSON (optional):
    {
      "enabled": true/false  # If not provided, just toggles the current state
    }
    """
    if not current_user.is_authenticated:
        return jsonify({"error": "Authentication required"}), 401

    conn = DirectConnection.query.get(conn_id)
    if not conn:
        return jsonify({"error": "Connection not found"}), 404
    if conn.user_id != current_user.id:
        return jsonify({"error": "Not authorized"}), 403

    data = request.json or {}
    if "enabled" in data:
        # Set specific state if provided
        conn.enabled = bool(data["enabled"])
    else:
        # Otherwise toggle current state
        conn.enabled = not conn.enabled

    # Make sure to commit the change to the database
    try:
        db.session.commit()
        print(
            f"Connection '{conn.name}' {'enabled' if conn.enabled else 'disabled'} successfully."
        )
    except Exception as e:
        db.session.rollback()
        error_msg = f"Failed to update connection status: {str(e)}"
        print(error_msg)
        return jsonify({"error": error_msg}), 500

    return jsonify(
        {
            "success": True,
            "message": f"Connection '{conn.name}' is now {'enabled' if conn.enabled else 'disabled'}",
            "connection": conn.to_dict(),
        }
    )


@api_v1_bp.route("/connections/<int:conn_id>", methods=["DELETE"])
@require_api_key
def delete_connection(conn_id):
    """
    Delete an existing DirectConnection entry by ID.
    """
    if not current_user.is_authenticated:
        return jsonify({"error": "Authentication required"}), 401

    conn = DirectConnection.query.get(conn_id)
    if not conn:
        return jsonify({"error": "Connection not found"}), 404
    if conn.user_id != current_user.id:
        return jsonify({"error": "Not authorized"}), 403

    db.session.delete(conn)
    db.session.commit()

    return jsonify({"success": True})


@api_v1_bp.route("/connections/<int:conn_id>/models", methods=["GET"])
@require_api_key
def fetch_connection_models(conn_id):
    """
    Attempt to fetch model list from the specified connection by calling <url>/v1/models
    with a Bearer <api_key> header.
    """
    if not current_user.is_authenticated:
        return jsonify({"error": "Authentication required"}), 401

    conn = DirectConnection.query.get(conn_id)
    if not conn:
        return jsonify({"error": "Connection not found"}), 404
    if conn.user_id != current_user.id:
        return jsonify({"error": "Not authorized"}), 403

    url_clean = conn.url.rstrip("/")
    if url_clean.endswith("/v1"):
        endpoint = url_clean + "/models"
    else:
        endpoint = url_clean + "/v1/models"
    headers = {}
    if conn.api_key:
        headers["Authorization"] = f"Bearer {conn.api_key}"

    # Allow model listing for all connection types
    # Build appropriate endpoint based on API type
    models_endpoint = endpoint
    
    # If this is a groq endpoint, adjust the URL path
    if "groq.com" in url_clean:
        models_endpoint = url_clean.rstrip("/") + "/models"
    # If this is an Anthropic endpoint, they don't have a models endpoint
    elif "anthropic.com" in url_clean:
        # Return some default Claude models
        return jsonify({
            "success": True, 
            "models": [
                {"id": "claude-3-opus-20240229", "object": "model"},
                {"id": "claude-3-sonnet-20240229", "object": "model"},
                {"id": "claude-3-haiku-20240307", "object": "model"},
                {"id": "claude-2.1", "object": "model"},
                {"id": "claude-2.0", "object": "model"},
                {"id": "claude-instant-1.2", "object": "model"}
            ], 
            "raw": {}
        })

    try:
        # Use the models_endpoint variable for the request
        resp = requests.get(models_endpoint, headers=headers, timeout=10)
        if resp.status_code != 200:
            # Instead of returning an error, return a default set of models based on provider
            if "groq.com" in url_clean:
                return jsonify({
                    "success": True,
                    "models": [
                        {"id": "llama3-8b-8192", "object": "model"},
                        {"id": "llama3-70b-8192", "object": "model"},
                        {"id": "mixtral-8x7b-32768", "object": "model"},
                        {"id": "gemma-7b-it", "object": "model"},
                    ],
                    "message": f"Couldn't fetch models (HTTP {resp.status_code}). Using default list."
                })
            elif "api.openai.com" in url_clean:
                return jsonify({
                    "success": True,
                    "models": [
                        {"id": "gpt-4o", "object": "model"},
                        {"id": "gpt-4-turbo", "object": "model"},
                        {"id": "gpt-4", "object": "model"},
                        {"id": "gpt-3.5-turbo", "object": "model"},
                        {"id": "babbage-002", "object": "model"},
                        {"id": "davinci-002", "object": "model"}
                    ],
                    "message": f"Couldn't fetch models (HTTP {resp.status_code}). Using default list."
                })
            else:
                # Generic fallback with empty list
                return jsonify({
                    "success": False,
                    "models": [],
                    "error": f"Failed to fetch models from {models_endpoint}",
                    "status_code": resp.status_code,
                    "response": resp.text,
                })
        
        data = resp.json()
        return jsonify({"success": True, "models": data.get("data", []), "raw": data})
    except Exception as e:
        # Return a fallback model list instead of an error
        if "api.openai.com" in url_clean:
            return jsonify({
                "success": True,
                "models": [
                    {"id": "gpt-4o", "object": "model"},
                    {"id": "gpt-4-turbo", "object": "model"},
                    {"id": "gpt-4", "object": "model"},
                    {"id": "gpt-3.5-turbo", "object": "model"}
                ],
                "message": f"Error fetching models: {str(e)}. Using default list."
            })
        elif "groq.com" in url_clean:
            return jsonify({
                "success": True,
                "models": [
                    {"id": "llama3-8b-8192", "object": "model"},
                    {"id": "llama3-70b-8192", "object": "model"},
                    {"id": "mixtral-8x7b-32768", "object": "model"},
                    {"id": "gemma-7b-it", "object": "model"},
                ],
                "message": f"Error fetching models: {str(e)}. Using default list."
            })
        else:
            return jsonify({"error": str(e), "success": False, "models": []}), 500
