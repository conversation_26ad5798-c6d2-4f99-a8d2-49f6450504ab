# src/api/routes.py
# This file sets up the core functionality for <PERSON>, the AI assistant.
#
# It includes:
# - The Athena class, which processes user input and generates responses.
# - Methods for handling different types of responses, including text, commands, and Python code execution.
# - A history tracking system to maintain conversation context.
# - Integration with logging to store interactions for future reference.
#
# Athena functions as the backend logic for chat interactions, supporting both API and direct user interactions.

import requests
import json
import logging
import os
import time
import traceback
from datetime import datetime, timedelta

from flask import Blueprint, jsonify, render_template, request
from flask_login import current_user, login_required

# Import models and auth components directly instead of using the deprecated src.login module
from src.models import db, User
from src.models.api_key import APIKey
from src.models.llm_provider_setting import LLMProviderSetting
from src.models.command_toggle import CommandToggle
from src.models.log_entry import LogEntry
from flask_bcrypt import Bcrypt
bcrypt = Bcrypt()
from src.utils.auth_extensions import login_manager
import src.core as core
from src.api.devices import devices_bp
from src.api.commands import commands_bp
from src.api.device_auth import device_auth_bp
import src.utils.token_count as token_count
from src.core.knowledge_db import KnowledgeDatabase
# Instantiate a global knowledge DB client
kb = KnowledgeDatabase()

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("athena_api")

core_bp = Blueprint("core", __name__, template_folder="../../templates")

athena = core.Athena()

# Register blueprints for cross-device API
core_bp.register_blueprint(devices_bp)
core_bp.register_blueprint(commands_bp)
core_bp.register_blueprint(device_auth_bp)


@core_bp.route("/")
@login_required
def home():
    """
    Render the main chat interface.
    Accessible only to authenticated users.
    """
    logger.info("=== Route: / ===")
    return render_template("index.html", user=current_user)


@core_bp.route("/settings")
@login_required
def settings():
    """
    Render the settings interface.
    Accessible only to authenticated users.
    """
    logger.info("=== Route: /settings ===")
    # Use the app's static_version from config for consistent cache busting
    from flask import current_app

    return render_template(
        "settings.html", static_version=current_app.config["STATIC_VERSION"]
    )


@core_bp.route("/settings/<section>")
@login_required
def settings_section(section):
    """
    Render specific settings sections.
    Accessible only to authenticated users.

    Note: Admin section is handled by a specific route in auth_bp.

    Args:
        section (str): The specific settings section to render.

    Returns:
        Rendered HTML template for the specified section or 404 error if not found.
    """
    logger.info(f"=== Route: /settings/{section} ===")

    # Handle admin section with a simple interface
    if section == "admin":
        logger.info(f"Admin section requested - rendering simple admin interface")
        # Create a simple admin interface without complex authentication
        from flask import render_template, current_app

        # Get basic system information for the admin panel
        admin_data = {
            'users': [],  # We'll populate this if needed
            'system_info': {
                'version': 'AthenaCore v1.0',
                'status': 'Running'
            }
        }

        # Use the app's static_version from config for consistent cache busting
        static_version = getattr(current_app.config, 'STATIC_VERSION', int(time.time()))

        return render_template(
            f"settings/{section}.html",
            section=section,
            static_version=static_version,
            admin_data=admin_data
        )

    try:
        # Use the app's static_version from config for consistent cache busting
        from flask import current_app

        return render_template(
            f"settings/{section}.html",
            static_version=current_app.config["STATIC_VERSION"],
        )
    except Exception as e:
        logger.error(f"Error loading settings section '{section}': {str(e)}")
        return f"Error loading section: {str(e)}", 404


@core_bp.route("/api/logs")
@login_required
def get_logs():
    """
    Retrieve logs with pagination.
    Accessible only to authenticated users.

    Query Parameters:
        page (int): The page number to retrieve.
        per_page (int): Number of logs per page.

    Returns:
        JSON response containing paginated logs and statistics.
    """
    logger.info("=== Route: /api/logs ===")
    page = request.args.get("page", 1, type=int)
    per_page = request.args.get("per_page", 10, type=int)

    # Query filters
    q = request.args.get("q", type=str)
    start_date = request.args.get("start_date", type=str)
    end_date = request.args.get("end_date", type=str)
    filter_type = request.args.get("type", "all", type=str)
    filter_status = request.args.get("status", "all", type=str)
    min_tokens = request.args.get("min_tokens", type=int)

    try:
        # Base query per-user
        query = LogEntry.query.filter_by(user_id=current_user.id)

        # Text search
        if q:
            q_like = f"%{q.lower().strip()}%"
            query = query.filter(
                (LogEntry.content.ilike(q_like)) |
                (LogEntry.response.ilike(q_like))
            )
        # Date range filters
        if start_date:
            try:
                sd = datetime.fromisoformat(start_date)
                query = query.filter(LogEntry.timestamp >= sd)
            except:
                pass
        if end_date:
            try:
                ed = datetime.fromisoformat(end_date) + timedelta(days=1)
                query = query.filter(LogEntry.timestamp < ed)
            except:
                pass
        # Type/status/min_tokens filters
        if filter_type.lower() != "all":
            query = query.filter_by(message_type=filter_type)
        if filter_status.lower() != "all":
            query = query.filter_by(status=filter_status)
        if min_tokens is not None:
            query = query.filter(LogEntry.token_count >= min_tokens)

        # Pagination and stats
        total_messages = query.count()
        total_pages = (total_messages + per_page - 1) // per_page
        entries = (
            query
            .order_by(LogEntry.timestamp.desc())
            .offset((page - 1) * per_page)
            .limit(per_page)
            .all()
        )
        logs_data = [e.to_dict() for e in entries]

        all_entries = query.all()
        conv_ids = {e.conversation_id for e in all_entries}
        avg_time = (sum(e.execution_time or 0 for e in all_entries) / total_messages) if total_messages > 0 else 0
        avg_tokens_spent = (sum(e.token_count or 0 for e in all_entries) / total_messages) if total_messages > 0 else 0

        return jsonify({
            "logs": logs_data,
            "total_pages": total_pages,
            "current_page": page,
            "stats": {
                "total_conversations": len(conv_ids),
                "total_messages": total_messages,
                "avg_response_time": round(avg_time, 2),
                "avg_tokens_spent": round(avg_tokens_spent, 2),
            },
        })
    except Exception as e:
        logger.error(f"Error retrieving logs from DB: {e}")
        return jsonify({
            "logs": [],
            "total_pages": 0,
            "current_page": page,
            "stats": {
                "total_conversations": 0,
                "total_messages": 0,
                "avg_response_time": 0,
                "avg_tokens_spent": 0,
            },
        }), 500


@core_bp.route("/api/chat", methods=["POST"])
@login_required
def chat():
    """
    Handle chat interactions.
    Accessible only to authenticated users.

    Expects JSON payload:
        {
            "message": "User's message",
            "conversation_id": "Optional conversation ID",
            "model": "Selected model from direct connections"
        }

    Returns:
        JSON response containing AI's response and any execution outputs.
    """
    try:
        logger.info("=== Route: /api/chat (POST) ===")

        # Validate request data
        if not request.is_json:
            logger.error("Request isn't valid JSON")
            return jsonify({"error": "Invalid request. Expected JSON."}), 400

        data = request.json
        if not data:
            logger.error("Empty JSON data received")
            return jsonify({"error": "Empty request"}), 400

        user_input = data.get("message")
        if not user_input:
            logger.error("No message provided in request")
            return jsonify({"error": "No message provided"}), 400

        logger.info(f"User input: {user_input[:100]}...")

        start_time = datetime.now()

        # Get conversation ID from request or create a new conversation
        conversation_id = data.get("conversation_id")

        # Import models here to avoid circular imports
        from src.models import Conversation, Message, db

        # Get or create conversation
        conversation = None
        if conversation_id:
            # Try to get existing conversation
            conversation = Conversation.query.filter_by(
                id=conversation_id, user_id=current_user.id
            ).first()

        # If no conversation found or ID not provided, create a new one
        if not conversation:
            conversation = Conversation(
                user_id=current_user.id,
                title=f"Chat {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                created_at=datetime.now(),
                updated_at=datetime.now(),
                is_active=True,
            )
            db.session.add(conversation)
            db.session.flush()  # Get ID without committing
            conversation_id = conversation.id
            logger.info(
                f"Created new conversation {conversation_id} for user {current_user.id}"
            )
        logger.info(f"Conversation ID: {conversation_id}")

        # Extract model from the payload and pass it to get_response.
        model = data.get("model")
        if not model:
            logger.error("No model specified in request")
            return jsonify({"error": "No model specified"}), 400

        use_kb = data.get("use_kb", False)  # Flag from KB toggle button
        logger.info(f"use_kb flag received: {use_kb}")

        # --- Token counting ---
        token_count_value = token_count.count_tokens(model, user_input)

        # Query knowledge base directly if use_kb is True
        knowledge_context = None
        if use_kb:
            try:
                # Try to get relevant documents for the query
                from src.core.knowledge_db import AthenaKnowledgeDB
                kb_client = AthenaKnowledgeDB()
                docs = kb_client.query_knowledge(user_input, limit=3)

                if docs and len(docs) > 0:
                    # Use a simpler format to avoid nested f-string issues
                    doc_info = []
                    for doc in docs:
                        if isinstance(doc, dict):
                            doc_id = doc.get('id', 'unknown')
                            has_content = bool(doc.get('content'))
                            doc_info.append(f"{doc_id}(has_content={has_content})")
                    
                    logger.info(f"KB query returned {len(docs)} docs: {doc_info}")

                # Filter out any documents without content
                valid_docs = []
                for doc in docs:
                    if isinstance(doc, dict) and doc.get('content'):
                        valid_docs.append(doc)
                    elif hasattr(doc, 'page_content'):
                        valid_docs.append({"content": doc.page_content, "metadata": getattr(doc, 'metadata', {})})
                    elif isinstance(doc, str):
                        valid_docs.append({"content": doc})

                # Create a list of content types to avoid nested f-string issues
                content_types = []
                for doc in valid_docs:
                    if isinstance(doc, dict) and 'content' in doc:
                        content_type = type(doc.get('content')).__name__
                        content_types.append(content_type)
                    else:
                        content_types.append('unknown')
                
                logger.info(f"After fixing, {len(valid_docs)} valid documents remain with content types: {content_types}")

                # Format KB context for the prompt
                if valid_docs:
                    knowledge_context = "\n\nRelevant information from Knowledge Base:\n\n"
                    for i, doc in enumerate(valid_docs):
                        content = doc.get('content', '')
                        metadata = doc.get('metadata', {})
                        title = metadata.get('title', f"Document {i+1}")
                        knowledge_context += f"---DOCUMENT: {title}---\n{content}\n\n"

                    knowledge_context += (
                        "\nUse the above information to answer the user's question. "
                        "If the information doesn't directly answer their question, acknowledge that and provide the most relevant information you can."
                    )
                    logger.info(
                        f"Created knowledge context with {len(valid_docs)} documents and {len(knowledge_context)} characters"
                    )
            except Exception as kb_error:
                logger.error(f"Error getting KB context: {str(kb_error)}")
                traceback.print_exc()

        # Get response with error handling for None returns
        result = athena.get_response(
            user_input, model=model, conversation_id=conversation_id, use_kb=use_kb, knowledge_context=knowledge_context
        )

        # Handle None or incomplete return values
        if result is None:
            response_text = "I encountered an error processing your request. Please try again or check your API configuration."
            command = None
            python_code = None
        else:
            try:
                # Try to unpack the result tuple
                response_text, command, python_code = result
            except (ValueError, TypeError):
                # If unpacking fails, handle it gracefully
                if isinstance(result, str):
                    # If result is just a string, use it as response
                    response_text = result
                    command = None
                    python_code = None
                else:
                    # Unknown result format
                    response_text = "Received an unexpected response format. Please check your configuration."
                    command = None
                    python_code = None
        logger.info(f"Response generated: {response_text[:100]}...")
        assistant_token_count = token_count.count_tokens(model, response_text)
        total_token_count = token_count_value + assistant_token_count
        output = None
        status = "success"
        message_type = "chat"

        try:
            if command:
                logger.info("Executing command...")
                output = athena.execute_command(command)
                message_type = "command"
                response_text = f"RESPONSE: {response_text}\nCOMMAND: {command}"
            elif python_code:
                logger.info("Executing Python code...")
                output = athena.execute_python(python_code)
                message_type = "python"
                response_text = (
                    f"RESPONSE: {response_text}\nPYTHON: {python_code}\n[END_PYTHON]"
                )
        except Exception as exec_error:
            logger.error(f"Error executing command or code: {str(exec_error)}")
            output = f"Error: {str(exec_error)}"
            status = "error"

        if output and "Error" in output:
            status = "error"
            logger.error(f"Execution resulted in error: {output[:100]}...")

        execution_time = (datetime.now() - start_time).total_seconds() * 1000
        logger.info(f"Execution time: {execution_time}ms")

        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "conversation_id": conversation_id,
            "message_type": message_type,
            "content": user_input,
            "response": response_text,
            "code": command if command else (python_code if python_code else None),
            "output": output if output else None,
            "execution_time": execution_time,
            "status": status,
            "user_token_count": token_count_value,
            "assistant_token_count": assistant_token_count,
            "token_count": total_token_count,
        }

        try:
            core.save_log(log_entry)
            athena.update_history("user", user_input)
            athena.update_history("assistant", response_text)
            athena.state_manager.add_memory(user_input, response_text)
        except Exception as memory_error:
            logger.error(f"Error updating history or memory: {str(memory_error)}")
            # Continue execution even if memory update fails

        try:
            # Save messages to the database
            # First, save the user message with token count metadata
            user_message = Message(
                conversation_id=conversation_id,
                role="user",
                content=user_input,
                timestamp=datetime.now(),
                message_metadata=json.dumps({"token_count": token_count_value}),
            )
            db.session.add(user_message)

            # Then, save the assistant's response with metadata
            metadata = {}
            if output:
                metadata["output"] = output
            if command:
                metadata["command"] = command
            elif python_code:
                metadata["code"] = python_code
            metadata["token_count"] = total_token_count

            assistant_message = Message(
                conversation_id=conversation_id,
                role="assistant",
                content=response_text,
                timestamp=datetime.now(),
                message_metadata=json.dumps(metadata) if metadata else None,
            )
            db.session.add(assistant_message)

            # Update conversation timestamp
            conversation.updated_at = datetime.now()
            db.session.commit()
        except Exception as db_error:
            logger.error(f"Error saving to database: {str(db_error)}")
            db.session.rollback()
            # Continue to return response even if DB save fails

        # Attach KB source documents if applicable
        sources = []
        if use_kb:
            try:
                # Debug: raw ChromaDB vector search result before semantic filter
                try:
                    raw_res = kb.collection.query(query_texts=[user_input], n_results=3)
                    logger.info(f"Raw Chroma response for '{user_input}': {raw_res}")
                except Exception as e:
                    logger.error(f"Error fetching raw Chroma response: {e}")
                # Debug: log total docs in KB
                total_docs = kb.get_document_count()
                logger.info(f"KnowledgeBase document count: {total_docs}")
                docs = kb.query_knowledge(user_input, limit=3)
                # More detailed logging to understand the document structure
                doc_ids = []
                for i, doc in enumerate(docs):
                    if doc:
                        doc_id = None
                        if 'id' in doc:
                            doc_id = doc['id']
                        elif 'metadata' in doc and doc['metadata'] and isinstance(doc['metadata'], dict) and 'id' in doc['metadata']:
                            doc_id = doc['metadata']['id']
                        
                        # Check if the document has content
                        has_content = False
                        if 'content' in doc and doc['content']:
                            has_content = True
                        elif 'documents' in doc and doc['documents']:
                            has_content = True
                            
                        doc_ids.append(f"{doc_id}(has_content={has_content})")
                    else:
                        doc_ids.append(f"None at index {i}")
                        
                logger.info(f"KB query returned {len(docs)} docs: {doc_ids}")
                
                # Fix any None documents in the results
                fixed_docs = []
                for doc in docs:
                    if doc is None:
                        continue
                        
                    # Ensure the document has all expected fields
                    if 'content' not in doc or not doc['content']:
                        # If there's a 'documents' field, use that as content
                        if 'documents' in doc and doc['documents']:
                            doc['content'] = doc['documents'][0] if isinstance(doc['documents'], list) else doc['documents']
                            
                    # Only add documents that have actual content
                    if ('content' in doc and doc['content']) or ('documents' in doc and doc['documents']):
                        # Make sure the document's content is a string for consistent handling
                        if 'content' in doc and isinstance(doc['content'], list) and len(doc['content']) > 0:
                            doc['content'] = doc['content'][0]
                        elif 'documents' in doc and isinstance(doc['documents'], list) and len(doc['documents']) > 0:
                            doc['content'] = doc['documents'][0]
                            
                        # Add the properly formatted document
                        fixed_docs.append(doc)
                        
                # Replace the original docs list with the fixed version
                docs = fixed_docs
                logger.info(f"After fixing, {len(docs)} valid documents remain with content types: {[type(d.get('content')).__name__ for d in docs]}")
                
                # Log the complete document structure for debugging
                for i, doc in enumerate(docs[:2]):  # Log first 2 docs to avoid excessive output
                    content_sample = str(doc.get('content', ''))[:100] + '...' if doc.get('content') else 'None'
                    logger.debug(f"Document {i} structure: {list(doc.keys())}, content sample: {content_sample}")
                
                # Use full document text for preview
                for d in docs:
                    meta = d.get("metadata", {})
                    content = d.get("content", "") or ""
                    src_name = meta.get("source") or meta.get("title") or "Document"
                    sources.append({
                        "id":      meta.get("id", ""),
                        "source":  src_name,
                        "preview": content,
                    })
            except Exception as e:
                logger.error(f"Error querying KB: {e}")
                sources = []

        return jsonify(
            {
                "response": response_text,
                "output": output,
                "code": python_code if python_code else None,
                "conversation_id": conversation_id,
                "token_count": total_token_count,
                "sources": sources,
            }
        )

    except Exception as e:
        logger.error(f"Unexpected error in chat endpoint: {str(e)}")
        traceback.print_exc()
        return jsonify(
            {"error": "An unexpected error occurred. Please try again."}
        ), 500


@core_bp.route("/api/models", methods=["GET"])
@login_required
def list_models():
    """List available models (OpenAI compatible)"""
    models = [
        {
            "id": "athena-default",
            "object": "model",
            "created": int(datetime.now().timestamp()),
            "owned_by": "athena",
        }
    ]

    return jsonify({"object": "list", "data": models})


@core_bp.route("/api/completions", methods=["POST"])
@login_required
def create_completion():
    """Create a completion (OpenAI compatible)"""
    data = request.json or {}

    prompt = data.get("prompt", "")
    model = data.get("model", "athena-default")
    max_tokens = data.get("max_tokens", 150)
    temperature = data.get("temperature", 0.7)

    start_time = datetime.now()
    # Pass model to get_response
    response_text, _, _ = athena.get_response(prompt, model=model)
    execution_time = (datetime.now() - start_time).total_seconds() * 1000

    log_entry = {
        "timestamp": datetime.now().isoformat(),
        "message_type": "completion",
        "content": prompt,
        "response": response_text,
        "execution_time": execution_time,
        "status": "success",
        "api": True,
    }
    core.save_log(log_entry)

    completion_id = f"cmpl-{os.urandom(6).hex()}"
    created_timestamp = int(datetime.now().timestamp())

    return jsonify(
        {
            "id": completion_id,
            "object": "text_completion",
            "created": created_timestamp,
            "model": model,
            "choices": [
                {
                    "text": response_text,
                    "index": 0,
                    "logprobs": None,
                    "finish_reason": "stop",
                }
            ],
            "usage": {
                "prompt_tokens": len(prompt) // 4,
                "completion_tokens": len(response_text) // 4,
                "total_tokens": (len(prompt) + len(response_text)) // 4,
            },
        }
    )


@core_bp.route("/api/chat/completions", methods=["POST"])
@login_required
def create_chat_completion():
    """Create a chat completion (OpenAI compatible)"""
    data = request.json or {}

    messages = data.get("messages", [])
    model = data.get("model", "athena-default")
    temperature = data.get("temperature", 0.7)
    max_tokens = data.get("max_tokens", 150)

    if not messages:
        return jsonify({"error": "Messages are required"}), 400

    conversation = []
    for msg in messages:
        role = msg.get("role", "user")
        content = msg.get("content", "")
        conversation.append(f"{role}: {content}")

    prompt = "\n".join(conversation)
    start_time = datetime.now()
    # Pass model to get_response
    response_text, _, _ = athena.get_response(prompt, model=model)
    execution_time = (datetime.now() - start_time).total_seconds() * 1000

    log_entry = {
        "timestamp": datetime.now().isoformat(),
        "message_type": "chat_completion",
        "content": prompt,
        "response": response_text,
        "execution_time": execution_time,
        "status": "success",
        "api": True,
    }
    core.save_log(log_entry)

    completion_id = f"chatcmpl-{os.urandom(6).hex()}"
    created_timestamp = int(datetime.now().timestamp())

    return jsonify(
        {
            "id": completion_id,
            "object": "chat.completion",
            "created": created_timestamp,
            "model": model,
            "choices": [
                {
                    "index": 0,
                    "message": {"role": "assistant", "content": response_text},
                    "finish_reason": "stop",
                }
            ],
            "usage": {
                "prompt_tokens": len(prompt) // 4,
                "completion_tokens": len(response_text) // 4,
                "total_tokens": (len(prompt) + len(response_text)) // 4,
            },
        }
    )


@core_bp.route("/api/keys", methods=["GET"])
@login_required
def list_api_keys():
    """Get all API keys for the user"""
    if current_user and current_user.is_authenticated:
        user_id = current_user.id
    else:
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            key_id = auth_header[7:]
            api_key = APIKey.query.get(key_id)
            if not api_key:
                return jsonify({"error": "Invalid API key"}), 403
            user_id = api_key.user_id
        else:
            return jsonify({"error": "Authentication required"}), 401

    keys = APIKey.query.filter_by(user_id=user_id).all()
    keys_data = [key.to_dict() for key in keys]

    return jsonify({"object": "list", "data": keys_data})


@core_bp.route("/api/keys", methods=["POST"])
@login_required
def create_api_key():
    """Create a new API key"""
    if not current_user.is_authenticated:
        return jsonify({"error": "Authentication required"}), 401

    data = request.json or {}
    key_name = data.get("name")

    if not key_name:
        count = APIKey.query.filter_by(user_id=current_user.id).count() + 1
        key_name = f"API Key {count}"

    new_key_id = f"ath-{os.urandom(16).hex()}"

    api_key = APIKey(
        id=new_key_id,
        name=key_name,
        user_id=current_user.id,
        created_at=datetime.utcnow(),
    )

    db.session.add(api_key)
    db.session.commit()

    return jsonify(
        {
            "id": api_key.id,
            "name": api_key.name,
            "created": int(api_key.created_at.timestamp()),
        }
    )


@core_bp.route("/api/keys/<key_id>", methods=["DELETE"])
@login_required
def delete_api_key(key_id):
    """Delete an API key"""
    if not current_user.is_authenticated:
        return jsonify({"error": "Authentication required"}), 401

    api_key = APIKey.query.get(key_id)
    if not api_key:
        return jsonify({"error": "Key not found"}), 404

    if api_key.user_id != current_user.id:
        return jsonify({"error": "Not authorized to delete this key"}), 403

    db.session.delete(api_key)
    db.session.commit()

    return jsonify({"id": key_id, "deleted": True})


@core_bp.route("/api/test", methods=["GET"])
@login_required
def test_api():
    """Test endpoint to verify API is working"""
    return jsonify(
        {
            "status": "success",
            "message": "API is working correctly",
            "version": "v1",
            "timestamp": datetime.now().isoformat(),
        }
    )


# LLM Settings endpoint using database storage
@core_bp.route("/api/settings/llm", methods=["GET", "POST"])
@login_required
def llm_settings():
    """
    Handle LLM provider settings via the database.
    """
    logger.info(f"=== LLM Settings API: {request.method} request received ===")

    if request.method == "GET":
        settings_record = LLMProviderSetting.query.filter_by(user_id=current_user.id).first()
        if not settings_record:
            settings_record = LLMProviderSetting(
                provider="openai",
                secret="",
                model="gpt-4o",
                temperature=0.7,
                max_tokens=4000,
                user_id=current_user.id,
            )
            db.session.add(settings_record)
            db.session.commit()
            logger.info("Created default LLM provider settings record")
        return jsonify(
            {
                "provider": settings_record.provider,
                "api_key": settings_record.secret,
                "model": settings_record.model,
                "temperature": settings_record.temperature,
                "max_tokens": settings_record.max_tokens,
                "embedding_model": settings_record.embedding_model,
            }
        )

    elif request.method == "POST":
        try:
            logger.info("Received new settings via API")
            new_config = request.json
            if not new_config or not isinstance(new_config, dict):
                error_msg = "Invalid configuration format"
                logger.error(error_msg)
                return jsonify({"success": False, "error": error_msg}), 400

            # Require OpenAI API Key if using Ada embedding
            desired_embedding = new_config.get("embedding_model", None)
            provided_key = new_config.get("api_key") or getattr(settings_record, 'secret', None)
            if desired_embedding == "text-embedding-ada-002" and not provided_key:
                error_msg = "OpenAI API Key is required for Ada embeddings"
                logger.error(error_msg)
                return jsonify({"success": False, "error": error_msg}), 400

            settings_record = LLMProviderSetting.query.filter_by(user_id=current_user.id).first()
            if not settings_record:
                settings_record = LLMProviderSetting(user_id=current_user.id)

            settings_record.provider = new_config.get("provider", "openai")
            if new_config.get("api_key"):
                settings_record.secret = new_config["api_key"]
                if settings_record.provider == "anthropic":
                    os.environ["ANTHROPIC_API_KEY"] = settings_record.secret
                    logger.info("Updated ANTHROPIC_API_KEY in environment")
                else:
                    os.environ["OPENAI_API_KEY"] = settings_record.secret
                    logger.info("Updated OPENAI_API_KEY in environment")
            settings_record.model = new_config.get("model", "gpt-4o")
            settings_record.temperature = new_config.get("temperature", 0.7)
            settings_record.max_tokens = new_config.get("max_tokens", 4000)
            settings_record.embedding_model = new_config.get("embedding_model", settings_record.embedding_model)
            db.session.commit()
            logger.info("LLM settings saved successfully via DB")

            if hasattr(athena, "reload_provider"):
                logger.info("Reloading Athena's provider")
                result = athena.reload_provider()
                if not result:
                    logger.warning("Failed to reload provider")
            else:
                logger.warning("Athena doesn't have reload_provider method")

            return jsonify({"success": True})

        except Exception as e:
            error_msg = f"Error saving config: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            return jsonify({"success": False, "error": error_msg}), 500


### NEW ROUTE: get_llm_models ###
@core_bp.route("/api/settings/models/<provider>", methods=["GET"])
@login_required
def get_llm_models(provider):
    """
    Dynamically fetch model list from the chosen provider.
    For OpenAI, we call /v1/models with the server's OPENAI_API_KEY.
    For Anthropic, adapt similarly or return an example list.
    """
    provider = provider.lower()
    if provider == "openai":
        settings = LLMProviderSetting.query.filter_by(user_id=current_user.id).first()
        openai_api_key = settings.secret if settings and settings.provider == "openai" else ""
        if not openai_api_key:
            return jsonify({"success": False, "error": "OpenAI API key not set"}), 400

        try:
            headers = {"Authorization": f"Bearer {openai_api_key}"}
            resp = requests.get(
                "https://api.openai.com/v1/models", headers=headers, timeout=10
            )
            if resp.status_code != 200:
                return jsonify(
                    {"success": False, "error": f"OpenAI API error: {resp.text}"}
                ), 400
            data = resp.json()
            # data should have "data" = list of model objects
            # We'll filter them to only show "gpt" or "text-" etc.
            # Adjust the filter as you see fit:
            model_ids = []
            for m in data.get("data", []):
                model_id = m.get("id", "")
                # Example filter: only show GPT-based or text-based models
                if any(
                    x in model_id.lower()
                    for x in ("gpt", "davinci", "curie", "babbage", "ada")
                ):
                    model_ids.append(model_id)

            model_ids = sorted(set(model_ids))
            return jsonify({"success": True, "models": model_ids})
        except Exception as e:
            return jsonify({"success": False, "error": str(e)}), 500

    elif provider == "anthropic":
        # Fetch Anthropic models using the API
        settings = LLMProviderSetting.query.filter_by(user_id=current_user.id).first()
        anthropic_api_key = settings.secret if settings and settings.provider == "anthropic" else ""
        if not anthropic_api_key:
            return jsonify(
                {"success": False, "error": "ANTHROPIC API key not set"}
            ), 400

        try:
            # Since Anthropic doesn't have a models listing endpoint like OpenAI,
            # we'll list the most current models manually
            # But we'll structure this to make it easy to update when they release new models
            available_models = [
                # Latest Claude models
                "claude-3-opus-20240229",
                "claude-3-sonnet-20240229",
                "claude-3-haiku-20240307",
                # Legacy Claude models
                "claude-2.1",
                "claude-2.0",
                "claude-instant-1.2",
                # Additional models for completeness
                "claude-2",
                "claude-1.3",
                "claude-1.3-100k",
                "claude-instant-v1",
            ]

            # Validate the API key with a simple call
            headers = {
                "x-api-key": anthropic_api_key,
                "anthropic-version": "2023-06-01",
            }
            resp = requests.get(
                "https://api.anthropic.com/v1/models", headers=headers, timeout=10
            )

            # If the API call is successful, we know the key is valid
            # In future versions, if Anthropic adds a models listing endpoint,
            # we can parse the response to get the actual models
            if resp.status_code == 200:
                return jsonify({"success": True, "models": available_models})
            else:
                # Return a useful error with status code
                return jsonify(
                    {
                        "success": False,
                        "error": f"Anthropic API error: {resp.status_code} - {resp.text}",
                    }
                ), 400

        except Exception as e:
            # Fallback to returning hardcoded models even if API call fails
            # This helps users who have valid keys but might have network issues
            return jsonify(
                {
                    "success": True,
                    "models": available_models,
                    "warning": f"Could not validate API connection: {str(e)}",
                }
            )

    elif provider == "google":
        # Fetch Google models using API key from DirectConnections
        google_api_key = None

        # Try to get Google API key from DirectConnections
        try:
            # Look for a DirectConnection with Google API configuration
            google_connection = DirectConnection.query.filter_by(
                user_id=current_user.id,
                enabled=True
            ).filter(
                DirectConnection.url.like('%google%') |
                DirectConnection.url.like('%generativelanguage%')
            ).filter(DirectConnection.api_key.isnot(None)).first()

            if google_connection and google_connection.api_key:
                google_api_key = google_connection.api_key
            else:
                # Fallback: look for any connection that might have a Google API key
                # This is less ideal but provides backward compatibility
                from src.models.configuration import Configuration
                config_entry = Configuration.query.filter_by(key='google_api_key').first()
                if config_entry and config_entry.value:
                    google_api_key = config_entry.value
        except Exception as e:
            logger.error(f"Error retrieving Google API key: {e}")

        if not google_api_key:
            return jsonify({
                "success": False,
                "error": "Google API key not configured. Please add a Google API connection in DirectConnections."
            }), 400

        try:
            # Since Google doesn't have a comprehensive models listing endpoint,
            # we'll list the most current models manually
            available_models = [
                # PaLM 2 models
                "chat-bison",
                "chat-bison-32k",
                "text-bison",
                "text-bison-32k",
                # Gemini models
                "gemini-pro",
                "gemini-pro-vision",
                "gemini-ultra",
                "gemini-1.5-pro",
                "gemini-1.5-flash",
            ]

            # Validate the API key
            headers = {"x-goog-api-key": google_api_key}
            model_endpoint = "https://generativelanguage.googleapis.com/v1beta/models"
            resp = requests.get(model_endpoint, headers=headers, timeout=10)

            if resp.status_code == 200:
                # If we can get the actual models, great
                try:
                    actual_models = resp.json().get("models", [])
                    model_names = [
                        model.get("name", "").split("/")[-1] for model in actual_models
                    ]
                    if model_names:
                        # If the API returned models, use those instead
                        return jsonify({"success": True, "models": sorted(model_names)})
                except Exception as e:
                    # If parsing fails, fall back to our predefined list
                    logger.error(f"Error parsing Google models response: {e}")

                # If we reached here, use our predefined list
                return jsonify({"success": True, "models": available_models})
            else:
                # Return error with status code
                return jsonify(
                    {
                        "success": False,
                        "error": f"Google API error: {resp.status_code} - {resp.text}",
                    }
                ), 400

        except Exception as e:
            # Fallback to returning hardcoded models even if API call fails
            return jsonify(
                {
                    "success": True,
                    "models": available_models,
                    "warning": f"Could not validate API connection: {str(e)}",
                }
            )

    elif provider == "microsoft":
        # Fetch Microsoft Azure OpenAI models using API key from DirectConnections
        azure_api_key = None
        azure_endpoint = None

        # Try to get Azure API key and endpoint from DirectConnections
        try:
            # Look for a DirectConnection with Azure OpenAI configuration
            azure_connection = DirectConnection.query.filter_by(
                user_id=current_user.id,
                enabled=True
            ).filter(
                DirectConnection.url.like('%azure%') |
                DirectConnection.url.like('%openai.azure%')
            ).filter(DirectConnection.api_key.isnot(None)).first()

            if azure_connection and azure_connection.api_key:
                azure_api_key = azure_connection.api_key
                azure_endpoint = azure_connection.url
            else:
                # Fallback: look in Configuration table
                from src.models.configuration import Configuration
                api_key_config = Configuration.query.filter_by(key='azure_openai_api_key').first()
                endpoint_config = Configuration.query.filter_by(key='azure_openai_endpoint').first()

                if api_key_config and api_key_config.value:
                    azure_api_key = api_key_config.value
                if endpoint_config and endpoint_config.value:
                    azure_endpoint = endpoint_config.value
        except Exception as e:
            logger.error(f"Error retrieving Azure API configuration: {e}")

        if not azure_api_key or not azure_endpoint:
            return jsonify(
                {
                    "success": False,
                    "error": "Azure OpenAI API key or endpoint not configured. Please add an Azure OpenAI connection in DirectConnections.",
                }
            ), 400

        try:
            # Since Azure OpenAI requires deployment-specific endpoints,
            # we'll provide both standard and preset models
            available_models = [
                # GPT models commonly deployed on Azure
                "gpt-35-turbo",
                "gpt-35-turbo-16k",
                "gpt-4",
                "gpt-4-32k",
                "gpt-4-turbo",
                "gpt-4-vision",
            ]

            # Try to list deployments if possible
            # Note: This requires the user to have admin rights on their Azure OpenAI resource
            headers = {"api-key": azure_api_key}
            try:
                deployments_url = (
                    f"{azure_endpoint}/openai/deployments?api-version=2023-05-15"
                )
                resp = requests.get(deployments_url, headers=headers, timeout=10)

                if resp.status_code == 200:
                    data = resp.json()
                    if "data" in data:
                        deployment_names = [d.get("id") for d in data.get("data", [])]
                        if deployment_names:
                            # If we got actual deployments, use those
                            return jsonify(
                                {"success": True, "models": sorted(deployment_names)}
                            )
            except Exception as e:
                # If we can't get deployments, just continue to fallback
                logger.error(f"Could not list Azure deployments: {e}")

            # Fallback to our predefined list
            return jsonify({"success": True, "models": available_models})

        except Exception as e:
            # Fallback to returning hardcoded models even if API call fails
            return jsonify(
                {
                    "success": True,
                    "models": available_models,
                    "warning": f"Could not validate Azure connection: {str(e)}",
                }
            )

    else:
        return jsonify(
            {"success": False, "error": f"Unknown provider: {provider}"}
        ), 400


### NEW ROUTE: commands_settings ###
@core_bp.route("/api/settings/commands", methods=["GET", "POST"])
@login_required
def commands_settings():
    """
    GET: Return the list of available commands (pulled from code via list_commands())
         merged with the current toggle settings in the database.
    POST: Update the toggles in the database.
    """

    if request.method == "GET":
        try:
            # 1) Get all commands from code
            available_cmds = (
                core.list_commands()
            )  # e.g. {"spotify": "...", "obsidian": "..."}

            logger.info("DEBUG: Available commands from code:", available_cmds)

            toggles = []
            # 2) For each command, ensure there's a DB row in command_toggles
            for cmd_name in available_cmds:
                toggle = CommandToggle.query.filter_by(
                    command_name=cmd_name, user_id=current_user.id
                ).first()
                if not toggle:
                    toggle = CommandToggle(
                        command_name=cmd_name, enabled=True, user_id=current_user.id
                    )
                    db.session.add(toggle)
                    db.session.commit()
                    logger.info(f"DEBUG: Created new toggle for {cmd_name} for user {current_user.id}")
                toggles.append(toggle)

            # 3) Build response
            data = []
            for t in toggles:
                data.append(
                    {
                        "id": t.id,
                        "command_name": t.command_name,
                        "enabled": t.enabled,
                        "description": available_cmds.get(t.command_name, ""),
                    }
                )

            response = {"success": True, "commands": data}
            logger.info("DEBUG: Returning response:", response)
            return jsonify(response)

        except Exception as e:
            logger.error("ERROR loading command toggles:", str(e))
            return jsonify({"success": False, "error": str(e)}), 500

    elif request.method == "POST":
        try:
            payload = request.json
            if not payload or "commands" not in payload:
                return jsonify({"success": False, "error": "Invalid payload"}), 400

            # 4) Update toggles in DB
            for cmd_item in payload["commands"]:
                cmd_name = cmd_item.get("command_name")
                enabled = cmd_item.get("enabled", True)
                if not cmd_name:
                    continue
                toggle = CommandToggle.query.filter_by(
                    command_name=cmd_name, user_id=current_user.id
                ).first()
                if toggle:
                    toggle.enabled = bool(enabled)
                else:
                    toggle = CommandToggle(
                        command_name=cmd_name, enabled=bool(enabled), user_id=current_user.id
                    )
                    db.session.add(toggle)

            db.session.commit()
            return jsonify({"success": True, "commands": payload["commands"]})
        except Exception as e:
            logger.error("ERROR updating command toggles:", str(e))
            return jsonify({"success": False, "error": str(e)}), 500


### NEW ROUTE: list_kb_docs ###
@core_bp.route("/api/kb/docs", methods=["GET"])
@login_required
def list_kb_docs():
    """
    Return all documents in the knowledge base for debugging.
    """
    docs = [d for d in kb.search_documents({"user_id": current_user.id}) if d.get("metadata", {}).get("user_id") == current_user.id]
    return jsonify({
        "count": len(docs),
        "documents": [{"id": d.get("id"), "title": d.get("title")} for d in docs],
    })


### NEW ROUTE: search_knowledge ###
@core_bp.route("/api/knowledge/search", methods=["POST"])
@login_required
def search_knowledge_api():
    """Search the knowledge base with optional full document retrieval."""
    try:
        data = request.get_json(force=True)
        query = data.get("query")
        limit = data.get("limit", 5)
        full_document = data.get("full_document", False)
        if not query:
            return jsonify({"error": "Query is required"}), 400
        # Perform search (with optional full-document retrieval)
        from src.core.knowledge_db import search_knowledge
        results = search_knowledge(query, limit, full_document, filters={"user_id": current_user.id})
        return jsonify({"results": results})
    except Exception as e:
        logger.error(f"Error searching knowledge: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500

### NEW ROUTE: get_document_by_id ###
@core_bp.route("/api/knowledge/document/<doc_id>", methods=["GET"])
@login_required
def get_document_api(doc_id):
    """Retrieve a full document by its ID from the knowledge base."""
    try:
        # Retrieve full document by ID
        document = kb.get_document(doc_id)
        if not document:
            return jsonify({"error": "Document not found"}), 404
        if document.get("metadata", {}).get("user_id") != current_user.id:
            return jsonify({"error": "Not authorized"}), 403
        return jsonify({"document": document})
    except Exception as e:
        logger.error(f"Error retrieving document: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500

### NEW ROUTE: start-services ###
@core_bp.route("/start-services", methods=["GET"])
@core_bp.route("/start_services", methods=["GET"])  # Alternative URL without hyphen
def start_services():
    """
    Initialize all Athena background services.
    This endpoint is called during startup to ensure all services are running.
    
    Returns:
        JSON response with status information
    """
    try:
        # Import and initialize services - using the proper import paths
        from src.services import init_app as init_services
        from flask import current_app
        
        # Initialize all services through the main service module
        init_services(current_app)
        
        # All services are now initialized
        config_status = True
        task_status = True
        socket_status = True
        
        logger = logging.getLogger("main")
        logger.info("Athena services initialized")
        
        return jsonify({
            "status": "success",
            "services": {
                "config": config_status,
                "task": task_status,
                "socket": socket_status
            }
        }), 200
    except Exception as e:
        # Log error and return failure response
        logger = logging.getLogger("main")
        logger.error(f"Error initializing services: {str(e)}")
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500
