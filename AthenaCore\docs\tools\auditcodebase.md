# AthenaCore Codebase Auditor

## Overview

The `auditcodebase.py` script is a powerful tool for analyzing the AthenaCore project structure, identifying dependencies, and maintaining codebase health. It builds a comprehensive dependency graph, identifies unused files, and provides detailed reports to help with refactoring and maintenance.

## Key Features

### Core Functionality

- **Recursive Analysis**: Analyzes all Python files in the AthenaCore project, not just root level
- **Import Resolution**: Handles both absolute and relative imports correctly, including package imports
- **Dependency Graph**: Builds a complete dependency graph with reverse dependencies
- **Circular Import Detection**: Identifies circular import relationships that may cause issues
- **Entry Point Tracing**: Identifies used files by traversing from defined entry points
- **Safelist Protection**: Prevents critical files from being mistakenly archived
- **Backup Creation**: Creates timestamped backups before any file operations
- **Detailed Reporting**: Generates comprehensive reports on codebase structure

### AthenaCore-Specific Features

- **Knowledge Base Analysis**: Special focus on KB components (`--kb-focus`)
- **Deprecated Import Detection**: Finds outdated imports, e.g., `src.login` (`--check-deprecated`)
- **Critical Component Verification**: Checks for required files like `kb_catalog.py` and `document_processor.py`

## Known Limitations and Considerations

### Important Note About Unused Files

The script determines "used" files by tracing imports from designated entry points. However, there are several reasons why files may be incorrectly flagged as "unused":

1. **Dynamic Imports**: Files loaded through `importlib`, `__import__()`, or other dynamic import mechanisms
2. **Flask Blueprints**: Blueprint files registered in Flask apps without direct imports
3. **Import through `__init__.py`**: Files imported in `__init__.py` using `import *` or other mechanisms
4. **Module Auto-discovery**: Files discovered through pattern matching or directory scanning
5. **Plugin Architecture**: Dynamically loaded plugins or extensions
6. **Import Resolution**: Complex relative imports like `..models` may not be fully resolved

### Files That Should Never Be Archived

The following file types should generally be considered "used" even if not directly imported:

- **API Endpoints**: `src/api/*` files that define routes but may not have direct imports
- **Controllers**: `src/controllers/*` files handling application logic
- **Core Components**: `src/core/*` files implementing core functionality
- **Command Modules**: Any files under `src/core/commands/` that define system commands
- **Model Definitions**: `src/models/*` files defining data models
- **Service Modules**: `src/services/*` files providing system services
- **Test Files**: Files under `tests/` that may only be used during testing

## How to Use

### Installation of Dependencies

Before using visualization features, install the required dependencies:

```bash
pip install matplotlib networkx
```

For detailed metrics with code complexity analysis:

```bash
pip install radon
```

### Basic Usage

For a safe audit without moving files:

```bash
python auditcodebase.py --dry-run
```

For a complete analysis with visualization:

```bash
python auditcodebase.py --dry-run --kb-focus --check-deprecated --visualize --detailed
```

To archive unused files after review (use with caution):

```bash
python auditcodebase.py
```

### Command Line Options

- `--dry-run`: Generate reports without moving files
- `--visualize`: Generate dependency graph visualization (requires matplotlib and networkx to be installed separately)
- `--detailed`: Calculate detailed code complexity metrics (requires radon)
- `--force`: Move files even if in safelist (use with extreme caution)
- `--kb-focus`: Focus analysis on Knowledge Base components
- `--check-deprecated`: Check for deprecated imports (like src.login)
- `--custom-entry`: Add custom entry points (e.g., `--custom-entry src/main.py scripts/tool.py`)
- `--output`: Specify custom output directory for reports
- `--verbose`: Show detailed progress during audit

## Package Structure Considerations

### Entry Points

The script currently uses the following as entry points:

```python
ENTRY_POINTS = [
    'main.py',                    # Main Flask application entry point
    'start_athena.py',            # Startup script for Athena services
    'wsgi.py',                    # WSGI entry point for production
    'run_tests.py',               # Test runner
    'cli.py',                     # CLI tools
    'auditcodebase.py',           # This script
]
```

**Important**: If AthenaCore uses `src/main.py` as an entry point rather than `main.py`, you should add it to the entry points:

```bash
python auditcodebase.py --custom-entry src/main.py
```

### `__init__.py` Files

The script currently marks `__init__.py` files as "used" if they are in directories containing other used Python files. However, it may not fully resolve imports within `__init__.py` files, especially complex patterns like:

```python
from .submodule import *  # Imports all from submodule
```

### Module Handling Recommendations

For the AthenaCore project structure:

1. **Consider all files in critical directories as "used"**: This is especially important for API endpoints, controllers, and service files.
2. **Add explicit imports**: Where possible, make imports explicit rather than using wildcard imports.
3. **Use the `--force` flag carefully**: Never use this flag unless you're absolutely sure.

## Troubleshooting

### Files Incorrectly Marked as Unused

If the script incorrectly marks files as unused:

1. Check if the file is imported dynamically or through a pattern not recognized by the script
2. Add the file or its directory to the `SAFELIST_PATTERNS` in the script
3. Use `--custom-entry` to add additional entry points that might import the file
4. Modify the `_is_special_file` method in the script to exclude specific patterns

### Improving Detection Accuracy

To improve the accuracy of used file detection:

1. Always run with `--dry-run` first and review reports
2. Consider adding more entry points with `--custom-entry`
3. Add directories with critical functionality to `SAFELIST_PATTERNS`
4. Modify the script to handle your project's specific import patterns

## Enhancement Suggestions

To enhance the script for the AthenaCore project:

1. **API Route Detection**: Add specific handling for Flask routes and blueprints
2. **Plugin Discovery**: Add detection for dynamically loaded plugins
3. **Runtime Import Analysis**: Consider instrumenting code to detect runtime imports
4. **Incremental Updates**: Allow for incremental updates to the dependency graph
5. **Integration with Testing**: Verify that archived files don't break tests

## Conclusion

The AthenaCore Codebase Auditor is a powerful tool for managing codebase health, but should be used with careful consideration of your project's specific import patterns and architecture. Always run in `--dry-run` mode first and thoroughly review reports before archiving any files.
