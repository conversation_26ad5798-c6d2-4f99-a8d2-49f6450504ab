"""
Error handlers for the API routes
"""

import logging
from datetime import datetime
from flask import Blueprint, jsonify, request, current_app
from flask_login import current_user
from werkzeug.exceptions import NotFound

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger("error_handlers")

error_handlers_bp = Blueprint("error_handlers", __name__)

@error_handlers_bp.app_errorhandler(NotFound)
def handle_not_found_error(e):
    """
    Handle 404 errors for API endpoints.
    This is particularly useful for conversation endpoints where we want to
    return a default empty conversation instead of a 404 error.
    """
    # Only handle API routes
    if not request.path.startswith('/api/'):
        # Let the default error handler handle non-API routes
        return e
    
    # Special handling for conversation endpoints
    if request.path.startswith('/api/conversations/') and current_user.is_authenticated:
        # Extract the conversation ID from the path
        parts = request.path.split('/')
        if len(parts) >= 4:
            conversation_id = parts[3].split('?')[0]  # Remove any query string
            
            # Get pagination parameters if any
            limit = request.args.get("limit", 100, type=int)
            offset = request.args.get("offset", 0, type=int)
            
            logger.info(f"Creating fallback response for missing conversation: {conversation_id}")
            
            # Return a default empty conversation
            return jsonify({
                "conversation": {
                    "id": conversation_id,
                    "title": "New conversation", 
                    "created_at": datetime.utcnow().isoformat(),
                    "updated_at": datetime.utcnow().isoformat(),
                    "user_id": current_user.id,
                    "is_active": True
                },
                "messages": [],
                "total_messages": 0,
                "limit": limit,
                "offset": offset
            })
    
    # Default response for other API routes
    return jsonify({"error": "Resource not found", "path": request.path}), 404
