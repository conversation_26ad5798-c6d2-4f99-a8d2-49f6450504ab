"""
Compatibility module for device models.

This module provides backward compatibility for code that imports from the
original location in src/login/device_models.py. As part of the refactoring process,
these models are being migrated to src/models/device.py.

This compatibility layer will be maintained throughout the transition period to
ensure existing code continues to work while encouraging migration to the new imports.
"""

import warnings
import sys
import logging

# Set up logging
logger = logging.getLogger('athena.deprecation')

# Issue a warning about the deprecated location
warnings.warn(
    "Importing from src.login.device_models is deprecated. "
    "Please update your imports to use src.models.device instead.",
    category=FutureWarning,
    stacklevel=2
)

# Import all models and functions from the new location
from src.models.device import (
    # Models
    Device,
    DeviceCapability,
    Command,
    CommandLog,
    Attachment,
    ScheduledTask,
    
    # Helper functions
    create_device,
    create_command,
    create_attachment,
    add_command_log,
    get_device_or_404,
    create_scheduled_task
)

# Log usage of this compatibility layer
frame = sys._getframe(1)
caller_module = frame.f_globals.get('__name__', 'unknown')
caller_function = frame.f_code.co_name
logger.info(f"Deprecated device models imports used by {caller_module}.{caller_function}")

# Define __all__ to explicitly control what's exported
__all__ = [
    # Models
    'Device',
    'DeviceCapability',
    'Command',
    'CommandLog',
    'Attachment',
    'ScheduledTask',
    
    # Helper functions
    'create_device',
    'create_command',
    'create_attachment',
    'add_command_log',
    'get_device_or_404',
    'create_scheduled_task'
]
