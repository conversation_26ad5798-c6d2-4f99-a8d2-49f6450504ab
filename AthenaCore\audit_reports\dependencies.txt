
backups\login\models.py imports:
  - src\models\__init__.py

backups\login\views.py imports:
  - src\login\views_compat.py
  - src\models\__init__.py

main.py imports:
  - src\admin\__init__.py
  - src\api\conversation_compat.py
  - src\api\conversations.py
  - src\api\demo.py
  - src\api\error_handlers.py
  - src\api\specific_conversation.py
  - src\controllers\auth_controller.py
  - src\controllers\main_controller.py
  - src\db\default_config.py
  - src\mcp\api.py
  - src\mcp\api_endpoints.py
  - src\models\__init__.py
  - src\routes\knowledge_routes.py
  - src\utils\auth_extensions.py

migrations\add_configurations_table.py imports:
  - src\login\models.py
  - src\models\__init__.py
  - src\models\configuration.py

migrations\v4_model_refactoring.py imports:
  - src\models\__init__.py

scripts\manage_migrations.py imports:
  - src\app_factory.py

scripts\track_refactoring_progress.py imports:
  - src\utils\migration_tracker.py

src\admin\__init__.py imports:
  - src\admin\debug_routes.py

src\admin\debug_routes.py imports:
  - src\core\debug_manager.py

src\api\api_v1.py imports:
  - src\core\athena.py
  - src\models\__init__.py

src\api\attachments.py imports:
  - src\models\__init__.py
  - src\models\device.py

src\api\commands.py imports:
  - src\models\__init__.py
  - src\models\device.py

src\api\config.py imports:
  - src\models\__init__.py
  - src\services\__init__.py
  - src\utils\api_response.py
  - src\utils\exceptions.py
  - src\utils\middleware.py

src\api\conversations.py imports:
  - src\api\conversation_handler.py
  - src\models\__init__.py

src\api\demo.py imports:
  - src\models\__init__.py
  - src\models\device.py
  - src\services\socket.py

src\api\device_auth.py imports:
  - src\models\__init__.py
  - src\models\device.py

src\api\devices.py imports:
  - src\models\__init__.py
  - src\models\device.py

src\api\routes.py imports:
  - src\api\commands.py
  - src\api\device_auth.py
  - src\api\devices.py
  - src\core\knowledge_db.py
  - src\models\__init__.py
  - src\models\api_key.py
  - src\models\command_toggle.py
  - src\models\llm_provider_setting.py
  - src\models\log_entry.py
  - src\services\__init__.py
  - src\utils\auth_extensions.py

src\api\search.py imports:
  - src\models\__init__.py
  - src\models\device.py

src\api\system_health.py imports:
  - src\api\routes.py

src\api\tasks.py imports:
  - src\models\__init__.py
  - src\models\device.py
  - src\services\socket.py

src\app_factory.py imports:
  - src\config\service.py
  - src\controllers\__init__.py
  - src\db\__init__.py
  - src\login\extensions.py
  - src\models\__init__.py
  - src\utils\error_handlers.py

src\config\service.py imports:
  - src\models\__init__.py

src\controllers\__init__.py imports:
  - src\routes\knowledge_routes.py

src\controllers\admin_controller.py imports:
  - src\services\base_service.py
  - src\services\user_service.py
  - src\utils\api_response.py
  - src\utils\middleware.py

src\controllers\api_controller.py imports:
  - src\services\auth_service.py
  - src\services\base_service.py
  - src\services\task_service.py
  - src\services\user_service.py
  - src\utils\api_response.py
  - src\utils\exceptions.py
  - src\utils\middleware.py

src\controllers\auth_controller.py imports:
  - src\config\service.py
  - src\models\__init__.py
  - src\services\auth_service.py
  - src\services\base_service.py
  - src\services\user_service.py

src\controllers\config_controller.py imports:
  - src\api\config.py

src\controllers\main_controller.py imports:
  - src\config\service.py
  - src\services\base_service.py
  - src\services\user_service.py

src\controllers\task_controller.py imports:
  - src\services\base_service.py
  - src\services\task_service.py
  - src\utils\api_response.py
  - src\utils\exceptions.py
  - src\utils\middleware.py

src\core\athena.py imports:
  - src\core\kb_context.py
  - src\core\knowledge_db.py
  - src\mcp\athena_mcp.py
  - src\mcp\smithery_client.py
  - src\models\__init__.py
  - src\models\llm_provider_setting.py
  - src\utils\config.py

src\core\command_processor.py imports:
  - src\core\commands\__init__.py
  - src\core\kb_search.py

src\core\commands\__init__.py imports:
  - src\core\commands\kb\kb_control.py
  - src\core\commands\obsidian\obsidian_control.py
  - src\core\commands\spotify\spotify_control.py
  - src\core\commands\system\system_control.py

src\core\commands\kb\kb_control.py imports:
  - src\core\kb_search.py
  - src\main.py

src\core\commands\obsidian\obsidian_control.py imports:
  - src\core\commands\base_command.py

src\core\commands\spotify\spotify_control.py imports:
  - src\core\commands\base_command.py
  - src\models\__init__.py

src\core\kb_catalog.py imports:
  - src\api\routes.py
  - src\core\document_processor.py
  - src\core\knowledge_db.py
  - src\utils\config.py

src\core\kb_context.py imports:
  - src\core\knowledge_db.py
  - src\utils\document_processor.py

src\core\kb_initializer.py imports:
  - src\core\knowledge_db.py

src\core\kb_search.py imports:
  - src\core\kb_context.py
  - src\core\knowledge_db.py

src\core\knowledge_db.py imports:
  - src\utils\config.py
  - src\utils\document_processor.py

src\core\logger.py imports:
  - src\models\__init__.py

src\core\state_manager.py imports:
  - src\core\vector_db.py

src\core\vector_db.py imports:
  - src\utils\config.py

src\db\default_config.py imports:
  - src\models\__init__.py
  - src\utils\config.py

src\db\migrations.py imports:
  - src\models\__init__.py

src\login\__init__.py imports:
  - src\controllers\auth_controller.py
  - src\models\__init__.py
  - src\models\user.py
  - src\utils\auth_extensions.py

src\login\compat.py imports:
  - src\login\models_compat.py

src\login\device_models.py imports:
  - src\models\__init__.py
  - src\models\device.py

src\login\device_models_compat.py imports:
  - src\models\device.py

src\login\extensions.py imports:
  - src\utils\auth_extensions.py

src\login\models.py imports:
  - src\login\models_compat.py
  - src\models\__init__.py
  - src\models\api.py
  - src\models\command.py
  - src\models\configuration.py
  - src\models\conversation.py
  - src\models\llm.py
  - src\models\logging.py
  - src\models\user.py

src\login\models_compat.py imports:
  - src\models\__init__.py
  - src\models\api.py
  - src\models\command.py
  - src\models\configuration.py
  - src\models\conversation.py
  - src\models\llm.py
  - src\models\logging.py
  - src\models\user.py

src\login\views.py imports:
  - src\controllers\admin_controller.py
  - src\controllers\auth_controller.py
  - src\models\__init__.py
  - src\utils\middleware.py

src\login\views_compat.py imports:
  - src\controllers\admin_controller.py
  - src\controllers\auth_controller.py
  - src\utils\middleware.py

src\main.py imports:
  - src\app_factory.py
  - src\config\service.py

src\mcp\api.py imports:
  - src\mcp\smithery_client.py
  - src\models\__init__.py
  - src\utils\config.py

src\mcp\api_endpoints.py imports:
  - src\mcp\connection_manager.py
  - src\mcp\deployment.py
  - src\mcp\server_creation.py
  - src\mcp\smithery_client.py
  - src\mcp\template_manager.py
  - src\models\__init__.py
  - src\utils\config.py

src\mcp\athena_mcp.py imports:
  - src\mcp\connection.py
  - src\mcp\smithery_client.py

src\mcp\connection.py imports:
  - src\mcp\smithery_client.py

src\mcp\deployment.py imports:
  - src\mcp\server_creation.py
  - src\mcp\smithery_client.py

src\mcp\server_creation.py imports:
  - src\core\athena.py
  - src\models\__init__.py
  - src\utils\config.py

src\mcp\smithery_client.py imports:
  - src\utils\config.py

src\mcp\template_manager.py imports:
  - src\models\__init__.py
  - src\utils\config.py

src\models\api.py imports:
  - src\models\api_key.py

src\models\api_key.py imports:
  - src\models\__init__.py

src\models\command.py imports:
  - src\models\command_toggle.py

src\models\command_toggle.py imports:
  - src\models\__init__.py

src\models\configuration.py imports:
  - src\models\__init__.py

src\models\conversation.py imports:
  - src\models\__init__.py

src\models\device.py imports:
  - src\models\__init__.py
  - src\models\user.py

src\models\direct_connection.py imports:
  - src\models\__init__.py

src\models\llm.py imports:
  - src\models\llm_provider_setting.py

src\models\llm_provider_setting.py imports:
  - src\models\__init__.py

src\models\log_entry.py imports:
  - src\models\__init__.py

src\models\logging.py imports:
  - src\models\log_entry.py

src\models\mcp_api_key.py imports:
  - src\models\__init__.py

src\models\mcp_server_template.py imports:
  - src\models\__init__.py

src\models\message.py imports:
  - src\models\__init__.py

src\models\task.py imports:
  - src\models\__init__.py

src\models\user.py imports:
  - src\models\__init__.py

src\models\user_log.py imports:
  - src\models\__init__.py

src\routes\knowledge_routes.py imports:
  - src\core\kb_catalog.py
  - src\core\knowledge_db.py
  - src\utils\config.py
  - src\utils\document_processor.py

src\services\auth_service.py imports:
  - src\config\service.py
  - src\models\__init__.py
  - src\models\api_key.py
  - src\models\user.py

src\services\config_service.py imports:
  - src\models\__init__.py
  - src\utils\exceptions.py

src\services\socket.py imports:
  - src\login\device_models.py

src\services\task_executor.py imports:
  - src\models\__init__.py
  - src\models\device.py
  - src\services\socket.py

src\services\task_scheduler.py imports:
  - src\models\__init__.py
  - src\models\device.py
  - src\services\task_executor.py

src\services\task_service.py imports:
  - src\models\__init__.py
  - src\models\task.py

src\services\user_service.py imports:
  - src\models\__init__.py
  - src\models\user.py

src\utils\__init__.py imports:
  - src\config\__init__.py

src\utils\auth_extensions.py imports:
  - src\models\user.py

src\utils\config.py imports:
  - src\models\__init__.py

src\utils\error_handlers.py imports:
  - src\utils\api_response.py
  - src\utils\exceptions.py

src\utils\middleware.py imports:
  - src\services\auth_service.py
  - src\services\base_service.py
  - src\utils\api_response.py
  - src\utils\exceptions.py

start_athena.py imports:
  - main.py
  - src\core\kb_catalog.py
  - src\core\kb_initializer.py

tests\conftest.py imports:
  - src\models\__init__.py

tests\simple_compat_test.py imports:
  - src\login\models.py
  - src\login\views.py

tests\start_services.py imports:
  - main.py
  - src\services\task_executor.py
  - src\services\task_scheduler.py

tests\test_attachments_api.py imports:
  - main.py
  - src\api\attachments.py
  - src\login\device_models.py
  - src\models\__init__.py

tests\test_compatibility_layers.py imports:
  - migrations\add_configurations_table.py
  - src\login\models.py
  - src\login\views.py

tests\test_cross_device_models.py imports:
  - main.py
  - src\login\device_models.py
  - src\models\__init__.py

tests\test_tasks_api.py imports:
  - main.py
  - src\login\device_models.py
  - src\models\__init__.py
  - src\services\task_executor.py
  - src\services\task_scheduler.py

tests\unit\config\test_config_service.py imports:
  - src\config\service.py
  - src\models\configuration.py

tests\unit\services\test_user_service.py imports:
  - src\models\__init__.py
  - src\services\base_service.py
  - src\services\user_service.py
