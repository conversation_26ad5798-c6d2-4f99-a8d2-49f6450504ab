"""
Obsidian control command module.
"""
import os
import platform
import subprocess
from typing import Dict, Any, List
from src.core.commands.base_command import BaseCommand  # <-- Fixed import here

class ObsidianControl(BaseCommand):
    """Command module to control Obsidian."""
    
    @property
    def name(self) -> str:
        return "obsidian"
        
    @property
    def description(self) -> str:
        return "Control Obsidian (open, create notes, search)"
    
    @property
    def supported_actions(self) -> List[str]:
        return ["open", "create", "search", "daily"]
    
    def execute(self, action: str, params: Dict[str, Any] = None) -> str:
        """
        Execute Obsidian commands.
        
        Args:
            action: The action to perform (open, create, search, daily)
            params: Additional parameters (e.g., note title, search query)
            
        Returns:
            Status message from the command
        """
        params = params or {}
        
        if action == "open":
            vault = params.get("vault")
            return self._open_obsidian(vault)
        elif action == "create":
            title = params.get("title")
            content = params.get("content", "")
            if not title:
                return "Error: Note title is required"
            return self._create_note(title, content)
        elif action == "search":
            query = params.get("query")
            if not query:
                return "Error: Search query is required"
            return self._search(query)
        elif action == "daily":
            return self._open_daily_note()
        else:
            return f"Error: Unsupported action '{action}'"
    
    def _open_obsidian(self, vault: str = None) -> str:
        """
        Open Obsidian app, optionally with a specific vault.
        
        Args:
            vault: Name of the vault to open (optional)
        """
        system = platform.system().lower()
        
        # Basic open Obsidian (no specific vault)
        if not vault:
            if system == "windows":
                cmd = "start obsidian://open"
            elif system == "darwin":  # macOS
                cmd = "open obsidian://open"
            else:  # Linux
                cmd = "xdg-open obsidian://open"
            
            return self._run_command(cmd)
        
        # Open specific vault
        if system == "windows":
            cmd = f"start obsidian://open?vault={vault}"
        elif system == "darwin":  # macOS
            cmd = f"open obsidian://open?vault={vault}"
        else:  # Linux
            cmd = f"xdg-open obsidian://open?vault={vault}"
        
        return self._run_command(cmd)
    
    def _create_note(self, title: str, content: str = "") -> str:
        """
        Create a new note in Obsidian.
        
        Args:
            title: Title of the new note
            content: Initial content for the note
        """
        import urllib.parse
        encoded_title = urllib.parse.quote(title)
        encoded_content = urllib.parse.quote(content)
        
        system = platform.system().lower()
        
        if system == "windows":
            cmd = f'start obsidian://new?name={encoded_title}&content={encoded_content}'
        elif system == "darwin":  # macOS
            cmd = f'open obsidian://new?name={encoded_title}&content={encoded_content}'
        else:  # Linux
            cmd = f'xdg-open obsidian://new?name={encoded_title}&content={encoded_content}'
        
        return self._run_command(cmd)
    
    def _search(self, query: str) -> str:
        """
        Search in Obsidian.
        
        Args:
            query: Search query
        """
        import urllib.parse
        encoded_query = urllib.parse.quote(query)
        
        system = platform.system().lower()
        
        if system == "windows":
            cmd = f'start obsidian://search?query={encoded_query}'
        elif system == "darwin":  # macOS
            cmd = f'open obsidian://search?query={encoded_query}'
        else:  # Linux
            cmd = f'xdg-open obsidian://search?query={encoded_query}'
        
        return self._run_command(cmd)
    
    def _open_daily_note(self) -> str:
        """Open today's daily note in Obsidian."""
        system = platform.system().lower()
        
        if system == "windows":
            cmd = 'start obsidian://daily-notes'
        elif system == "darwin":  # macOS
            cmd = 'open obsidian://daily-notes'
        else:  # Linux
            cmd = 'xdg-open obsidian://daily-notes'
        
        return self._run_command(cmd)
    
    def _run_command(self, command: str) -> str:
        """
        Helper method to run a shell command safely.
        
        Args:
            command: Shell command to execute
            
        Returns:
            Command output or error message
        """
        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            if result.returncode == 0:
                output = result.stdout.strip()
                return output if output else "Command executed successfully"
            else:
                return f"Error: {result.stderr.strip()}"
        except Exception as e:
            return f"Error executing command: {str(e)}"
