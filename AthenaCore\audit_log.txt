2025-05-30 20:26:16,111 - INFO - Starting AthenaCore codebase audit at 2025-05-30 20:26:16.111002
2025-05-30 20:26:16,111 - INFO - Project root: C:\Projects\AthenaNew\AthenaCore
2025-05-30 20:26:16,112 - INFO - Creating backup at C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250530_202616
2025-05-30 20:26:17,068 - INFO - Backup completed: C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250530_202616
2025-05-30 20:26:17,068 - INFO - Indexing all Python files in the project...
2025-05-30 20:26:17,080 - INFO - Found 149 Python files in project
2025-05-30 20:26:17,081 - INFO - Building module name map...
2025-05-30 20:26:17,090 - INFO - Built module map with 149 entries
2025-05-30 20:26:17,091 - INFO - Analyzing imports and building dependency graph...
2025-05-30 20:26:17,513 - WARNING - Found 1 circular import relationships
2025-05-30 20:26:17,514 - INFO - Found 265 import relationships between files
2025-05-30 20:26:17,515 - INFO - Identifying used files by traversing import graph from entry points...
2025-05-30 20:26:17,516 - INFO - Entry point: main.py
2025-05-30 20:26:17,516 - INFO - Entry point: start_athena.py
2025-05-30 20:26:17,516 - INFO - Entry point: src\main.py
2025-05-30 20:26:17,517 - INFO - Found 72 used files and 77 unused files
2025-05-30 20:26:17,517 - INFO - Generating audit reports...
2025-05-30 20:26:17,537 - INFO - Reports saved to C:\Projects\AthenaNew\AthenaCore\audit_reports
2025-05-30 20:28:13,526 - INFO - Starting AthenaCore codebase audit at 2025-05-30 20:28:13.526297
2025-05-30 20:28:13,526 - INFO - Project root: C:\Projects\AthenaNew\AthenaCore
2025-05-30 20:28:13,526 - INFO - Creating backup at C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250530_202813
2025-05-30 20:28:13,776 - INFO - Backup completed: C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250530_202813
2025-05-30 20:28:13,776 - INFO - Indexing all Python files in the project...
2025-05-30 20:28:13,783 - INFO - Found 149 Python files in project
2025-05-30 20:28:13,783 - INFO - Building module name map...
2025-05-30 20:28:13,788 - INFO - Built module map with 149 entries
2025-05-30 20:28:13,788 - INFO - Analyzing imports and building dependency graph...
2025-05-30 20:28:14,027 - WARNING - Found 1 circular import relationships
2025-05-30 20:28:14,028 - INFO - Found 265 import relationships between files
2025-05-30 20:28:14,028 - INFO - Identifying used files by traversing import graph from entry points...
2025-05-30 20:28:14,028 - INFO - Entry point: main.py
2025-05-30 20:28:14,029 - INFO - Entry point: start_athena.py
2025-05-30 20:28:14,029 - INFO - Entry point: src\main.py
2025-05-30 20:28:14,029 - INFO - Found 72 used files and 77 unused files
2025-05-30 20:28:14,029 - INFO - Generating audit reports...
2025-05-30 20:28:14,040 - INFO - Reports saved to C:\Projects\AthenaNew\AthenaCore\audit_reports
2025-05-30 20:30:52,134 - INFO - Starting AthenaCore codebase audit at 2025-05-30 20:30:52.134450
2025-05-30 20:30:52,134 - INFO - Project root: C:\Projects\AthenaNew\AthenaCore
2025-05-30 20:30:52,134 - INFO - Creating backup at C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250530_203052
2025-05-30 20:30:52,406 - INFO - Backup completed: C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250530_203052
2025-05-30 20:30:52,406 - INFO - Indexing all Python files in the project...
2025-05-30 20:30:52,416 - INFO - Found 149 Python files in project
2025-05-30 20:30:52,416 - INFO - Building module name map...
2025-05-30 20:30:52,421 - INFO - Built module map with 149 entries
2025-05-30 20:30:52,421 - INFO - Analyzing imports and building dependency graph...
2025-05-30 20:30:52,725 - WARNING - Found 1 circular import relationships
2025-05-30 20:30:52,726 - INFO - Found 265 import relationships between files
2025-05-30 20:30:52,726 - INFO - Identifying used files by traversing import graph from entry points...
2025-05-30 20:30:52,726 - INFO - Entry point: auditcodebase.py
2025-05-30 20:30:52,726 - INFO - Entry point: main.py
2025-05-30 20:30:52,726 - INFO - Entry point: start_athena.py
2025-05-30 20:30:52,726 - INFO - Entry point: src\main.py
2025-05-30 20:30:52,727 - INFO - Found 72 used files and 77 unused files
2025-05-30 20:30:52,727 - INFO - Generating audit reports...
2025-05-30 20:30:52,740 - INFO - Reports saved to C:\Projects\AthenaNew\AthenaCore\audit_reports
2025-05-30 20:44:31,924 - INFO - Starting AthenaCore codebase audit at 2025-05-30 20:44:31.924540
2025-05-30 20:44:31,925 - INFO - Project root: C:\Projects\AthenaNew\AthenaCore
2025-05-30 20:44:31,925 - INFO - Creating backup at C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250530_204431
2025-05-30 20:44:32,197 - INFO - Backup completed: C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250530_204431
2025-05-30 20:44:32,197 - INFO - Indexing all Python files in the project...
2025-05-30 20:44:32,207 - INFO - Found 149 Python files in project
2025-05-30 20:44:32,207 - INFO - Building module name map...
2025-05-30 20:44:32,213 - INFO - Built module map with 149 entries
2025-05-30 20:44:32,213 - INFO - Analyzing imports and building dependency graph...
2025-05-30 20:44:32,453 - WARNING - Found 1 circular import relationships
2025-05-30 20:44:32,454 - INFO - Found 265 import relationships between files
2025-05-30 20:44:32,454 - INFO - Identifying used files by traversing import graph from entry points...
2025-05-30 20:44:32,454 - INFO - Entry point: auditcodebase.py
2025-05-30 20:44:32,454 - INFO - Entry point: main.py
2025-05-30 20:44:32,454 - INFO - Entry point: start_athena.py
2025-05-30 20:44:32,454 - INFO - Entry point: src\main.py
2025-05-30 20:44:32,461 - INFO - Found 129 used files and 20 unused files
2025-05-30 20:44:32,461 - INFO - Generating audit reports...
2025-05-30 20:44:32,473 - INFO - Reports saved to C:\Projects\AthenaNew\AthenaCore\audit_reports
2025-05-30 20:49:06,991 - INFO - Starting AthenaCore codebase audit at 2025-05-30 20:49:06.991644
2025-05-30 20:49:06,992 - INFO - Project root: C:\Projects\AthenaNew\AthenaCore
2025-05-30 20:49:06,992 - INFO - Creating backup at C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250530_204906
2025-05-30 20:49:07,186 - INFO - Backup completed: C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250530_204906
2025-05-30 20:49:07,186 - INFO - Indexing all Python files in the project...
2025-05-30 20:49:07,194 - INFO - Found 149 Python files in project
2025-05-30 20:49:07,194 - INFO - Building module name map...
2025-05-30 20:49:07,199 - INFO - Built module map with 149 entries
2025-05-30 20:49:07,199 - INFO - Analyzing imports and building dependency graph...
2025-05-30 20:49:07,441 - WARNING - Found 1 circular import relationships
2025-05-30 20:49:07,441 - INFO - Found 265 import relationships between files
2025-05-30 20:49:07,441 - INFO - Identifying used files by traversing import graph from entry points...
2025-05-30 20:49:07,441 - INFO - Entry point: auditcodebase.py
2025-05-30 20:49:07,441 - INFO - Entry point: main.py
2025-05-30 20:49:07,441 - INFO - Entry point: start_athena.py
2025-05-30 20:49:07,441 - INFO - Entry point: src\main.py
2025-05-30 20:49:07,446 - INFO - Found 131 used files and 18 unused files
2025-05-30 20:49:07,446 - INFO - Generating audit reports...
2025-05-30 20:49:07,456 - INFO - Reports saved to C:\Projects\AthenaNew\AthenaCore\audit_reports
2025-05-31 15:50:57,573 - INFO - Starting AthenaCore codebase audit at 2025-05-31 15:50:57.573978
2025-05-31 15:50:57,573 - INFO - Project root: C:\Projects\AthenaNew\AthenaCore
2025-05-31 15:50:57,574 - INFO - Creating backup at C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250531_155057
2025-05-31 15:50:59,292 - INFO - Backup completed: C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250531_155057
2025-05-31 15:50:59,293 - INFO - Indexing all Python files in the project...
2025-05-31 15:50:59,300 - INFO - Found 149 Python files in project
2025-05-31 15:50:59,300 - INFO - Building module name map...
2025-05-31 15:50:59,305 - INFO - Built module map with 149 entries
2025-05-31 15:50:59,305 - INFO - Analyzing imports and building dependency graph...
2025-05-31 15:50:59,559 - WARNING - Found 1 circular import relationships
2025-05-31 15:50:59,559 - INFO - Found 265 import relationships between files
2025-05-31 15:50:59,560 - INFO - Identifying used files by traversing import graph from entry points...
2025-05-31 15:50:59,560 - INFO - Entry point: auditcodebase.py
2025-05-31 15:50:59,560 - INFO - Entry point: main.py
2025-05-31 15:50:59,560 - INFO - Entry point: start_athena.py
2025-05-31 15:50:59,560 - INFO - Entry point: src\main.py
2025-05-31 15:50:59,564 - INFO - Found 131 used files and 18 unused files
2025-05-31 15:50:59,564 - INFO - Generating audit reports...
2025-05-31 15:50:59,577 - INFO - Reports saved to C:\Projects\AthenaNew\AthenaCore\audit_reports
2025-05-31 15:51:02,698 - INFO - Generating dependency visualizations...
2025-05-31 15:51:05,007 - INFO - Visualization saved to C:\Projects\AthenaNew\AthenaCore\audit_reports\dependency_graph.png
2025-05-31 15:51:08,412 - INFO - Starting AthenaCore codebase audit at 2025-05-31 15:51:08.412134
2025-05-31 15:51:08,412 - INFO - Project root: C:\Projects\AthenaNew\AthenaCore
2025-05-31 15:51:08,412 - INFO - Creating backup at C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250531_155108
2025-05-31 15:51:08,603 - INFO - Backup completed: C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250531_155108
2025-05-31 15:51:08,603 - INFO - Indexing all Python files in the project...
2025-05-31 15:51:08,611 - INFO - Found 149 Python files in project
2025-05-31 15:51:08,611 - INFO - Building module name map...
2025-05-31 15:51:08,616 - INFO - Built module map with 149 entries
2025-05-31 15:51:08,616 - INFO - Analyzing imports and building dependency graph...
2025-05-31 15:51:08,873 - WARNING - Found 1 circular import relationships
2025-05-31 15:51:08,874 - INFO - Found 265 import relationships between files
2025-05-31 15:51:08,874 - INFO - Identifying used files by traversing import graph from entry points...
2025-05-31 15:51:08,874 - INFO - Entry point: auditcodebase.py
2025-05-31 15:51:08,874 - INFO - Entry point: main.py
2025-05-31 15:51:08,874 - INFO - Entry point: start_athena.py
2025-05-31 15:51:08,874 - INFO - Entry point: src\main.py
2025-05-31 15:51:08,878 - INFO - Found 131 used files and 18 unused files
2025-05-31 15:51:08,878 - INFO - Generating audit reports...
2025-05-31 15:51:08,890 - INFO - Reports saved to C:\Projects\AthenaNew\AthenaCore\audit_reports
2025-05-31 15:51:34,095 - INFO - Starting AthenaCore codebase audit at 2025-05-31 15:51:34.095638
2025-05-31 15:51:34,095 - INFO - Project root: C:\Projects\AthenaNew\AthenaCore
2025-05-31 15:51:34,095 - INFO - Creating backup at C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250531_155134
2025-05-31 15:51:34,315 - INFO - Backup completed: C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250531_155134
2025-05-31 15:51:34,316 - INFO - Indexing all Python files in the project...
2025-05-31 15:51:34,324 - INFO - Found 149 Python files in project
2025-05-31 15:51:34,324 - INFO - Building module name map...
2025-05-31 15:51:34,329 - INFO - Built module map with 149 entries
2025-05-31 15:51:34,329 - INFO - Analyzing imports and building dependency graph...
2025-05-31 15:51:34,597 - WARNING - Found 1 circular import relationships
2025-05-31 15:51:34,597 - INFO - Found 265 import relationships between files
2025-05-31 15:51:34,597 - INFO - Identifying used files by traversing import graph from entry points...
2025-05-31 15:51:34,597 - INFO - Entry point: auditcodebase.py
2025-05-31 15:51:34,597 - INFO - Entry point: main.py
2025-05-31 15:51:34,597 - INFO - Entry point: start_athena.py
2025-05-31 15:51:34,597 - INFO - Entry point: src\main.py
2025-05-31 15:51:34,601 - INFO - Found 131 used files and 18 unused files
2025-05-31 15:51:34,601 - INFO - Generating audit reports...
2025-05-31 15:51:34,615 - INFO - Reports saved to C:\Projects\AthenaNew\AthenaCore\audit_reports
2025-05-31 16:12:57,969 - INFO - Starting AthenaCore codebase audit at 2025-05-31 16:12:57.969266
2025-05-31 16:12:57,969 - INFO - Project root: C:\Projects\AthenaNew\AthenaCore
2025-05-31 16:12:57,969 - INFO - Creating backup at C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250531_161257
2025-05-31 16:12:58,236 - INFO - Backup completed: C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250531_161257
2025-05-31 16:12:58,236 - INFO - Indexing all Python files in the project...
2025-05-31 16:12:58,244 - INFO - Found 150 Python files in project
2025-05-31 16:12:58,244 - INFO - Building module name map...
2025-05-31 16:12:58,250 - INFO - Built module map with 150 entries
2025-05-31 16:12:58,250 - INFO - Analyzing imports and building dependency graph...
2025-05-31 16:12:58,267 - WARNING - Error parsing auditcodebase_backup.py: expected an indented block after 'for' statement on line 530 (auditcodebase_backup.py, line 533)
2025-05-31 16:12:58,545 - WARNING - Found 1 circular import relationships
2025-05-31 16:12:58,546 - INFO - Found 265 import relationships between files
2025-05-31 16:12:58,547 - ERROR - Audit failed with error: 'CodebaseAuditor' object has no attribute 'find_used_files'
Traceback (most recent call last):
  File "C:\Projects\AthenaNew\AthenaCore\auditcodebase.py", line 200, in run
    self.find_used_files()
AttributeError: 'CodebaseAuditor' object has no attribute 'find_used_files'
2025-05-31 16:13:08,595 - INFO - Starting AthenaCore codebase audit at 2025-05-31 16:13:08.595743
2025-05-31 16:13:08,595 - INFO - Project root: C:\Projects\AthenaNew\AthenaCore
2025-05-31 16:13:08,595 - INFO - Creating backup at C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250531_161308
2025-05-31 16:13:08,846 - INFO - Backup completed: C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250531_161308
2025-05-31 16:13:08,846 - INFO - Indexing all Python files in the project...
2025-05-31 16:13:08,853 - INFO - Found 150 Python files in project
2025-05-31 16:13:08,853 - INFO - Building module name map...
2025-05-31 16:13:08,857 - INFO - Built module map with 150 entries
2025-05-31 16:13:08,857 - INFO - Analyzing imports and building dependency graph...
2025-05-31 16:13:08,873 - WARNING - Error parsing auditcodebase_backup.py: expected an indented block after 'for' statement on line 530 (auditcodebase_backup.py, line 533)
2025-05-31 16:13:09,114 - WARNING - Found 1 circular import relationships
2025-05-31 16:13:09,115 - INFO - Found 265 import relationships between files
2025-05-31 16:13:09,115 - ERROR - Audit failed with error: 'CodebaseAuditor' object has no attribute 'find_used_files'
Traceback (most recent call last):
  File "C:\Projects\AthenaNew\AthenaCore\auditcodebase.py", line 200, in run
    self.find_used_files()
AttributeError: 'CodebaseAuditor' object has no attribute 'find_used_files'
2025-05-31 16:55:31,328 - INFO - Starting AthenaCore codebase audit at 2025-05-31 16:55:31.328266
2025-05-31 16:55:31,328 - INFO - Project root: C:\Projects\AthenaNew\AthenaCore
2025-05-31 16:55:31,328 - INFO - Creating backup at C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250531_165531
2025-05-31 16:55:31,600 - INFO - Backup completed: C:\Projects\AthenaNew\AthenaCore\audit_backups\AthenaCore_backup_20250531_165531
2025-05-31 16:55:31,600 - INFO - Indexing all Python files in the project...
2025-05-31 16:55:31,613 - INFO - Found 149 Python files in project
2025-05-31 16:55:31,613 - INFO - Building module name map...
2025-05-31 16:55:31,618 - INFO - Built module map with 149 entries
2025-05-31 16:55:31,618 - INFO - Analyzing imports and building dependency graph...
2025-05-31 16:55:32,026 - INFO - Found 264 import relationships between files
2025-05-31 16:55:32,026 - INFO - Identifying used files by traversing import graph from entry points...
2025-05-31 16:55:32,026 - INFO - Entry point: auditcodebase.py
2025-05-31 16:55:32,027 - INFO - Entry point: main.py
2025-05-31 16:55:32,027 - INFO - Entry point: start_athena.py
2025-05-31 16:55:32,027 - INFO - Entry point: src\main.py
2025-05-31 16:55:32,034 - INFO - Found 131 used files and 18 unused files
2025-05-31 16:55:32,034 - INFO - Processing test files...
2025-05-31 16:55:32,043 - INFO - Found 11 test files
2025-05-31 16:55:32,044 - INFO - Found 120 used production files
2025-05-31 16:55:32,044 - INFO - Generating audit reports...
2025-05-31 16:55:32,061 - INFO - Reports saved to C:\Projects\AthenaNew\AthenaCore\audit_reports
2025-05-31 16:55:32,062 - INFO - Archiving unused files...
2025-05-31 16:55:32,064 - INFO - Archived: backups\login\models.py
2025-05-31 16:55:32,067 - INFO - Archived: backups\login\views.py
2025-05-31 16:55:32,070 - INFO - Archived: migrations\add_configurations_table.py
2025-05-31 16:55:32,072 - INFO - Archived: migrations\add_config_entries.py
2025-05-31 16:55:32,075 - INFO - Archived: migrations\v1_add_configurations_table.py
2025-05-31 16:55:32,077 - INFO - Archived: migrations\v2_add_tasks_table.py
2025-05-31 16:55:32,079 - INFO - Archived: migrations\v3_add_authentication_tables.py
2025-05-31 16:55:32,082 - INFO - Archived: migrations\v4_model_refactoring.py
2025-05-31 16:55:32,085 - INFO - Archived: scripts\codebase_analyzer.py
2025-05-31 16:55:32,088 - INFO - Archived: scripts\identify_obsolete_files.py
2025-05-31 16:55:32,090 - INFO - Archived: scripts\manage_migrations.py
2025-05-31 16:55:32,093 - INFO - Archived: scripts\migrate_imports.py
2025-05-31 16:55:32,095 - INFO - Archived: scripts\prepare_for_removal.py
2025-05-31 16:55:32,097 - INFO - Archived: scripts\run_migrations.py
2025-05-31 16:55:32,099 - INFO - Archived: scripts\track_refactoring_progress.py
2025-05-31 16:55:32,102 - INFO - Archived: scripts\update_imports.py
2025-05-31 16:55:32,104 - INFO - Archived: scripts\verify_dependencies.py
2025-05-31 16:55:32,107 - INFO - Archived: src\admin\debug_routes.py
2025-05-31 16:55:32,107 - INFO - Archived 18 unused files to C:\Projects\AthenaNew\AthenaCore\archived_files\unused_files_20250531_165532
