"""
Configuration service module.

This module provides a standardized service for accessing configuration settings
stored in the database, following the service layer pattern established in the
refactoring plan.
"""

import json
import logging
import os
from typing import Any, Dict, List, Optional, Union, cast
from functools import lru_cache

from flask import current_app
from sqlalchemy.exc import SQLAlchemyError

from src.models import Configuration, db
from src.utils.exceptions import ConfigurationError, ResourceNotFoundError
from .base_service import BaseService

# Configure logging
logger = logging.getLogger(__name__)

class ConfigurationService(BaseService):
    """Service for managing application configuration from the database."""
    
    def initialize(self):
        """Initialize the configuration service."""
        self._cache = {}
        logger.info("Configuration service initialized")
    
    def get_value(self, key: str, default: Any = None, use_cache: bool = True) -> Any:
        """
        Get a configuration value by key.
        
        Args:
            key: Configuration key
            default: Default value if the key is not found
            use_cache: Whether to use the cache
            
        Returns:
            The configuration value, converted to the appropriate type
            
        Raises:
            ConfigurationError: If there's an error retrieving the configuration
        """
        # Check cache first if enabled
        if use_cache and key in self._cache:
            logger.debug(f"Retrieved {key} from cache")
            return self._cache[key]
        
        try:
            # Query the configuration from the database
            config = Configuration.query.filter_by(key=key).first()
            
            # If not found, return the default value
            if config is None:
                # Fall back to environment variables during transition
                env_value = os.getenv(key.upper())
                if env_value is not None:
                    logger.debug(f"Retrieved {key} from environment variables")
                    return env_value
                
                logger.debug(f"Configuration {key} not found, using default: {default}")
                return default
            
            # Convert the value to the appropriate type
            value = self._convert_value(config.value, config.value_type)
            
            # Cache the value if caching is enabled
            if use_cache:
                self._cache[key] = value
            
            logger.debug(f"Retrieved {key} from database")
            return value
            
        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving configuration {key}: {str(e)}")
            raise ConfigurationError(f"Error retrieving configuration: {str(e)}")
        except Exception as e:
            logger.error(f"Error retrieving configuration {key}: {str(e)}")
            raise ConfigurationError(f"Error retrieving configuration: {str(e)}")
    
    def set_value(self, key: str, value: Any, value_type: str = None, 
                 description: str = None, is_sensitive: bool = False) -> Configuration:
        """
        Set a configuration value.
        
        Args:
            key: Configuration key
            value: Value to set
            value_type: Type of the value (string, int, float, bool, json)
            description: Description of the configuration
            is_sensitive: Whether the value is sensitive
            
        Returns:
            The configuration object
            
        Raises:
            ConfigurationError: If there's an error setting the configuration
        """
        try:
            # Determine the value type if not provided
            if value_type is None:
                value_type = self._determine_value_type(value)
            
            # Convert the value to a string for storage
            str_value = self._value_to_string(value, value_type)
            
            # Check if the configuration exists
            config = Configuration.query.filter_by(key=key).first()
            
            if config:
                # Update existing configuration
                config.value = str_value
                config.value_type = value_type
                if description is not None:
                    config.description = description
                config.is_sensitive = is_sensitive
            else:
                # Create new configuration
                config = Configuration(
                    key=key,
                    value=str_value,
                    value_type=value_type,
                    description=description,
                    is_sensitive=is_sensitive
                )
                db.session.add(config)
            
            # Commit the changes
            db.session.commit()
            
            # Update the cache
            self._cache[key] = value
            
            logger.info(f"Configuration {key} set successfully")
            return config
            
        except SQLAlchemyError as e:
            db.session.rollback()
            logger.error(f"Database error setting configuration {key}: {str(e)}")
            raise ConfigurationError(f"Error setting configuration: {str(e)}")
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error setting configuration {key}: {str(e)}")
            raise ConfigurationError(f"Error setting configuration: {str(e)}")
    
    def delete_value(self, key: str) -> bool:
        """
        Delete a configuration value.
        
        Args:
            key: Configuration key
            
        Returns:
            True if the configuration was deleted, False otherwise
            
        Raises:
            ConfigurationError: If there's an error deleting the configuration
        """
        try:
            # Check if the configuration exists
            config = Configuration.query.filter_by(key=key).first()
            
            if not config:
                logger.warning(f"Configuration {key} not found for deletion")
                return False
            
            # Delete the configuration
            db.session.delete(config)
            db.session.commit()
            
            # Remove from cache
            if key in self._cache:
                del self._cache[key]
            
            logger.info(f"Configuration {key} deleted successfully")
            return True
            
        except SQLAlchemyError as e:
            db.session.rollback()
            logger.error(f"Database error deleting configuration {key}: {str(e)}")
            raise ConfigurationError(f"Error deleting configuration: {str(e)}")
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error deleting configuration {key}: {str(e)}")
            raise ConfigurationError(f"Error deleting configuration: {str(e)}")
    
    def get_all_values(self, include_sensitive: bool = False) -> List[Dict[str, Any]]:
        """
        Get all configuration values.
        
        Args:
            include_sensitive: Whether to include sensitive values
            
        Returns:
            List of configuration dictionaries
            
        Raises:
            ConfigurationError: If there's an error retrieving configurations
        """
        try:
            # Query all configurations
            configs = Configuration.query.all()
            
            # Convert to dictionaries
            result = [
                config.to_dict(include_sensitive=include_sensitive)
                for config in configs
            ]
            
            logger.debug(f"Retrieved {len(result)} configurations")
            return result
            
        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving configurations: {str(e)}")
            raise ConfigurationError(f"Error retrieving configurations: {str(e)}")
        except Exception as e:
            logger.error(f"Error retrieving configurations: {str(e)}")
            raise ConfigurationError(f"Error retrieving configurations: {str(e)}")
    
    def clear_cache(self) -> None:
        """Clear the configuration cache."""
        self._cache = {}
        logger.debug("Configuration cache cleared")
    
    def _convert_value(self, value_str: str, value_type: str) -> Any:
        """
        Convert a string value to the appropriate type.
        
        Args:
            value_str: String value to convert
            value_type: Type of the value
            
        Returns:
            The converted value
        """
        if value_type == "string":
            return value_str
        elif value_type == "int":
            return int(value_str)
        elif value_type == "float":
            return float(value_str)
        elif value_type == "bool":
            return value_str.lower() in ("true", "yes", "1", "t", "y")
        elif value_type == "json":
            return json.loads(value_str)
        else:
            # Default to string for unknown types
            logger.warning(f"Unknown value type {value_type}, defaulting to string")
            return value_str
    
    def _determine_value_type(self, value: Any) -> str:
        """
        Determine the type of a value.
        
        Args:
            value: Value to determine the type of
            
        Returns:
            String representing the value type
        """
        if isinstance(value, bool):
            return "bool"
        elif isinstance(value, int):
            return "int"
        elif isinstance(value, float):
            return "float"
        elif isinstance(value, (dict, list)):
            return "json"
        else:
            return "string"
    
    def _value_to_string(self, value: Any, value_type: str) -> str:
        """
        Convert a value to a string for storage.
        
        Args:
            value: Value to convert
            value_type: Type of the value
            
        Returns:
            String representation of the value
        """
        if value_type == "json":
            return json.dumps(value)
        else:
            return str(value)
