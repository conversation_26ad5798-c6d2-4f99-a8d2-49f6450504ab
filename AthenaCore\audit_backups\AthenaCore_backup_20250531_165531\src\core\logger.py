import uuid
import logging
from pathlib import Path
from datetime import datetime
import json as _json

from flask import has_request_context
from flask_login import current_user

# Import database directly instead of the deprecated login module
from src.models import db
from src.models import LogEntry

# Configure logger
logger = logging.getLogger('athena.core')

# Only set up the logger if it doesn't already have handlers
if not logger.handlers:
    logger.setLevel(logging.INFO)
    
    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # Create formatter and add it to the handler
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    
    # Add the handlers to the logger
    logger.addHandler(console_handler)
    
    # Create file handler for detailed logging
    try:
        log_dir = Path('logs')
        log_dir.mkdir(exist_ok=True)
        file_handler = logging.FileHandler('logs/athena_system.log')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    except Exception as e:
        logger.warning(f"Could not set up file logging: {e}")


def save_log(log_entry):
    """
    Saves a log entry to the database. Handles error cases.

    Args:
        log_entry (dict): The log entry to save, containing details about the interaction
                          such as timestamp, message content, response, execution time, etc.

    The function:
    1. Ensures a unique id for the log entry.
    2. Parses the timestamp.
    3. Determines the user associated with the log entry.
    4. Prepares the output as a JSON string.
    5. Inserts the log entry into the SQL LogEntry table.
    """
    logger.debug("Saving log entry to database")
    try:
        # Ensure a unique id
        if 'id' not in log_entry:
            log_entry['id'] = str(uuid.uuid4())
        # Parse timestamp
        ts = log_entry.get('timestamp')
        try:
            timestamp = datetime.fromisoformat(ts)
        except Exception:
            timestamp = datetime.utcnow()
        # Determine user
        user_id = None
        if has_request_context() and current_user.is_authenticated:
            user_id = current_user.id
        # Prepare output as JSON string
        output_val = log_entry.get('output')
        if output_val is not None:
            try:
                output_val = _json.dumps(output_val)
            except Exception:
                output_val = str(output_val)
        # Insert into SQL LogEntry
        entry = LogEntry(
            id=log_entry['id'],
            user_id=user_id,
            timestamp=timestamp,
            conversation_id=(str(log_entry.get('conversation_id'))
                             if log_entry.get('conversation_id')
                             else None),
            message_type=log_entry.get('message_type'),
            content=log_entry.get('content'),
            response=log_entry.get('response'),
            code=log_entry.get('code'),
            output=output_val,
            execution_time=log_entry.get('execution_time'),
            status=log_entry.get('status'),
            api=log_entry.get('api'),
            token_count=log_entry.get('token_count'),
        )
        db.session.add(entry)
        db.session.commit()
        logger.debug(f"Log entry persisted with id {entry.id} for user {user_id}")

    except Exception as e:
        logger.error(f"Error saving log to DB: {e}")
