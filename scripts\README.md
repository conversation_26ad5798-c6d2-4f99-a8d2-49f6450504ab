# AthenaNew Scripts Directory

This directory contains utility scripts for development, maintenance, and administration of the AthenaNew project ecosystem. All scripts are designed as standalone utilities that can be run independently of the main application.

## 📁 Script Categories

### 🗄️ Database Utilities
- **[fix_db_paths.py](#fix_db_pathspy)** - Database path and permissions fixer
- **[inspect_db.py](#inspect_dbpy)** - Database structure inspector  
- **[migrate_db.py](#migrate_dbpy)** - Database migration utility

### 🔍 Diagnostic Tools
- **[check_connections.py](#check_connectionspy)** - DirectConnection diagnostic tool
- **[verify_openai_key.py](#verify_openai_keypy)** - OpenAI API key verification

### 📚 Import/Export Tools
- **[import_kb_folder.py](#import_kb_folderpy)** - Knowledge base document import utility

### 🌐 MCP (Model Context Protocol) Tools
- **[list_available_servers.py](#list_available_serverspy)** - MCP server discovery utility

---

## 🚀 Quick Reference

| Script | Purpose | Usage | Prerequisites |
|--------|---------|-------|---------------|
| `fix_db_paths.py` | Fix database paths and permissions | `python scripts/fix_db_paths.py` | None |
| `inspect_db.py` | Inspect database structure | `python scripts/inspect_db.py` | Database file exists |
| `migrate_db.py` | Merge database files | `python scripts/migrate_db.py` | None |
| `check_connections.py` | Diagnose DirectConnections | `python scripts/check_connections.py` | Server NOT running |
| `verify_openai_key.py` | Test OpenAI API key | `python scripts/verify_openai_key.py [key]` | OpenAI API key |
| `import_kb_folder.py` | Import documents to KB | `python scripts/import_kb_folder.py [--folder PATH]` | OpenAI API key |
| `list_available_servers.py` | List MCP servers | `python scripts/list_available_servers.py` | Smithery API key |

## ⚙️ Configuration Requirements

### API Keys Setup

#### OpenAI API Key
Required for: `import_kb_folder.py`, `verify_openai_key.py`

**Configuration Options** (in priority order):
1. **Command line**: `python script.py sk-your-key`
2. **Config file**: Add to `AthenaCore/config.json`:
   ```json
   {
     "openai_api_key": "sk-your-openai-key-here"
   }
   ```
3. **Database**: Store in DirectConnection table via Athena UI
4. **Environment**: `export OPENAI_API_KEY="sk-your-openai-key-here"`

#### Smithery API Key
Required for: `list_available_servers.py`

Add to `AthenaCore/config.json`:
```json
{
  "smithery_api_key": "your-smithery-api-key-here"
}
```

### Database Access Requirements
- ✅ Proper database file permissions (read/write)
- ✅ AthenaCore modules accessible in Python path
- ⚠️ **For diagnostic scripts**: Athena server should NOT be running
- 📁 Database file location: `AthenaCore/athena.db` or `instance/athena.db`

## 🔧 Common Troubleshooting

### Database Issues
- **"Database file not found"**: Run `fix_db_paths.py` to create proper structure
- **"Database is locked"**: Stop Athena server before running diagnostic scripts
- **"Permission denied"**: Check file permissions on database files

### API Key Issues  
- **"Invalid API key format"**: OpenAI keys must start with 'sk-'
- **"API request failed"**: Run `verify_openai_key.py` to test functionality
- **"No API key found"**: Configure key in one of the supported locations

### Import/Export Issues
- **"Unsupported file format"**: Check supported formats (PDF, TXT, MD, DOCX, HTML, CSV, JSON)
- **"Import failed"**: Verify OpenAI API key and network connectivity
- **"Permission denied"**: Check write permissions in target directories

## 📊 Output Formats
- **🎨 Colored output**: Scripts with colorama support
- **📊 Structured output**: Database inspection, server lists, diagnostics
- **📈 Progress indicators**: ✅ ❌ 🟡 symbols for status
- **🔒 Security**: Automatic masking of sensitive information

## 📋 Development Guidelines

### Adding New Scripts
1. **📝 Follow naming conventions**: Use descriptive names with underscores
2. **📚 Include documentation**: Add comprehensive docstrings and usage instructions
3. **🛡️ Handle errors gracefully**: Provide meaningful error messages
4. **📖 Update this README**: Add to appropriate category with full documentation
5. **💡 Include examples**: Provide clear command-line usage examples
6. **⚙️ Specify prerequisites**: List dependencies, API keys, and configuration

### Script Template
```python
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
script_name.py - Brief description

Usage:
    python scripts/script_name.py [options]
"""

import os
import sys
import argparse
import logging

def main():
    """Main function with clear purpose."""
    # Implementation here
    pass

if __name__ == "__main__":
    sys.exit(main())
```

## 🔗 Related Documentation
- **📚 AthenaCore Documentation**: `AthenaCore/docs/` - Core system documentation
- **📖 Project Documentation**: `docs/` - Comprehensive project documentation  
- **🧪 Test Documentation**: `tests/README.md` - Testing framework organization
- **🏗️ Component Documentation**: Individual README files in component directories
- **🔧 Development Setup**: `docs/development/DEVELOPER_SETUP.md` - Complete setup guide

---

## 🚀 Detailed Script Documentation

### Database Utilities

#### fix_db_paths.py
**Purpose**: Creates or modifies the instance folder and ensures the database file has proper permissions.

**Usage**:
```bash
python scripts/fix_db_paths.py
```

**Features**:
- ✅ Creates instance directory if it doesn't exist
- 🔄 Backs up existing database files (`.bak` extension)
- 🆕 Creates new database with proper permissions
- ⚙️ Updates config.json with absolute database paths
- 🔧 Fixes SQLAlchemy path format issues (forward slashes)

**Output Example**:
```
Database Path Fixer for Athena
----------------------------------------
Project root: C:\Projects\AthenaNew\scripts
Instance directory: C:\Projects\AthenaNew\scripts\instance
Database path: C:\Projects\AthenaNew\scripts\instance\athena.db
Backing up existing database to C:\Projects\AthenaNew\scripts\instance\athena.db.bak
Created new database file with test table
Updated config.json with absolute database path: C:/Projects/AthenaNew/scripts/instance/athena.db

Database path fix completed!
You can now start Athena with: python AthenaCore/start_athena.py
```

**Prerequisites**: None

---

#### inspect_db.py
**Purpose**: Inspects database structure and displays comprehensive table information.

**Usage**:
```bash
python scripts/inspect_db.py
```

**Features**:
- 📋 Lists all tables in the database
- 📊 Shows column information (name, type) for each table
- 👁️ Displays sample rows for configuration/settings tables
- 🔍 Provides complete database structure overview

**Output Example**:
```
Checking database at C:\Projects\AthenaNew\AthenaCore\athena.db

Tables in the database:
- users
  Columns in users:
    - id (INTEGER)
    - username (VARCHAR(80))
    - email (VARCHAR(120))
- direct_connections
  Columns in direct_connections:
    - id (INTEGER)
    - name (VARCHAR(100))
    - api_key (TEXT)
  Sample row: (1, 'OpenAI', 'sk-...')
```

**Prerequisites**: Database file must exist at `AthenaCore/athena.db`

---

#### migrate_db.py
**Purpose**: Merges multiple database files into a single instance database with comprehensive logging.

**Usage**:
```bash
python scripts/migrate_db.py
```

**Features**:
- 🔄 Automatically backs up existing databases with timestamps
- 🔀 Merges `athena.db` and `database.db` into `instance/athena.db`
- 🔑 Handles table conflicts using primary keys (INSERT OR IGNORE)
- 📝 Logs all operations to `db_migration.log`
- 🛡️ Preserves data integrity during migration
- ⚠️ Skips tables without primary keys to avoid duplicates

**Output Example**:
```
2025-05-31 18:30:15 - db_migration - INFO - Database migration script started
2025-05-31 18:30:15 - db_migration - INFO - Backed up athena.db to athena.db.20250531183015.bak
2025-05-31 18:30:15 - db_migration - INFO - Created instance directory
2025-05-31 18:30:15 - db_migration - INFO - Found 5 tables in athena.db
2025-05-31 18:30:15 - db_migration - INFO - Created table users in target database
2025-05-31 18:30:15 - db_migration - INFO - Copied 3 rows from users
2025-05-31 18:30:15 - db_migration - INFO - Database migration completed!
```

**Prerequisites**: None (creates backups automatically)

---

### Diagnostic Tools

#### check_connections.py
**Purpose**: Comprehensive diagnostic tool for checking DirectConnection entries in the database.

**Usage**:
```bash
python scripts/check_connections.py
```

**Features**:
- 📋 Lists all DirectConnection entries with details
- ✅ Validates API key formats (OpenAI format: starts with 'sk-')
- 🔍 Checks connection status (enabled/disabled)
- 💡 Provides specific recommendations for fixing issues
- 🔒 Masks sensitive API key information for security
- ⚠️ Highlights problematic configurations

**Output Example**:
```
Connecting to database at: C:\Projects\AthenaNew\AthenaCore\data\athena.db

=== DirectConnection Diagnostic Results ===

Found 2 connection entries in the database

Connection #1
  Name: OpenAI
  URL: https://api.openai.com/v1
  Enabled: Yes
  API Key: sk-1***xyz9
  Key Format: Valid OpenAI format (starts with sk-)
  Models: gpt-4,gpt-3.5-turbo

Connection #2
  Name: Test Connection
  URL: https://api.openai.com/v1
  Enabled: No
  API Key: invalid-key
  Key Format: INVALID - OpenAI keys should start with 'sk-'
  Models: None specified

=== Recommendations ===
Found 1 valid and enabled connections.
If you're still having issues, please check that:
1. Your OpenAI API key is valid and has proper permissions
2. Your API key has not expired
3. You have billing set up on your OpenAI account
```

**Prerequisites**:
- AthenaCore database must be accessible
- ⚠️ **Important**: Athena server should NOT be running during diagnosis

---

#### verify_openai_key.py
**Purpose**: Comprehensive OpenAI API key verification with actual API testing.

**Usage**:
```bash
# Test with command line API key
python scripts/verify_openai_key.py sk-your-api-key-here

# Test with configured API key (searches multiple sources)
python scripts/verify_openai_key.py
```

**Features**:
- 🔍 Tests API key from multiple sources in priority order:
  1. Command line argument
  2. AthenaConfig (config.json)
  3. DirectConnection database
  4. Environment variable (OPENAI_API_KEY)
- ✅ Validates API key format (must start with 'sk-')
- 🌐 Makes actual API call to verify functionality
- 🎨 Colored output for better readability (with colorama)
- 📊 Comprehensive error reporting and diagnostics
- 🔒 Secure API key masking in all output

**Output Example**:
```
[2025-05-31 18:35:22] INFO: OpenAI API Key Verification Tool
==================================================
[2025-05-31 18:35:22] INFO: Using API key provided as command line argument: sk-1***xyz9
[2025-05-31 18:35:22] INFO: Testing API key sk-1***xyz9...
[2025-05-31 18:35:22] INFO: Making test request to OpenAI embeddings API...
[2025-05-31 18:35:23] INFO: API request successful! Received embedding of length 1536
[2025-05-31 18:35:23] INFO: ✅ API key is valid and working correctly!
```

**Prerequisites**:
- Valid OpenAI API key
- Internet connection for API testing
- Optional: `colorama` package for colored output (`pip install colorama`)

---

### Import/Export Tools

#### import_kb_folder.py
**Purpose**: Standalone script to import documents from the Knowledgebase folder into Athena's knowledge base without requiring the server to be running.

**Usage**:
```bash
# Import from default Knowledgebase folder
python scripts/import_kb_folder.py

# Import from custom folder
python scripts/import_kb_folder.py --folder /path/to/documents

# Show help
python scripts/import_kb_folder.py --help
```

**Features**:
- 🔄 Works without requiring the Athena server to be running
- 📄 Supports multiple file formats:
  - PDF documents
  - Text files (TXT)
  - Markdown files (MD, MARKDOWN)
  - Word documents (DOCX)
  - HTML files (HTML, HTM)
  - CSV files
  - JSON files
- 🔑 Automatic API key detection from multiple sources:
  1. AthenaConfig (config.json)
  2. DirectConnection database (SQLAlchemy + SQLite fallback)
  3. Environment variable (OPENAI_API_KEY)
- 📁 Creates Knowledgebase folder and README if it doesn't exist
- 🔄 Fallback embedding methods if OpenAI API is unavailable
- 📊 Comprehensive logging and progress reporting
- ⚡ Skip existing documents option
- 🛡️ Robust error handling and recovery

**Output Example**:
```
2025-05-31 18:40:15 - kb_folder_import - INFO - Found OpenAI API key in config.json
2025-05-31 18:40:15 - kb_folder_import - INFO - Using OpenAI API key: sk-1***xyz9
2025-05-31 18:40:15 - kb_folder_import - INFO - Found 5 documents in C:\Projects\AthenaNew\Knowledgebase
2025-05-31 18:40:15 - kb_folder_import - INFO - Importing documents from C:\Projects\AthenaNew\Knowledgebase...
2025-05-31 18:40:18 - kb_folder_import - INFO - Import complete: 4 imported, 1 skipped, 0 failed
```

**Prerequisites**:
- OpenAI API key (recommended) or fallback embedding method
- AthenaCore knowledge base system accessible
- Sufficient disk space for document processing

---

### MCP Tools

#### list_available_servers.py
**Purpose**: Discovers and lists available MCP (Model Context Protocol) servers from the Smithery registry.

**Usage**:
```bash
python scripts/list_available_servers.py
```

**Features**:
- 🌐 Queries Smithery registry API for available servers
- 🏥 Tests server health endpoints for availability
- 📋 Provides formatted server names for use in MCP requests
- 🔄 Fallback testing with known server endpoints
- 🔍 Comprehensive error handling and reporting
- 📊 Displays server descriptions and metadata

**Output Example**:
```
Using Smithery API key: 529c11***da0c7

Attempting direct REST API connection to list available servers...

✅ Successfully retrieved 15 servers from registry

1. smithery-ai/fetch
   Description: Web content fetching and processing server

2. smithery-ai/claude-3-haiku
   Description: Claude 3 Haiku model server

Testing direct server connections:

Testing smithery-ai/fetch:
✅ smithery-ai/fetch health check passed

Testing smithery-ai/claude-3-haiku:
✅ smithery-ai/claude-3-haiku health check passed

===== WORKING SERVERS =====

1. mcp:@smithery-ai/fetch
2. mcp:@smithery-ai/claude-3-haiku

Use these server names in your MCP requests
```

**Prerequisites**:
- Smithery API key configured in `AthenaCore/config.json`
- Internet connection for registry access
- Valid Smithery account with API access
