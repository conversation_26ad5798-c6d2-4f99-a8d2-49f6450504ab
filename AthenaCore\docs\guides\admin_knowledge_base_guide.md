# Admin Knowledge Base Management Guide

This guide provides examples of how administrators can manage the knowledge base system in Athena.

## 1. Viewing All Documents (Admin-Only)

As an admin, you can view all documents across all users by adding the `all_users=true` parameter:

```
GET /api/knowledge/documents?all_users=true
```

This will return all documents in the system regardless of which user they belong to.

## 2. Assigning Documents to Specific Users

### Example: Using cURL

```bash
curl -X POST \
  http://localhost:5000/api/knowledge/documents/dd8317c4-8418-4ac3-95ff-dd12a9210da2/assign \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer YOUR_ADMIN_API_KEY' \
  -d '{
    "user_id": "user123"
}'
```

### Example: Using JavaScript/Fetch

```javascript
// Admin UI - Assign Document to User
async function assignDocumentToUser(documentId, userId) {
  const response = await fetch(`/api/knowledge/documents/${documentId}/assign`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer YOUR_ADMIN_API_KEY'
    },
    body: JSON.stringify({
      user_id: userId
    })
  });
  
  const result = await response.json();
  if (result.success) {
    console.log(`Document assigned to user ${userId} successfully`);
  } else {
    console.error(`Failed to assign document: ${result.error}`);
  }
}
```

## 3. Admin Knowledge Base Dashboard

Here's a code example for an admin dashboard that could show all documents and allow assigning them to users:

```html
<!-- Admin Knowledge Base Dashboard -->
<div class="admin-kb-dashboard">
  <h2>Knowledge Base Administration</h2>
  
  <!-- Filter Controls -->
  <div class="filter-controls">
    <select id="userFilter">
      <option value="all">All Users</option>
      <option value="system">System Documents</option>
      <!-- Dynamically populated user list -->
    </select>
    <button onclick="fetchDocuments()">Apply Filters</button>
  </div>
  
  <!-- Documents Table -->
  <table class="documents-table">
    <thead>
      <tr>
        <th>Title</th>
        <th>Type</th>
        <th>Owner</th>
        <th>Date Added</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody id="documentsTableBody">
      <!-- Dynamically populated document list -->
    </tbody>
  </table>
  
  <!-- Assignment Modal -->
  <div id="assignmentModal" class="modal">
    <div class="modal-content">
      <h3>Assign Document to User</h3>
      <select id="targetUserSelect">
        <!-- Dynamically populated user list -->
      </select>
      <button onclick="confirmAssignment()">Assign</button>
      <button onclick="closeModal()">Cancel</button>
    </div>
  </div>
</div>

<script>
  let currentDocumentId = null;
  
  // Fetch all documents (admin view)
  async function fetchDocuments() {
    const userFilter = document.getElementById('userFilter').value;
    let url = '/api/knowledge/documents?all_users=true';
    
    if (userFilter !== 'all') {
      url += `&user_id=${userFilter}`;
    }
    
    const response = await fetch(url);
    const data = await response.json();
    
    if (data.success) {
      renderDocumentsTable(data.documents);
    } else {
      console.error('Failed to fetch documents:', data.error);
    }
  }
  
  // Render documents table
  function renderDocumentsTable(documents) {
    const tableBody = document.getElementById('documentsTableBody');
    tableBody.innerHTML = '';
    
    documents.forEach(doc => {
      const row = document.createElement('tr');
      
      // Add document info cells
      row.innerHTML = `
        <td>${doc.metadata.title || 'Untitled'}</td>
        <td>${doc.metadata.type || 'Unknown'}</td>
        <td>${doc.user_id || 'System'}</td>
        <td>${new Date(doc.created_at || Date.now()).toLocaleString()}</td>
        <td>
          <button onclick="viewDocument('${doc.id}')">View</button>
          <button onclick="openAssignmentModal('${doc.id}')">Assign</button>
          <button onclick="deleteDocument('${doc.id}')">Delete</button>
        </td>
      `;
      
      tableBody.appendChild(row);
    });
  }
  
  // Open assignment modal
  function openAssignmentModal(documentId) {
    currentDocumentId = documentId;
    // Populate user dropdown
    fetchUsers().then(users => {
      const select = document.getElementById('targetUserSelect');
      select.innerHTML = '';
      
      users.forEach(user => {
        const option = document.createElement('option');
        option.value = user.id;
        option.textContent = user.username || user.email || user.id;
        select.appendChild(option);
      });
      
      // Show modal
      document.getElementById('assignmentModal').style.display = 'block';
    });
  }
  
  // Confirm document assignment
  async function confirmAssignment() {
    const userId = document.getElementById('targetUserSelect').value;
    
    if (!userId || !currentDocumentId) {
      alert('Please select a valid user and document');
      return;
    }
    
    try {
      const response = await fetch(`/api/knowledge/documents/${currentDocumentId}/assign`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          user_id: userId
        })
      });
      
      const result = await response.json();
      
      if (result.success) {
        alert(`Document successfully assigned to user ${userId}`);
        closeModal();
        fetchDocuments(); // Refresh the table
      } else {
        alert(`Assignment failed: ${result.error}`);
      }
    } catch (error) {
      alert(`Error during assignment: ${error.message}`);
    }
  }
  
  // Fetch users for the dropdown
  async function fetchUsers() {
    const response = await fetch('/api/admin/users');
    const data = await response.json();
    return data.users || [];
  }
  
  // Close the modal
  function closeModal() {
    document.getElementById('assignmentModal').style.display = 'none';
    currentDocumentId = null;
  }
  
  // Initialize
  document.addEventListener('DOMContentLoaded', fetchDocuments);
</script>
```

## 4. Managing Multi-Tenant Document Libraries

For business cases involving many files (50+) that need to be assigned to different users:

### Initial Setup
1. Import all documents initially as "system" documents
2. Create user groups in your admin panel
3. Assign documents to specific users or groups based on access requirements

### Bulk Assignment Script

This Python script demonstrates how to bulk assign documents to users:

```python
import requests
import json
import csv

# Configuration
API_URL = "http://localhost:5000/api"
ADMIN_API_KEY = "your_admin_api_key_here"

headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {ADMIN_API_KEY}"
}

# Function to assign a document to a user
def assign_document(document_id, user_id):
    url = f"{API_URL}/knowledge/documents/{document_id}/assign"
    payload = {"user_id": user_id}
    
    response = requests.post(url, headers=headers, json=payload)
    
    if response.status_code == 200:
        result = response.json()
        if result.get("success"):
            print(f"Successfully assigned document {document_id} to user {user_id}")
            return True
        else:
            print(f"Failed to assign document: {result.get('error')}")
            return False
    else:
        print(f"Request failed with status code {response.status_code}")
        return False

# Bulk assign from CSV file
def bulk_assign_from_csv(csv_path):
    success_count = 0
    failure_count = 0
    
    with open(csv_path, 'r') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            document_id = row.get('document_id')
            user_id = row.get('user_id')
            
            if not document_id or not user_id:
                print(f"Skipping row with missing data: {row}")
                continue
                
            if assign_document(document_id, user_id):
                success_count += 1
            else:
                failure_count += 1
    
    print(f"Assignment complete: {success_count} successful, {failure_count} failed")

# Example usage
if __name__ == "__main__":
    # Option 1: Assign a single document
    assign_document("document-uuid-here", "user-id-here")
    
    # Option 2: Bulk assign from CSV
    bulk_assign_from_csv("document_assignments.csv")
```

### CSV Format for Bulk Assignment

Create a CSV file with the following format:

```
document_id,user_id
dd8317c4-8418-4ac3-95ff-dd12a9210da2,user1
a72bc45d-91f3-4e87-bd36-e27a1cc67890,user2
f19ae523-7ac4-4d91-a83b-12fe42567d81,user3
```

## 5. Knowledge Base Security Best Practices

1. **Regular Audits**: Perform regular audits of document assignments to ensure proper access control.
2. **Role-Based Access**: Use role-based access control (RBAC) for document management.
3. **Document Expiration**: Consider setting expiration dates for document access.
4. **Audit Logging**: Enable comprehensive logging for all document access and assignments.
5. **Validation**: Validate user and document IDs before performing any assignment operation.

## 6. Troubleshooting

### Common Issues and Solutions

1. **Document not visible to assigned user**
   - Verify the document was properly assigned
   - Check if the user has the correct permissions
   - Ensure the document metadata is correctly set

2. **Assignment API returns error**
   - Verify you're using admin credentials
   - Check that both document ID and user ID exist
   - Ensure the API endpoint URL is correct

3. **System documents not appearing**
   - Verify the `include_system=true` parameter is set
   - Check if system documents were properly imported
   - Review the knowledgebase initialization logs

For further assistance, contact the system administrator or refer to the technical documentation.
