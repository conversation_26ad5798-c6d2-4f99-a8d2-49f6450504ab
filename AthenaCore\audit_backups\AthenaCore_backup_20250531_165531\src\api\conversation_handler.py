"""
Helper module for handling conversation requests
"""

import logging
from datetime import datetime
from flask import jsonify
from flask_login import current_user

logger = logging.getLogger("conversation_handler")

def handle_missing_conversation(conversation_id, limit=100, offset=0):
    """
    Creates a response for missing conversations to avoid 404 errors
    """
    logger.info(f"Creating default response for missing conversation: {conversation_id}")
    
    return jsonify({
        "conversation": {
            "id": conversation_id,
            "title": "New conversation", 
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat(),
            "user_id": current_user.id,
            "is_active": True
        },
        "messages": [],
        "total_messages": 0,
        "limit": limit,
        "offset": offset
    })
