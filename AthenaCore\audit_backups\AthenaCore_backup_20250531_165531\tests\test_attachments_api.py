# tests/test_attachments_api.py

"""
Tests for the Attachment API endpoints.

This file contains tests for:
- Uploading attachments
- Downloading attachments
- Listing and retrieving attachment metadata
- Updating attachment properties
- Deleting attachments
"""

import json
import unittest
import os
import io
import hashlib
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from flask import url_for
from flask_login import current_user
from werkzeug.datastructures import FileStorage

from src.models import db, User
from src.login.device_models import Device, Command, Attachment
from src.api.attachments import get_file_type, calculate_file_hash

# Import the app and create a test client
from main import app

class AttachmentsAPITestCase(unittest.TestCase):
    """Test case for the Attachments API endpoints."""
    
    def setUp(self):
        """Set up test client and fixtures."""
        # Configure the application for testing
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        app.config['WTF_CSRF_ENABLED'] = False
        app.config['ATTACHMENT_STORAGE_PATH'] = 'test_attachments'
        
        # Create test client
        self.client = app.test_client()
        
        # Create application context
        self.app_context = app.app_context()
        self.app_context.push()
        
        # Create database tables
        db.create_all()
        
        # Create test user
        self.user = User(
            username="testuser",
            email="<EMAIL>"
        )
        self.user.set_password("testpassword")
        db.session.add(self.user)
        db.session.commit()
        
        # Create test device
        self.device = Device(
            device_uuid="test-device-uuid",
            name="Test Device",
            device_type="test_device",
            user_id=self.user.id,
            is_active=True
        )
        db.session.add(self.device)
        db.session.commit()
        
        # Create test command
        self.command = Command(
            command_uuid="test-command-uuid",
            user_id=self.user.id,
            target_device_id=self.device.id,
            capability_name="test_capability",
            parameters=json.dumps({"test": "value"}),
            status="completed"
        )
        db.session.add(self.command)
        db.session.commit()
        
        # Create test attachment storage directory
        os.makedirs(app.config['ATTACHMENT_STORAGE_PATH'], exist_ok=True)
        
        # Login the test user
        with self.client.session_transaction() as session:
            session['user_id'] = self.user.id
        
    def tearDown(self):
        """Clean up after tests."""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
        
        # Clean up test attachment files
        import shutil
        if os.path.exists(app.config['ATTACHMENT_STORAGE_PATH']):
            shutil.rmtree(app.config['ATTACHMENT_STORAGE_PATH'])
    
    def test_upload_attachment(self):
        """Test uploading an attachment."""
        # Create a test file
        test_file = io.BytesIO(b"Test file content")
        
        # Mock the create_attachment function and file operations
        with patch('src.api.attachments.create_attachment') as mock_create_attachment, \
             patch('src.api.attachments.calculate_file_hash') as mock_calculate_hash, \
             patch('werkzeug.utils.secure_filename', return_value='test_file.txt'):
            
            # Create a mock attachment
            mock_attachment = MagicMock()
            mock_attachment.attachment_uuid = "test-attachment-uuid"
            mock_attachment.to_dict.return_value = {
                "attachment_uuid": "test-attachment-uuid",
                "filename": "test_file.txt",
                "status": "complete"
            }
            mock_create_attachment.return_value = mock_attachment
            mock_calculate_hash.return_value = "test-hash-value"
            
            # Send request
            response = self.client.post(
                '/api/attachments',
                data={
                    'file': (test_file, 'test_file.txt'),
                    'command_id': self.command.id,
                    'device_id': self.device.id,
                    'is_public': 'false',
                    'expires_in_hours': '24'
                },
                content_type='multipart/form-data'
            )
            
            # Check response
            self.assertEqual(response.status_code, 201)
            data = json.loads(response.data)
            self.assertEqual(data['attachment']['attachment_uuid'], "test-attachment-uuid")
            self.assertEqual(data['message'], "File uploaded successfully")
            
            # Verify mocks were called with correct arguments
            mock_create_attachment.assert_called_once()
            args, kwargs = mock_create_attachment.call_args
            self.assertEqual(kwargs['user_id'], self.user.id)
            self.assertEqual(kwargs['filename'], "test_file.txt")
            self.assertEqual(kwargs['command_id'], str(self.command.id))
            self.assertEqual(kwargs['is_public'], False)
    
    def test_list_attachments(self):
        """Test listing attachments."""
        # Create some test attachments
        for i in range(3):
            attachment = Attachment(
                attachment_uuid=f"test-attachment-{i}",
                user_id=self.user.id,
                filename=f"test_file_{i}.txt",
                file_type="text/plain",
                file_size=100,
                file_hash="test-hash-value",
                storage_path=f"test/path/{i}.txt",
                status="complete",
                command_id=self.command.id if i == 0 else None,
                device_id=self.device.id if i == 1 else None,
                is_public=False,
                transfer_progress=100,
                expires_at=datetime.utcnow() + timedelta(hours=24)
            )
            db.session.add(attachment)
        db.session.commit()
        
        # Send request
        response = self.client.get('/api/attachments')
        
        # Check response
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(len(data['attachments']), 3)
        
        # Test filtering by command_id
        response = self.client.get(f'/api/attachments?command_id={self.command.id}')
        data = json.loads(response.data)
        self.assertEqual(len(data['attachments']), 1)
        self.assertEqual(data['attachments'][0]['attachment_uuid'], "test-attachment-0")
        
        # Test filtering by device_id
        response = self.client.get(f'/api/attachments?device_id={self.device.id}')
        data = json.loads(response.data)
        self.assertEqual(len(data['attachments']), 1)
        self.assertEqual(data['attachments'][0]['attachment_uuid'], "test-attachment-1")
        
        # Test filtering by filename
        response = self.client.get('/api/attachments?filename=file_2')
        data = json.loads(response.data)
        self.assertEqual(len(data['attachments']), 1)
        self.assertEqual(data['attachments'][0]['attachment_uuid'], "test-attachment-2")
    
    def test_get_attachment_info(self):
        """Test getting attachment metadata."""
        # Create a test attachment
        attachment = Attachment(
            attachment_uuid="test-attachment-uuid",
            user_id=self.user.id,
            filename="test_file.txt",
            file_type="text/plain",
            file_size=100,
            file_hash="test-hash-value",
            storage_path="test/path/file.txt",
            status="complete",
            command_id=self.command.id,
            device_id=self.device.id,
            is_public=True,
            transfer_progress=100,
            expires_at=datetime.utcnow() + timedelta(hours=24)
        )
        db.session.add(attachment)
        db.session.commit()
        
        # Send request
        response = self.client.get(f'/api/attachments/{attachment.attachment_uuid}/info')
        
        # Check response
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['attachment_uuid'], "test-attachment-uuid")
        self.assertEqual(data['filename'], "test_file.txt")
        self.assertEqual(data['file_type'], "text/plain")
        self.assertEqual(data['status'], "complete")
        self.assertTrue(data['is_public'])
    
    def test_update_attachment(self):
        """Test updating attachment metadata."""
        # Create a test attachment
        attachment = Attachment(
            attachment_uuid="test-attachment-uuid",
            user_id=self.user.id,
            filename="test_file.txt",
            file_type="text/plain",
            file_size=100,
            file_hash="test-hash-value",
            storage_path="test/path/file.txt",
            status="complete",
            is_public=False,
            transfer_progress=100,
            expires_at=datetime.utcnow() + timedelta(hours=24)
        )
        db.session.add(attachment)
        db.session.commit()
        
        # Prepare update data
        update_data = {
            "filename": "updated_file.txt",
            "is_public": True,
            "expires_in_hours": 48
        }
        
        # Send request
        response = self.client.put(
            f'/api/attachments/{attachment.attachment_uuid}',
            json=update_data,
            content_type='application/json'
        )
        
        # Check response
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['message'], "Attachment updated successfully")
        self.assertEqual(data['attachment']['filename'], "updated_file.txt")
        self.assertEqual(data['attachment']['file_type'], "text/plain")
        self.assertTrue(data['attachment']['is_public'])
        
        # Verify attachment was updated in database
        updated_attachment = Attachment.query.filter_by(attachment_uuid=attachment.attachment_uuid).first()
        self.assertEqual(updated_attachment.filename, "updated_file.txt")
        self.assertTrue(updated_attachment.is_public)
        
        # Expiration should be around 48 hours from now
        expiration_delta = updated_attachment.expires_at - datetime.utcnow()
        self.assertGreater(expiration_delta.total_seconds(), 47 * 3600)  # Allow some processing time
    
    def test_delete_attachment(self):
        """Test deleting an attachment."""
        # Create a test attachment
        attachment = Attachment(
            attachment_uuid="test-attachment-uuid",
            user_id=self.user.id,
            filename="test_file.txt",
            file_type="text/plain",
            file_size=100,
            file_hash="test-hash-value",
            storage_path="test/path/file.txt",
            status="complete",
            is_public=False,
            transfer_progress=100
        )
        db.session.add(attachment)
        db.session.commit()
        
        # Mock file operations
        with patch('pathlib.Path.exists', return_value=True), \
             patch('pathlib.Path.unlink') as mock_unlink, \
             patch('pathlib.Path.parent'), \
             patch('pathlib.Path.iterdir', return_value=[]):
            
            # Send request
            response = self.client.delete(f'/api/attachments/{attachment.attachment_uuid}')
            
            # Check response
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            self.assertEqual(data['message'], "Attachment deleted successfully")
            self.assertEqual(data['attachment_uuid'], "test-attachment-uuid")
            
            # Verify attachment was deleted from database
            deleted_attachment = Attachment.query.filter_by(attachment_uuid=attachment.attachment_uuid).first()
            self.assertIsNone(deleted_attachment)
            
            # Verify file deletion was attempted
            mock_unlink.assert_called_once()
    
    def test_download_attachment(self):
        """Test downloading an attachment."""
        # Create a test attachment
        attachment = Attachment(
            attachment_uuid="test-attachment-uuid",
            user_id=self.user.id,
            filename="test_file.txt",
            file_type="text/plain",
            file_size=100,
            file_hash="test-hash-value",
            storage_path="test/path/file.txt",
            status="complete",
            is_public=True,
            transfer_progress=100
        )
        db.session.add(attachment)
        db.session.commit()
        
        # Create a test file
        test_file_content = b"Test file content"
        test_file_path = os.path.join(app.config['ATTACHMENT_STORAGE_PATH'], "test", "path")
        os.makedirs(test_file_path, exist_ok=True)
        with open(os.path.join(test_file_path, "file.txt"), "wb") as f:
            f.write(test_file_content)
        
        # Send request
        response = self.client.get(f'/api/attachments/{attachment.attachment_uuid}')
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, test_file_content)
        self.assertEqual(response.headers['Content-Type'], "text/plain")
        self.assertEqual(
            response.headers['Content-Disposition'], 
            f'attachment; filename=test_file.txt'
        )
    
    def test_download_nonpublic_attachment_unauthorized(self):
        """Test attempting to download a non-public attachment without authentication."""
        # Create a test attachment
        attachment = Attachment(
            attachment_uuid="test-attachment-uuid",
            user_id=self.user.id,
            filename="test_file.txt",
            file_type="text/plain",
            file_size=100,
            file_hash="test-hash-value",
            storage_path="test/path/file.txt",
            status="complete",
            is_public=False,
            transfer_progress=100
        )
        db.session.add(attachment)
        db.session.commit()
        
        # Create unauthenticated client
        client = app.test_client()
        
        # Send request
        response = client.get(f'/api/attachments/{attachment.attachment_uuid}')
        
        # Check response (should be forbidden)
        self.assertEqual(response.status_code, 403)
        data = json.loads(response.data)
        self.assertEqual(data['error'], "Access denied. This attachment is not public.")
    
    def test_command_attachments(self):
        """Test getting attachments for a specific command."""
        # Create test attachments for the command
        for i in range(3):
            attachment = Attachment(
                attachment_uuid=f"test-attachment-{i}",
                user_id=self.user.id,
                filename=f"test_file_{i}.txt",
                file_type="text/plain",
                file_size=100,
                file_hash="test-hash-value",
                storage_path=f"test/path/{i}.txt",
                status="complete",
                command_id=self.command.id,
                is_public=False,
                transfer_progress=100
            )
            db.session.add(attachment)
        db.session.commit()
        
        # Send request
        response = self.client.get(f'/api/attachments/commands/{self.command.id}')
        
        # Check response
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(len(data['attachments']), 3)
        self.assertEqual(data['command']['command_uuid'], self.command.command_uuid)
    
    def test_device_attachments(self):
        """Test getting attachments for a specific device."""
        # Create test attachments for the device
        for i in range(3):
            attachment = Attachment(
                attachment_uuid=f"test-attachment-{i}",
                user_id=self.user.id,
                filename=f"test_file_{i}.txt",
                file_type="text/plain",
                file_size=100,
                file_hash="test-hash-value",
                storage_path=f"test/path/{i}.txt",
                status="complete",
                device_id=self.device.id,
                is_public=False,
                transfer_progress=100
            )
            db.session.add(attachment)
        db.session.commit()
        
        # Send request
        response = self.client.get(f'/api/attachments/devices/{self.device.device_uuid}')
        
        # Check response
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(len(data['attachments']), 3)
        self.assertEqual(data['device']['device_uuid'], self.device.device_uuid)

if __name__ == '__main__':
    unittest.main()
