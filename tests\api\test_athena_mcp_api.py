import requests
import json

# Base URL for Athena API
BASE_URL = "http://localhost:5000/api/v1"

# Function to test chat with MCP model
def test_mcp_chat():
    # Test payload with MCP model
    payload = {
        "message": "What's the weather in Centralia, WA?",
        "model": "mcp:@smithery-ai/fetch",
        "user_id": 1
    }
    
    print(f"Sending chat request with model: {payload['model']}")
    print(f"Message: {payload['message']}")
    
    # Send POST request to chat endpoint
    response = requests.post(f"{BASE_URL}/chat", json=payload)
    
    # Print response
    print(f"Status code: {response.status_code}")
    if response.ok:
        try:
            result = response.json()
            print("\nResponse content:")
            print(json.dumps(result, indent=2))
            return result
        except json.JSONDecodeError:
            print("Could not parse JSON response")
            print(f"Raw response: {response.text[:500]}...")
    else:
        print(f"Error: {response.text}")

if __name__ == "__main__":
    print("Testing Athena MCP Integration")
    print("-" * 40)
    test_mcp_chat()
