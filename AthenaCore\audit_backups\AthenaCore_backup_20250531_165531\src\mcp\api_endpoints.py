# MCP Server Creation and Management API Endpoints

import os
import json
import tempfile
import logging
import asyncio
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from werkzeug.utils import secure_filename

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user

from src.mcp.server_creation import AthenaMCPServerFactory, create_default_server
from src.mcp.deployment import MCPDeploymentManager, deploy_server
from src.mcp.smithery_client import SmitheryClient
from src.mcp.connection_manager import MCPConnectionManager
from src.utils.config import AthenaConfig
from src.models import User, db, MCPApiKey, MCPServerTemplate
from src.mcp.template_manager import MCPTemplateManager

logger = logging.getLogger(__name__)

# Create Blueprint for MCP server management
mcp_server_bp = Blueprint('mcp_server', __name__, url_prefix='/mcp')

@mcp_server_bp.route('/search', methods=['GET'])
def search_mcp_servers_web():
    """Search for MCP servers in the Smithery registry for the web interface."""
    try:
        query = request.args.get('query', '')
        logger.info(f"Searching for MCP servers with query: {query}")
        
        # Get Athena config
        config = AthenaConfig.load()
        
        # Create Smithery client
        smithery_client = SmitheryClient(api_key=config.smithery_api_key)
        
        # Search for servers
        results = smithery_client.search_servers(query)
        
        return jsonify({
            "success": True,
            "results": results
        })
        
    except Exception as e:
        logger.error(f"Error searching for MCP servers: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@mcp_server_bp.route('/servers', methods=['GET'])
def get_available_servers():
    """Get available MCP servers for the frontend."""
    try:
        query = request.args.get('query', '')
        logger.info(f"Getting available MCP servers, query param: {query}")
        
        # Get Athena config
        config = AthenaConfig.load()
        
        # Create Smithery client
        smithery_client = SmitheryClient(api_key=config.smithery_api_key)
        
        # Get servers (use search function with empty query if no specific query)
        if query:
            servers = smithery_client.search_servers(query)
        else:
            # For now, return a sample list or call search with empty string
            servers = smithery_client.search_servers('')
        
        return jsonify({
            "success": True,
            "servers": servers
        })
        
    except Exception as e:
        logger.error(f"Error getting available MCP servers: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

# Store active server processes for testing
active_test_servers = {}

# Store active MCP connections
active_mcp_connections = {}

# Path for storing server templates
TEMPLATE_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
                          'server_templates')
os.makedirs(TEMPLATE_DIR, exist_ok=True)

# Initialize template manager
template_manager = MCPTemplateManager(AthenaConfig.load())


@mcp_server_bp.route('/create', methods=['POST'])
@login_required
def create_server():
    """Create a new MCP server from definition."""
    try:
        # Get server definition from request
        server_def = request.json
        if not server_def:
            return jsonify({"error": "No server definition provided"}), 400
        
        # Validate required fields
        for field in ['name', 'description', 'version']:
            if field not in server_def:
                return jsonify({"error": f"Missing required field: {field}"}), 400
        
        # Get Athena instance from app context
        athena = current_app.athena
        config = current_app.config['ATHENA_CONFIG']
        
        # Create server factory
        factory = AthenaMCPServerFactory(athena, config)
        
        # Create the server
        server = factory.create_server(
            name=server_def['name'],
            description=server_def['description'],
            version=server_def['version'],
            dependencies=server_def.get('dependencies', [])
        )
        
        # Add tools from definition
        for tool_def in server_def.get('tools', []):
            # Handle tool registration (simplified for now)
            # In a real implementation, this would dynamically create functions
            # and register them with the server
            pass
        
        # Add resources from definition
        for resource_def in server_def.get('resources', []):
            # Handle resource registration (simplified for now)
            pass
        
        # Export configuration for response
        server_config = factory.export_server_config(server_def['name'])
        
        return jsonify({
            "success": True,
            "message": f"Created server: {server_def['name']}",
            "server_config": server_config
        })
        
    except Exception as e:
        logger.error(f"Error creating MCP server: {str(e)}")
        return jsonify({"error": str(e)}), 500


@mcp_server_bp.route('/test', methods=['POST'])
@login_required
def test_server():
    """Start a test instance of an MCP server."""
    try:
        # Get server definition from request
        server_def = request.json
        if not server_def:
            return jsonify({"error": "No server definition provided"}), 400
        
        # Create temporary file for server code
        with tempfile.NamedTemporaryFile(suffix='.py', delete=False, mode='w') as f:
            server_file = f.name
            # Generate Python code for the server
            # This is simplified - would need more sophisticated code generation
            f.write(_generate_server_code(server_def))
        
        # Start server as a subprocess
        user_id = current_user.id
        process = asyncio.create_subprocess_exec(
            sys.executable, server_file,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        # Store process for later termination
        active_test_servers[f"{user_id}_{server_def['name']}"] = {
            "process": process,
            "file": server_file,
            "started_at": datetime.now().isoformat()
        }
        
        return jsonify({
            "success": True,
            "message": f"Started test server: {server_def['name']}",
            "server_id": f"{user_id}_{server_def['name']}",
            "status": "running"
        })
        
    except Exception as e:
        logger.error(f"Error testing MCP server: {str(e)}")
        return jsonify({"error": str(e)}), 500


@mcp_server_bp.route('/test/<server_id>/stop', methods=['POST'])
@login_required
async def stop_test_server(server_id):
    """Stop a running test server."""
    try:
        if server_id not in active_test_servers:
            return jsonify({"error": "Test server not found"}), 404
        
        # Verify user owns this test server
        user_id = current_user.id
        if not server_id.startswith(f"{user_id}_"):
            return jsonify({"error": "Not authorized to stop this server"}), 403
        
        # Terminate process
        server_info = active_test_servers[server_id]
        process = server_info["process"]
        await process.terminate()
        
        # Clean up temporary file
        if os.path.exists(server_info["file"]):
            os.unlink(server_info["file"])
        
        # Remove from active servers
        del active_test_servers[server_id]
        
        return jsonify({
            "success": True,
            "message": f"Stopped test server: {server_id}",
            "status": "stopped"
        })
        
    except Exception as e:
        logger.error(f"Error stopping test server: {str(e)}")
        return jsonify({"error": str(e)}), 500


@mcp_server_bp.route('/test/<server_id>/logs', methods=['GET'])
@login_required
async def get_test_server_logs(server_id):
    """Get logs from a running test server."""
    try:
        if server_id not in active_test_servers:
            return jsonify({"error": "Test server not found"}), 404
        
        # Verify user owns this test server
        user_id = current_user.id
        if not server_id.startswith(f"{user_id}_"):
            return jsonify({"error": "Not authorized to access these logs"}), 403
        
        # Get process stdout/stderr
        server_info = active_test_servers[server_id]
        process = server_info["process"]
        
        stdout, stderr = await process.communicate()
        
        return jsonify({
            "success": True,
            "stdout": stdout.decode('utf-8'),
            "stderr": stderr.decode('utf-8'),
            "status": "running" if process.returncode is None else "stopped"
        })
        
    except Exception as e:
        logger.error(f"Error getting test server logs: {str(e)}")
        return jsonify({"error": str(e)}), 500


@mcp_server_bp.route('/deploy', methods=['POST'])
@login_required
async def deploy_mcp_server():
    """Deploy an MCP server to Smithery."""
    try:
        # Get server definition from request
        server_def = request.json
        if not server_def:
            return jsonify({"error": "No server definition provided"}), 400
        
        # Get Athena instance from app context
        athena = current_app.athena
        config = current_app.config['ATHENA_CONFIG']
        
        # Create server factory
        factory = AthenaMCPServerFactory(athena, config)
        
        # Create the server
        server = factory.create_server(
            name=server_def['name'],
            description=server_def['description'],
            version=server_def['version'],
            dependencies=server_def.get('dependencies', [])
        )
        
        # Create Smithery client
        smithery_client = SmitheryClient(config)
        
        # Deploy to Smithery
        success, message = await deploy_server(server, smithery_client)
        
        return jsonify({
            "success": success,
            "message": message
        })
        
    except Exception as e:
        logger.error(f"Error deploying MCP server: {str(e)}")
        return jsonify({"error": str(e)}), 500


@mcp_server_bp.route('/templates', methods=['GET'])
def get_templates():
    """Get all templates (system and user)."""
    try:
        # Get system templates
        system_templates = template_manager.get_system_templates()
        
        # Get user templates if user is logged in
        user_templates = []
        if hasattr(current_user, 'is_authenticated') and current_user.is_authenticated:
            user_id = current_user.id
            user_templates = template_manager.get_user_templates(user_id)
        
        # Combine templates
        templates = system_templates + user_templates
        
        return jsonify({
            "success": True,
            "templates": templates
        })
        
    except Exception as e:
        logger.error(f"Error getting templates: {str(e)}")
        return jsonify({"error": str(e)}), 500


@mcp_server_bp.route('/templates/<template_id>', methods=['GET'])
def get_template(template_id):
    """Get a template by ID."""
    try:
        # Check if user is logged in
        user_id = current_user.id if hasattr(current_user, 'is_authenticated') and current_user.is_authenticated else None
        
        # Get template
        template = template_manager.get_template(template_id, user_id)
        
        if not template:
            return jsonify({"error": "Template not found"}), 404
        
        return jsonify({
            "success": True,
            "template": template
        })
        
    except Exception as e:
        logger.error(f"Error getting template: {str(e)}")
        return jsonify({"error": str(e)}), 500


@mcp_server_bp.route('/templates', methods=['POST'])
@login_required
def create_template():
    """Create a new template."""
    try:
        # Get request data
        data = request.json
        if not data:
            return jsonify({"error": "No template data provided"}), 400
        
        # Get current user ID
        user_id = current_user.id
        
        # Validate required fields
        if 'name' not in data:
            return jsonify({"error": "Name is required"}), 400
        
        if 'server_definition' not in data:
            return jsonify({"error": "Server definition is required"}), 400
        
        # Create template
        success, message, template_id = template_manager.create_template(
            user_id=user_id,
            name=data['name'],
            description=data.get('description', ''),
            server_definition=data['server_definition']
        )
        
        if not success:
            return jsonify({"error": message}), 400
        
        return jsonify({
            "success": True,
            "message": message,
            "template_id": template_id
        })
        
    except Exception as e:
        logger.error(f"Error creating template: {str(e)}")
        return jsonify({"error": str(e)}), 500


@mcp_server_bp.route('/templates/<template_id>', methods=['PUT'])
@login_required
def update_template(template_id):
    """Update a template."""
    try:
        # Get request data
        data = request.json
        if not data:
            return jsonify({"error": "No template data provided"}), 400
        
        # Get current user ID
        user_id = current_user.id
        
        # Update template
        success, message = template_manager.update_template(
            template_id=int(template_id),
            user_id=user_id,
            name=data.get('name'),
            description=data.get('description'),
            server_definition=data.get('server_definition')
        )
        
        if not success:
            return jsonify({"error": message}), 400 if "not found" in message else 500
        
        return jsonify({
            "success": True,
            "message": message
        })
        
    except Exception as e:
        logger.error(f"Error updating template: {str(e)}")
        return jsonify({"error": str(e)}), 500


@mcp_server_bp.route('/templates/<template_id>', methods=['DELETE'])
@login_required
def delete_template(template_id):
    """Delete a template."""
    try:
        # Get current user ID
        user_id = current_user.id
        
        # Delete template
        success, message = template_manager.delete_template(
            template_id=int(template_id),
            user_id=user_id
        )
        
        if not success:
            return jsonify({"error": message}), 400 if "not found" in message else 500
        
        return jsonify({
            "success": True,
            "message": message
        })
        
    except Exception as e:
        logger.error(f"Error deleting template: {str(e)}")
        return jsonify({"error": str(e)}), 500


@mcp_server_bp.route('/search', methods=['GET'])
def search_mcp_servers():
    """Search for MCP servers in the Smithery registry."""
    try:
        # Get query parameters
        query = request.args.get('query', '')
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        
        # Create Smithery client
        config = AthenaConfig.load()
        # Extract the API key from the config instead of passing the whole config object
        smithery_api_key = config.smithery_api_key if hasattr(config, 'smithery_api_key') else None
        smithery_client = SmitheryClient(api_key=smithery_api_key)
        
        # Search for servers
        # The list_servers method expects 'q' parameter but search_servers passes through the query parameter
        servers, total = smithery_client.search_servers(query, page, page_size)
        
        logger.info(f"MCP search query: '{query}', found {total} servers")
        
        # Log the first server object for debugging if available
        if servers and len(servers) > 0:
            logger.debug(f"First server object keys: {list(servers[0].keys())}")
        
        return jsonify({
            "success": True,
            "results": servers,
            "total": total,
            "page": page,
            "page_size": page_size
        })
        
    except Exception as e:
        logger.error(f"Error searching MCP servers: {str(e)}")
        return jsonify({"error": str(e)}), 500


@mcp_server_bp.route('/connect', methods=['POST'])
def connect_to_mcp():
    """Connect to an external MCP server."""
    try:
        # Get request data
        data = request.json
        if not data:
            return jsonify({"error": "No connection data provided"}), 400
        
        # Validate required fields
        if 'mcp_name' not in data:
            return jsonify({"error": "MCP name is required"}), 400
        
        mcp_name = data['mcp_name']
        config = data.get('config', {})
        
        # Create Smithery client
        athena_config = AthenaConfig.load()
        # Extract the API key from the config instead of passing the whole config object
        smithery_api_key = athena_config.smithery_api_key if hasattr(athena_config, 'smithery_api_key') else None
        smithery_client = SmitheryClient(api_key=smithery_api_key)
        
        # Initialize connection manager
        connection_manager = MCPConnectionManager(smithery_client)
        
        # Normalize server name for API call
        normalized_name = mcp_name
        if mcp_name.startswith('@'):
            normalized_name = mcp_name[1:]
            
        # Log the normalized name for debugging
        logger.info(f"Attempting to connect to MCP server with normalized name: {normalized_name}")
        
        # Create connection
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        connection = loop.run_until_complete(connection_manager.create_connection(normalized_name, config))
        
        if not connection:
            return jsonify({"error": f"Failed to connect to MCP server '{mcp_name}'"}), 500
        
        # Store connection for later use
        connection_id = f"{normalized_name}"
        active_mcp_connections[connection_id] = {
            "connection": connection,
            "config": config,
            "created_at": datetime.now().isoformat()
        }
        
        return jsonify({
            "success": True,
            "message": f"Connected to MCP server: {mcp_name}",
            "connection_id": connection_id
        })
        
    except Exception as e:
        logger.error(f"Error connecting to MCP server: {str(e)}")
        return jsonify({"error": str(e)}), 500


@mcp_server_bp.route('/connections', methods=['GET'])
def get_mcp_connections():
    """Get all active MCP connections."""
    try:
        # Get all active connections
        connections = []
        for connection_id, data in active_mcp_connections.items():
            connection = data["connection"]
            connections.append({
                "id": connection_id,
                "name": connection.name if hasattr(connection, 'name') else connection.qualified_name,
                "qualified_name": connection.qualified_name,
                "created": data["created_at"],
                "server_url": connection.base_url if hasattr(connection, 'base_url') else "",
                "status": "active"
            })
        
        return jsonify({
            "success": True,
            "connections": connections
        })
        
    except Exception as e:
        logger.error(f"Error getting MCP connections: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500


@mcp_server_bp.route('/disconnect/<connection_id>', methods=['POST'])
def disconnect_from_mcp(connection_id):
    """Disconnect from an MCP server."""
    try:
        if connection_id not in active_mcp_connections:
            return jsonify({
                "success": False,
                "error": f"Connection ID not found: {connection_id}"
            }), 404
        
        # Get the connection manager
        connection_data = active_mcp_connections[connection_id]
        connection = connection_data["connection"]
        
        # Log the disconnection
        logger.info(f"Disconnecting from MCP server: {connection.qualified_name} (ID: {connection_id})")
        
        # Remove the connection from active connections
        del active_mcp_connections[connection_id]
        
        return jsonify({
            "success": True,
            "message": f"Disconnected from MCP server: {connection.qualified_name}"
        })
        
    except Exception as e:
        logger.error(f"Error disconnecting from MCP server: {str(e)}")
        return jsonify({"error": str(e)}), 500


def _get_system_templates():
    """Get list of system-provided templates."""
    templates = [
        {
            "id": "web_services",
            "name": "Web Services MCP",
            "description": "MCP server with web search and content extraction tools",
            "user_created": False
        },
        {
            "id": "file_system",
            "name": "File System MCP",
            "description": "MCP server for file operations and document processing",
            "user_created": False
        },
        {
            "id": "database",
            "name": "Database Explorer MCP",
            "description": "MCP server for database access and querying",
            "user_created": False
        },
        {
            "id": "system",
            "name": "System Command MCP",
            "description": "MCP server for system command execution and monitoring",
            "user_created": False
        },
    ]
    
    return templates


def _generate_server_code(server_def):
    """Generate Python code for an MCP server from a definition."""
    # This is a simplified implementation
    code = [
        "#!/usr/bin/env python\n",
        "# Generated MCP Server\n\n",
        "import json\n",
        "import logging\n",
        "from mcp.server.fastmcp import FastMCP, Context\n\n",
        
        f"# Create server\n",
        f"server = FastMCP(\n",
        f"    \"{server_def['name']}\",\n",
        f"    description=\"{server_def['description']}\",\n",
        f"    version=\"{server_def['version']}\",\n",
        f"    dependencies={json.dumps(server_def.get('dependencies', []))}\n",
        f")\n\n"
    ]
    
    # Add tools
    for tool in server_def.get('tools', []):
        params = ", ".join([f"{p['name']}: str" for p in tool.get('parameters', [])])
        if params:
            params += ", "
        params += "ctx: Context"
        
        code.append(f"@server.tool()\n")
        code.append(f"def {tool['name']}({params}) -> str:\n")
        code.append(f"    \"\"{tool.get('description', '')}\"\"\n")
        code.append(f"    # Implementation generated from template\n")
        code.append(f"    return \"Response from {tool['name']}\"\n\n")
    
    # Add resources
    for resource in server_def.get('resources', []):
        code.append(f"@server.resource(\"{resource['uri_template']}\")\n")
        code.append(f"def resource_{resource['uri_template'].replace('/', '_').replace('{', '').replace('}', '')}(ctx: Context) -> str:\n")
        code.append(f"    \"\"{resource.get('description', '')}\"\"\n")
        code.append(f"    # Implementation generated from template\n")
        code.append(f"    return json.dumps({{\"data\": \"Sample resource data\"}})\n\n")
    
    # Add main section
    code.append("if __name__ == \"__main__\":\n")
    code.append("    server.run()\n")
    
    return "".join(code)
