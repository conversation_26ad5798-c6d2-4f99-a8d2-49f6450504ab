import os
import json
import sys
import requests

def main():
    # Load config from file
    config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                             'AthenaCore', 'config.json')
    
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    smithery_api_key = config.get("smithery_api_key")
    if not smithery_api_key:
        print("No Smithery API key found in config.json")
        return 1
    
    print(f"Using Smithery API key: {smithery_api_key[:6]}...{smithery_api_key[-4:]}")
    
    # Try to get a list of available servers directly using REST API
    headers = {
        "Authorization": f"Bearer {smithery_api_key}"
    }
    
    print("\nAttempting direct REST API connection to list available servers...")
    
    # First, try the standard Smithery registry endpoint
    try:
        resp = requests.get("https://registry.smithery.ai/api/v1/servers", headers=headers)
        if resp.status_code == 200:
            servers = resp.json()
            print(f"\n✅ Successfully retrieved {len(servers)} servers from registry")
            for i, server in enumerate(servers, 1):
                print(f"\n{i}. {server.get('owner', 'unknown')}/{server.get('name', 'unknown')}")
                print(f"   Description: {server.get('description', 'No description')}")
            return 0
        else:
            print(f"❌ Failed to retrieve servers: HTTP {resp.status_code}")
            print(f"Response: {resp.text[:1000]}")
    except Exception as e:
        print(f"❌ Error connecting to registry: {str(e)}")
    
    # Try another approach - get directly from server.smithery.ai
    print("\nTrying alternative approach - direct server.smithery.ai connection...")
    
    # Test with known MCP server endpoints
    test_servers = [
        # Format: (owner, model)
        ("smithery-ai", "fetch"),
        ("smithery-ai", "claude-3-haiku"),
        ("smithery-ai", "fetch-v2"),
        ("openai", "gpt-4"),
        ("anthropic", "claude-3-opus"),
        ("anthropic", "claude-3-sonnet"),
        ("anthropic", "claude-3-haiku"),
    ]
    
    print("\nTesting direct server connections:")
    working_servers = []
    
    for owner, model in test_servers:
        server_id = f"{owner}/{model}"
        print(f"\nTesting {server_id}:")
        
        # First try a direct HTTP test
        try:
            test_url = f"https://server.smithery.ai/{server_id}/health"
            resp = requests.get(test_url, headers=headers)
            if resp.status_code == 200:
                print(f"✅ {server_id} health check passed")
                working_servers.append(server_id)
            else:
                print(f"❌ {server_id} health check failed: HTTP {resp.status_code}")
        except Exception as e:
            print(f"❌ Error testing {server_id}: {str(e)}")
    
    if working_servers:
        print("\n===== WORKING SERVERS =====\n")
        for i, server in enumerate(working_servers, 1):
            print(f"{i}. mcp:@{server}")
        print("\nUse these server names in your MCP requests")
    else:
        print("\n❌ No working servers found. Check your API key and network connection.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
