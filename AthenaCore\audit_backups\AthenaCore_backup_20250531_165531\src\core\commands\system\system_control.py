# src/core/commands/system/system_control.py

import os
import platform
import subprocess
import webbrowser
import logging
from typing import Dict, List, Any
from ..base_command import BaseCommand

logger = logging.getLogger('athena.commands.system')

class SystemControl(BaseCommand):
    """Command module to handle system operations like opening URLs and directories."""

    def __init__(self):
        """Initialize the system command module."""
        self._name = "system"
        self._description = "Control system operations like opening files, URLs, and directories"
        self._os_type = platform.system().lower()
        logger.info(f"SystemControl initialized on {self._os_type} platform")

    @property
    def name(self) -> str:
        return self._name

    @property
    def description(self) -> str:
        return self._description

    @property
    def supported_actions(self) -> List[str]:
        return ["open", "browse", "start", "explorer", "dir"]

    def execute(self, action: str, params: Dict[str, Any] = None) -> str:
        """Execute a system command action.

        Args:
            action: The action to perform (open, browse, start, etc.)
            params: Dictionary of parameters for the action

        Returns:
            Output message from the command execution
        """
        if params is None:
            params = {}

        # Get positional arguments if provided
        args = params.get("args", [])
        target = " ".join(args) if args else ""

        # Handle different actions with appropriate methods
        if action.lower() in ["open", "browse"] and target.startswith(("http://", "https://")):
            return self._open_url(target)
        elif action.lower() in ["open", "explorer", "dir"] and self._is_directory(target):
            return self._open_directory(target)
        elif action.lower() in ["open", "start"] and self._is_file(target):
            return self._open_file(target)
        elif action.lower() == "start" and target:
            # Try to start an application by name
            return self._start_application(target)
        else:
            # Try intelligent handling based on the target if action is open
            if action.lower() == "open" and target:
                # Known websites (add common URLs)
                known_websites = {
                    "youtube": "https://youtube.com",
                    "google": "https://google.com",
                    "gmail": "https://mail.google.com",
                    "github": "https://github.com",
                    "twitter": "https://twitter.com",
                    "x": "https://twitter.com",
                    "amazon": "https://amazon.com",
                    "facebook": "https://facebook.com",
                    "linkedin": "https://linkedin.com",
                }
                
                # Check if target is a known website
                if target.lower() in known_websites:
                    return self._open_url(known_websites[target.lower()])
                
                # Check if it's a path
                if os.path.exists(target):
                    if os.path.isdir(target):
                        return self._open_directory(target)
                    elif os.path.isfile(target):
                        return self._open_file(target)
                
                # Assume it's an application or a URL without protocol
                if ".".lower() in target.lower() and not target.lower().startswith("http"):
                    # Might be a domain, add http://
                    return self._open_url(f"http://{target}")
                else:
                    # Try to start as an application
                    return self._start_application(target)
                    
            return f"Unsupported action '{action}' or invalid target '{target}'. Try 'open <website>' or 'open <directory>'."

    def _open_url(self, url: str) -> str:
        """Open a URL in the default browser."""
        try:
            webbrowser.open(url)
            return f"Opened URL: {url}"
        except Exception as e:
            logger.error(f"Error opening URL {url}: {str(e)}")
            return f"Failed to open URL: {url}. Error: {str(e)}"

    def _open_directory(self, path: str) -> str:
        """Open a directory in the file explorer."""
        try:
            # Handle different OS platforms
            if self._os_type == "windows":
                os.startfile(path)
            elif self._os_type == "darwin":  # macOS
                subprocess.call(["open", path])
            else:  # Linux and others
                subprocess.call(["xdg-open", path])
            return f"Opened directory: {path}"
        except Exception as e:
            logger.error(f"Error opening directory {path}: {str(e)}")
            return f"Failed to open directory: {path}. Error: {str(e)}"

    def _open_file(self, path: str) -> str:
        """Open a file with its default application."""
        try:
            # Handle different OS platforms
            if self._os_type == "windows":
                os.startfile(path)
            elif self._os_type == "darwin":  # macOS
                subprocess.call(["open", path])
            else:  # Linux and others
                subprocess.call(["xdg-open", path])
            return f"Opened file: {path}"
        except Exception as e:
            logger.error(f"Error opening file {path}: {str(e)}")
            return f"Failed to open file: {path}. Error: {str(e)}"

    def _start_application(self, app_name: str) -> str:
        """Start an application by name."""
        try:
            # Handle different OS platforms
            if self._os_type == "windows":
                subprocess.Popen(["cmd", "/c", "start", "", app_name])
            elif self._os_type == "darwin":  # macOS
                subprocess.call(["open", "-a", app_name])
            else:  # Linux and others
                subprocess.Popen([app_name], shell=True)
            return f"Started application: {app_name}"
        except Exception as e:
            logger.error(f"Error starting application {app_name}: {str(e)}")
            return f"Failed to start application: {app_name}. Error: {str(e)}"

    def _is_directory(self, path: str) -> bool:
        """Check if the given path is a directory."""
        # Handle special case for 'root directory'
        if path.lower() == "root directory":
            # Use the application root directory
            return True
        return os.path.isdir(path) if path else False

    def _is_file(self, path: str) -> bool:
        """Check if the given path is a file."""
        return os.path.isfile(path) if path else False
