# ────────────────────────────────────────────────────────────
#  src/core/knowledge_db.py      build 2025-04-23 c
# ────────────────────────────────────────────────────────────
"""
Athena Knowledge-Base interface backed by Chroma.
Compatible with Chroma ≥ 0.5 (local PersistentClient or remote HttpClient).
"""

from __future__ import annotations

import logging
import uuid
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Union, BinaryIO
import json

import chromadb
from chromadb.utils.embedding_functions import (
    OpenAIEmbeddingFunction,
    SentenceTransformerEmbeddingFunction,
)
from logging.handlers import RotatingFileHandler

from src.utils.config import AthenaConfig
import numpy as np
from sentence_transformers import Sen<PERSON>ceTransformer
from transformers import BlipProcessor, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>itionalGeneration, AutoTokenizer
from src.utils.document_processor import partition_pdf_multimodal

# Track chunking/indexing progress per document
from typing import Dict, Tuple
processing_progress: Dict[str, int] = {}

# Add imports for CLIP
import torch
from PIL import Image
from transformers import CLIPProcessor, CLIPModel

# ╭──────────────────── logger helper ─────────────────────╮
def _setup_logger() -> logging.Logger:
    lg = logging.getLogger("AthenaKnowledgeDB")
    lg.setLevel(logging.DEBUG)
    if not lg.handlers:
        log_dir = Path(__file__).resolve().parents[2] / "logs"
        log_dir.mkdir(parents=True, exist_ok=True)
        fh = RotatingFileHandler(
            log_dir / "athena_knowledge_db.log",
            maxBytes=5_242_880,
            backupCount=5,
            encoding="utf-8",
        )
        fh.setFormatter(logging.Formatter("%(asctime)s [%(levelname)s] %(message)s"))
        fh.setLevel(logging.DEBUG)
        lg.addHandler(fh)
        # Also log to console for debug visibility
        console = logging.StreamHandler()
        console.setLevel(logging.DEBUG)
        console.setFormatter(logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s"))
        lg.addHandler(console)
        lg.propagate = False
    return lg


# ╭──────────────────────── main class ─────────────────────╮
class KnowledgeDatabase:
    """Persistent knowledge-base wrapper around a Chroma collection."""

    # ────────────────────────────────────────────────────────
    # initialisation
    # ────────────────────────────────────────────────────────
    def __init__(self) -> None:
        self.logger = _setup_logger()
        cfg = AthenaConfig.load()
        
        # Default to memory-only mode until we confirm persistence works
        self.is_persistent = False

        # Normalize the persistence directory path
        persist_dir = (
            Path(cfg.CHROMA_PERSIST_DIR).expanduser()
            if Path(cfg.CHROMA_PERSIST_DIR).is_absolute()
            else Path(__file__).resolve().parents[2] / cfg.CHROMA_PERSIST_DIR
        )
        
        # Ensure the directory exists
        try:
            persist_dir.mkdir(parents=True, exist_ok=True)
            self.persist_dir = str(persist_dir)
            self.logger.info(f"Chroma persist dir -> {self.persist_dir}")
        except Exception as e:
            self.logger.error(f"Failed to create persist directory: {e}")
            self.persist_dir = str(persist_dir)
            self.logger.warning(f"Using directory without confirming it's writable: {self.persist_dir}")

        try:
            # Determine client type and initialize appropriately
            if cfg.CHROMA_API_IMPL == "rest":
                # Remote server mode
                self.logger.info("Initializing ChromaDB HttpClient")
                self.client = chromadb.HttpClient(
                    host=cfg.CHROMA_SERVER_HOST,
                    port=int(cfg.CHROMA_SERVER_HTTP_PORT),
                )
                # Remote server is persistent by design
                self.is_persistent = True
                self.logger.info(f"Using ChromaDB REST API at {cfg.CHROMA_SERVER_HOST}:{cfg.CHROMA_SERVER_HTTP_PORT}")
            else:
                # Local persistent mode
                self.logger.info(f"Initializing ChromaDB PersistentClient at {self.persist_dir}")
                # Ensure directory exists and is writable
                try:
                    Path(self.persist_dir).mkdir(parents=True, exist_ok=True)
                    # Test file creation to confirm write access
                    test_file = Path(self.persist_dir) / "write_test"
                    test_file.write_text("test")
                    test_file.unlink()  # Remove test file
                    self.logger.info("Successfully verified write access to persist directory")
                except Exception as write_err:
                    self.logger.error(f"Directory permissions error: {write_err}")
                    raise
                    
                # Create the persistent client
                self.client = chromadb.PersistentClient(path=self.persist_dir)
                self.is_persistent = True
                self.logger.info(f"Initialized PersistentClient at {self.persist_dir}")
        except Exception as e:
            self.logger.error(f"ChromaDB client initialization failed: {e}")
            self.logger.error(traceback.format_exc())
            self.logger.warning("Falling back to in-memory Chroma client (no persistence).")
            self.client = chromadb.Client()
            self.is_persistent = False
            self.logger.warning("Using in-memory ChromaDB client - data will NOT be persisted between restarts!")

        self.embedding_function = self._select_embeddings(cfg)
        
        # Initialize tokenizer only for non-OpenAI models
        is_openai_model = cfg.EMBEDDING_MODEL and cfg.EMBEDDING_MODEL.startswith(("text-embedding-", "gpt-"))
        if is_openai_model:
            self.logger.info(f"Skipping tokenizer load for OpenAI model {cfg.EMBEDDING_MODEL}")
            self.tokenizer = None
        else:
            # Load tokenizer for embedding model for exact token-based chunking
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(cfg.EMBEDDING_MODEL, use_fast=True)
                self.logger.info(f"Loaded tokenizer for {cfg.EMBEDDING_MODEL}")
            except Exception as e:
                self.tokenizer = None
                self.logger.warning(f"Tokenizer load failed: {e}")
                
        # --- Load CLIP Model ---
        try:
            clip_model_name = cfg.CLIP_MODEL_NAME
            self.logger.info(f"Loading CLIP model: {clip_model_name}")
            self.clip_processor = CLIPProcessor.from_pretrained(clip_model_name)
            self.clip_model = CLIPModel.from_pretrained(clip_model_name)
            self.logger.info(f"Successfully loaded CLIP model and processor: {clip_model_name}")
        except Exception as e:
            self.logger.error(f"Failed to load CLIP model '{cfg.CLIP_MODEL_NAME}': {e}")
            self.logger.error(traceback.format_exc())
            self.clip_processor = None
            self.clip_model = None
            # Consider raising an error or providing a non-functional fallback
            self.logger.warning("CLIP model loading failed. Image embedding will not be available.")

        self.collection = self._get_collection()

    # ╭───────────────────────────────────────────────────────
    # internal helpers
    # ────────────────────────────────────────────────────────
    def _select_embeddings(self, cfg):
        model_name = cfg.EMBEDDING_MODEL
        self.logger.info(f"Selecting embedding function based on model: {model_name}")

        # Explicitly check if it's an OpenAI model (common pattern)
        is_openai_model = model_name and model_name.startswith(("text-embedding-", "gpt-"))
        self.logger.debug(f"Model appears to be OpenAI model: {is_openai_model}")

        # Multi-layered approach to getting the OpenAI API key
        openai_api_key = None
        
        # 1. Check if the config already has the key from AthenaConfig.load()
        if cfg.openai_api_key:
            openai_api_key = cfg.openai_api_key
            self.logger.debug(f"Using OpenAI API key from AthenaConfig")
        
        # 2. If not found, try direct environment variable as fallback
        if not openai_api_key:
            import os
            env_api_key = os.environ.get("OPENAI_API_KEY")
            if env_api_key:
                openai_api_key = env_api_key
                self.logger.debug(f"Using OpenAI API key from environment variable")
        
        # 3. As a last resort, attempt direct database access
        if not openai_api_key and is_openai_model:
            try:
                # Try direct SQLite access without Flask
                import sqlite3
                from pathlib import Path
                
                # Find the database file
                db_path = Path(__file__).resolve().parents[2] / "athena.db"
                if db_path.exists():
                    conn = sqlite3.connect(str(db_path))
                    cursor = conn.cursor()
                    cursor.execute("SELECT value FROM config_entries WHERE key=? AND user_id IS NULL", ("openai_api_key",))
                    result = cursor.fetchone()
                    if result and result[0]:
                        openai_api_key = result[0]
                        self.logger.info("Retrieved OpenAI API key directly from SQLite database")
                    conn.close()
            except Exception as e:
                self.logger.debug(f"Direct SQLite access for API key failed: {e}")
        
        self.logger.debug(f"OpenAI API key available: {bool(openai_api_key)}")
        
        # Set the key in the environment for OpenAI libraries that use it directly
        if openai_api_key:
            import os
            os.environ["OPENAI_API_KEY"] = openai_api_key
        
        # --- Try OpenAI first if API key exists AND model suggests it --- #
        if openai_api_key and is_openai_model:
            try:
                openai_model = model_name
                self.logger.info(f"Attempting to use OpenAIEmbeddingFunction with model: {openai_model}")
                embedding_fn = OpenAIEmbeddingFunction(
                    api_key=openai_api_key, model_name=openai_model
                )
                self.logger.info(f"Successfully initialized OpenAIEmbeddingFunction")
                return embedding_fn
            except Exception as e:
                self.logger.error(f"Failed to initialize OpenAIEmbeddingFunction: {e}")
                self.logger.error(traceback.format_exc())
                # Fall through to Sentence Transformers if OpenAI fails
        elif is_openai_model:
            self.logger.warning(f"OpenAI model {model_name} specified but no API key provided. Falling back to SentenceTransformer.")
        
        # --- Try SentenceTransformer as fallback --- #
        try:
            import sentence_transformers  # noqa: F401
            # Use the model name from config if it's not an OpenAI model
            st_model_name = model_name if not is_openai_model else "all-MiniLM-L6-v2"
            self.logger.info(f"Using SentenceTransformerEmbeddingFunction with model: {st_model_name}")
            return SentenceTransformerEmbeddingFunction(model_name=st_model_name)
        except ImportError:
            self.logger.warning("sentence-transformers library not found. pip install sentence-transformers")
            # Fall through to default
        except Exception as e:
            self.logger.error(f"Failed to initialize SentenceTransformerEmbeddingFunction: {e}")
            self.logger.error(traceback.format_exc())
            # Fall through to default

        # Fallback to Chroma's default
        try:
            from chromadb.utils import embedding_functions
            self.logger.warning("Falling back to Chroma's DefaultEmbeddingFunction (likely Sentence Transformers based).")
            return embedding_functions.DefaultEmbeddingFunction()
        except Exception as e:
            self.logger.critical(f"Failed to initialize ANY embedding function: {e}")
            self.logger.critical(traceback.format_exc())
            raise RuntimeError("Could not initialize any embedding function.") from e

    def _get_collection(self):
        name = "athena_knowledge_base"
        meta = {
            "description": "Athena knowledge base",
            "created_at": datetime.utcnow().isoformat(),
            "hnsw:space": "cosine",
        }
        self.logger.info(f"Getting or creating collection: {name}")
        try:
            # Explicit version check for ChromaDB
            chromadb_version = getattr(chromadb, "__version__", "unknown")
            self.logger.info(f"ChromaDB version: {chromadb_version}")
            
            # First check if the collection exists
            try:
                collections = self.client.list_collections()
                collection_exists = any(col.name == name for col in collections)
                
                if collection_exists:
                    # Try to get the existing collection
                    try:
                        collection = self.client.get_collection(name=name)
                        self.logger.info(f"Found existing collection '{name}'")
                        
                        # Try to add a document to test if embedding functions match
                        try:
                            # A simple test to see if the embedding function works with the collection
                            test_id = f"test_{uuid.uuid4()}"
                            collection.add(
                                ids=[test_id],
                                documents=["Test document for embedding function compatibility check"],
                                metadatas=[{"test": True}]
                            )
                            # If successful, delete the test document and continue
                            collection.delete(ids=[test_id])
                            self.logger.info("Existing collection compatible with current embedding function")
                            return collection
                        except ValueError as e:
                            if "Embedding function name mismatch" in str(e):
                                self.logger.warning(f"Embedding function mismatch detected: {e}")
                                self.logger.warning("Deleting and recreating collection with new embedding function")
                                # Delete the collection and recreate
                                self.client.delete_collection(name=name)
                                # Fall through to create new collection
                            else:
                                # Some other ValueError
                                raise
                    except Exception as e:
                        self.logger.error(f"Error accessing collection: {e}")
                        # Try to recreate
                
                # Create new collection
                try:  # ≤0.4 signature
                    self.logger.info(f"Attempting to create collection '{name}' using ≤0.4 API")
                    collection = self.client.get_or_create_collection(
                        name=name,
                        metadata=meta,
                        embedding_function=self.embedding_function,
                    )
                    self.logger.info("Using ChromaDB ≤0.4 API successfully")
                    return collection
                except TypeError:  # ≥0.5 signature
                    self.logger.info(f"Creating collection '{name}' using ≥0.5 API")
                    col = self.client.get_or_create_collection(name=name, metadata=meta)
                    self.logger.info("Modifying collection to add embedding function")
                    col.modify(embedding_function=self.embedding_function)
                    return col
                
            except Exception as e:
                self.logger.error(f"Error in collection operations: {e}")
                raise
            
        except Exception as e:  # pragma: no cover
            self.logger.critical(f"Collection init failed: {e}")
            self.logger.critical(traceback.format_exc())
            return None

    def _persist(self):
        # For ChromaDB version < 0.4.6, explicit persist() is needed
        # For >= 0.4.6 with PersistentClient, persistence is automatic
        if not self.is_persistent:
            self.logger.warning("Not persisting data - running in memory-only mode")
            return
            
        if hasattr(self.client, "persist"):
            try:
                self.client.persist()
                self.logger.debug("Explicitly called persist() on ChromaDB client")
            except Exception as e:
                self.logger.warning(f"Failed to persist ChromaDB: {e}")
                self.logger.warning(traceback.format_exc())

    @staticmethod
    def _parse_tags(val):
        if isinstance(val, list):
            return val
        if isinstance(val, str):
            try:
                return json.loads(val)
            except Exception:
                return [v.strip() for v in val.split(',') if v.strip()]
        return []

    @staticmethod
    def _serialize_metadata_value(val):
        """Ensure metadata values are JSON serializable primitives or strings."""
        if isinstance(val, list):
            try:
                return json.dumps(val)
            except TypeError:
                # Fallback for lists containing non-serializable items
                return json.dumps([str(item) for item in val])
        elif isinstance(val, dict): # Handle dicts similarly to lists
             try:
                return json.dumps(val)
             except TypeError:
                return json.dumps({str(k): str(v) for k, v in val.items()})
        elif not isinstance(val, (str, int, float, bool)) or val is None:
            # Convert other non-primitive types to string
            return str(val)
        return val # Return primitives as is

    def _chunk_content(self, content: str, doc_id: str, model_max_length: int = 512, overlap: int = 100) -> List[str]:
        """Split text content into overlapping chunks suitable for embedding.
        
        Args:
            content: Text content to chunk
            model_max_length: Maximum tokens per chunk (default: 512)
            overlap: Number of tokens to overlap between chunks (default: 100)
            
        Returns:
            List of text chunks
        """
        self.logger.debug(f"Chunking document {doc_id[:8]}... with length {len(content)} chars")
        chunks = []
        
        # Skip token-based chunking for very long content - it will just fail with the sequence length error
        # A conservative estimate: assume 4 chars per token on average
        estimated_tokens = len(content) // 4
        
        self.logger.debug(f"Estimated tokens for document: {estimated_tokens}, tokenizer available: {self.tokenizer is not None}")
        
        # Only use token-based chunking if content likely fits in tokenizer's capabilities
        use_token_chunking = estimated_tokens < model_max_length * 100 and self.tokenizer
        self.logger.debug(f"Decision to use token-based chunking: {use_token_chunking}")

        if use_token_chunking:
            self.logger.debug(f"Attempting token-based chunking for doc {doc_id[:8]}...")
            # First try token-based chunking with the loaded tokenizer
            try:
                # Don't encode the entire content at once - do it in reasonable segments
                # Break content into manageable sections first
                section_size = model_max_length * 50  # Increased section size for fewer logs
                sections = []
                
                for i in range(0, len(content), section_size):
                    section = content[i:i + section_size]
                    sections.append(section)
                
                self.logger.debug(f"Processing document in {len(sections)} sections for tokenization")
                
                # Process each section
                for section_idx, section in enumerate(sections):
                    try:
                        self.logger.debug(f"Processing section {section_idx+1}/{len(sections)}, length: {len(section)} chars")
                        section_start_time = datetime.utcnow()
                        
                        # Further split sections into subsections to avoid token length errors
                        est_section_tokens = len(section) // 4
                        num_subsections = max(1, est_section_tokens // (model_max_length - 100))
                        subsection_size = len(section) // num_subsections
                        
                        self.logger.debug(f"Section {section_idx+1}: Est tokens: {est_section_tokens}, num_subsections: {num_subsections}, subsection_size: {subsection_size}")
                        
                        subsection_chunks = []
                        for j in range(0, len(section), subsection_size):
                            subsection = section[j:j + subsection_size]
                            subsection_idx = j // subsection_size + 1
                            
                            # Safety check - if subsection is still too long, use character-based approach
                            if len(subsection) // 4 > model_max_length:
                                self.logger.warning(f"Section {section_idx+1}, Subsection {subsection_idx}: Still too large ({len(subsection)} chars), adding directly.")
                                subsection_chunks.append(subsection)
                                continue
                                
                            # Tokenize just this subsection
                            try:
                                #self.logger.debug(f"Tokenizing subsection {subsection_idx}, length: {len(subsection)}")
                                tokenized = self.tokenizer.encode(subsection, add_special_tokens=False)
                                #self.logger.debug(f"Tokenized subsection {subsection_idx}, length: {len(tokenized)}")
                                
                                # Use model_max_length tokens per chunk
                                start = 0
                                while start < len(tokenized):
                                    end = min(start + model_max_length, len(tokenized))
                                    chunk_tokens = tokenized[start:end]
                                    chunk_text = self.tokenizer.decode(chunk_tokens, skip_special_tokens=True)
                                    subsection_chunks.append(chunk_text)
                                    start = end - overlap
                                
                            except Exception as e:
                                self.logger.warning(f"Tokenization/Decoding failed for subsection {subsection_idx}: {str(e)}")
                                self.logger.warning(traceback.format_exc())
                                subsection_chunks.append(subsection) # Fallback: use raw subsection
                        
                        chunks.extend(subsection_chunks)
                        section_end_time = datetime.utcnow()
                        elapsed_ms = (section_end_time - section_start_time).total_seconds() * 1000
                        self.logger.debug(f"Finished processing section {section_idx+1} in {elapsed_ms:.1f}ms, added {len(subsection_chunks)} chunks.")
                            
                    except Exception as e:
                        self.logger.warning(f"Processing failed for section {section_idx+1}: {str(e)}")
                        self.logger.warning(traceback.format_exc())
                        # Fallback: Split the failed section into smaller chunks by character
                        self.logger.debug(f"Falling back to character splitting for section {section_idx+1}")
                        for j in range(0, len(section), 2000):
                            chunk = section[j:j + 2000]
                            chunks.append(chunk)
                
                if chunks:
                    self.logger.info(f"Token-based chunking for {doc_id[:8]}... resulted in {len(chunks)} chunks.")
                
            except Exception as e:
                self.logger.warning(f"Token-based chunking completely failed: {str(e)}")
                self.logger.warning(traceback.format_exc())
                chunks = []  # Reset chunks for fallback
        else:
            self.logger.info(f"Content too long ({len(content)} chars, ~{estimated_tokens} tokens) or no tokenizer. Using character-based approach.")
            
        # If token-based chunking failed or yielded nothing or was skipped, fall back to character-based
        if not chunks:
            self.logger.info(f"Using character-based chunking for {doc_id[:8]}...")
            
            try:
                from langchain.text_splitter import RecursiveCharacterTextSplitter
                
                self.logger.debug("Using langchain's RecursiveCharacterTextSplitter")
                # Use langchain's more sophisticated text splitter if available
                text_splitter = RecursiveCharacterTextSplitter(
                    chunk_size=2000,  # ~500 words
                    chunk_overlap=200, # Decent overlap
                    length_function=len,
                    separators=["\n\n", "\n", ". ", " ", ""]
                )
                start_time = datetime.utcnow()
                chunks = text_splitter.split_text(content)
                end_time = datetime.utcnow()
                elapsed_ms = (end_time - start_time).total_seconds() * 1000
                self.logger.info(f"Character-based chunking (langchain) for {doc_id[:8]}... completed in {elapsed_ms:.1f}ms, resulted in {len(chunks)} chunks.")
            
            except ImportError:
                self.logger.warning("Langchain not available, using basic character chunking")
                
                # Basic fallback if langchain isn't available - improved to respect word boundaries
                words = content.split()
                self.logger.debug(f"Word-based chunking with {len(words)} words")
                
                current_chunk = []
                current_length = 0
                max_chars = 2000  # ~500 words
                chars_overlap = 200  # Overlap
                
                for word in words:
                    # Add word to current chunk
                    current_chunk.append(word)
                    current_length += len(word) + 1  # +1 for the space
                    
                    # Check if current chunk is long enough
                    if current_length >= max_chars:
                        # Join the words together with spaces and add to chunks
                        chunks.append(" ".join(current_chunk))
                        
                        # Create overlap: keep last N characters worth of words for the next chunk
                        # Find the index roughly chars_overlap characters from the end
                        overlap_length = 0
                        overlap_index = len(current_chunk)
                        
                        while overlap_index > 0 and overlap_length < chars_overlap:
                            overlap_index -= 1
                            overlap_length += len(current_chunk[overlap_index]) + 1
                        
                        # Reset with overlapping words
                        current_chunk = current_chunk[overlap_index:]
                        current_length = sum(len(word) + 1 for word in current_chunk)
                
                # Don't forget the last chunk if it's not empty
                if current_chunk:
                    chunks.append(" ".join(current_chunk))
                
                self.logger.info(f"Character-based chunking (basic) for {doc_id[:8]}... resulted in {len(chunks)} chunks.")
        
        # Ensure no empty chunks
        chunks = [chunk for chunk in chunks if chunk.strip()]
        
        self.logger.info(f"Final chunk count for {doc_id[:8]}...: {len(chunks)}")
        return chunks

    # ────────────────────────────────────────────────────────
    # CRUD API
    # ────────────────────────────────────────────────────────
    # add ----------------------------------------------------
    def add_document(
        self, content: str, metadata: Dict, doc_id: Optional[str] = None
    ) -> Optional[str]: # Return Optional[str] to indicate potential failure
        if not self.collection:
            self.logger.error("Cannot add document: collection is not initialized")
            return None
            
        # Ensure metadata is a dictionary, not a string
        if not isinstance(metadata, dict):
            self.logger.warning(f"Received non-dict metadata of type {type(metadata).__name__}. Converting to dictionary.")
            try:
                # If it's a JSON string, try to parse it
                if isinstance(metadata, str):
                    try:
                        metadata = json.loads(metadata)
                    except json.JSONDecodeError:
                        # If not valid JSON, create a basic dict
                        metadata = {"content": str(metadata)}
                else:
                    # For other types, create a basic dict
                    metadata = {"content": str(metadata)}
            except Exception as e:
                self.logger.error(f"Error converting metadata to dictionary: {e}")
                metadata = {}
        
        # Ensure we have a timestamp and doc_id
        metadata.setdefault("timestamp", datetime.utcnow().isoformat())
        doc_id = doc_id or str(uuid.uuid4())
        # initialize progress tracking
        processing_progress[doc_id] = 0

        # Log attempt details
        title = metadata.get('title', 'Untitled')
        self.logger.info(f"Attempting to add document '{title}' with ID {doc_id}")
        self.logger.info(f"DB State - is_persistent: {self.is_persistent}, client_type: {type(self.client).__name__}")
        self.logger.debug(f"Original Metadata: {metadata}")

        # Process metadata: ensure all values are serializable primitives/strings
        processed_meta = {}
        for key, val in metadata.items():
            processed_meta[key] = self._serialize_metadata_value(val)
        self.logger.debug(f"Processed Metadata: {processed_meta}")

        # Ensure original_id is present before chunking
        processed_meta["original_id"] = doc_id

        # Chunk the content using the helper method
        model_max_length = getattr(self.tokenizer, 'model_max_length', 512) if self.tokenizer else 512
        chunk_overlap = 100 # Standard overlap
        chunks = self._chunk_content(content, doc_id, model_max_length=model_max_length, overlap=chunk_overlap)

        total_chunks = len(chunks)
        if total_chunks == 0:
            self.logger.warning(f"No chunks generated for document {doc_id} ('{title}'). Adding document as a single chunk.")
            # Treat the whole content as one chunk if splitting failed or content is short
            if content:
               chunks = [content]
               total_chunks = 1
            else:
                self.logger.error(f"Document {doc_id} ('{title}') has no content and could not be chunked. Skipping add.")
                del processing_progress[doc_id] # Clean up progress
                return None # Indicate failure

        chunk_ids = []
        chunk_metadatas = []
        chunk_documents = []

        for i, chunk_text in enumerate(chunks):
            # Skip empty chunks that might result from splitting
            if not chunk_text or chunk_text.isspace():
                self.logger.warning(f"Skipping empty chunk {i} for document {doc_id}")
                total_chunks -= 1 # Adjust total count
                continue
            
            chunk_id = f"{doc_id}_chunk_{i}"
            chunk_meta = processed_meta.copy() # Start with original doc metadata
            chunk_meta["chunk_index"] = i
            # Update total_chunks in metadata in case some were skipped
            chunk_meta["total_chunks"] = total_chunks
            # doc_id in chunk_meta already set via original_id

            chunk_ids.append(chunk_id)
            chunk_metadatas.append(chunk_meta)
            # Store the raw chunk text, don't append title here
            chunk_documents.append(chunk_text)

            # --- Add Logging START ---
            if i < 3:
                self.logger.debug(f"Chunk {i} content before DB add (first 300 chars): {chunk_text[:300]}")
            # --- Add Logging END ---

            # Update progress (simple percentage)
            progress = int((i + 1) / len(chunks) * 100)
            processing_progress[doc_id] = progress

        # Ensure we actually have chunks to add
        if not chunk_documents:
            self.logger.error(f"No valid chunks generated or all chunks were empty for document {doc_id} ('{title}'). Skipping add.")
            if doc_id in processing_progress: del processing_progress[doc_id]
            return None

        try:
            self.logger.info(f"Adding {len(chunk_documents)} chunks to collection for doc {doc_id} ('{title}')")
            # Add all chunks in one batch
            self.collection.add(
                ids=chunk_ids,
                documents=chunk_documents,
                metadatas=chunk_metadatas
            )
            # Persist once after adding all chunks for the document
            self._persist()
            self.logger.info(f"Successfully added and potentially persisted {len(chunk_documents)} chunks for {doc_id}")
            # Clear progress after successful addition
            if doc_id in processing_progress: del processing_progress[doc_id]
            return doc_id
        except Exception as e:
            self.logger.error(f"Failed to add batch of chunks for document {doc_id}: {e}")
            self.logger.error(traceback.format_exc())
            # Clear progress on error
            if doc_id in processing_progress: del processing_progress[doc_id]
            return None # Indicate failure

    # delete -------------------------------------------------
    def delete_document(self, doc_id: str) -> bool:
        if not self.collection:
            return False
        try:
            # Fetch all chunk IDs for this document
            res = self.collection.get(where={"original_id": doc_id})
            ids = res.get("ids", [])
            # Include the doc_id itself in case it's unchunked
            if doc_id not in ids:
                ids.append(doc_id)
            # Delete all related entries
            self.collection.delete(ids=ids)
            self._persist()
            self.logger.info(f"KB -1 {doc_id}")
            return True
        except Exception as e:
            self.logger.error(f"delete_document failed: {e}")
            return False

    # update status -----------------------------------------
    def update_document_status(self, doc_id: str, new_status: str) -> bool:
        if not self.collection:
            return False
        try:
            res = self.collection.get(ids=[doc_id])
            if not res or not res["documents"]:
                return False
            meta = res["metadatas"][0]
            meta["status"] = new_status
            self.collection.delete(ids=[doc_id])
            self.collection.add(
                documents=[res["documents"][0]], metadatas=[meta], ids=[doc_id]
            )
            self._persist()
            self.logger.info(f"KB status -> {doc_id}:{new_status}")
            return True
        except Exception as e:
            self.logger.error(f"update_document_status failed: {e}")
            return False

    # list / search -----------------------------------------
    def search_documents(self, filters: Optional[Dict] = None) -> List[Dict]:
        if self.collection is None:
            return []
        try:
            # Fetch all fields via default get()
            res = self.collection.get(where=filters) if filters else self.collection.get()
            ids = res.get("ids", [])
            documents = res.get("documents", [])
            metadatas = res.get("metadatas", [])
            if not ids:
                self.logger.warning("No documents found in ChromaDB search")
                return []
            # Group chunk entries by original document
            groups: Dict[str, Dict] = {}
            for doc_id, doc, meta in zip(ids, documents, metadatas):
                orig = meta.get("original_id", doc_id)
                if orig not in groups:
                    groups[orig] = {
                        "id": orig,
                        "title": meta.get("title", "Untitled"),
                        "type": meta.get("type", "text"),
                        "source": meta.get("source", "manual"),
                        "tags": self._parse_tags(meta.get("tags", "")),
                        "timestamp": meta.get("timestamp", ""),
                        "chunk_count": 0
                    }
                groups[orig]["chunk_count"] += 1
            return list(groups.values())
        except Exception as e:
            self.logger.error(f"search_documents failed: {e}")
            self.logger.debug(traceback.format_exc())
            return []

    # semantic search for documents -----------------------------
    def get_documents_from_search(self, search_term: str, limit: int = 5, filters: Optional[Dict] = None) -> List[Dict]:
        """
        Search for documents using semantic search with the given term.
        
        Args:
            search_term: The search term to use
            limit: Maximum number of results to return
            filters: Optional filters to apply
            
        Returns:
            List of matching documents with metadata
        """
        if not self.collection:
            self.logger.warning("No collection available for document search")
            return []
            
        # Debug: log inputs
        self.logger.debug(f"get_documents_from_search called with search_term={search_term!r}, filters={filters}, limit={limit}")
        try:
            # Fetch metadata to count chunks per document
            try:
                if filters:
                    res_all = self.collection.get(where=filters, include=["metadatas"])
                else:
                    res_all = self.collection.get(include=["metadatas"])
            except Exception as e:
                self.logger.error(f"Error fetching metadata with filters={filters}: {e}")
                # Fallback to unfiltered metadata fetch
                res_all = self.collection.get(include=["metadatas"])
            metas_all = res_all.get("metadatas", [])
            chunk_counts: Dict[str, int] = {}
            for meta in metas_all:
                orig = meta.get("original_id") or meta.get("id")
                # Skip entries without a valid original_id
                if not orig:
                    continue
                chunk_counts[orig] = chunk_counts.get(orig, 0) + 1

            doc_results: List[Dict] = []
            for orig, count in chunk_counts.items():
                # Build where clause: combine filters and original_id under $and for ChromaDB
                if filters:
                    clauses = [{"original_id": orig}]
                    for k, v in filters.items():
                        clauses.append({k: v})
                    where_clause = {"$and": clauses}
                else:
                    where_clause = {"original_id": orig}
                self.logger.debug(f"Querying original_id={orig} with where_clause={where_clause}")
                qres = self.collection.query(query_texts=[search_term], n_results=1, where=where_clause)
                docs_q = qres.get("documents", [[]])[0]
                metas_q = qres.get("metadatas", [[]])[0]
                dists_q = qres.get("distances", [[]])[0]
                if docs_q:
                    snippet = docs_q[0]
                    meta0 = metas_q[0]
                    dist = float(dists_q[0]) if dists_q else None
                    doc_results.append({
                        "id": orig,
                        "title": meta0.get("title", "Untitled"),
                        "content": snippet,
                        "type": meta0.get("type", "text"),
                        "source": meta0.get("source", "manual"),
                        "tags": self._parse_tags(meta0.get("tags", "")),
                        "timestamp": meta0.get("timestamp", ""),
                        "distance": dist,
                        "chunk_count": count,
                    })
            # sort by similarity and return top results
            doc_results.sort(key=lambda x: x["distance"] if x.get("distance") is not None else float("inf"))
            return doc_results[:limit]
        except Exception as e:
            self.logger.error(f"Error in get_documents_from_search: {e}")
            self.logger.error(traceback.format_exc())
            return []

    # semantic query ----------------------------------------
    def query_knowledge(
        self,
        query: str,
        limit: int = 5,
        filters: Optional[Dict] = None,
        threshold: float | None = None,
    ) -> List[Dict]:
        """
        Perform semantic search and return the top N relevant chunks.

        Args:
            query: The search query text.
            limit: Maximum number of chunks to return.
            filters: Optional filters to apply to the search.
            threshold: Optional similarity score threshold (e.g., for cosine distance, lower is better).

        Returns:
            List of dictionaries, each representing a relevant chunk:
            {'id': chunk_id, 'content': chunk_text, 'metadata': chunk_metadata, 'distance': similarity_distance}
        """
        if not self.collection:
            self.logger.warning("query_knowledge called but collection is not initialized.")
            return []
        try:
            # Perform semantic query, explicitly including documents, metadatas, and distances
            self.logger.debug(f"Executing Chroma query: query='{query}', limit={limit}, filters={filters}")
            results = self.collection.query(
                query_texts=[query],
                n_results=limit,
                where=filters,
                include=["documents", "metadatas", "distances"] # Ensure these are included
            )
            self.logger.debug(f"Chroma query raw result: {results}")

            # Process results into a list of chunk dictionaries
            chunk_list = []
            if results and results.get("ids") and results["ids"][0]:
                ids = results["ids"][0]
                documents = results.get("documents", [[]])[0]
                metadatas = results.get("metadatas", [[]])[0]
                distances = results.get("distances", [[]])[0]

                for i, chunk_id in enumerate(ids):
                    distance = distances[i] if distances and i < len(distances) else None

                    # Optional: Apply distance threshold if provided
                    if threshold is not None and distance is not None and distance > threshold:
                        self.logger.debug(f"Chunk {chunk_id} skipped due to distance {distance} > threshold {threshold}")
                        continue

                    chunk_data = {
                        "id": chunk_id,
                        "content": documents[i] if documents and i < len(documents) else None,
                        "metadata": metadatas[i] if metadatas and i < len(metadatas) else None,
                        "distance": distance,
                    }
                    chunk_list.append(chunk_data)
                    self.logger.debug(f"Added chunk to results: id={chunk_id}, distance={distance}")

            # Sort by distance (ascending, lower is better for distance)
            chunk_list.sort(key=lambda x: x.get("distance") if x.get("distance") is not None else float('inf'))

            self.logger.info(f"query_knowledge returning {len(chunk_list)} chunks for query: '{query[:50]}...'")
            return chunk_list

        except Exception as e:
            self.logger.error(f"query_knowledge failed: {e}")
            self.logger.error(traceback.format_exc())
            return []

    # get single --------------------------------------------
    def get_document(self, doc_id: str) -> Optional[Dict]:
        if not self.collection:
            return None
        try:
            # Try direct retrieval
            res = self.collection.get(ids=[doc_id])
            docs = res.get("documents", [])
            metas = res.get("metadatas", [])
            if docs:
                meta = metas[0]
                return {
                    "id": doc_id,
                    "title": meta.get("title", "Untitled"),
                    "content": docs[0],
                    "type": meta.get("type", "text"),
                    "source": meta.get("source", "manual"),
                    "tags": self._parse_tags(meta.get("tags", "")),
                    "timestamp": meta.get("timestamp", ""),
                }
            # Fallback: assemble chunked content
            res = self.collection.get(
                where={"original_id": doc_id}, include=["documents", "metadatas"]
            )
            docs = res.get("documents", [])
            metas = res.get("metadatas", [])
            if docs and metas:
                # sort by chunk_index
                items = list(zip(metas, docs))
                items.sort(key=lambda x: x[0].get("chunk_index", 0))
                content = "".join([doc for _, doc in items])
                meta0 = items[0][0]
                return {
                    "id": doc_id,
                    "title": meta0.get("title", "Untitled"),
                    "content": content,
                    "type": meta0.get("type", "text"),
                    "source": meta0.get("source", "manual"),
                    "tags": self._parse_tags(meta0.get("tags", "")),
                    "timestamp": meta0.get("timestamp", ""),
                }
            return None
        except Exception as e:
            self.logger.error(f"get_document failed: {e}")
            self.logger.error(traceback.format_exc())
            return None

    # full update -------------------------------------------
    def update_document(self, doc_id: str, updates: Dict) -> bool:
        if not self.collection:
            return False
        try:
            res = self.collection.get(ids=[doc_id])
            if not res or not res["documents"]:
                return False
            meta = res["metadatas"][0]
            meta.update(updates)
            self.collection.delete(ids=[doc_id])
            self.collection.add(
                documents=[res["documents"][0]], metadatas=[meta], ids=[doc_id]
            )
            self._persist()
            self.logger.info(f"KB updated {doc_id}")
            return True
        except Exception as e:
            self.logger.error(f"update_document failed: {e}")
            return False

    # count --------------------------------------------------
    def get_document_count(self, filters: Optional[Dict] = None) -> int:
        if not self.collection:
            return 0
        try:
            if filters:
                res = self.collection.get(where=filters, include=["metadatas"])
                return len(res["metadatas"])
            return self.collection.count()
        except Exception as e:
            self.logger.error(f"get_document_count failed: {e}")
            return 0

    # --- Replace the old ingest_pdf_multimodal with add_document_multimodal ---
    def add_document_multimodal(
        self,
        pdf_input: Union[bytes, str, BinaryIO],
        doc_metadata: Dict, # Expects keys like 'source', 'doc_id'
    ) -> bool:
        """
        Processes a PDF, extracts text and images, embeds them separately,
        and stores them in the Chroma collection.

        Args:
            pdf_input: Path to the PDF file, bytes, or a file-like object.
            doc_metadata: Dictionary containing metadata about the document,
                          must include at least 'source' and 'doc_id'.

        Returns:
            True if ingestion was successful, False otherwise.
        """
        if not self.collection:
            self.logger.error("Cannot add multimodal document: Collection not initialized.")
            return False
        if not self.embedding_function:
             self.logger.error("Cannot add multimodal document: Text embedding function not available.")
             return False
        if not self.clip_model or not self.clip_processor:
             self.logger.warning("CLIP model not loaded. Proceeding with text-only ingestion.")
             # Decide if we should proceed without images or return False
             # Proceeding for now, but images won't be added.

        doc_id = doc_metadata.get("doc_id")
        source = doc_metadata.get("source", "unknown") # Use 'unknown' if source is missing

        if not doc_id:
            self.logger.error("Cannot add multimodal document: 'doc_id' missing from metadata.")
            return False

        self.logger.info(f"Starting multimodal processing for doc_id: {doc_id}, source: {source}")

        try:
            # 1. Partition PDF into text and image elements
            text_chunks, image_data = partition_pdf_multimodal(pdf_input)
            self.logger.info(f"Partitioned PDF {doc_id}: Found {len(text_chunks)} text chunks and {len(image_data)} images.")

            if not text_chunks and not image_data:
                 self.logger.warning(f"No text chunks or images extracted from {doc_id} ({source}). Skipping addition.")
                 return False # Or maybe True, if technically successful but empty? False seems better.

            all_ids = []
            all_embeddings = []
            all_metadatas = []
            all_documents = [] # Chroma requires a 'document' field for each entry

            # 2. Process Text Chunks
            if self.embedding_function:
                # Filter out potentially empty chunks just in case
                valid_text_chunks = [tc for tc in text_chunks if tc.get("text")]
                if valid_text_chunks:
                    texts_to_embed = [tc["text"] for tc in valid_text_chunks]
                    try:
                        text_embeddings = self.embedding_function(texts_to_embed)
                        if len(text_embeddings) != len(valid_text_chunks):
                            self.logger.error(f"Embedding function returned {len(text_embeddings)} embeddings for {len(valid_text_chunks)} texts. Mismatch!")
                            # Handle mismatch - maybe skip text embedding? For now, log and continue if possible
                        else:
                            self.logger.info(f"Generated {len(text_embeddings)} text embeddings.")
                            for i, tc in enumerate(valid_text_chunks):
                                page_num = tc["metadata"].get("page", "N/A")
                                block_idx = tc["metadata"].get("block_index", i)
                                chunk_id = f"{doc_id}_text_p{page_num}_b{block_idx}"

                                metadata = {
                                    **doc_metadata, # Merge original first
                                    "doc_id": doc_id,
                                    "source": source,
                                    "type": "text", # Set specific type LAST
                                    "page": page_num,
                                    "block_index": block_idx,
                                }
                                # Ensure all metadata values are serializable
                                metadata = {k: self._serialize_metadata_value(v) for k, v in metadata.items()}


                                all_ids.append(chunk_id)
                                all_documents.append(tc["text"])
                                all_metadatas.append(metadata)
                                all_embeddings.append(text_embeddings[i])

                    except Exception as e:
                        self.logger.error(f"Failed to embed text chunks for {doc_id}: {e}", exc_info=True)
                        # Decide how to handle: skip text? fail ingestion? For now, log and continue.

            # 3. Process Images
            if self.clip_model and self.clip_processor: # Only process if CLIP is available
                image_count = 0
                for i, img_item in enumerate(image_data):
                    img = img_item.get("image")
                    meta = img_item.get("metadata", {})
                    page_num = meta.get("page", "N/A")
                    caption = meta.get("caption")
                    bbox = meta.get("bbox") # List [x0, y0, x1, y1] or None
                    xref = meta.get("xref", i) # Use xref if available, else index
                    img_format = meta.get("format", "unknown")
                    figure_id = f"{doc_id}_img_p{page_num}_f{xref}"

                    if not img:
                        self.logger.warning(f"Skipping image entry {i} for {doc_id}: Image data missing.")
                        continue

                    try:
                        image_embedding = self.embed_image_with_clip(img)
                        if image_embedding:
                            image_count += 1
                            # Use caption as document text, or placeholder if no caption
                            document_text = caption if caption else f"Image on page {page_num} (xref: {xref})"

                            metadata = {
                                **doc_metadata, # Merge original first
                                "doc_id": doc_id,
                                "source": source,
                                "type": "image", # Set specific type LAST
                                "page": page_num,
                                "figure_id": figure_id, # Unique ID for the figure
                                "caption": caption,
                                "bbox": bbox,
                                "format": img_format,
                                "xref": xref,
                            }
                            # Ensure all metadata values are serializable
                            metadata = {k: self._serialize_metadata_value(v) for k, v in metadata.items() if v is not None}


                            all_ids.append(figure_id)
                            all_documents.append(document_text)
                            all_metadatas.append(metadata)
                            all_embeddings.append(image_embedding)
                        else:
                            self.logger.warning(f"Failed to embed image {i} (xref: {xref}) on page {page_num} for doc {doc_id}.")
                    except Exception as e:
                        self.logger.error(f"Error processing image {i} (xref: {xref}) for {doc_id}: {e}", exc_info=True)

                self.logger.info(f"Generated embeddings for {image_count} images.")
            elif image_data:
                 self.logger.warning(f"CLIP model not available, skipping {len(image_data)} images for {doc_id}.")


            # 4. Add all collected data to ChromaDB
            if not all_ids:
                 self.logger.warning(f"No valid text or image chunks with embeddings generated for {doc_id}. Nothing to add.")
                 return False # Indicate nothing was added

            self.logger.info(f"Adding {len(all_ids)} total entries ({len(all_embeddings)} embeddings) to Chroma for doc_id: {doc_id}")
            try:
                self.collection.add(
                    ids=all_ids,
                    embeddings=all_embeddings,
                    metadatas=all_metadatas,
                    documents=all_documents
                )
                self._persist() # Persist changes if applicable
                self.logger.info(f"Successfully added multimodal content for doc_id: {doc_id} to Chroma.")
                return True
            except Exception as e:
                self.logger.error(f"Failed to add multimodal chunks to Chroma for {doc_id}: {e}", exc_info=True)
                # Consider cleanup or rollback? Difficult with Chroma's API.
                return False

        except Exception as e:
            self.logger.error(f"Multimodal ingestion failed for doc_id {doc_id} (source: {source}): {e}", exc_info=True)
            return False

    def query_relevant_chunks(
        self,
        query_texts: List[str] | str,
        n_results: int = 10,
        filters: Optional[Dict] = None,
        include: Optional[List[str]] = ["metadatas", "documents", "distances"]
    ) -> List[Dict]:
        """Query the collection for relevant chunks and return raw results."""
        if not self.collection:
            self.logger.error("Cannot query: collection is not initialized")
            return []

        query_list = [query_texts] if isinstance(query_texts, str) else query_texts
        self.logger.info(f"Querying for {n_results} relevant chunks using query: '{query_list[0][:100]}...' ({len(query_list)} queries)")
        try:
            # Perform semantic query, explicitly including documents, metadatas, and distances
            results = self.collection.query(
                query_texts=query_list,
                n_results=n_results,
                where=filters,
                include=include
            )
            self.logger.info(f"Query returned {len(results.get('ids', [[]])[0]) if results else 0} chunks for the first query.")
            # The result structure for multiple queries is a dict of lists of lists.
            # For a single query, it's a dict of lists.
            # We'll simplify it for the single query case which is most common here.
            if isinstance(query_texts, str) and results:
                # Simplify structure for single query result
                single_results = []
                ids = results.get('ids', [[]])[0]
                distances = results.get('distances', [[]])[0]
                metadatas = results.get('metadatas', [[]])[0]
                documents = results.get('documents', [[]])[0]
                for i, chunk_id in enumerate(ids):
                    single_results.append({
                        "id": chunk_id,
                        "distance": distances[i] if distances else None,
                        "metadata": metadatas[i] if metadatas else None,
                        "document": documents[i] if documents else None,
                    })
                return single_results
            # Return raw results for multiple queries or if structure is unexpected
            return results # Or adapt as needed for multi-query results
        except Exception as e:
            self.logger.error(f"Failed to query knowledge base: {e}")
            self.logger.error(traceback.format_exc())
            return []

    def get_processing_progress(self, doc_id: str) -> Optional[int]:
        """Get the percentage of chunks processed for a given document ID."""
        return processing_progress.get(doc_id)

    # --- Add new method for CLIP embedding ---
    def embed_image_with_clip(self, image: Image.Image) -> Optional[List[float]]:
        """Embeds a PIL image using the loaded CLIP model."""
        if not self.clip_model or not self.clip_processor:
            self.logger.error("CLIP model/processor not loaded. Cannot embed image.")
            return None

        try:
            inputs = self.clip_processor(images=image, return_tensors="pt", padding=True)
            with torch.no_grad():
                image_features = self.clip_model.get_image_features(**inputs)
            
            # Normalize the features (optional but common practice for CLIP)
            # image_features = image_features / image_features.norm(p=2, dim=-1, keepdim=True)

            embedding = image_features.squeeze(0).cpu().numpy().tolist()
            self.logger.debug(f"Successfully embedded image with CLIP. Embedding dim: {len(embedding)}")
            return embedding
        except Exception as e:
            self.logger.error(f"Failed to embed image with CLIP: {e}")
            self.logger.error(traceback.format_exc())
            return None

    def reload_embeddings(self, api_key: str, embedding_model: str) -> None:
        """
        Reload embedding function and tokenizer based on new model and API key.
        """
        self.logger.info(f"Reloading embeddings with model: {embedding_model}")
        # Prepare temporary config
        class TempCfg:
            openai_api_key = api_key
            EMBEDDING_MODEL = embedding_model

        temp_cfg = TempCfg()
        # Re-select embedding function
        self.embedding_function = self._select_embeddings(temp_cfg)
        # Reload tokenizer if not OpenAI model
        is_openai = embedding_model.startswith(("text-embedding-", "gpt-"))
        if is_openai:
            self.tokenizer = None
        else:
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(embedding_model, use_fast=True)
                self.logger.info(f"Loaded tokenizer for {embedding_model}")
            except Exception as e:
                self.logger.warning(f"Tokenizer reload failed: {e}")
                self.tokenizer = None
        self.logger.info("Embeddings and tokenizer reloaded")


# ╭─────────────────── AthenaKnowledgeDB Class ─────────────────────╮
class AthenaKnowledgeDB:
    """Wrapper around KnowledgeDatabase for simplified Athena integration.
    This class provides a simpler interface for the Athena core to query and use the knowledge base.
    """
    _instance = None
    
    def __new__(cls):
        """Singleton pattern to ensure only one instance exists."""
        if cls._instance is None:
            cls._instance = super(AthenaKnowledgeDB, cls).__new__(cls)
            cls._instance.kb = KnowledgeDatabase()
            cls._instance.logger = logging.getLogger("AthenaKnowledgeDB")
        return cls._instance
    
    def query_knowledge(self, query: str, limit: int = 3, filters: Optional[Dict] = None):
        """Query the knowledge base for relevant documents.
        
        Args:
            query (str): The user's query or message
            limit (int, optional): Maximum number of documents to return. Defaults to 3.
            filters (Optional[Dict], optional): Filters to apply. Defaults to None.
        
        Returns:
            List: List of documents with content and metadata
        """
        try:
            self.logger.debug(f"Executing Chroma query: query='{query}', limit={limit}, filters={filters}")
            results = self.kb.query_knowledge(query, limit=limit, filters=filters)
            self.logger.debug(f"Chroma query raw result: {results}")
            
            # Process results into a more usable format
            documents = []
            if results and len(results) > 0:
                for i, result in enumerate(results):
                    if isinstance(result, dict) and 'content' in result:
                        self.logger.debug(f"Added chunk to results: id={result.get('id', 'unknown')}, distance={result.get('distance', 'unknown')}")
                        documents.append(result)
            
            self.logger.info(f"query_knowledge returning {len(documents)} chunks for query: '{query[:40]}...'")
            return documents
        except Exception as e:
            self.logger.error(f"Error in query_knowledge: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return []
    
    def get_document_by_id(self, doc_id: str):
        """Get a complete document by its ID.
        
        Args:
            doc_id (str): The document ID to retrieve
        
        Returns:
            Dict: The document with all its content and metadata
        """
        return self.kb.get_document_by_id(doc_id)
    
    def search_knowledge(self, query: str, limit: int = 5, full_document: bool = False):
        """Search the knowledge base for relevant documents.
        
        Args:
            query (str): The search query
            limit (int, optional): Maximum number of results. Defaults to 5.
            full_document (bool, optional): Whether to include full document. Defaults to False.
        
        Returns:
            List: List of matching documents
        """
        return self.kb.search_knowledge(query, limit=limit, full_document=full_document)


# Instantiate a global KnowledgeDatabase for module-level use
kb = KnowledgeDatabase()


def query_knowledge(query: str, limit: int = 5, filters: Optional[Dict] = None) -> List[Dict]:
    """
    Query the knowledge base for relevant chunks of text that match the query
    
    Args:
        query: Search query text
        limit: Maximum number of chunks to return
        filters: Optional filters to apply to the search
        
    Returns:
        List of chunks with metadata and content, including distance scores
    """
    if not query:
        return []
        
    try:
        # Use the class method to query chunks directly
        return kb.query_knowledge(query, limit=limit, filters=filters)
    except Exception as e:
        logger = logging.getLogger("AthenaKnowledgeDB")
        logger.error(f"Error querying knowledge base chunks: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return []


def search_knowledge(query: str, limit: int = 5, full_document: bool = False, filters: Optional[Dict] = None):
    """
    Search the knowledge base for documents whose metadata (like title or source)
    matches the search term OR whose content chunks match the term via vector search.

    Args:
        search_term (str): Term to search for in metadata or chunk content.
        limit (int): Maximum number of documents to return.
        filters (Optional[Dict]): ChromaDB compatible filters (e.g., {"status": "approved"}).

    Returns:
        List[Dict]: A list of unique documents matching the search, each containing
                  metadata and potentially relevant chunks.
    """
    kb.logger.info(f"Searching documents with term: '{query}', limit: {limit}, filters: {filters}")
    unique_docs = {}

    # 1. Search by metadata (e.g., title, source) - this might be less relevant now
    #    but kept for backward compatibility or specific use cases.
    # metadata_query_filter = {"$or": [{"title": {"$contains": query}},
    #                                 {"source": {"$contains": query}}]}
    # if filters:
    #     combined_filter = {"$and": [metadata_query_filter, filters]}
    # else:
    #     combined_filter = metadata_query_filter

    # try:
    #     metadata_results = kb.collection.get(
    #         where=combined_filter,
    #         include=["metadatas"]
    #     )
    #     if metadata_results and metadata_results.get('ids'):
    #         for doc_id, meta in zip(metadata_results['ids'], metadata_results['metadatas']):
    #             if doc_id not in unique_docs:
    #                 unique_docs[doc_id] = {"metadata": meta, "relevant_chunks": []}
    #                 kb.logger.debug(f"Found doc via metadata: {doc_id}")
    # except Exception as e:
    #     kb.logger.error(f"Error searching metadata: {e}")

    # 2. Search by vector similarity (content chunks or image captions)
    try:
        query_results = kb.query_knowledge(
            query=query, limit=limit * 5, filters=filters # Fetch more chunks initially
        ) # Increase limit slightly to ensure diversity

        if query_results:
            for result in query_results:
                metadata = result.get("metadata", {})
                doc_id = metadata.get("doc_id")
                chunk_text = result.get("document", "") # Text chunk or image caption
                chunk_type = metadata.get("type", "text") # Get type
                distance = result.get("distance", float('inf'))

                if not doc_id:
                    kb.logger.warning(f"Found chunk without doc_id: {result.get('id')}")
                    continue

                if doc_id not in unique_docs:
                    # Fetch complete document metadata if needed (or rely on chunk metadata)
                    # For simplicity, we'll use the metadata from the first relevant chunk found
                    # In a real app, might want kb.get_document(doc_id) metadata
                    base_metadata = {k: v for k, v in metadata.items() if k not in ['chunk_index', 'text', 'page', 'type', 'figure_id', 'caption', 'related_section']} # Base doc metadata
                    unique_docs[doc_id] = {
                        "id": doc_id, # Use doc_id as the primary identifier
                        "metadata": base_metadata,
                        "relevant_chunks": [],
                        "status": metadata.get("status", "unknown"), # Propagate status if available
                        "tags": kb._parse_tags(metadata.get("tags", "")), # Parse tags
                    }
                    kb.logger.debug(f"Found new doc via vector search: {doc_id}")

                # Add relevant chunk info (text or image caption)
                chunk_info = {
                    "chunk_id": result.get("id"),
                    "text": chunk_text, # This is the caption for images
                    "page": metadata.get("page"),
                    "type": chunk_type,
                    "distance": distance,
                }
                if chunk_type == 'image':
                     chunk_info['caption'] = metadata.get('caption') # Explicitly add caption if type is image
                     chunk_info['figure_id'] = metadata.get('figure_id') # Add figure_id if type is image


                unique_docs[doc_id]["relevant_chunks"].append(chunk_info)

    except Exception as e:
        kb.logger.error(f"Error during vector search: {e}")
        kb.logger.error(traceback.format_exc())


    # Sort relevant chunks within each document by distance (most relevant first)
    for doc in unique_docs.values():
        doc["relevant_chunks"].sort(key=lambda x: x.get("distance") if x.get("distance") is not None else float('inf'))
        # Optionally trim chunks per document if needed
        # doc["relevant_chunks"] = doc["relevant_chunks"][:5] # Limit chunks shown per doc

    # Convert to list and sort documents (e.g., by most relevant chunk distance)
    doc_list = list(unique_docs.values())
    doc_list.sort(key=lambda x: x["relevant_chunks"][0]["distance"] if x["relevant_chunks"] else float('inf'))

    kb.logger.info(f"Returning {min(len(doc_list), limit)} unique documents out of {len(doc_list)} found.")
    return doc_list[:limit]


def get_document_by_id(doc_id: str):
    """Retrieve a complete document by its ID from the knowledge base."""
    return kb.get_document(doc_id)