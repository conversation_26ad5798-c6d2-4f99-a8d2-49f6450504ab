"""
Main controller module.

This module provides controller functions for handling main application routes,
including the dashboard, landing page, and other core application views.
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user

from src.services.user_service import UserService
from src.services.base_service import get_service
from src.config.service import config_service

# Create a blueprint for main routes
main_bp = Blueprint("main", __name__)

@main_bp.route("/")
def index():
    """Render the landing page."""
    # If user is already authenticated, redirect to dashboard
    if current_user.is_authenticated:
        return redirect(url_for("main.dashboard"))
    
    # Get application name and version from configuration
    app_name = config_service.get('APP_NAME', 'Athena')
    app_version = config_service.get('APP_VERSION', '1.0.0')
    
    # Render the landing page
    return render_template(
        "main/index.html",
        app_name=app_name,
        app_version=app_version
    )

@main_bp.route("/dashboard")
@login_required
def dashboard():
    """Render the user dashboard."""
    # Get the user service
    user_service = get_service(UserService)
    
    # Check if the user is an admin
    is_admin = user_service.is_admin(current_user)
    
    # Get application settings from configuration
    app_name = config_service.get('APP_NAME', 'Athena')
    app_version = config_service.get('APP_VERSION', '1.0.0')
    
    # Render the dashboard - use index.html since dashboard.html doesn't exist
    return render_template(
        "index.html",
        is_admin=is_admin,
        app_name=app_name,
        app_version=app_version
    )

@main_bp.route("/admin")
@login_required
def admin_panel():
    """Render the admin panel."""
    # Get the user service
    user_service = get_service(UserService)
    
    # Check if the user is an admin
    if not user_service.is_admin(current_user):
        flash("You do not have permission to access the admin panel", "danger")
        return redirect(url_for("main.dashboard"))
    
    # Render the admin panel
    return render_template("admin/index.html")

@main_bp.route("/settings")
@login_required
def settings():
    """
    Render the user settings page.
    
    The section is specified as a query parameter, e.g., /settings?section=account
    """
    # Get the section from query parameters
    section = request.args.get("section", "account")
    
    # Validate the section
    valid_sections = ["account", "api", "appearance", "notifications"]
    if section not in valid_sections:
        section = "account"  # Default to account if section is invalid
    
    # Render the appropriate settings template
    return render_template(f"settings/{section}.html")

@main_bp.route("/health")
def health_check():
    """
    Health check endpoint for monitoring.
    
    Returns a JSON response with the application status and version.
    """
    # Get application settings from configuration
    app_name = config_service.get('APP_NAME', 'Athena')
    app_version = config_service.get('APP_VERSION', '1.0.0')
    
    # Return health status
    return jsonify({
        "status": "ok",
        "app": app_name,
        "version": app_version,
        "environment": config_service.get('FLASK_ENV', 'production')
    })

def register_blueprints(app):
    """
    Register all main blueprints with the app.
    
    Args:
        app: Flask application instance
    """
    # Register the main blueprint
    app.register_blueprint(main_bp)
