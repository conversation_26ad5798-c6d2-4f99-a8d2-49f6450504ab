"""
Database migration utilities.

This module provides utilities for managing database migrations, tracking schema
changes, and ensuring database consistency during the refactoring process.
"""

import os
import re
import datetime
import logging
import importlib.util
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple

from flask import Flask
from flask_migrate import Migrate
from flask_sqlalchemy import SQLAlchemy

# Import the database instance
from src.models import db

# Configure logging
logger = logging.getLogger(__name__)

class MigrationManager:
    """Manager for database migrations."""
    
    def __init__(self, app: Flask, db_instance):
        """
        Initialize the migration manager.
        
        Args:
            app: Flask application instance
            db_instance: SQLAlchemy database instance
        """
        self.app = app
        self.db = db_instance
        self.migrate = Migrate(app, db_instance)
        self.migrations_dir = Path(app.root_path).parent / "migrations"
    
    def get_all_migrations(self) -> List[Dict[str, Any]]:
        """
        Get a list of all available migrations.
        
        Returns:
            List of migration info dictionaries
        """
        migrations = []
        
        # Create migrations directory if it doesn't exist
        if not self.migrations_dir.exists():
            self.migrations_dir.mkdir(parents=True)
            return migrations
        
        # Find all migration files
        migration_files = list(self.migrations_dir.glob("*.py"))
        for file_path in migration_files:
            # Skip __init__.py and other non-migration files
            if file_path.name == "__init__.py" or file_path.name.startswith("_"):
                continue
            
            # Extract migration info
            migration_info = self._extract_migration_info(file_path)
            if migration_info:
                migrations.append(migration_info)
        
        # Sort migrations by version
        migrations.sort(key=lambda m: m["version"])
        
        return migrations
    
    def _extract_migration_info(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """
        Extract migration information from a migration file.
        
        Args:
            file_path: Path to the migration file
            
        Returns:
            Dictionary with migration information or None if not a valid migration
        """
        try:
            # Check for migration version in filename
            version_match = re.match(r"v(\d+)_([a-z0-9_]+)\.py", file_path.name)
            if not version_match:
                return None
            
            version = int(version_match.group(1))
            name = version_match.group(2).replace("_", " ")
            
            # Load the module to extract more information
            spec = importlib.util.spec_from_file_location("migration", file_path)
            if spec is None or spec.loader is None:
                return None
                
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # Extract migration metadata
            description = getattr(module, "description", name)
            dependencies = getattr(module, "dependencies", [])
            
            return {
                "version": version,
                "name": name,
                "description": description,
                "dependencies": dependencies,
                "file_path": str(file_path),
                "module_name": file_path.stem
            }
            
        except Exception as e:
            logger.error(f"Error extracting migration info from {file_path}: {str(e)}")
            return None
    
    def run_migration(self, version: int) -> Tuple[bool, str]:
        """
        Run a specific migration.
        
        Args:
            version: Migration version to run
            
        Returns:
            Tuple of (success, message)
        """
        # Get all migrations
        migrations = self.get_all_migrations()
        
        # Find the specified migration
        migration = next((m for m in migrations if m["version"] == version), None)
        if not migration:
            return False, f"Migration version {version} not found"
        
        try:
            # Import the migration module
            spec = importlib.util.spec_from_file_location(
                migration["module_name"], 
                migration["file_path"]
            )
            if spec is None or spec.loader is None:
                return False, f"Failed to load migration module: {migration['file_path']}"
                
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # Check if the migration has an upgrade function
            if not hasattr(module, "upgrade"):
                return False, f"Migration {migration['name']} does not have an upgrade function"
            
            # Run the migration in an application context
            with self.app.app_context():
                # Run the upgrade function
                module.upgrade(self.db)
                
                # Record the migration in the migrations table
                self._record_migration(migration)
                
            return True, f"Migration {migration['name']} completed successfully"
            
        except Exception as e:
            logger.error(f"Error running migration {migration['name']}: {str(e)}")
            return False, f"Error running migration: {str(e)}"
    
    def run_all_pending_migrations(self) -> Tuple[bool, str, List[Dict[str, Any]]]:
        """
        Run all pending migrations.
        
        Returns:
            Tuple of (success, message, list of executed migrations)
        """
        # Get all migrations
        all_migrations = self.get_all_migrations()
        
        # Get applied migrations
        applied_migrations = self._get_applied_migrations()
        applied_versions = [m["version"] for m in applied_migrations]
        
        # Filter pending migrations
        pending_migrations = [m for m in all_migrations if m["version"] not in applied_versions]
        
        if not pending_migrations:
            return True, "No pending migrations", []
        
        # Sort pending migrations by version
        pending_migrations.sort(key=lambda m: m["version"])
        
        # Apply migrations
        executed_migrations = []
        with self.app.app_context():
            for migration in pending_migrations:
                try:
                    # Import the migration module
                    spec = importlib.util.spec_from_file_location(
                        migration["module_name"], 
                        migration["file_path"]
                    )
                    if spec is None or spec.loader is None:
                        return False, f"Failed to load migration module: {migration['file_path']}", executed_migrations
                        
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)
                    
                    # Check if the migration has an upgrade function
                    if not hasattr(module, "upgrade"):
                        logger.warning(f"Migration {migration['name']} does not have an upgrade function")
                        continue
                    
                    # Run the upgrade function
                    module.upgrade(self.db)
                    
                    # Record the migration
                    self._record_migration(migration)
                    
                    executed_migrations.append(migration)
                    logger.info(f"Migration {migration['name']} completed successfully")
                    
                except Exception as e:
                    logger.error(f"Error running migration {migration['name']}: {str(e)}")
                    return False, f"Error running migration {migration['name']}: {str(e)}", executed_migrations
        
        if executed_migrations:
            return True, f"Applied {len(executed_migrations)} migrations", executed_migrations
        else:
            return True, "No migrations applied", []
    
    def create_migration_file(self, name: str, description: Optional[str] = None) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        Create a new migration file.
        
        Args:
            name: Name of the migration (will be converted to snake_case)
            description: Optional description of the migration
            
        Returns:
            Tuple of (success, message, migration info)
        """
        # Create migrations directory if it doesn't exist
        if not self.migrations_dir.exists():
            self.migrations_dir.mkdir(parents=True)
        
        # Create __init__.py if it doesn't exist
        init_file = self.migrations_dir / "__init__.py"
        if not init_file.exists():
            init_file.touch()
        
        # Get all migrations to determine the next version
        migrations = self.get_all_migrations()
        next_version = 1
        if migrations:
            next_version = max(m["version"] for m in migrations) + 1
        
        # Convert name to snake_case
        snake_name = re.sub(r"[^a-z0-9]+", "_", name.lower()).strip("_")
        
        # Create the migration file
        timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        file_name = f"v{next_version}_{snake_name}.py"
        file_path = self.migrations_dir / file_name
        
        # Check if file already exists
        if file_path.exists():
            return False, f"Migration file {file_name} already exists", None
        
        # Create the migration file content
        content = self._generate_migration_template(
            version=next_version,
            name=name,
            description=description or name,
            timestamp=timestamp
        )
        
        # Write the migration file
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            
            # Extract migration info
            migration_info = self._extract_migration_info(file_path)
            
            return True, f"Created migration file {file_name}", migration_info
            
        except Exception as e:
            logger.error(f"Error creating migration file {file_name}: {str(e)}")
            return False, f"Error creating migration file: {str(e)}", None
    
    def _generate_migration_template(self, version: int, name: str, description: str, timestamp: str) -> str:
        """
        Generate a migration file template.
        
        Args:
            version: Migration version
            name: Migration name
            description: Migration description
            timestamp: Timestamp string
            
        Returns:
            Migration file content
        """
        return f'''"""
Migration v{version}: {name}

{description}

Generated: {timestamp}
"""

from typing import Any
from sqlalchemy import Column, String, Integer, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.sql import text

# Migration version and metadata
version = {version}
description = "{description}"
dependencies = []  # List of migration versions this depends on

def upgrade(db):
    """
    Apply the migration.
    
    Args:
        db: SQLAlchemy database instance
    """
    # Example: Create a new table
    # db.engine.execute("""
    #     CREATE TABLE new_table (
    #         id INTEGER PRIMARY KEY,
    #         name TEXT NOT NULL,
    #         created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    #     )
    # """)
    
    # TODO: Implement migration logic
    pass

def downgrade(db):
    """
    Revert the migration.
    
    Args:
        db: SQLAlchemy database instance
    """
    # Example: Drop the table
    # db.engine.execute("DROP TABLE IF EXISTS new_table")
    
    # TODO: Implement rollback logic
    pass
'''
    
    def _record_migration(self, migration: Dict[str, Any]) -> None:
        """
        Record a migration in the migrations table.
        
        Args:
            migration: Migration information
        """
        # Check if the migrations table exists
        result = db.session.execute(text("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='migrations'
        """))
        
        if not result.fetchone():
            # Create the migrations table
            db.session.execute(text("""
                CREATE TABLE migrations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    version INTEGER NOT NULL,
                    name TEXT NOT NULL,
                    description TEXT,
                    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            db.session.commit()
        
        # Check if the migration is already recorded
        result = db.session.execute(text(
            "SELECT version FROM migrations WHERE version = :version"
        ), {"version": migration["version"]})
        
        if not result.fetchone():
            # Record the migration
            db.session.execute(text("""
                INSERT INTO migrations (version, name, description, applied_at)
                VALUES (:version, :name, :description, CURRENT_TIMESTAMP)
            """), {
                "version": migration["version"],
                "name": migration["name"],
                "description": migration["description"]
            })
            db.session.commit()
    
    def _get_applied_migrations(self) -> List[Dict[str, Any]]:
        """
        Get a list of applied migrations from the database.
        
        Returns:
            List of applied migration info dictionaries
        """
        with self.app.app_context():
            # Check if the migrations table exists
            result = db.session.execute(text("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='migrations'
            """))
            
            if not result.fetchone():
                return []
            
            # Get all applied migrations
            result = db.session.execute(text("""
                SELECT version, name, description, applied_at
                FROM migrations
                ORDER BY version
            """))
            
            migrations = []
            for row in result:
                migrations.append({
                    "version": row[0],
                    "name": row[1],
                    "description": row[2],
                    "applied_at": row[3]
                })
            
            return migrations

# Create a migration manager instance
def init_migration_manager(app: Flask):
    """
    Initialize the migration manager.
    
    Args:
        app: Flask application instance
        
    Returns:
        MigrationManager instance
    """
    return MigrationManager(app, db)
