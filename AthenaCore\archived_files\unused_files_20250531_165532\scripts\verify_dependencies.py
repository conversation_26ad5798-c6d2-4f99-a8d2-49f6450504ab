#!/usr/bin/env python
"""
Dependency Verification Script

This script analyzes the codebase to identify any files that might still be
directly importing from deprecated locations rather than using the new structure.
It helps ensure a smooth transition by catching any missed references.

Usage:
    python scripts/verify_dependencies.py

This script will:
1. Scan the codebase for imports from deprecated locations
2. Identify files that might need to be updated
3. Generate a report of files needing attention
"""

import os
import re
import sys
from pathlib import Path
import importlib
import datetime
import json

# Define deprecated module paths and their replacements
DEPRECATED_MODULES = {
    "src.login.models": "src.models",
    "src.login.views": "src.controllers.auth_controller",
}

# File patterns to check
INCLUDE_EXTENSIONS = ['.py']

# Directories to exclude
EXCLUDE_DIRS = [
    '.git',
    '.vscode',
    'venv',
    'env',
    '__pycache__',
    'node_modules',
    'build',
    'dist',
]

def get_import_statements(file_path):
    """Extract import statements from a file."""
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    import_pattern = r'^(?:from|import)\s+([\w\.]+(?: as \w+)?(?:,\s*[\w\.]+(?: as \w+)?)*)(?:\s+import\s+(.+))?'
    matches = re.findall(import_pattern, content, re.MULTILINE)
    
    imports = []
    for match in matches:
        module = match[0].strip()
        # Check if this is a 'from X import Y' statement
        if match[1]:
            # Handle multiple imports
            imported_items = [item.strip().split(' as ')[0] for item in match[1].split(',')]
            for item in imported_items:
                imports.append(f"{module}.{item}")
        else:
            # Handle 'import X' or 'import X as Y' statements
            imports.append(module.split(' as ')[0])
    
    return imports

def check_file_for_deprecated_imports(file_path):
    """Check a file for imports from deprecated modules."""
    try:
        imports = get_import_statements(file_path)
        deprecated_found = []
        
        for imp in imports:
            for deprecated_module, replacement in DEPRECATED_MODULES.items():
                if imp.startswith(deprecated_module):
                    deprecated_found.append({
                        "import": imp,
                        "deprecated_module": deprecated_module,
                        "replacement": replacement
                    })
        
        return deprecated_found
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return []

def scan_directory(directory):
    """Scan directory recursively for files with deprecated imports."""
    results = {}
    
    for root, dirs, files in os.walk(directory):
        # Skip excluded directories
        dirs[:] = [d for d in dirs if d not in EXCLUDE_DIRS]
        
        for file in files:
            _, ext = os.path.splitext(file)
            if ext.lower() in INCLUDE_EXTENSIONS:
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, directory)
                
                deprecated_imports = check_file_for_deprecated_imports(file_path)
                
                if deprecated_imports:
                    results[rel_path] = deprecated_imports
    
    return results

def generate_report(results, project_root):
    """Generate a report of files with deprecated imports."""
    if not results:
        print("No deprecated imports found. All files are using the new structure.")
        return
    
    # Count of files and imports
    file_count = len(results)
    import_count = sum(len(imports) for imports in results.values())
    
    # Generate a markdown report
    timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    report_lines = [
        "# Deprecated Import Report",
        f"Generated: {timestamp}",
        "",
        f"Found {file_count} files with {import_count} deprecated imports.",
        "",
        "## Files Requiring Updates",
        "",
        "| File | Deprecated Import | Recommended Replacement |",
        "| ---- | ----------------- | ----------------------- |",
    ]
    
    for file_path, imports in sorted(results.items()):
        for imp_info in imports:
            report_lines.append(f"| {file_path} | {imp_info['import']} | {imp_info['replacement']} |")
    
    report_lines.append("")
    report_lines.append("## Recommended Actions")
    report_lines.append("")
    report_lines.append("1. Update these imports to use the new structure")
    report_lines.append("2. Run the `migrate_imports.py` script to automatically fix common patterns")
    report_lines.append("3. For complex cases, manually update the imports following the replacement guide")
    
    # Write the report to a file
    report_path = os.path.join(project_root, "DEPRECATED-IMPORTS-REPORT.md")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("\n".join(report_lines))
    
    print(f"Report generated at: {report_path}")
    
    # Also generate a JSON version for programmatic use
    json_path = os.path.join(project_root, "deprecated_imports.json")
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2)
    
    print(f"JSON data saved to: {json_path}")

def main():
    """Main entry point for the script."""
    print("Dependency Verification Script")
    print("=============================")
    
    # Get the project root directory
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    print(f"Scanning project: {project_root}")
    
    # Scan for deprecated imports
    print("Checking for deprecated imports...")
    results = scan_directory(project_root)
    
    # Generate the report
    generate_report(results, project_root)
    
    # Exit with error code if deprecated imports were found
    if results:
        return 1
    return 0

if __name__ == "__main__":
    sys.exit(main())
