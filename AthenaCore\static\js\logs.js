(function() {
    // Only proceed if we're on the logs page (check for logs-content element)
    const logsContent = document.getElementById('logs-content');
    if (!logsContent) {
        console.debug('Not on logs page, skipping logs.js initialization');
        return; // Exit early if not on logs page
    }

    let currentPage = 1;
    let totalPages = 1;

    // Get page number from the data attribute, URL, or default
    const pageDataEl = document.getElementById('page-data');
    if (pageDataEl && pageDataEl.dataset.pageNumber) {
        // Use the page number from data attribute
        currentPage = parseInt(pageDataEl.dataset.pageNumber, 10);
    } else if (window.logsPage && !isNaN(window.logsPage)) {
        // Fallback to the logsPage variable if set
        currentPage = parseInt(window.logsPage, 10);
    } else {
        // Extract from URL if present (e.g., logs-page-1)
        const urlMatch = window.location.href.match(/logs-page-(\d+)/);
        if (urlMatch && urlMatch[1]) {
            currentPage = parseInt(urlMatch[1], 10);
        }
    }

    // Format timestamp to local date/time
    function formatTimestamp(timestamp) {
        return new Date(timestamp).toLocaleString(undefined, {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    // Escape HTML to prevent XSS and preserve line breaks
    function escapeHtml(unsafe) {
        return String(unsafe || '')
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;")
            .replace(/\n/g, "<br>"); // Preserve line breaks
    }

    // Update stats in the header
    function updateStats(stats) {
        if (!stats) {
            console.warn('Stats object is null or undefined');
            return;
        }

        const totalConversationsEl = document.getElementById('total-conversations');
        if (totalConversationsEl) {
            totalConversationsEl.textContent = stats.total_conversations || 0;
        }

        const totalMessagesEl = document.getElementById('total-messages');
        if (totalMessagesEl) {
            totalMessagesEl.textContent = stats.total_messages || 0;
        }

        const avgResponseTimeEl = document.getElementById('avg-response-time');
        if (avgResponseTimeEl) {
            const avgTime = stats.avg_response_time || 0;
            avgResponseTimeEl.textContent = `${avgTime.toFixed(1)}ms`;
        }

        const avgTokensSpentEl = document.getElementById('avg-tokens-spent');
        if (avgTokensSpentEl) {
            const avgTokens = stats.avg_tokens_spent || 0;
            avgTokensSpentEl.textContent = `${avgTokens.toFixed(0)}`;
        }
    }

    // Create HTML for a single log entry
    function createLogEntry(log) {
        // Use total token_count for log entry (sum of user and assistant tokens)
        const totalTokens = typeof log.token_count !== 'undefined'
            ? log.token_count
            : (typeof log.user_token_count !== 'undefined' && typeof log.assistant_token_count !== 'undefined'
                ? log.user_token_count + log.assistant_token_count
                : undefined);
        
        // Build header text: ConversationID-LogID
        const headerId = `${log.conversation_id}-${log.id}`;
        return `
            <div class="log-entry">
                <div class="log-header">
                    <div class="log-info">
                        <span class="log-timestamp">${formatTimestamp(log.timestamp)}</span>
                        <span class="tag tag-${log.message_type}">${log.message_type}</span>
                        <span class="tag tag-${log.status}">${log.status}</span>
                    </div>
                    <!-- Display ConversationID-LogID in top-right -->
                    <span class="log-id">${headerId}</span>
                </div>
                <div class="log-content">
                    <div class="message user-message">
                        <div class="avatar-wrapper">
                            <img class="avatar" src="${window.USER_AVATAR_URL}" alt="${window.USER_NAME}">
                            <div class="username-label">${window.USER_NAME}</div>
                        </div>
                        <div class="message-content">${formatResponse(log.content)}</div>
                        <div class="message-meta"></div>
                    </div>
                    <div class="message assistant-message">
                        <div class="avatar-wrapper">
                            <img class="avatar" src="/static/img/AthenaLogoHQ.png" alt="Athena">
                            <div class="username-label">Athena</div>
                        </div>
                        <div class="message-content">${formatResponse(log.response)}</div>
                        ${log.code ? `
                            <div class="code-block-container">
                                <button class="copy-code-button">Copy</button>
                                <button class="run-code-button">Run</button>
                                <pre><code>${escapeHtml(log.code)}</code></pre>
                            </div>` : ''}
                        ${log.output ? `<div class="output-block"><pre>${escapeHtml(log.output)}</pre></div>` : ''}
                        <div class="message-meta"><span>${log.execution_time.toFixed(1)} ms</span></div>
                    </div>
                    ${typeof totalTokens !== 'undefined' ? `<div class="log-tokens">${totalTokens} ${pluralizeToken(totalTokens)}</div>` : ''}
                </div>
            </div>
        `;
    }

    function pluralizeToken(count) {
        return count === 1 ? 'token' : 'tokens';
    }

    // Format response by interleaving code fences and think sections
    function formatResponse(text) {
      if (!text) return '';
      const segments = text.split(/```/g);
      let result = '';
      segments.forEach((segment, idx) => {
        if (idx % 2 === 1) {
          // Code block segment
          let codeContent = segment.trim();
          const lines = codeContent.split('\n');
          if (/^\w+$/.test(lines[0])) {
            lines.shift();
            codeContent = lines.join('\n');
          }
          result += `<div class="code-block-container"><button class="copy-code-button">Copy</button><button class="run-code-button">Run</button><pre><code>${escapeHtml(codeContent)}</code></pre></div>`;
        } else {
          // Plain text segment: handle think tags
          const tokens = segment.split(/(<\/?(?:think|thinking)>)/gi);
          let inThink = false;
          tokens.forEach(tok => {
            if (/^<(?:think|thinking)>$/i.test(tok)) {
              inThink = true;
            } else if (/^<\/(?:think|thinking)>$/i.test(tok)) {
              inThink = false;
            } else if (inThink) {
              result += `<div class="think-block">${escapeHtml(tok).replace(/\n/g,'<br>')}</div>`;
            } else {
              result += escapeHtml(tok).replace(/\n/g,'<br>');
            }
          });
        }
      });
      return result;
    }

    // Load and display logs
    async function loadLogs(page) {
        // Show search spinner
        const searchSpinner = document.getElementById('search-spinner');
        if (searchSpinner) searchSpinner.style.display = 'inline-block';

        // Error handling if logs-content doesn't exist
        if (!logsContent) {
            console.error('Error loading logs: logs-content element not found');
            return;
        }

        try {
            // Show loading state
            logsContent.innerHTML = `
                <div class="loading">
                    <div class="loading-spinner"></div>
                </div>
            `;

            // Fetch logs from the API with filters
            const params = new URLSearchParams();
            params.set('page', page);
            params.set('per_page', 10);
            const searchInput = document.getElementById('search-input');
            const startDateInput = document.getElementById('start-date');
            const endDateInput = document.getElementById('end-date');
            const minTokensInput = document.getElementById('min-tokens');
            if (minTokensInput && minTokensInput.value) params.set('min_tokens', minTokensInput.value);
            const typeSelect = document.getElementById('filter-type');
            const statusSelect = document.getElementById('filter-status');
            if (searchInput && searchInput.value) params.set('q', searchInput.value);
            if (startDateInput && startDateInput.value) params.set('start_date', startDateInput.value);
            if (endDateInput && endDateInput.value) params.set('end_date', endDateInput.value);
            if (typeSelect && typeSelect.value !== 'all') params.set('type', typeSelect.value);
            if (statusSelect && statusSelect.value !== 'all') params.set('status', statusSelect.value);
            const url = `/api/logs?${params.toString()}`;
            const response = await fetch(url, { credentials: 'same-origin' });
            if (!response.ok) throw new Error('Failed to fetch logs');

            const data = await response.json();

            // Log the received data for debugging
            console.log('Received logs data:', data);

            // Update pagination state - handle both new and old API formats
            currentPage = data.current_page || data.page || 1;
            totalPages = data.total_pages || 0;

            // Add null checks for all DOM elements
            const currentPageEl = document.getElementById('current-page');
            if (currentPageEl) currentPageEl.textContent = currentPage;

            const totalPagesEl = document.getElementById('total-pages');
            if (totalPagesEl) totalPagesEl.textContent = totalPages;

            const prevPageEl = document.getElementById('prev-page');
            if (prevPageEl) prevPageEl.disabled = currentPage <= 1;

            const nextPageEl = document.getElementById('next-page');
            if (nextPageEl) nextPageEl.disabled = currentPage >= totalPages;

            // Update stats with null check
            if (typeof updateStats === 'function') {
                updateStats(data.stats || {});
            }

            // Display logs or empty state
            logsContent.innerHTML = data.logs.map(createLogEntry).join('');

            // Attach copy & run handlers to code blocks
            logsContent.querySelectorAll('.code-block-container').forEach(container => {
                const copyBtn = container.querySelector('.copy-code-button');
                const runBtn = container.querySelector('.run-code-button');
                const codeEl = container.querySelector('code');
                // Copy code
                if (copyBtn && codeEl) {
                    copyBtn.addEventListener('click', () => {
                        navigator.clipboard.writeText(codeEl.textContent.trim());
                        copyBtn.textContent = 'Copied';
                        setTimeout(() => copyBtn.textContent = 'Copy', 2000);
                    });
                }
                // Run code
                if (runBtn && codeEl) {
                    runBtn.addEventListener('click', () => {
                        const overlay = document.createElement('div');
                        overlay.className = 'iframe-overlay';
                        const iframeContainer = document.createElement('div');
                        iframeContainer.className = 'iframe-container';
                        const closeBtn = document.createElement('button');
                        closeBtn.className = 'close-iframe-button';
                        closeBtn.textContent = 'Close';
                        iframeContainer.appendChild(closeBtn);
                        overlay.appendChild(iframeContainer);
                        document.body.appendChild(overlay);
                        const code = codeEl.textContent;
                        const htmlDoc = '<html><body><script>' + code + '<\/script></body></html>';
                        const iframe = document.createElement('iframe');
                        iframe.className = 'code-run-iframe';
                        iframe.setAttribute('sandbox', 'allow-scripts');
                        iframe.srcdoc = htmlDoc;
                        iframeContainer.appendChild(iframe);
                        closeBtn.addEventListener('click', () => {
                            document.body.removeChild(overlay);
                        });
                    });
                }
            });
        } catch (error) {
            console.error('Error loading logs:', error);
            logsContent.innerHTML = `
                <div class="error-state">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <line x1="12" y1="8" x2="12" y2="12"/>
                        <line x1="12" y1="16" x2="12.01" y2="16"/>
                    </svg>
                    <h3 style="margin-top: 1rem;">Failed to load logs</h3>
                    <p style="margin-top: 0.5rem;">${error.message}</p>
                </div>
            `;
        } finally {
            // Hide search spinner
            if (searchSpinner) searchSpinner.style.display = 'none';
        }
    }

    document.getElementById('prev-page').addEventListener('click', () => {
        if (currentPage > 1) {
            loadLogs(currentPage - 1);
        }
    });

    document.getElementById('next-page').addEventListener('click', () => {
        if (currentPage < totalPages) {
            loadLogs(currentPage + 1);
        }
    });

    // Initialize filter listeners
    const searchInputEl = document.getElementById('search-input');
    const startDateEl = document.getElementById('start-date');
    const endDateEl = document.getElementById('end-date');
    const minTokensEl = document.getElementById('min-tokens');
    const typeSelectEl = document.getElementById('filter-type');
    const statusSelectEl = document.getElementById('filter-status');
    if (searchInputEl) searchInputEl.addEventListener('keydown', e => { if (e.key === 'Enter') loadLogs(1); });
    if (startDateEl) startDateEl.addEventListener('change', () => loadLogs(1));
    if (endDateEl) endDateEl.addEventListener('change', () => loadLogs(1));
    if (minTokensEl) minTokensEl.addEventListener('change', () => loadLogs(1));
    if (typeSelectEl) typeSelectEl.addEventListener('change', () => loadLogs(1));
    if (statusSelectEl) statusSelectEl.addEventListener('change', () => loadLogs(1));
    // Search button listener
    const searchBtnEl = document.getElementById('search-btn');
    if (searchBtnEl) searchBtnEl.addEventListener('click', () => loadLogs(1));
    // Clear filters button listener
    const clearBtnEl = document.getElementById('clear-btn');
    if (clearBtnEl) clearBtnEl.addEventListener('click', () => {
        if (searchInputEl) searchInputEl.value = '';
        if (startDateEl) startDateEl.value = '';
        if (endDateEl) endDateEl.value = '';
        if (minTokensEl) minTokensEl.value = '';
        if (typeSelectEl) typeSelectEl.value = 'all';
        if (statusSelectEl) statusSelectEl.value = 'all';
        loadLogs(1);
    });

    // Initial load with whichever page is in logsPage
    loadLogs(currentPage);

    // Auto-refresh logs every 30 seconds, only when at top to prevent scroll jumps
    // Prevent multiple intervals
    if (!window.logsRefreshInterval) {
        window.logsRefreshInterval = setInterval(async () => {
            const scrollPos = window.pageYOffset || document.documentElement.scrollTop;
            if (scrollPos > 0) return;  // skip if user has scrolled down
            await loadLogs(currentPage);
        }, 30000);
    }

    function initLogsPage() {
        // Initial load with whichever page is in logsPage
        loadLogs(currentPage);
    }

    window.initLogsPage = initLogsPage;
    console.log('[AthenaAgent] initLogsPage defined');

})();
