"""
Unit tests for the user service.

This module contains tests for the UserService class, verifying that
all user management functionality works as expected.
"""

import pytest
from datetime import datetime
from flask_login import login_user, current_user

from src.models import User, APIKey
from src.services.user_service import UserService
from src.services.base_service import get_service


class TestUserService:
    """Test cases for UserService."""

    def test_get_user_by_id(self, app, session):
        """Test retrieving a user by ID."""
        # Create a test user
        user = User(username="testuser", email="<EMAIL>")
        user.set_password("password123")
        session.add(user)
        session.commit()
        
        # Get the user service
        user_service = get_service(UserService)
        
        # Test getting the user
        retrieved_user = user_service.get_user_by_id(user.id)
        assert retrieved_user is not None
        assert retrieved_user.id == user.id
        assert retrieved_user.username == "testuser"
        
        # Test getting a non-existent user
        non_existent = user_service.get_user_by_id(9999)
        assert non_existent is None

    def test_get_user_by_username(self, app, session):
        """Test retrieving a user by username."""
        # Create a test user
        user = User(username="testuser", email="<EMAIL>")
        user.set_password("password123")
        session.add(user)
        session.commit()
        
        # Get the user service
        user_service = get_service(UserService)
        
        # Test getting the user
        retrieved_user = user_service.get_user_by_username("testuser")
        assert retrieved_user is not None
        assert retrieved_user.id == user.id
        assert retrieved_user.username == "testuser"
        
        # Test getting a non-existent user
        non_existent = user_service.get_user_by_username("nonexistent")
        assert non_existent is None

    def test_get_user_by_email(self, app, session):
        """Test retrieving a user by email."""
        # Create a test user
        user = User(username="testuser", email="<EMAIL>")
        user.set_password("password123")
        session.add(user)
        session.commit()
        
        # Get the user service
        user_service = get_service(UserService)
        
        # Test getting the user
        retrieved_user = user_service.get_user_by_email("<EMAIL>")
        assert retrieved_user is not None
        assert retrieved_user.id == user.id
        assert retrieved_user.email == "<EMAIL>"
        
        # Test getting a non-existent user
        non_existent = user_service.get_user_by_email("<EMAIL>")
        assert non_existent is None

    def test_create_user(self, app, session):
        """Test creating a new user."""
        # Get the user service
        user_service = get_service(UserService)
        
        # Create a new user
        user = user_service.create_user(
            username="newuser",
            email="<EMAIL>",
            password="password123",
            role="user"
        )
        
        # Verify the user was created
        assert user is not None
        assert user.id is not None
        assert user.username == "newuser"
        assert user.email == "<EMAIL>"
        assert user.role == "user"
        assert user.check_password("password123") is True
        
        # Verify the user exists in the database
        db_user = User.query.filter_by(username="newuser").first()
        assert db_user is not None
        assert db_user.id == user.id
        
        # Test creating a user with an existing username
        with pytest.raises(ValueError):
            user_service.create_user(
                username="newuser",
                email="<EMAIL>",
                password="password456"
            )
        
        # Test creating a user with an existing email
        with pytest.raises(ValueError):
            user_service.create_user(
                username="anotheruser",
                email="<EMAIL>",
                password="password456"
            )

    def test_update_user(self, app, session):
        """Test updating a user."""
        # Create a test user
        user = User(username="testuser", email="<EMAIL>")
        user.set_password("password123")
        session.add(user)
        session.commit()
        
        # Get the user service
        user_service = get_service(UserService)
        
        # Update the user
        updated_user = user_service.update_user(
            user.id,
            email="<EMAIL>",
            role="admin",
            theme_preference="light"
        )
        
        # Verify the user was updated
        assert updated_user is not None
        assert updated_user.id == user.id
        assert updated_user.email == "<EMAIL>"
        assert updated_user.role == "admin"
        assert updated_user.theme_preference == "light"
        
        # Update the password
        updated_user = user_service.update_user(
            user.id,
            password="newpassword"
        )
        
        # Verify the password was updated
        assert updated_user.check_password("newpassword") is True
        assert updated_user.check_password("password123") is False
        
        # Test updating a non-existent user
        non_existent = user_service.update_user(
            9999,
            email="<EMAIL>"
        )
        assert non_existent is None

    def test_delete_user(self, app, session):
        """Test deleting a user."""
        # Create a test user
        user = User(username="testuser", email="<EMAIL>")
        user.set_password("password123")
        session.add(user)
        session.commit()
        
        # Get the user service
        user_service = get_service(UserService)
        
        # Delete the user
        result = user_service.delete_user(user.id)
        assert result is True
        
        # Verify the user was deleted
        deleted_user = User.query.get(user.id)
        assert deleted_user is None
        
        # Test deleting a non-existent user
        result = user_service.delete_user(9999)
        assert result is False

    def test_authenticate(self, app, session):
        """Test user authentication."""
        # Create a test user
        user = User(username="testuser", email="<EMAIL>")
        user.set_password("password123")
        session.add(user)
        session.commit()
        
        # Get the user service
        user_service = get_service(UserService)
        
        # Test authentication with username
        authenticated = user_service.authenticate("testuser", "password123")
        assert authenticated is not None
        assert authenticated.id == user.id
        
        # Test authentication with email
        authenticated = user_service.authenticate("<EMAIL>", "password123")
        assert authenticated is not None
        assert authenticated.id == user.id
        
        # Test authentication with wrong password
        authenticated = user_service.authenticate("testuser", "wrongpassword")
        assert authenticated is None
        
        # Test authentication with non-existent user
        authenticated = user_service.authenticate("nonexistent", "password123")
        assert authenticated is None

    def test_is_admin(self, app, session):
        """Test checking if a user is an admin."""
        # Create a regular user
        regular_user = User(username="regular", email="<EMAIL>", role="user")
        regular_user.set_password("password123")
        
        # Create an admin user
        admin_user = User(username="admin", email="<EMAIL>", role="admin")
        admin_user.set_password("password123")
        
        session.add(regular_user)
        session.add(admin_user)
        session.commit()
        
        # Get the user service
        user_service = get_service(UserService)
        
        # Test regular user
        assert user_service.is_admin(regular_user) is False
        
        # Test admin user
        assert user_service.is_admin(admin_user) is True

    def test_get_all_users(self, app, session):
        """Test retrieving all users with pagination."""
        # Create multiple test users
        for i in range(25):
            user = User(
                username=f"user{i}",
                email=f"user{i}@example.com",
                role="user"
            )
            user.set_password("password123")
            session.add(user)
        session.commit()
        
        # Get the user service
        user_service = get_service(UserService)
        
        # Test default pagination (page 1, 20 per page)
        result = user_service.get_all_users()
        assert len(result["users"]) == 20
        assert result["total"] >= 25
        assert result["pages"] >= 2
        assert result["page"] == 1
        assert result["per_page"] == 20
        assert result["has_next"] is True
        assert result["has_prev"] is False
        
        # Test custom pagination (page 2, 10 per page)
        result = user_service.get_all_users(page=2, per_page=10)
        assert len(result["users"]) == 10
        assert result["total"] >= 25
        assert result["pages"] >= 3
        assert result["page"] == 2
        assert result["per_page"] == 10
        assert result["has_next"] is True
        assert result["has_prev"] is True

    def test_api_key_management(self, app, session):
        """Test API key creation, validation, and deletion."""
        # Create a test user
        user = User(username="testuser", email="<EMAIL>")
        user.set_password("password123")
        session.add(user)
        session.commit()
        
        # Get the user service
        user_service = get_service(UserService)
        
        # Create an API key
        api_key = user_service.create_api_key(user.id, "Test API Key")
        assert api_key is not None
        assert api_key.user_id == user.id
        assert api_key.name == "Test API Key"
        
        # Get API keys for the user
        api_keys = user_service.get_api_keys(user.id)
        assert len(api_keys) == 1
        assert api_keys[0].id == api_key.id
        
        # Validate the API key
        validated_user = user_service.validate_api_key(api_key.id)
        assert validated_user is not None
        assert validated_user.id == user.id
        
        # Check that last_used was updated
        updated_api_key = APIKey.query.get(api_key.id)
        assert updated_api_key.last_used is not None
        
        # Delete the API key
        result = user_service.delete_api_key(api_key.id)
        assert result is True
        
        # Verify the API key was deleted
        deleted_api_key = APIKey.query.get(api_key.id)
        assert deleted_api_key is None
        
        # Test creating an API key for a non-existent user
        api_key = user_service.create_api_key(9999)
        assert api_key is None
        
        # Test validating a non-existent API key
        validated_user = user_service.validate_api_key("nonexistent")
        assert validated_user is None
        
        # Test deleting a non-existent API key
        result = user_service.delete_api_key("nonexistent")
        assert result is False
