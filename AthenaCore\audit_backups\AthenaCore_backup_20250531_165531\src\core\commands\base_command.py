"""
Base command interface for all command modules.
"""
import subprocess
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional


class BaseCommand(ABC):
    """Base class for all command implementations."""

    @property
    @abstractmethod
    def name(self) -> str:
        """Name of the command module."""
        pass

    @property
    @abstractmethod
    def description(self) -> str:
        """Description of what the command module does."""
        pass

    @property
    @abstractmethod
    def supported_actions(self) -> List[str]:
        """List of actions supported by this command."""
        pass

    @abstractmethod
    def execute(self, action: str, params: Dict[str, Any] = None) -> str:
        """
        Execute a command action with parameters.

        Args:
            action: The specific action to perform
            params: Dictionary of parameters for the action

        Returns:
            Output message from the command execution
        """
        pass

    def _run_command(self, command: str) -> str:
        """
        Helper method to run a shell command safely.

        Args:
            command: Shell command to execute

        Returns:
            Command output or error message
        """
        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='replace'
            )

            if result.returncode == 0:
                output = result.stdout.strip()
                return output if output else "Command executed successfully"
            else:
                return f"Error: {result.stderr.strip()}"
        except Exception as e:
            return f"Error executing command: {str(e)}"
