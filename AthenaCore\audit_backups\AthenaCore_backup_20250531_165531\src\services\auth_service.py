"""
Authentication service module.

This module provides services for authentication-related operations, including
login, token generation, and API key validation.
"""

import logging
import datetime
import uuid
import jwt
from typing import Optional, Dict, <PERSON>, <PERSON><PERSON>, Union

from flask import current_app, request
from flask_login import login_user, logout_user

from src.models.user import User
from src.models.api_key import APIKey
from src.models import db
from src.config.service import config_service
from .base_service import BaseService
from .user_service import UserService

# Configure logging
logger = logging.getLogger(__name__)

class AuthenticationService(BaseService):
    """Service for authentication-related operations."""
    
    def initialize(self) -> None:
        """Initialize the authentication service."""
        self._user_service = None
    
    @property
    def user_service(self) -> UserService:
        """Get the user service instance."""
        if self._user_service is None:
            from .base_service import get_service
            self._user_service = get_service(UserService)
        return self._user_service
    
    def login(self, username_or_email: str, password: str, remember: bool = False) -> Tu<PERSON>[bool, Optional[User], Optional[str]]:
        """
        Authenticate a user by username/email and password.
        
        Args:
            username_or_email: Username or email to authenticate
            password: Password to authenticate
            remember: Whether to remember the user's login
            
        Returns:
            Tuple of (success, user, error_message)
        """
        user = None
        error = None
        
        # Check if the input is an email or username
        if '@' in username_or_email:
            # Find by email
            user = self.user_service.get_user_by_email(username_or_email)
        else:
            # Find by username
            user = self.user_service.get_user_by_username(username_or_email)
        
        # Check if user exists and password is correct
        if user is None:
            error = "Invalid username or email"
            logger.warning(f"Login attempt with invalid username/email: {username_or_email}")
            return False, None, error
        
        if not user.check_password(password):
            error = "Invalid password"
            logger.warning(f"Login attempt with invalid password for user: {username_or_email}")
            return False, None, error
        
        if not user.is_active:
            error = "Account is inactive"
            logger.warning(f"Login attempt for inactive account: {username_or_email}")
            return False, None, error
        
        # Login the user
        login_user(user, remember=remember)
        logger.info(f"User logged in successfully: {user.username}")
        
        return True, user, None
    
    def logout(self) -> None:
        """Log out the current user."""
        logout_user()
    
    def generate_api_key(self, user_id: int, name: Optional[str] = None) -> Optional[APIKey]:
        """
        Generate a new API key for a user.
        
        Args:
            user_id: ID of the user to generate the API key for
            name: Optional name for the API key
            
        Returns:
            Generated API key or None if the user doesn't exist
        """
        # Verify user exists
        user = self.user_service.get_user_by_id(user_id)
        if not user:
            logger.warning(f"Attempted to generate API key for non-existent user ID: {user_id}")
            return None
        
        # Generate a new API key
        api_key_id = str(uuid.uuid4())
        
        # Create API key record
        api_key = APIKey(
            id=api_key_id,
            name=name,
            user_id=user_id
        )
        
        # Save to database
        db.session.add(api_key)
        db.session.commit()
        
        logger.info(f"Generated new API key for user {user.username}")
        return api_key
    
    def verify_api_key(self, api_key_id: str) -> Optional[User]:
        """
        Verify an API key and return the associated user.
        
        Args:
            api_key_id: API key to verify
            
        Returns:
            User associated with the API key or None if invalid
        """
        # Find the API key
        api_key = APIKey.query.get(api_key_id)
        
        if not api_key:
            logger.warning(f"API key verification failed: Key not found")
            return None
        
        # Update last used timestamp
        api_key.last_used = datetime.datetime.utcnow()
        db.session.commit()
        
        # Get the associated user
        user = self.user_service.get_user_by_id(api_key.user_id)
        
        if not user or not user.is_active:
            logger.warning(f"API key verification failed: User inactive or not found")
            return None
        
        logger.info(f"API key verification successful for user {user.username}")
        return user
    
    def delete_api_key(self, api_key_id: str, user_id: int) -> Tuple[bool, Optional[str]]:
        """
        Delete an API key.
        
        Args:
            api_key_id: ID of the API key to delete
            user_id: ID of the user who owns the API key
            
        Returns:
            Tuple of (success, error_message)
        """
        # Find the API key
        api_key = APIKey.query.get(api_key_id)
        
        if not api_key:
            return False, "API key not found"
        
        # Verify ownership
        if api_key.user_id != user_id:
            logger.warning(f"Attempted to delete API key owned by user {api_key.user_id} by user {user_id}")
            return False, "Unauthorized"
        
        # Delete the API key
        db.session.delete(api_key)
        db.session.commit()
        
        logger.info(f"Deleted API key {api_key_id} for user {user_id}")
        return True, None
    
    def generate_jwt_token(self, user: User, expires_in: int = 3600) -> str:
        """
        Generate a JWT token for API authentication.
        
        Args:
            user: User to generate the token for
            expires_in: Token validity period in seconds
            
        Returns:
            JWT token
        """
        # Get JWT secret key from configuration
        jwt_secret = config_service.get('JWT_SECRET_KEY', 'dev-secret-key')
        
        # Create payload
        payload = {
            'sub': user.id,
            'iat': datetime.datetime.utcnow(),
            'exp': datetime.datetime.utcnow() + datetime.timedelta(seconds=expires_in),
            'username': user.username,
            'email': user.email,
            'role': user.role
        }
        
        # Generate token
        token = jwt.encode(payload, jwt_secret, algorithm='HS256')
        
        logger.info(f"Generated JWT token for user {user.username}")
        return token
    
    def verify_jwt_token(self, token: str) -> Tuple[bool, Optional[User], Optional[str]]:
        """
        Verify a JWT token and return the associated user.
        
        Args:
            token: JWT token to verify
            
        Returns:
            Tuple of (success, user, error_message)
        """
        # Get JWT secret key from configuration
        jwt_secret = config_service.get('JWT_SECRET_KEY', 'dev-secret-key')
        
        try:
            # Decode token
            payload = jwt.decode(token, jwt_secret, algorithms=['HS256'])
            
            # Get user ID from payload
            user_id = payload.get('sub')
            
            # Get user
            user = self.user_service.get_user_by_id(user_id)
            
            if not user:
                return False, None, "User not found"
                
            if not user.is_active:
                return False, None, "User is inactive"
            
            return True, user, None
            
        except jwt.ExpiredSignatureError:
            return False, None, "Token has expired"
        except jwt.InvalidTokenError:
            return False, None, "Invalid token"
        except Exception as e:
            logger.error(f"Error verifying JWT token: {str(e)}")
            return False, None, "Token verification error"
    
    def get_api_keys_for_user(self, user_id: int) -> list:
        """
        Get all API keys for a user.
        
        Args:
            user_id: ID of the user to get API keys for
            
        Returns:
            List of API keys
        """
        return APIKey.query.filter_by(user_id=user_id).all()
    
    def authenticate_request(self) -> Tuple[bool, Optional[User], Optional[str]]:
        """
        Authenticate an API request using various authentication methods.
        
        This method checks for authentication in the following order:
        1. JWT token in Authorization header
        2. API key in X-API-Key header
        3. API key in query parameter
        
        Returns:
            Tuple of (success, user, error_message)
        """
        # Check for JWT token in Authorization header
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
            success, user, error = self.verify_jwt_token(token)
            if success:
                return True, user, None
        
        # Check for API key in header
        api_key = request.headers.get('X-API-Key')
        if api_key:
            user = self.verify_api_key(api_key)
            if user:
                return True, user, None
        
        # Check for API key in query parameter
        api_key = request.args.get('api_key')
        if api_key:
            user = self.verify_api_key(api_key)
            if user:
                return True, user, None
        
        return False, None, "Authentication required"
