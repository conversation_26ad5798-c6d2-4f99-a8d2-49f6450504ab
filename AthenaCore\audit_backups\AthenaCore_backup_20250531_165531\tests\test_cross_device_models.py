# tests/test_cross_device_models.py

"""
Unit tests for the Cross-Device API System database models.

This file contains tests for:
- Command model with background task capabilities
- Attachment model for file handling 
- ScheduledTask model for autonomous operations
"""

import unittest
import json
from datetime import datetime, timedelta

from src.models import db, User
from src.login.device_models import (
    Device, Command, Attachment, ScheduledTask,
    create_command, create_attachment, add_command_log
)

# Import the Flask app for testing context
from main import app

class CrossDeviceModelsTestCase(unittest.TestCase):
    """Test the Cross-Device API System database models."""
    
    def setUp(self):
        """Set up test database and fixtures."""
        # Configure app for testing
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        
        # Create app context
        self.app_context = app.app_context()
        self.app_context.push()
        
        # Create tables
        db.create_all()
        
        # Create test user
        self.user = User(
            username="testuser",
            email="<EMAIL>"
        )
        self.user.set_password("testpassword")
        db.session.add(self.user)
        db.session.commit()
        
        # Create test device
        self.device = Device(
            device_uuid="test-device-uuid",
            name="Test Device",
            device_type="test_device",
            user_id=self.user.id,
            is_active=True
        )
        db.session.add(self.device)
        db.session.commit()
    
    def tearDown(self):
        """Clean up after tests."""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_command_background_properties(self):
        """Test Command model with background task properties."""
        # Create a background command
        command = Command(
            command_uuid="test-command-uuid",
            user_id=self.user.id,
            target_device_id=self.device.id,
            capability_name="test_capability",
            parameters=json.dumps({"test": "value"}),
            is_background=True,
            parent_command_id=None,
            priority_level=8,
            max_runtime=1800,
            progress=25,
            status="running"
        )
        db.session.add(command)
        db.session.commit()
        
        # Retrieve the command and verify properties
        retrieved_command = Command.query.filter_by(command_uuid="test-command-uuid").first()
        self.assertIsNotNone(retrieved_command)
        self.assertTrue(retrieved_command.is_background)
        self.assertEqual(retrieved_command.priority_level, 8)
        self.assertEqual(retrieved_command.max_runtime, 1800)
        self.assertEqual(retrieved_command.progress, 25)
        self.assertEqual(retrieved_command.status, "running")
    
    def test_command_parent_child_relationship(self):
        """Test parent-child relationship between commands."""
        # Create parent command
        parent_command = Command(
            command_uuid="parent-command-uuid",
            user_id=self.user.id,
            target_device_id=self.device.id,
            capability_name="parent_capability",
            parameters=json.dumps({"parent": "value"}),
            is_background=True
        )
        db.session.add(parent_command)
        db.session.commit()
        
        # Create child command
        child_command = Command(
            command_uuid="child-command-uuid",
            user_id=self.user.id,
            target_device_id=self.device.id,
            capability_name="child_capability",
            parameters=json.dumps({"child": "value"}),
            is_background=True,
            parent_command_id=parent_command.id
        )
        db.session.add(child_command)
        db.session.commit()
        
        # Verify relationship
        retrieved_child = Command.query.filter_by(command_uuid="child-command-uuid").first()
        self.assertEqual(retrieved_child.parent_command_id, parent_command.id)
    
    def test_attachment_model(self):
        """Test Attachment model."""
        # Create attachment
        attachment = Attachment(
            attachment_uuid="test-attachment-uuid",
            user_id=self.user.id,
            device_id=self.device.id,
            filename="test_file.txt",
            file_type="text/plain",
            file_size=1024,
            file_hash="abcdef1234567890",
            storage_path="user/123/test_file.txt",
            is_public=False,
            status="complete",
            transfer_progress=100
        )
        db.session.add(attachment)
        db.session.commit()
        
        # Retrieve attachment and verify properties
        retrieved_attachment = Attachment.query.filter_by(attachment_uuid="test-attachment-uuid").first()
        self.assertIsNotNone(retrieved_attachment)
        self.assertEqual(retrieved_attachment.filename, "test_file.txt")
        self.assertEqual(retrieved_attachment.file_type, "text/plain")
        self.assertEqual(retrieved_attachment.file_size, 1024)
        self.assertEqual(retrieved_attachment.file_hash, "abcdef1234567890")
        self.assertEqual(retrieved_attachment.storage_path, "user/123/test_file.txt")
        self.assertFalse(retrieved_attachment.is_public)
        self.assertEqual(retrieved_attachment.status, "complete")
        self.assertEqual(retrieved_attachment.transfer_progress, 100)
    
    def test_scheduled_task_model(self):
        """Test ScheduledTask model."""
        # Create scheduled task with interval schedule
        now = datetime.utcnow()
        next_run = now + timedelta(minutes=30)
        
        scheduled_task = ScheduledTask(
            task_uuid="test-task-uuid",
            user_id=self.user.id,
            name="Test Task",
            capability_name="test_capability",
            parameters=json.dumps({"test": "value"}),
            schedule_type="interval",
            schedule_data=json.dumps({"minutes": 30}),
            is_active=True,
            is_recurring=True,
            priority_level=6,
            next_run=next_run,
            target_device_id=self.device.id
        )
        db.session.add(scheduled_task)
        db.session.commit()
        
        # Retrieve task and verify properties
        retrieved_task = ScheduledTask.query.filter_by(task_uuid="test-task-uuid").first()
        self.assertIsNotNone(retrieved_task)
        self.assertEqual(retrieved_task.name, "Test Task")
        self.assertEqual(retrieved_task.capability_name, "test_capability")
        self.assertEqual(retrieved_task.schedule_type, "interval")
        
        # Verify schedule data
        schedule_data = json.loads(retrieved_task.schedule_data)
        self.assertEqual(schedule_data["minutes"], 30)
        
        # Verify other properties
        self.assertTrue(retrieved_task.is_active)
        self.assertTrue(retrieved_task.is_recurring)
        self.assertEqual(retrieved_task.priority_level, 6)
        
        # Verify next_run time (within a small margin of error)
        self.assertAlmostEqual(
            retrieved_task.next_run.timestamp(),
            next_run.timestamp(),
            delta=1  # Allow 1 second difference due to DB operations
        )
        
        # Verify device relationship
        self.assertEqual(retrieved_task.target_device_id, self.device.id)
    
    def test_task_with_different_targeting(self):
        """Test ScheduledTask with different targeting methods."""
        # Create task with device type filter
        type_task = ScheduledTask(
            task_uuid="type-filter-task",
            user_id=self.user.id,
            name="Type Filter Task",
            capability_name="test_capability",
            schedule_type="interval",
            schedule_data=json.dumps({"hours": 1}),
            device_type_filter="desktop"
        )
        db.session.add(type_task)
        
        # Create task with capability filter
        capability_task = ScheduledTask(
            task_uuid="capability-filter-task",
            user_id=self.user.id,
            name="Capability Filter Task",
            capability_name="test_capability",
            schedule_type="cron",
            schedule_data=json.dumps({"expression": "0 * * * *"}),
            capability_filter="process_files"
        )
        db.session.add(capability_task)
        
        # Create task with trigger condition
        trigger_task = ScheduledTask(
            task_uuid="trigger-task",
            user_id=self.user.id,
            name="Trigger Task",
            capability_name="test_capability",
            schedule_type="trigger",
            schedule_data=json.dumps({}),
            trigger_condition=json.dumps({
                "type": "device_status",
                "device_id": self.device.id,
                "status": "online"
            })
        )
        db.session.add(trigger_task)
        
        db.session.commit()
        
        # Verify tasks were created with correct filtering
        tasks = ScheduledTask.query.filter_by(user_id=self.user.id).all()
        self.assertEqual(len(tasks), 3)
        
        type_filter_task = ScheduledTask.query.filter_by(task_uuid="type-filter-task").first()
        self.assertEqual(type_filter_task.device_type_filter, "desktop")
        self.assertIsNone(type_filter_task.capability_filter)
        self.assertIsNone(type_filter_task.target_device_id)
        
        capability_filter_task = ScheduledTask.query.filter_by(task_uuid="capability-filter-task").first()
        self.assertEqual(capability_filter_task.capability_filter, "process_files")
        self.assertIsNone(capability_filter_task.device_type_filter)
        self.assertIsNone(capability_filter_task.target_device_id)
        
        trigger_condition_task = ScheduledTask.query.filter_by(task_uuid="trigger-task").first()
        self.assertIsNotNone(trigger_condition_task.trigger_condition)
        trigger_data = json.loads(trigger_condition_task.trigger_condition)
        self.assertEqual(trigger_data["type"], "device_status")
        self.assertEqual(trigger_data["device_id"], self.device.id)
        self.assertEqual(trigger_data["status"], "online")

if __name__ == '__main__':
    unittest.main()
