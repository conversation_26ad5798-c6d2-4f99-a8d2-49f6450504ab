"""
Spotify control command module using the Spotify Web API.
This module extends the available commands to include:
- play, pause, next, previous, current
- volume (setting volume level)
- seek (jump to a specific position in the track)
- repeat (set repeat mode: off, track, context)
- shuffle (turn shuffle on/off)
- transfer (transfer playback to a given device)
- devices (list available devices)
"""

import os
import time
import json
from typing import Any, Dict, List

import spotipy
from spotipy.oauth2 import SpotifyOAuth

from src.core.commands.base_command import BaseCommand  # Correct import
from src.models import MC<PERSON><PERSON><PERSON><PERSON>, db
from flask_login import current_user

# Scope for Spotify permissions.
SPOTIFY_SCOPE = (
    "user-read-playback-state user-modify-playback-state "
    "user-read-currently-playing user-read-playback-position "
    "user-library-read user-library-modify user-read-email user-read-private"
)

_spotify_client = None
username = os.getenv("SPOTIFY_USERNAME", "OneLostHero")


def get_spotify_credentials():
    """
    Retrieve Spotify credentials from the database.
    Supports both new explicit columns and a legacy JSON blob.
    """
    key_entry = MCPApiKey.query.filter_by(user_id=current_user.id, service_id='spotify').first()
    if not key_entry:
        return None, None, None, None

    # Prefer new explicit columns
    client_id = key_entry.spotify_client_id
    client_secret = key_entry.spotify_client_secret
    redirect_uri = key_entry.spotify_redirect_uri or os.getenv('REDIRECT_URI')
    username = key_entry.spotify_username

    if not (client_id and client_secret and redirect_uri and username):
        # Fallback to legacy JSON blob if needed
        try:
            creds = json.loads(key_entry.api_key)
            client_id = client_id or creds.get('Spotify_ClientID')
            client_secret = client_secret or creds.get('Spotify_ClientSecret')
            redirect_uri = redirect_uri or creds.get('Spotify_RedirectURI', os.getenv('REDIRECT_URI'))
            username = username or creds.get('Spotify_Username')
        except Exception:
            pass

    return client_id, client_secret, redirect_uri, username


def spotify_authenticate():
    """
    Authenticate with Spotify and return a Spotify client.
    """
    try:
        client_id, client_secret, redirect_uri, username = get_spotify_credentials()
        print(f"[SPOTIFY DEBUG] client_id={client_id}, client_secret={'SET' if client_secret else 'MISSING'}, redirect_uri={redirect_uri}, username={username}")
        if not client_id or not client_secret:
            print("Spotify credentials not found in DB. Please set them in the UI.")
            return None

        cache_dir = os.path.join(os.path.expanduser("~"), ".athena", "spotify_cache")
        os.makedirs(cache_dir, exist_ok=True)
        cache_path = os.path.join(cache_dir, ".spotify_cache")

        auth_manager = SpotifyOAuth(
            client_id=client_id,
            client_secret=client_secret,
            redirect_uri=redirect_uri,
            scope=SPOTIFY_SCOPE,
            username=username,
            cache_path=cache_path,
            open_browser=True,
            show_dialog=True  # Force the authorization dialog
        )

        token_info = auth_manager.get_cached_token()
        if not token_info or not auth_manager.validate_token(token_info):
            print("Auth token missing or expired. Requesting new authorization...")
            auth_manager.get_access_token(as_dict=False)

        client = spotipy.Spotify(auth_manager=auth_manager)

        try:
            devices = client.devices()
            print("Spotify devices available:", devices)
            client.current_user()
            print("✓ Spotify client authenticated successfully with device access")
        except spotipy.SpotifyException as e:
            if "403" in str(e):
                print("⚠ Authentication succeeded but permission to access devices denied.")
                print("Please verify that your Spotify account is registered as a test user in the Developer Dashboard")
                print(f"Dashboard URL: https://developer.spotify.com/dashboard/applications/{client_id}")
            else:
                raise
        return client
    except Exception as e:
        print(f"⚠ Spotify authentication error: {str(e)}")
        return None


class SpotifyControl(BaseCommand):
    """Command module to control Spotify via the Spotify Web API."""

    def __init__(self):
        global _spotify_client
        self.spotify = _spotify_client

    @property
    def name(self) -> str:
        return "spotify"

    @property
    def description(self) -> str:
        return "Control Spotify playback using the Spotify Web API"

    @property
    def supported_actions(self) -> List[str]:
        return [
            "play", "pause", "next", "previous", "current",
            "volume", "seek", "repeat", "shuffle", "transfer", "devices"
        ]

    def _ensure_spotify_client(self):
        global _spotify_client
        if self.spotify is None:
            print("Spotify client not initialized, attempting to authenticate...")
            _spotify_client = spotify_authenticate()
            self.spotify = _spotify_client
        else:
            try:
                self.spotify.current_user()
            except spotipy.SpotifyException as e:
                print("Spotify token expired or invalid, re-authenticating...")
                _spotify_client = spotify_authenticate()
                self.spotify = _spotify_client
        if self.spotify:
            print("Spotify client is ready.")
        else:
            print("Failed to initialize Spotify client.")
        return self.spotify is not None

    def _get_active_device(self):
        devices = self.spotify.devices()
        print("Retrieved devices:", devices)
        active_device = None
        for device in devices.get('devices', []):
            if device.get('is_active'):
                active_device = device.get('id')
                print(f"Active device found: {device.get('name')} (ID: {active_device})")
                break

        if not active_device and devices.get('devices'):
            first_device = devices['devices'][0]
            active_device = first_device.get('id')
            print(f"No active device found. Using available device: {first_device.get('name')} (ID: {active_device})")
        return active_device

    def execute(self, action: str, params: Dict[str, Any] = None) -> str:
        print(f"[DEBUG] SpotifyControl.execute called with action: {action}, params: {params}")
        params = params or {}

        if not self._ensure_spotify_client():
            print("[DEBUG] Could not initialize Spotify client.")
            return "Error: Could not initialize Spotify client. Please check credentials and network connection."

        try:
            if action == "play":
                return self._play()
            elif action == "pause":
                return self._pause()
            elif action == "next":
                return self._next_track()
            elif action == "previous":
                return self._previous_track()
            elif action == "current":
                return self._current_track()
            elif action == "volume":
                level = params.get("level")
                if level is not None:
                    try:
                        level = int(level)
                    except Exception:
                        return "Invalid volume level. Must be an integer between 0 and 100."
                    return self._set_volume(level)
                else:
                    return "No volume level specified."
            elif action == "seek":
                position = params.get("position_ms")
                if position is not None:
                    try:
                        position = int(position)
                    except Exception:
                        return "Invalid position value. Must be an integer (milliseconds)."
                    return self._seek(position)
                else:
                    return "No seek position specified."
            elif action == "repeat":
                state = params.get("state")
                if state in ["off", "track", "context"]:
                    return self._repeat(state)
                else:
                    return "Invalid repeat state. Must be one of: off, track, context."
            elif action == "shuffle":
                state = params.get("state")
                if state is not None:
                    if isinstance(state, str):
                        if state.lower() == "true":
                            state = True
                        elif state.lower() == "false":
                            state = False
                        else:
                            return "Invalid shuffle state. Must be true or false."
                    return self._shuffle(state)
                else:
                    return "No shuffle state specified."
            elif action == "transfer":
                device_id = params.get("device_id")
                force_play = params.get("force_play", False)
                if device_id:
                    if isinstance(force_play, str):
                        force_play = force_play.lower() == "true"
                    return self._transfer_playback(device_id, force_play)
                else:
                    return "No device_id specified for transfer."
            elif action == "devices":
                return self._list_devices()
            else:
                return f"Unknown Spotify action: {action}"
        except Exception as e:
            print(f"[DEBUG] Exception in execute: {str(e)}")
            if "authentication" in str(e).lower() or "token" in str(e).lower():
                print("Spotify authentication error during command execution, re-authenticating...")
                global _spotify_client
                _spotify_client = spotify_authenticate()
                self.spotify = _spotify_client
                if self.spotify:
                    return self.execute(action, params)
            elif "premium" in str(e).lower():
                return "Error: Spotify Premium is required for playback control"
            return f"Spotify API error: {str(e)}"

    def _pause(self) -> str:
        try:
            active_device = self._get_active_device()
            if not active_device:
                return "No Spotify devices available. Please open Spotify on a device and try again."
            self.spotify.pause_playback(device_id=active_device)
            return "Music playback paused."
        except spotipy.SpotifyException as e:
            return f"Error pausing playback: {str(e)}"
        except Exception as e:
            return f"Error pausing playback: {str(e)}"

    def _play(self) -> str:
        try:
            active_device = self._get_active_device()
            if not active_device:
                return "No Spotify devices available. Please open Spotify on a device and try again."
            self.spotify.start_playback(device_id=active_device)
            time.sleep(0.5)
            current = self._get_current_track_info()
            if current:
                return f"Playing: {current}"
            return "Music playback started."
        except spotipy.SpotifyException as e:
            if "Premium required" in str(e):
                return "Error: Spotify Premium is required for playback control"
            return f"Error starting playback: {str(e)}"

    def _next_track(self) -> str:
        try:
            active_device = self._get_active_device()
            if not active_device:
                return "No Spotify devices available. Please open Spotify on a device and try again."
            self.spotify.next_track(device_id=active_device)
            time.sleep(0.5)
            current = self._get_current_track_info()
            if current:
                return f"Skipped to next track: {current}"
            return "Skipped to the next track."
        except spotipy.SpotifyException as e:
            return f"Error skipping to next track: {str(e)}"

    def _previous_track(self) -> str:
        try:
            active_device = self._get_active_device()
            if not active_device:
                return "No Spotify devices available. Please open Spotify on a device and try again."
            self.spotify.previous_track(device_id=active_device)
            time.sleep(0.5)
            current = self._get_current_track_info()
            if current:
                return f"Skipped to previous track: {current}"
            return "Skipped to the previous track."
        except spotipy.SpotifyException as e:
            return f"Error skipping to previous track: {str(e)}"

    def _current_track(self) -> str:
        info = self._get_current_track_info()
        return info if info else "No track is currently playing."

    def _get_current_track_info(self) -> str:
        try:
            current_track = self.spotify.current_user_playing_track()
            if current_track is None or not current_track.get('item'):
                return None
            artist_name = current_track['item']['artists'][0]['name']
            album_name = current_track['item']['album']['name']
            track_title = current_track['item']['name']
            return f"{track_title} by {artist_name} from {album_name}"
        except Exception as e:
            print(f"Error getting current track: {str(e)}")
            return None

    def _set_volume(self, level: int) -> str:
        try:
            active_device = self._get_active_device()
            if not active_device:
                return "No Spotify devices available. Please open Spotify on a device and try again."
            self.spotify.volume(volume_percent=level, device_id=active_device)
            return f"Volume set to {level}%"
        except spotipy.SpotifyException as e:
            return f"Error setting volume: {str(e)}"

    def _seek(self, position_ms: int) -> str:
        try:
            active_device = self._get_active_device()
            if not active_device:
                return "No Spotify devices available for seeking. Please open Spotify on a device and try again."
            self.spotify.seek_track(position_ms, device_id=active_device)
            return f"Seeked to {position_ms} ms."
        except spotipy.SpotifyException as e:
            return f"Error seeking track: {str(e)}"

    def _repeat(self, state: str) -> str:
        try:
            active_device = self._get_active_device()
            if not active_device:
                return "No Spotify devices available for setting repeat. Please open Spotify on a device and try again."
            self.spotify.repeat(state, device_id=active_device)
            return f"Repeat mode set to {state}."
        except spotipy.SpotifyException as e:
            return f"Error setting repeat mode: {str(e)}"

    def _shuffle(self, state: bool) -> str:
        try:
            active_device = self._get_active_device()
            if not active_device:
                return "No Spotify devices available for shuffle. Please open Spotify on a device and try again."
            self.spotify.shuffle(state, device_id=active_device)
            shuffle_state = "on" if state else "off"
            return f"Shuffle turned {shuffle_state}."
        except spotipy.SpotifyException as e:
            return f"Error setting shuffle: {str(e)}"

    def _transfer_playback(self, device_id: str, force_play: bool) -> str:
        try:
            self.spotify.transfer_playback(device_ids=[device_id], force_play=force_play)
            return f"Transferred playback to device with ID: {device_id}."
        except spotipy.SpotifyException as e:
            return f"Error transferring playback: {str(e)}"

    def _list_devices(self) -> str:
        try:
            devices = self.spotify.devices()
            if not devices.get('devices'):
                return "No Spotify devices found."
            device_list = []
            for device in devices.get('devices'):
                status = "Active" if device.get('is_active') else "Inactive"
                device_list.append(f"{device.get('name')} (ID: {device.get('id')}, {status})")
            return "Available devices:\n" + "\n".join(device_list)
        except spotipy.SpotifyException as e:
            return f"Error retrieving devices: {str(e)}"

    def _force_manual_auth(self):
        try:
            print("\n--- SPOTIFY MANUAL AUTHENTICATION ---")
            print("Opening browser for Spotify authentication...")
            cache_dir = os.path.join(os.path.expanduser("~"), ".athena", "spotify_cache")
            os.makedirs(cache_dir, exist_ok=True)
            cache_path = os.path.join(cache_dir, ".spotify_cache")
            auth_manager = SpotifyOAuth(
                client_id=SPOTIFY_CLIENT_ID,
                client_secret=SPOTIFY_CLIENT_SECRET,
                redirect_uri=SPOTIFY_REDIRECT_URI,
                scope=SPOTIFY_SCOPE,
                username=username,
                cache_path=cache_path,
                open_browser=True,
                show_dialog=True
            )
            if os.path.exists(cache_path):
                os.remove(cache_path)
            token = auth_manager.get_access_token(as_dict=False)
            self.spotify = spotipy.Spotify(auth_manager=auth_manager)
            global _spotify_client
            _spotify_client = self.spotify
            print("✓ Authentication successful!")
            return True
        except Exception as e:
            print(f"✗ Authentication failed: {str(e)}")
            return False
