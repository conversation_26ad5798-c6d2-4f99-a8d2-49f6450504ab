# ===========================================================================
# CRITICAL DEPRECATION NOTICE
# ===========================================================================
# This file is DEPRECATED and will be REMOVED in the next release.
# All functionality has been migrated to the src/models directory.
#
# Please update your imports immediately to use the new location.
# Example:
#   OLD: from src.login.models import User, APIKey
#   NEW: from src.models import User, APIKey
#
# This file currently maintains compatibility by forwarding imports,
# but this compatibility layer will be removed in a future version.
# ===========================================================================

# Logging to track usage of deprecated imports
import logging
import inspect
import sys
import warnings
import importlib

# Import bcrypt for backward compatibility
from flask_bcrypt import Bcrypt
bcrypt = Bcrypt()

# Set up tracking of model usage
logger = logging.getLogger('athena.deprecation')

# Get the caller module for better error tracking
frame = sys._getframe(1)
caller_module = frame.f_globals.get('__name__', 'unknown')
caller_function = frame.f_code.co_name if frame and frame.f_code else 'unknown'

# Log the usage of this deprecated file
logger.warning(f"Deprecated module src.login.models used directly by {caller_module}.{caller_function}")

# Issue a deprecation warning
warnings.warn(
    f"The module src.login.models is deprecated and will be removed in a future version. "
    f"Please update your imports to use src.models instead.",
    category=DeprecationWarning,
    stacklevel=2
)

# Only import models from src.models if they're not already in the caller's globals
# This helps prevent duplicate model definition issues
try:
    # Import the database instance directly
    from src.models import db
    
    # Import all model classes from the new location
    from src.models import (
        User, UserLog, APIKey, Configuration,
        LLMProviderSetting, CommandToggle, DirectConnection,
        MCPApiKey, MCPServerTemplate, Conversation, Message, LogEntry
    )
    
    # Expose all imported models to maintain backward compatibility
    __all__ = [
        'db',
        'User', 'UserLog', 'APIKey', 'Configuration',
        'LLMProviderSetting', 'CommandToggle', 'DirectConnection',
        'MCPApiKey', 'MCPServerTemplate', 'Conversation', 'Message', 'LogEntry'
    ]
    
    logger.debug(f"Successfully imported models for {caller_module}")
    
except ImportError as e:
    logger.error(f"Error importing models: {e}")
    # If imports fail, provide a meaningful error message
    warnings.warn(
        f"Failed to import models from src.models: {e}. "
        f"This might cause functionality issues.",
        category=ImportWarning,
        stacklevel=2
    )
