#!/usr/bin/env python3
"""
Codebase Structure Analyzer

This script analyzes the current structure of the Athena codebase and generates reports on:
1. File organization and folder structure
2. Configuration mechanisms and environment variables
3. Dependency usage across the codebase
4. Potential unused code and files
5. Documentation coverage

Usage:
    python codebase_analyzer.py [--output-dir OUTPUT_DIR]
"""

import os
import re
import sys
import json
import argparse
from collections import defaultdict, Counter
from datetime import datetime
import ast
import importlib.util

class CodebaseAnalyzer:
    def __init__(self, root_dir, output_dir=None):
        self.root_dir = os.path.abspath(root_dir)
        self.output_dir = output_dir or os.path.join(self.root_dir, "analysis_results")
        self.file_index = {}
        self.import_graph = defaultdict(set)
        self.env_var_usage = {}
        self.file_stats = {}
        self.folder_stats = defaultdict(int)
        self.file_types = Counter()
        self.documentation_stats = {}
        self.potential_unused = set()
        
        # Ensure output directory exists
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def scan_files(self):
        """Scan all files and build index"""
        excluded_dirs = {".git", "__pycache__", "venv", "env", ".venv", "node_modules"}
        
        for root, dirs, files in os.walk(self.root_dir):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if d not in excluded_dirs]
            
            rel_path = os.path.relpath(root, self.root_dir)
            if rel_path == ".":
                rel_path = ""
            
            # Record folder stats
            self.folder_stats[rel_path] = len(files)
            
            for file in files:
                file_path = os.path.join(root, file)
                rel_file_path = os.path.join(rel_path, file) if rel_path else file
                
                # Skip very large files
                if os.path.getsize(file_path) > 10 * 1024 * 1024:  # 10MB
                    continue
                    
                file_ext = os.path.splitext(file)[1].lower()
                self.file_types[file_ext] += 1
                
                self.file_index[rel_file_path] = {
                    "path": file_path,
                    "size": os.path.getsize(file_path),
                    "last_modified": datetime.fromtimestamp(os.path.getmtime(file_path)).isoformat(),
                    "extension": file_ext
                }
        
        print(f"Indexed {len(self.file_index)} files in {len(self.folder_stats)} directories")
    
    def analyze_python_files(self):
        """Analyze Python files for imports, environment variables, and documentation"""
        env_var_pattern = re.compile(r'os\.getenv\([\'"]([A-Za-z0-9_]+)[\'"]')
        config_pattern = re.compile(r'config\.[\'"]?([A-Za-z0-9_]+)[\'"]?')
        
        for rel_path, file_info in self.file_index.items():
            if not rel_path.endswith('.py'):
                continue
                
            try:
                with open(file_info["path"], 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Extract environment variables
                env_vars = env_var_pattern.findall(content)
                config_vars = config_pattern.findall(content)
                
                if env_vars or config_vars:
                    self.env_var_usage[rel_path] = {
                        "env_vars": env_vars,
                        "config_vars": config_vars
                    }
                
                # Parse AST for imports
                try:
                    tree = ast.parse(content)
                    imports = set()
                    
                    for node in ast.walk(tree):
                        if isinstance(node, ast.Import):
                            for name in node.names:
                                imports.add(name.name)
                        elif isinstance(node, ast.ImportFrom):
                            if node.module:
                                for name in node.names:
                                    imports.add(f"{node.module}.{name.name}")
                    
                    self.import_graph[rel_path] = imports
                    
                    # Check for docstrings
                    doc_count = 0
                    class_count = 0
                    function_count = 0
                    
                    for node in ast.walk(tree):
                        if isinstance(node, (ast.FunctionDef, ast.ClassDef, ast.Module)):
                            if ast.get_docstring(node):
                                doc_count += 1
                            
                            if isinstance(node, ast.ClassDef):
                                class_count += 1
                            elif isinstance(node, ast.FunctionDef):
                                function_count += 1
                    
                    self.documentation_stats[rel_path] = {
                        "documented_items": doc_count,
                        "classes": class_count,
                        "functions": function_count,
                        "doc_coverage": doc_count / max(1, class_count + function_count + 1)  # +1 for module
                    }
                    
                except SyntaxError:
                    print(f"Syntax error in {rel_path}, skipping AST analysis")
            
            except Exception as e:
                print(f"Error analyzing {rel_path}: {e}")
    
    def analyze_dependencies(self):
        """Analyze project dependencies from requirements.txt"""
        req_file = os.path.join(self.root_dir, "requirements.txt")
        if not os.path.exists(req_file):
            print("requirements.txt not found")
            return {}
            
        dependencies = {}
        try:
            with open(req_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        parts = line.split('==')
                        if len(parts) == 2:
                            pkg, version = parts
                            dependencies[pkg.strip()] = version.strip()
                        else:
                            dependencies[line] = "unspecified"
        except Exception as e:
            print(f"Error analyzing requirements.txt: {e}")
        
        return dependencies
    
    def find_unused_files(self):
        """Identify potentially unused files based on import graph"""
        # Build reverse import graph
        imported_by = defaultdict(set)
        module_to_file = {}
        
        # Map Python modules to file paths
        for rel_path in self.file_index:
            if rel_path.endswith('.py'):
                # Convert file path to module path
                module_path = rel_path.replace('/', '.').replace('\\', '.').replace('.py', '')
                module_to_file[module_path] = rel_path
                
                # Handle __init__.py files
                if rel_path.endswith('__init__.py'):
                    package_path = os.path.dirname(rel_path).replace('/', '.').replace('\\', '.')
                    module_to_file[package_path] = rel_path
        
        # Build reverse import graph
        for file, imports in self.import_graph.items():
            for imp in imports:
                if imp in module_to_file:
                    imported_file = module_to_file[imp]
                    imported_by[imported_file].add(file)
        
        # Find Python files not imported anywhere
        python_files = {f for f in self.file_index if f.endswith('.py')}
        entry_points = {f for f in python_files if "main.py" in f or "app.py" in f or "start_" in f}
        
        potentially_unused = python_files - entry_points
        for file in list(potentially_unused):
            if file in imported_by:
                potentially_unused.remove(file)
        
        self.potential_unused = potentially_unused
    
    def analyze_structure(self):
        """Analyze current folder structure against best practices"""
        structure_analysis = {
            "issues": [],
            "recommendations": []
        }
        
        # Check for key directories
        expected_dirs = ["src", "tests", "docs", "static", "templates"]
        for dir_name in expected_dirs:
            dir_path = os.path.join(self.root_dir, dir_name)
            if not os.path.isdir(dir_path):
                structure_analysis["issues"].append(f"Missing {dir_name} directory")
                structure_analysis["recommendations"].append(f"Create {dir_name} directory for better organization")
        
        # Check for files in root that should be in subdirectories
        for rel_path in self.file_index:
            if '/' not in rel_path and '\\' not in rel_path:  # File in root directory
                if rel_path.endswith('.py') and rel_path not in ["setup.py", "main.py", "app.py", "start_athena.py"]:
                    structure_analysis["issues"].append(f"Python file {rel_path} should not be in root directory")
                    structure_analysis["recommendations"].append(f"Move {rel_path} to appropriate module in src/")
        
        # Check for documentation
        doc_files = [f for f in self.file_index if f.endswith(('.md', '.rst', '.txt')) and 'README' in f]
        if not doc_files:
            structure_analysis["issues"].append("Missing documentation files")
            structure_analysis["recommendations"].append("Add README.md and documentation in docs/ directory")
        
        return structure_analysis
    
    def run_analysis(self):
        """Run all analysis methods and generate reports"""
        self.scan_files()
        self.analyze_python_files()
        self.find_unused_files()
        
        dependencies = self.analyze_dependencies()
        structure_analysis = self.analyze_structure()
        
        # Generate reports
        reports = {
            "file_structure": {
                "total_files": len(self.file_index),
                "file_types": dict(self.file_types),
                "folder_stats": dict(self.folder_stats)
            },
            "configuration": {
                "env_vars": sum(len(info["env_vars"]) for info in self.env_var_usage.values()),
                "config_vars": sum(len(info["config_vars"]) for info in self.env_var_usage.values()),
                "files_using_env_vars": len(self.env_var_usage)
            },
            "dependencies": {
                "total": len(dependencies),
                "details": dependencies
            },
            "documentation": {
                "total_python_files": sum(1 for f in self.file_index if f.endswith('.py')),
                "documented_files": sum(1 for stats in self.documentation_stats.values() if stats["doc_coverage"] > 0.5),
                "avg_coverage": sum(stats["doc_coverage"] for stats in self.documentation_stats.values()) / 
                                max(1, len(self.documentation_stats))
            },
            "potential_issues": {
                "unused_files": list(self.potential_unused),
                "structure_issues": structure_analysis["issues"],
                "structure_recommendations": structure_analysis["recommendations"]
            }
        }
        
        # Save main report
        with open(os.path.join(self.output_dir, "analysis_summary.json"), 'w') as f:
            json.dump(reports, f, indent=2)
        
        # Save detailed file index
        with open(os.path.join(self.output_dir, "file_index.json"), 'w') as f:
            json.dump(self.file_index, f, indent=2)
        
        # Save environment variable usage
        with open(os.path.join(self.output_dir, "env_var_usage.json"), 'w') as f:
            json.dump(self.env_var_usage, f, indent=2)
        
        # Save import graph
        with open(os.path.join(self.output_dir, "import_graph.json"), 'w') as f:
            # Convert sets to lists for JSON serialization
            serializable_graph = {k: list(v) for k, v in self.import_graph.items()}
            json.dump(serializable_graph, f, indent=2)
        
        # Generate human-readable summary
        self._generate_summary_report()
        
        print(f"Analysis complete. Results saved to {self.output_dir}")
        return reports
    
    def _generate_summary_report(self):
        """Generate human-readable summary report"""
        summary = [
            "# Athena Codebase Analysis Summary",
            f"Analysis date: {datetime.now().isoformat()}",
            "",
            "## File Structure",
            f"- Total files: {len(self.file_index)}",
            f"- Python files: {self.file_types.get('.py', 0)}",
            f"- JavaScript files: {self.file_types.get('.js', 0)}",
            f"- CSS files: {self.file_types.get('.css', 0)}",
            f"- HTML files: {self.file_types.get('.html', 0)}",
            "",
            "## Configuration",
            f"- Environment variables used: {sum(len(info['env_vars']) for info in self.env_var_usage.values())}",
            f"- Files using environment variables: {len(self.env_var_usage)}",
            "",
            "## Documentation",
            f"- Documented Python files: {sum(1 for stats in self.documentation_stats.values() if stats['doc_coverage'] > 0.5)} " +
            f"out of {sum(1 for f in self.file_index if f.endswith('.py'))}",
            f"- Average documentation coverage: {sum(stats['doc_coverage'] for stats in self.documentation_stats.values()) / max(1, len(self.documentation_stats)):.2%}",
            "",
            "## Potential Issues",
            f"- Potentially unused Python files: {len(self.potential_unused)}",
            "- Structure recommendations:",
        ]
        
        structure_analysis = self.analyze_structure()
        for rec in structure_analysis["recommendations"]:
            summary.append(f"  - {rec}")
        
        with open(os.path.join(self.output_dir, "analysis_summary.md"), 'w') as f:
            f.write("\n".join(summary))

def main():
    parser = argparse.ArgumentParser(description="Analyze Athena codebase structure")
    parser.add_argument("--root-dir", default=".", help="Root directory of the codebase")
    parser.add_argument("--output-dir", help="Directory to save analysis results")
    
    args = parser.parse_args()
    
    analyzer = CodebaseAnalyzer(args.root_dir, args.output_dir)
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
