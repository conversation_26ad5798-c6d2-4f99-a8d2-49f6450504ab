# src/api/attachments.py

"""
API endpoints for attachment handling in the Cross-Device API System.

This file implements:
- File attachment upload and download
- Attachment listing and management
- Attachment association with devices and commands
- Secure file storage and access control
"""

import os
import json
import logging
import uuid
import hashlib
from datetime import datetime, timedelta
from pathlib import Path
from werkzeug.utils import secure_filename

from flask import Blueprint, jsonify, request, current_app, send_file
from flask_login import current_user, login_required
from functools import wraps

from src.models import db, User
from src.models.device import (
    Device, Command, Attachment, get_device_or_404,
    create_attachment
)

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger("attachments_api")

# Create blueprint
attachments_bp = Blueprint("attachments", __name__, url_prefix="/api/attachments")

# Constants
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'json', 'csv', 'xlsx', 'docx', 'md', 'zip'}
MAX_CONTENT_LENGTH = 50 * 1024 * 1024  # 50MB limit

# Helper Functions

def allowed_file(filename):
    """
    Check if a file has an allowed extension.
    """
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_file_type(filename):
    """
    Determine the file type from the extension.
    """
    if '.' in filename:
        ext = filename.rsplit('.', 1)[1].lower()
        if ext in ['jpg', 'jpeg', 'png', 'gif', 'bmp']:
            return f"image/{ext}"
        elif ext == 'pdf':
            return "application/pdf"
        elif ext in ['txt', 'md']:
            return "text/plain"
        elif ext == 'json':
            return "application/json"
        elif ext == 'csv':
            return "text/csv"
        elif ext == 'xlsx':
            return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        elif ext == 'docx':
            return "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        elif ext == 'zip':
            return "application/zip"
    return "application/octet-stream"

def calculate_file_hash(file_path):
    """
    Calculate the SHA-256 hash of a file.
    """
    sha256_hash = hashlib.sha256()
    with open(file_path, "rb") as f:
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)
    return sha256_hash.hexdigest()

def get_storage_directory():
    """
    Get the directory for storing attachments.
    Create it if it doesn't exist.
    """
    storage_dir = Path(current_app.config.get('ATTACHMENT_STORAGE_PATH', os.path.join(current_app.root_path, 'attachments')))
    storage_dir.mkdir(parents=True, exist_ok=True)
    return storage_dir

def get_attachment_path(attachment):
    """
    Get the full filesystem path for an attachment.
    """
    if not attachment.storage_path:
        return None
    
    storage_dir = get_storage_directory()
    return storage_dir / attachment.storage_path

# API Endpoints

@attachments_bp.route("", methods=["POST"])
@login_required
def upload_attachment():
    """
    Upload a new attachment file.
    
    Form Data:
        file: The file to upload
        command_id (optional): ID of a command to associate with this attachment
        device_id (optional): ID of a device to associate with this attachment
        is_public (optional): Whether the attachment should be publicly accessible
        expires_in_hours (optional): Hours until attachment expires (0 for no expiration)
        
    Returns:
        JSON response with the uploaded attachment metadata
    """
    logger.info(f"Attachment upload request received from user: {current_user.id}")
    
    # Check if the post request has the file part
    if 'file' not in request.files:
        return jsonify({
            "error": "No file part in the request"
        }), 400
        
    file = request.files['file']
    
    # Check if user submitted an empty form
    if file.filename == '':
        return jsonify({
            "error": "No file selected"
        }), 400
        
    # Check if the file is allowed
    if not allowed_file(file.filename):
        return jsonify({
            "error": f"File type not allowed. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}"
        }), 400
    
    # Get optional parameters
    command_id = request.form.get("command_id")
    device_id = request.form.get("device_id")
    is_public = request.form.get("is_public", "false").lower() == "true"
    expires_in_hours = int(request.form.get("expires_in_hours", "24"))
    
    # Validate command_id if provided
    if command_id:
        command = Command.query.filter_by(id=command_id, user_id=current_user.id).first()
        if not command:
            return jsonify({
                "error": f"Command with ID {command_id} not found"
            }), 404
    
    # Validate device_id if provided
    if device_id:
        device = Device.query.filter_by(id=device_id, user_id=current_user.id).first()
        if not device:
            return jsonify({
                "error": f"Device with ID {device_id} not found"
            }), 404
    
    try:
        # Secure the filename and create a unique path
        filename = secure_filename(file.filename)
        unique_id = str(uuid.uuid4())
        storage_path = f"{current_user.id}/{unique_id}/{filename}"
        
        # Calculate the full path
        storage_dir = get_storage_directory()
        full_path = storage_dir / f"{current_user.id}" / unique_id
        full_path.mkdir(parents=True, exist_ok=True)
        
        file_path = full_path / filename
        
        # Save the file
        file.save(file_path)
        
        # Calculate file size and hash
        file_size = os.path.getsize(file_path)
        file_hash = calculate_file_hash(file_path)
        
        # Create attachment record
        attachment = create_attachment(
            user_id=current_user.id,
            filename=filename,
            file_type=get_file_type(filename),
            file_size=file_size,
            file_hash=file_hash,
            storage_path=storage_path,
            command_id=command_id,
            device_id=device_id,
            is_public=is_public,
            expires_in_hours=expires_in_hours
        )
        
        # Update status
        attachment.status = "complete"
        attachment.transfer_progress = 100
        
        db.session.commit()
        
        logger.info(f"Attachment uploaded successfully: {attachment.attachment_uuid}")
        
        return jsonify({
            "attachment": attachment.to_dict(),
            "message": "File uploaded successfully"
        }), 201
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error uploading attachment: {str(e)}")
        return jsonify({
            "error": f"Failed to upload attachment: {str(e)}"
        }), 500

@attachments_bp.route("", methods=["GET"])
@login_required
def list_attachments():
    """
    Get a list of attachments for the current user.
    
    Query Parameters:
        command_id (optional): Filter by command ID
        device_id (optional): Filter by device ID
        filename (optional): Filter by filename (partial match)
        limit (optional): Maximum number of attachments to return (default: 20)
        
    Returns:
        JSON response with list of attachments
    """
    command_id = request.args.get("command_id")
    device_id = request.args.get("device_id")
    filename = request.args.get("filename")
    limit = int(request.args.get("limit", 20))
    
    # Build query
    query = Attachment.query.filter_by(user_id=current_user.id)
    
    # Apply filters
    if command_id:
        query = query.filter_by(command_id=command_id)
        
    if device_id:
        query = query.filter_by(device_id=device_id)
        
    if filename:
        query = query.filter(Attachment.filename.ilike(f"%{filename}%"))
    
    # Handle expired attachments
    current_time = datetime.utcnow()
    expired_attachments = Attachment.query.filter(
        Attachment.user_id == current_user.id,
        Attachment.expires_at <= current_time
    ).all()
    
    # Delete expired attachments
    for attachment in expired_attachments:
        try:
            # Delete the file from storage
            attachment_path = get_attachment_path(attachment)
            if attachment_path and attachment_path.exists():
                attachment_path.unlink()
                
                # Try to remove parent directories if empty
                parent_dir = attachment_path.parent
                if parent_dir.exists() and not any(parent_dir.iterdir()):
                    parent_dir.rmdir()
                    
                    # Try to remove user directory if empty
                    user_dir = parent_dir.parent
                    if user_dir.exists() and not any(user_dir.iterdir()):
                        user_dir.rmdir()
            
            # Delete the record
            db.session.delete(attachment)
        except Exception as e:
            logger.error(f"Error deleting expired attachment {attachment.attachment_uuid}: {str(e)}")
    
    db.session.commit()
    
    # Order by most recently created
    query = query.order_by(Attachment.created_at.desc())
    
    # Apply limit
    if limit > 0:
        query = query.limit(limit)
        
    attachments = query.all()
    
    return jsonify({
        "attachments": [attachment.to_dict() for attachment in attachments],
        "count": len(attachments)
    })

@attachments_bp.route("/<string:attachment_uuid>", methods=["GET"])
def download_attachment(attachment_uuid):
    """
    Download an attachment file.
    
    Path Parameters:
        attachment_uuid (str): UUID of the attachment
        
    Returns:
        File download response
    """
    # Check if the attachment exists
    attachment = Attachment.query.filter_by(attachment_uuid=attachment_uuid).first_or_404(
        description=f"Attachment with UUID {attachment_uuid} not found"
    )
    
    # Check if attachment is public or if user is authenticated and owns it
    if not attachment.is_public:
        if not (current_user.is_authenticated and current_user.id == attachment.user_id):
            return jsonify({
                "error": "Access denied. This attachment is not public."
            }), 403
    
    # Check if attachment has expired
    if attachment.expires_at and datetime.utcnow() > attachment.expires_at:
        return jsonify({
            "error": "Attachment has expired"
        }), 404
    
    # Get the file path
    attachment_path = get_attachment_path(attachment)
    if not attachment_path or not attachment_path.exists():
        return jsonify({
            "error": "Attachment file not found in storage"
        }), 404
    
    # Send the file
    try:
        return send_file(
            attachment_path,
            mimetype=attachment.file_type or "application/octet-stream",
            as_attachment=True,
            download_name=attachment.filename
        )
    except Exception as e:
        logger.error(f"Error downloading attachment {attachment_uuid}: {str(e)}")
        return jsonify({
            "error": f"Failed to download attachment: {str(e)}"
        }), 500

@attachments_bp.route("/<string:attachment_uuid>", methods=["DELETE"])
@login_required
def delete_attachment(attachment_uuid):
    """
    Delete an attachment.
    
    Path Parameters:
        attachment_uuid (str): UUID of the attachment to delete
        
    Returns:
        JSON response confirming deletion
    """
    # Check if the attachment exists and belongs to user
    attachment = Attachment.query.filter_by(
        attachment_uuid=attachment_uuid,
        user_id=current_user.id
    ).first_or_404(description=f"Attachment with UUID {attachment_uuid} not found")
    
    try:
        # Delete the file from storage
        attachment_path = get_attachment_path(attachment)
        if attachment_path and attachment_path.exists():
            attachment_path.unlink()
            
            # Try to remove parent directories if empty
            parent_dir = attachment_path.parent
            if parent_dir.exists() and not any(parent_dir.iterdir()):
                parent_dir.rmdir()
                
                # Try to remove user directory if empty
                user_dir = parent_dir.parent
                if user_dir.exists() and not any(user_dir.iterdir()):
                    user_dir.rmdir()
        
        # Delete the record
        db.session.delete(attachment)
        db.session.commit()
        
        logger.info(f"Attachment deleted: {attachment_uuid}")
        
        return jsonify({
            "message": "Attachment deleted successfully",
            "attachment_uuid": attachment_uuid
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error deleting attachment: {str(e)}")
        return jsonify({
            "error": f"Failed to delete attachment: {str(e)}"
        }), 500

@attachments_bp.route("/<string:attachment_uuid>/info", methods=["GET"])
def get_attachment_info(attachment_uuid):
    """
    Get information about a specific attachment.
    
    Path Parameters:
        attachment_uuid (str): UUID of the attachment
        
    Returns:
        JSON response with attachment details
    """
    # Check if the attachment exists
    attachment = Attachment.query.filter_by(
        attachment_uuid=attachment_uuid
    ).first_or_404(description=f"Attachment with UUID {attachment_uuid} not found")
    
    # Check if attachment is public or if user is authenticated and owns it
    if not attachment.is_public:
        if not (current_user.is_authenticated and current_user.id == attachment.user_id):
            return jsonify({
                "error": "Access denied. This attachment is not public."
            }), 403
    
    return jsonify(attachment.to_dict())

@attachments_bp.route("/<string:attachment_uuid>", methods=["PUT"])
@login_required
def update_attachment(attachment_uuid):
    """
    Update attachment metadata.
    
    Path Parameters:
        attachment_uuid (str): UUID of the attachment
        
    Request Body:
        filename (str, optional): New filename
        is_public (bool, optional): Whether the attachment should be publicly accessible
        expires_in_hours (int, optional): Hours until attachment expires (0 for no expiration)
        
    Returns:
        JSON response with the updated attachment
    """
    data = request.json or {}
    
    if not data:
        return jsonify({
            "error": "No update data provided"
        }), 400
    
    # Check if the attachment exists and belongs to user
    attachment = Attachment.query.filter_by(
        attachment_uuid=attachment_uuid,
        user_id=current_user.id
    ).first_or_404(description=f"Attachment with UUID {attachment_uuid} not found")
    
    # Update the fields
    if "filename" in data:
        # Validate and secure the filename
        if not data["filename"]:
            return jsonify({
                "error": "Filename cannot be empty"
            }), 400
            
        new_filename = secure_filename(data["filename"])
        
        # Check if the extension is allowed
        if not allowed_file(new_filename):
            return jsonify({
                "error": f"File type not allowed. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}"
            }), 400
            
        attachment.filename = new_filename
        attachment.file_type = get_file_type(new_filename)
    
    if "is_public" in data:
        attachment.is_public = bool(data["is_public"])
        
    if "expires_in_hours" in data:
        try:
            expires_in_hours = int(data["expires_in_hours"])
            if expires_in_hours < 0:
                return jsonify({
                    "error": "expires_in_hours must be a non-negative integer"
                }), 400
                
            if expires_in_hours > 0:
                attachment.expires_at = datetime.utcnow() + timedelta(hours=expires_in_hours)
            else:
                attachment.expires_at = None
                
        except (ValueError, TypeError):
            return jsonify({
                "error": "expires_in_hours must be an integer"
            }), 400
    
    try:
        db.session.commit()
        
        logger.info(f"Attachment updated: {attachment_uuid}")
        
        return jsonify({
            "attachment": attachment.to_dict(),
            "message": "Attachment updated successfully"
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error updating attachment: {str(e)}")
        return jsonify({
            "error": f"Failed to update attachment: {str(e)}"
        }), 500

@attachments_bp.route("/commands/<int:command_id>", methods=["GET"])
@login_required
def get_command_attachments(command_id):
    """
    Get attachments associated with a specific command.
    
    Path Parameters:
        command_id (int): ID of the command
        
    Returns:
        JSON response with list of attachments
    """
    # Check if the command exists and belongs to user
    command = Command.query.filter_by(
        id=command_id,
        user_id=current_user.id
    ).first_or_404(description=f"Command with ID {command_id} not found")
    
    # Get attachments
    attachments = Attachment.query.filter_by(
        command_id=command_id
    ).order_by(Attachment.created_at.desc()).all()
    
    return jsonify({
        "attachments": [attachment.to_dict() for attachment in attachments],
        "count": len(attachments),
        "command": command.to_dict()
    })

@attachments_bp.route("/devices/<string:device_uuid>", methods=["GET"])
@login_required
def get_device_attachments(device_uuid):
    """
    Get attachments associated with a specific device.
    
    Path Parameters:
        device_uuid (str): UUID of the device
        
    Returns:
        JSON response with list of attachments
    """
    # Check if the device exists and belongs to user
    device = get_device_or_404(device_uuid, current_user.id)
    
    # Get attachments
    attachments = Attachment.query.filter_by(
        device_id=device.id
    ).order_by(Attachment.created_at.desc()).all()
    
    return jsonify({
        "attachments": [attachment.to_dict() for attachment in attachments],
        "count": len(attachments),
        "device": device.to_dict()
    })
