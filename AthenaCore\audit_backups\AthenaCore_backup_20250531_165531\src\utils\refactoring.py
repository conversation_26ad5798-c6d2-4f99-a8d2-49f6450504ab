"""
Refactoring utilities for Athena Core.

This module provides helper functions for the refactoring process, including:
- Forward compatibility imports
- Deprecation warnings
- Migration helpers
"""

import functools
import inspect
import warnings
import logging
from typing import Any, Callable, Dict, List, Optional, Type, Union

logger = logging.getLogger(__name__)

def deprecated(reason: str) -> Callable:
    """
    Decorator to mark functions or classes as deprecated.
    
    This will issue a deprecation warning when the decorated function is called
    or the decorated class is instantiated.
    
    Args:
        reason: Explanation of why the function/class is deprecated and what to use instead
        
    Returns:
        Decorated function or class
    """
    def decorator(func_or_class):
        if inspect.isclass(func_or_class):
            original_init = func_or_class.__init__
            
            @functools.wraps(original_init)
            def new_init(self, *args, **kwargs):
                warnings.warn(
                    f"{func_or_class.__name__} is deprecated: {reason}",
                    category=DeprecationWarning,
                    stacklevel=2
                )
                original_init(self, *args, **kwargs)
            
            func_or_class.__init__ = new_init
            return func_or_class
        else:
            @functools.wraps(func_or_class)
            def wrapper(*args, **kwargs):
                warnings.warn(
                    f"{func_or_class.__name__} is deprecated: {reason}",
                    category=DeprecationWarning,
                    stacklevel=2
                )
                return func_or_class(*args, **kwargs)
            return wrapper
    
    return decorator

def moved_module(new_module_path: str) -> Callable:
    """
    Decorator to mark functions or classes that have been moved to a new module.
    
    This will issue a warning when the decorated function is called or the
    decorated class is instantiated, indicating the new location.
    
    Args:
        new_module_path: The new import path for the function or class
        
    Returns:
        Decorated function or class
    """
    def decorator(func_or_class):
        if inspect.isclass(func_or_class):
            original_init = func_or_class.__init__
            
            @functools.wraps(original_init)
            def new_init(self, *args, **kwargs):
                warnings.warn(
                    f"{func_or_class.__name__} has been moved to {new_module_path}. "
                    f"Please update your imports.",
                    category=FutureWarning,
                    stacklevel=2
                )
                original_init(self, *args, **kwargs)
            
            func_or_class.__init__ = new_init
            return func_or_class
        else:
            @functools.wraps(func_or_class)
            def wrapper(*args, **kwargs):
                warnings.warn(
                    f"{func_or_class.__name__} has been moved to {new_module_path}. "
                    f"Please update your imports.",
                    category=FutureWarning,
                    stacklevel=2
                )
                return func_or_class(*args, **kwargs)
            return wrapper
    
    return decorator

def forward_compatibility_import(original_module_name: str, new_module_name: str, 
                                 names: List[str]) -> None:
    """
    Set up forward compatibility imports.
    
    This function should be called at the end of a module that has had its contents
    moved elsewhere. It will import the specified names from the new module and
    add them to the original module's global namespace.
    
    Args:
        original_module_name: The name of the original module
        new_module_name: The name of the new module where items were moved
        names: List of names to import from the new module
    """
    import importlib
    import sys
    
    # Get the original module
    original_module = sys.modules[original_module_name]
    
    # Import the new module
    new_module = importlib.import_module(new_module_name)
    
    # Add each name from the new module to the original module
    for name in names:
        if hasattr(new_module, name):
            # Apply the moved_module decorator if it's a function or class
            item = getattr(new_module, name)
            if inspect.isfunction(item) or inspect.isclass(item):
                item = moved_module(new_module_name)(item)
            
            setattr(original_module, name, item)
        else:
            logger.warning(f"Name '{name}' not found in {new_module_name}")

def create_proxy_module(original_path: str, new_path: str, names: List[str]) -> None:
    """
    Create a proxy module that forwards imports to the new location.
    
    This function creates a .py file at the original path that imports and
    re-exports names from the new module path, with deprecation warnings.
    
    Args:
        original_path: Path to create the proxy module at
        new_path: Import path for the new module location
        names: List of names to import from the new module
    """
    import os
    
    # Ensure the directory exists
    os.makedirs(os.path.dirname(original_path), exist_ok=True)
    
    # Generate the proxy module content
    content = [
        f'"""',
        f'This module has been moved to {new_path}.',
        f'This is a compatibility module that will be removed in a future version.',
        f'Please update your imports to use {new_path} directly.',
        f'"""',
        '',
        'import warnings',
        '',
        f'warnings.warn(',
        f'    "The module {os.path.basename(original_path)} has been moved to {new_path}. '
        f'Please update your imports.",',
        f'    category=FutureWarning,',
        f'    stacklevel=2',
        f')',
        '',
        f'from {new_path} import {", ".join(names)}',
        '',
        f'__all__ = {names!r}',
        ''
    ]
    
    # Write the proxy module
    with open(original_path, 'w') as f:
        f.write('\n'.join(content))
    
    logger.info(f"Created proxy module at {original_path} -> {new_path}")

def log_migration(original_path: str, new_path: str) -> None:
    """
    Log a migration action for documentation purposes.
    
    Args:
        original_path: Original path of the file/module
        new_path: New path where it was moved
    """
    import os
    from datetime import datetime
    
    # Ensure the migrations log directory exists
    log_dir = os.path.join('docs', 'refactoring')
    os.makedirs(log_dir, exist_ok=True)
    
    # Append to the migrations log
    log_path = os.path.join(log_dir, 'migrations.log')
    timestamp = datetime.now().isoformat()
    
    with open(log_path, 'a') as f:
        f.write(f"{timestamp} | {original_path} -> {new_path}\n")
