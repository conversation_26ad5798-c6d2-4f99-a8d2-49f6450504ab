"""
Test Knowledge Base API endpoints directly.

This script tests the Knowledge Base API endpoints to verify they're working correctly.
"""

import requests
import json
import os
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("KB_Tester")

# Base URL for API requests
BASE_URL = "http://localhost:5000"

def test_add_text():
    """Test adding text to the Knowledge Base."""
    logger.info("Testing add text endpoint...")
    
    url = f"{BASE_URL}/api/knowledge/add/text"
    
    # Payload for text addition
    payload = {
        "text": "This is a test document for the Athena Knowledge Base.",
        "metadata": {
            "title": "Test Document",
            "category": "test",
            "source": "test_script"
        }
    }
    
    # Make the request
    logger.info(f"Sending POST request to {url}")
    logger.info(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        # Use requests session to maintain cookies for authentication
        session = requests.Session()
        # First get the login page to get cookies
        session.get(f"{BASE_URL}/login")
        
        # Log in to get authenticated
        login_data = {
            "username": "admin",  # Change to your username if different
            "password": "admin"   # Change to your password if different
        }
        session.post(f"{BASE_URL}/login", data=login_data)
        
        # Now make the actual API request
        response = session.post(url, json=payload)
        
        # Log response details
        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            logger.info(f"Response data: {json.dumps(response_data, indent=2)}")
            
            if response.status_code == 200 and response_data.get("status") == "success":
                logger.info("✅ Successfully added text to Knowledge Base!")
            else:
                logger.error("❌ Failed to add text to Knowledge Base!")
        except Exception as e:
            logger.error(f"Failed to parse response as JSON: {e}")
            logger.error(f"Raw response: {response.text}")
    
    except Exception as e:
        logger.error(f"Request failed: {e}")

def test_add_url():
    """Test adding a URL to the Knowledge Base."""
    logger.info("Testing add URL endpoint...")
    
    url = f"{BASE_URL}/api/knowledge/add/url"
    
    # Payload for URL addition
    payload = {
        "url": "https://en.wikipedia.org/wiki/Artificial_intelligence",
        "category": "reference"
    }
    
    # Make the request
    logger.info(f"Sending POST request to {url}")
    logger.info(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        # Use requests session to maintain cookies for authentication
        session = requests.Session()
        # First get the login page to get cookies
        session.get(f"{BASE_URL}/login")
        
        # Log in to get authenticated
        login_data = {
            "username": "admin",  # Change to your username if different
            "password": "admin"   # Change to your password if different
        }
        session.post(f"{BASE_URL}/login", data=login_data)
        
        # Now make the actual API request
        response = session.post(url, json=payload)
        
        # Log response details
        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            logger.info(f"Response data: {json.dumps(response_data, indent=2)}")
            
            if response.status_code == 200 and response_data.get("status") == "success":
                logger.info("✅ Successfully added URL to Knowledge Base!")
            else:
                logger.error("❌ Failed to add URL to Knowledge Base!")
        except Exception as e:
            logger.error(f"Failed to parse response as JSON: {e}")
            logger.error(f"Raw response: {response.text}")
    
    except Exception as e:
        logger.error(f"Request failed: {e}")

def test_add_file():
    """Test adding a file to the Knowledge Base."""
    logger.info("Testing add file endpoint...")
    
    url = f"{BASE_URL}/api/knowledge/add/file"
    
    # Create a test file
    test_file_path = "test_kb_file.txt"
    with open(test_file_path, "w") as f:
        f.write("This is a test file for the Athena Knowledge Base.\n")
        f.write("It contains multiple lines to test document processing.\n")
        f.write("The system should split this into chunks and index them correctly.")
    
    # Make the request
    logger.info(f"Sending POST request to {url}")
    
    try:
        # Use requests session to maintain cookies for authentication
        session = requests.Session()
        # First get the login page to get cookies
        session.get(f"{BASE_URL}/login")
        
        # Log in to get authenticated
        login_data = {
            "username": "admin",  # Change to your username if different
            "password": "admin"   # Change to your password if different
        }
        session.post(f"{BASE_URL}/login", data=login_data)
        
        # Now make the actual API request
        files = {"file": open(test_file_path, "rb")}
        data = {"category": "test_file"}
        
        response = session.post(url, files=files, data=data)
        
        # Close the file
        files["file"].close()
        
        # Log response details
        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            logger.info(f"Response data: {json.dumps(response_data, indent=2)}")
            
            if response.status_code == 200 and response_data.get("status") == "success":
                logger.info("✅ Successfully added file to Knowledge Base!")
            else:
                logger.error("❌ Failed to add file to Knowledge Base!")
        except Exception as e:
            logger.error(f"Failed to parse response as JSON: {e}")
            logger.error(f"Raw response: {response.text}")
    
    except Exception as e:
        logger.error(f"Request failed: {e}")
    
    # Clean up the test file
    try:
        os.remove(test_file_path)
        logger.info(f"Cleaned up test file: {test_file_path}")
    except Exception as e:
        logger.error(f"Failed to clean up test file: {e}")

def test_get_stats():
    """Test getting Knowledge Base statistics."""
    logger.info("Testing get stats endpoint...")
    
    url = f"{BASE_URL}/api/knowledge/stats"
    
    # Make the request
    logger.info(f"Sending GET request to {url}")
    
    try:
        # Use requests session to maintain cookies for authentication
        session = requests.Session()
        # First get the login page to get cookies
        session.get(f"{BASE_URL}/login")
        
        # Log in to get authenticated
        login_data = {
            "username": "admin",  # Change to your username if different
            "password": "admin"   # Change to your password if different
        }
        session.post(f"{BASE_URL}/login", data=login_data)
        
        # Now make the actual API request
        response = session.get(url)
        
        # Log response details
        logger.info(f"Response status code: {response.status_code}")
        
        try:
            response_data = response.json()
            logger.info(f"Response data: {json.dumps(response_data, indent=2)}")
            
            if response.status_code == 200 and response_data.get("status") == "success":
                logger.info("✅ Successfully retrieved Knowledge Base stats!")
            else:
                logger.error("❌ Failed to retrieve Knowledge Base stats!")
        except Exception as e:
            logger.error(f"Failed to parse response as JSON: {e}")
            logger.error(f"Raw response: {response.text}")
    
    except Exception as e:
        logger.error(f"Request failed: {e}")

def run_all_tests():
    """Run all tests in sequence."""
    logger.info("=== Starting Knowledge Base API Tests ===")
    
    # First test getting stats
    test_get_stats()
    
    # Test adding text
    test_add_text()
    
    # Test adding a URL
    test_add_url()
    
    # Test adding a file
    test_add_file()
    
    # Test getting stats again to see if anything was added
    test_get_stats()
    
    logger.info("=== Knowledge Base API Tests Complete ===")

if __name__ == "__main__":
    run_all_tests()
