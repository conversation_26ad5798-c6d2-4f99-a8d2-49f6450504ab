"""
Migration v1: Add Configurations Table

Create configurations table for database-driven configuration system.

Generated: 2025-05-22
"""

# Migration version and metadata
version = 1
description = "Create configurations table for database-driven configuration system"
dependencies = []  # List of migration versions this depends on

def upgrade(db):
    """
    Apply the migration.
    
    Args:
        db: SQLAlchemy database instance
    """
    # Create the configurations table
    db.engine.execute("""
        CREATE TABLE IF NOT EXISTS configurations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key VARCHAR(255) NOT NULL UNIQUE,
            value TEXT,
            value_type VARCHAR(50) NOT NULL DEFAULT 'string',
            description TEXT,
            is_secret BOOLEAN NOT NULL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # Create index on key column for faster lookups
    db.engine.execute("""
        CREATE INDEX IF NOT EXISTS idx_configurations_key
        ON configurations (key)
    """)
    
    # Add initial configurations
    initial_configs = [
        ("app_name", "Athena Core", "string", "Application name", 0),
        ("debug_mode", "false", "boolean", "Enable debug mode", 0),
        ("log_level", "info", "string", "Logging level", 0),
        ("api_rate_limit", "100", "integer", "API rate limit per hour", 0),
        ("enable_analytics", "true", "boolean", "Enable analytics collection", 0)
    ]
    
    for config in initial_configs:
        key, value, value_type, description, is_secret = config
        # Format for SQLite parameterized query
        db.engine.execute(
            """
            INSERT OR IGNORE INTO configurations 
            (key, value, value_type, description, is_secret)
            VALUES (?, ?, ?, ?, ?)
            """,
            (key, value, value_type, description, is_secret)
        )

def downgrade(db):
    """
    Revert the migration.
    
    Args:
        db: SQLAlchemy database instance
    """
    # Drop the configurations table
    db.engine.execute("DROP TABLE IF EXISTS configurations")
