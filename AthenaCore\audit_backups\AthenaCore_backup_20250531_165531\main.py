# main.py
# Suppress PyTorch deprecation warnings about _register_pytree_node
import warnings
warnings.filterwarnings('ignore', message='.*_register_pytree_node.*is deprecated.*')

# Add Path to builtins to make it available to all modules
from pathlib import Path
import builtins
builtins.Path = Path
import time
import os
import sys

from flask import Flask, send_from_directory, request, render_template
from flask_migrate import Migrate
from flask_socketio import SocketIO

import src.api as api
# Import components directly instead of using the deprecated src.login module
from src.models import db
from flask_bcrypt import Bcrypt
bcrypt = Bcrypt()
from src.utils.auth_extensions import login_manager
from src.controllers.auth_controller import auth_bp
import src.services as services
from src.mcp.api import mcp_bp
from src.mcp.api_endpoints import mcp_server_bp
from src.api.conversations import conversations_bp
from src.api.conversation_compat import conversation_compat_bp
from src.api.specific_conversation import specific_conv_bp
from src.api.error_handlers import error_handlers_bp
from src.routes.knowledge_routes import knowledge_bp
from src.api.demo import demo_bp

# Import configuration and models
import src.utils as utils

# Load configuration
config = utils.AthenaConfig.load()

# Initialize Flask app
app = Flask(__name__)

# Initialize SocketIO
socketio = SocketIO(app, cors_allowed_origins="*")

# Secret key for sessions
app.config['SECRET_KEY'] = config.secret_key

# Database URI - Use absolute path for reliability
project_root = Path(__file__).resolve().parent.parent
instance_dir = project_root / 'instance'
instance_dir.mkdir(exist_ok=True)  # Ensure instance directory exists
db_path = instance_dir / 'athena.db'
db_uri = f"sqlite:///{db_path.absolute()}"

# Override any relative database paths in configuration
app.config['SQLALCHEMY_DATABASE_URI'] = db_uri
os.environ['DATABASE_URI'] = db_uri
os.environ['SQLALCHEMY_DATABASE_URI'] = db_uri
print(f"Using database at: {db_path.absolute()}")

# Update config for consistency
config.db_uri = db_uri
# Disable track modifications
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
# Add cache busting for development
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0  # Disable caching for all static files

# Set up configuration for attachments and tasks
app.config['ATTACHMENT_STORAGE_PATH'] = config.attachment_storage_path if hasattr(config, 'attachment_storage_path') else Path(app.root_path) / 'attachments'
app.config['MAX_CONCURRENT_TASKS'] = config.max_concurrent_tasks if hasattr(config, 'max_concurrent_tasks') else 5
app.config['SCHEDULER_CHECK_INTERVAL'] = config.scheduler_check_interval if hasattr(config, 'scheduler_check_interval') else 60

# Generate a unique timestamp for cache busting
app.config['STATIC_VERSION'] = int(time.time())

# Override static file serving with cache busting
@app.route('/static/<path:filename>')
def serve_static(filename):
    # Add cache busting headers
    response = send_from_directory(app.static_folder, filename)
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response

# Add template context processor for cache busting
@app.context_processor
def inject_static_version():
    return {'static_version': app.config['STATIC_VERSION']}

# Initialize SQLAlchemy
db.init_app(app)
# Initialize Bcrypt for password hashing
bcrypt.init_app(app)

# Initialize Flask-Migrate
migrate = Migrate(app, db)

# Import and initialize login manager (after app creation)
login_manager.init_app(app)
login_manager.login_view = "auth.login"
login_manager.login_message_category = "info"

# Register blueprints
app.register_blueprint(auth_bp)
app.register_blueprint(api.core_bp)
app.register_blueprint(api.api_v1_bp)
app.register_blueprint(api.tasks_bp)
app.register_blueprint(api.attachments_bp)
app.register_blueprint(api.search_bp)
app.register_blueprint(api.system_health_bp)
app.register_blueprint(mcp_bp)
app.register_blueprint(mcp_server_bp)
# Register the error handler first so it can catch all errors
app.register_blueprint(error_handlers_bp)
# Register the specific conversation handler next so it takes precedence for specific IDs
app.register_blueprint(specific_conv_bp)
app.register_blueprint(conversations_bp)
app.register_blueprint(conversation_compat_bp)
app.register_blueprint(demo_bp)

# Register admin/debug blueprint
from src.admin import debug_bp
app.register_blueprint(debug_bp)

# Register the knowledge blueprint
app.register_blueprint(knowledge_bp)

# Register the main blueprint with routes for dashboard and index
from src.controllers.main_controller import main_bp
app.register_blueprint(main_bp)

# Initialize services
services.init_app(app)

# Initialize SocketIO service
services.init_socketio(socketio)

# Ensure logs directory exists
Path("logs").mkdir(exist_ok=True)
# Ensure attachments directory exists
Path(app.config["ATTACHMENT_STORAGE_PATH"]).mkdir(exist_ok=True, parents=True)

# Ensure upload folder exists
app.config["UPLOAD_FOLDER"] = os.path.join(app.root_path, "static", "uploads", "documents")
os.makedirs(app.config["UPLOAD_FOLDER"], exist_ok=True)

# Create database tables if they don't exist
with app.app_context():
    db.create_all()
    
    # Import and create default configurations
    from src.db.default_config import create_default_openai_connection
    create_default_openai_connection()
    
    # Only fix URLs without modifying the schema
    from src.models import DirectConnection
    try:
        # Only fix URLs ending with /chat/completion (missing 's')
        connections = DirectConnection.query.all()
        for conn in connections:
            if conn.url and conn.url.endswith('/chat/completion'):
                conn.url = conn.url + 's'
                print(f"Fixed URL for connection {conn.id}: {conn.name}")
        # Commit the URL fixes if any
        db.session.commit()
        print(f"Checked {len(connections)} connection(s) for URL fixes")
    except Exception as e:
        db.session.rollback()
        print(f"Error checking connections: {e}")

# Socket.IO event handlers
@socketio.on('connect')
def handle_connect():
    print('Client connected to Socket.IO')
    return True  # Explicitly allow the connection

@socketio.on('disconnect')
def handle_disconnect():
    print('Client disconnected from Socket.IO')

@socketio.on('join')
def on_join(data):
    room = data.get('room')
    if room:
        from flask_socketio import join_room
        join_room(room)
        print(f'Client joined room: {room}')

if __name__ == "__main__":
    socketio.run(app, host="0.0.0.0", port=5000, debug=True, allow_unsafe_werkzeug=True)
