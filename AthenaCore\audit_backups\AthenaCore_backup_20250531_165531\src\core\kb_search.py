"""
Knowledge Base Search Command Handler
This module adds a special /kb or /search command to Athena
"""

import logging
import re
from datetime import datetime
from typing import List, Dict, Any
from src.core.knowledge_db import kb  # Import the shared instance
from src.core.kb_context import store_document_context

logger = logging.getLogger(__name__)


def handle_kb_search_command(athena_instance, conversation_id, input_text):
    """Handles /kb, /knowledge, or /search commands to list or search knowledge base documents"""

    input_lower = input_text.lower().strip()

    if input_lower == "/kb" or input_lower == "/knowledge" or input_lower == "/search":
        # List all documents if no search term
        try:
            docs = kb.search_documents()

            # Store docs in context for follow-up questions
            store_document_context(conversation_id, docs)
            logger.info(
                f"Stored {len(docs)} documents in context for conversation {conversation_id}"
            )

            return format_kb_results(docs)
        except Exception as e:
            logger.error(f"Error retrieving documents: {e}")
            return "<div class='kb-search-result'>Error retrieving knowledge base documents. Please try again later.</div>"

    # Check if it's a knowledge base search command
    if (
        input_lower.startswith("/kb ")
        or input_lower.startswith("/knowledge ")
        or input_lower.startswith("/search ")
    ):
        # Extract search term
        if input_lower.startswith("/kb "):
            search_term = input_text[3:].strip()
        elif input_lower.startswith("/knowledge "):
            search_term = input_text[10:].strip()
        else:
            search_term = input_text[7:].strip()

        if not search_term:
            return "<div class='kb-search-result'>Please provide a search term after the command.</div>"

        # Search for documents
        try:
            logger.info(f"Searching knowledge base for term: '{search_term}'")

            # Directly query for relevant chunks using the new method
            limit = 5 # Number of chunks to retrieve
            retrieved_chunks = kb.query_relevant_chunks(search_term, n_results=limit)
            logger.info(f"Retrieved {len(retrieved_chunks)} chunks from knowledge base for '{search_term}'")

            # Prepare the results in the format expected by downstream functions
            filtered_docs = retrieved_chunks # Pass the chunks directly

            # Store the retrieved chunks in context for follow-up questions
            store_document_context(conversation_id, filtered_docs)
            logger.info(
                f"Stored {len(filtered_docs)} retrieved chunks in context for conversation {conversation_id}"
            )

            # Return formatted results (format_kb_results needs to handle chunks)
            return format_kb_results(filtered_docs, is_chunk_search=True)
        except Exception as e:
            logger.error(f"Error searching knowledge base: {e}")
            import traceback

            logger.error(traceback.format_exc())
            return f"<div class='kb-search-result'>Error searching knowledge base: {str(e)}</div>"

    # If we get here, it wasn't a KB command
    return None


def format_kb_results(docs, is_chunk_search=False):
    """Format documents or chunks for display in the UI"""
    if is_chunk_search:
        response = "## Relevant Knowledge Snippets\n\n"
    else:
        response = "## Knowledge Base Documents\n\n"

    # Get document type icons
    def get_type_icon(doc_type):
        icons = {
            "text": "TEXT",
            "pdf": "PDF",
            "image": "IMAGE",
            "markdown": "MD",
            "html": "HTML",
            "code": "CODE",
        }
        return icons.get(doc_type.lower(), "DOC")

    if not docs or len(docs) == 0:
        response += "No documents found matching your search criteria."
        return f"<div class='kb-search-result'>\n{response}\n</div>"

    for i, doc in enumerate(docs):
        # Handle different document formats
        if isinstance(doc, dict):
            # Format might be from ChromaDB query, API or direct memory storage
            if "metadata" in doc:
                # Standard ChromaDB query format
                metadata = doc.get("metadata", {})
                content = doc.get("content", "")
            elif "documents" in doc and isinstance(doc["documents"], list):
                # ChromaDB result format
                metadata = doc.get("metadatas", [{}])[0] if "metadatas" in doc else {}
                content = doc.get("documents", [""])[0]
            else:
                # Direct memory storage format
                metadata = doc
                content = doc.get("content", "")
        else:
            # Fallback for other formats
            metadata = {}
            content = str(doc)[:100] + "..."

        # Get document properties with safe defaults
        title = metadata.get("title", doc.get("title", f"Document {i + 1}"))
        doc_type = metadata.get("type", doc.get("type", "text"))
        doc_id = metadata.get("id", doc.get("id", f"unknown-{i}"))
        created_at = metadata.get("timestamp", metadata.get("created_at", ""))

        # Format creation date if available
        date_str = ""
        if created_at:
            try:
                # Try to format the date nicely
                dt = datetime.fromisoformat(created_at.replace("Z", "+00:00"))
                date_str = f"📅 Added: {dt.strftime('%Y-%m-%d')}"
            except (ValueError, TypeError) as e:
                logger.debug(f"Could not parse document date: {e}")

        # Get icon for document type
        type_icon = get_type_icon(doc_type)

        # Build formatted response with nicer card-like formatting
        response += f"### {type_icon} **{i + 1}. {title}**\n\n"
        response += f"**Type:** {doc_type}\n\n"
        if date_str:
            response += f"{date_str}\n"
        # Don't show Doc ID if we're showing Chunk ID later for chunk search
        if not is_chunk_search:
            response += f"**Doc ID:** `{doc_id[:12]}...`\n\n"

        # Add chunk-specific info if available
        if is_chunk_search:
            chunk_index = metadata.get("chunk_index", -1)
            total_chunks = metadata.get("total_chunks", -1)
            distance = doc.get("distance", None)
            if chunk_index != -1 and total_chunks != -1:
                response += f"**Chunk:** {chunk_index + 1} of {total_chunks}\n"
            if distance is not None:
                similarity = 1 - distance # Assuming cosine distance
                response += f"**Similarity:** {similarity:.2f}\n"

            # Show Chunk ID when searching for chunks
            response += f"**Chunk ID:** `{doc_id[:12]}...`\n\n"

        # Handle content display (always show preview for chunks)
        content_preview = ""
        if content:
            # For text content, create a clean preview
            if len(content) > 250: # Slightly longer preview for chunks
                content_preview = content[:250] + "..."
            else:
                content_preview = content

            # Clean up preview text
            content_preview = content_preview.replace("\n", " ").strip()

            # Filter out non-printable characters
            content_preview = re.sub(r"[^\x20-\x7E]", "", content_preview)

        response += f"**Snippet:**\n> {content_preview if content_preview else '[No text content in snippet]'}\n\n"

    # Add helpful instruction footer
    response += "---\n\n"
    if is_chunk_search:
        response += "**Note:** These are the most relevant snippets found. Ask questions based on these, or refine your search.\n"
    else:
        response += "**How to use:** You can:\n"
        response += "- Ask specific questions about any document\n"
        response += "- Request a summary of a document by its title or number\n"
        response += "- Compare information between documents\n"
        response += "- Ask for explanations in simpler terms\n"

    # Add special formatting wrapper that the frontend can identify
    final_response = f"<div class='kb-search-result'>\n{response}\n</div>"
    return final_response
