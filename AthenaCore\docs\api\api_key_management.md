# API Key Management in Athena

This document explains how API keys are managed in the AthenaCore system, including the migration from environment variables to database storage.

## Overview

As of the latest update, AthenaCore now stores API keys and configuration values in the database rather than relying solely on environment variables. This change provides several benefits:

- **Improved Security**: API keys are stored in the database, reducing the risk of accidental exposure in environment files or logs
- **Centralized Management**: All keys can be managed from a single location
- **User-Specific Overrides**: Support for user-specific API key configuration (when user_id is specified)
- **Fallback Mechanism**: Environment variables still work as a fallback if database values are not available

## Database Schema

API keys are stored in the `config_entries` table, which has the following schema:

```sql
CREATE TABLE config_entries (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key TEXT UNIQUE NOT NULL,
    value TEXT,
    user_id INTEGER,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

- `key`: The configuration key (e.g., `openai_api_key`, `smithery_api_key`)
- `value`: The actual API key or configuration value
- `user_id`: Optional reference to a specific user (NULL for global settings)

## Migration Process

A migration script is provided to move API keys from environment variables to the database:

```bash
python migrations/add_config_entries.py
```

This script:
1. Creates the `config_entries` table if it doesn't exist
2. Checks for existing API keys in the `.env` file or environment
3. Adds these keys to the database if they don't already exist
4. Updates existing database entries if keys are found in the environment

## Supported API Keys

The following API keys are currently supported for database storage:

- `openai_api_key`: OpenAI API key
- `smithery_api_key`: Smithery API key for MCP integration
- `anthropic_api_key`: Anthropic/Claude API key
- `google_api_key`: Google AI API key
- `azure_openai_api_key`: Azure OpenAI API key
- `azure_openai_endpoint`: Azure OpenAI endpoint URL

## Configuration Loading Priority

When loading configuration values, Athena follows this priority order:

1. User-specific database entries (if a user is authenticated)
2. Global database entries
3. Environment variables
4. Default values

## Managing API Keys

### Adding New Keys

You can add new API keys to the database using SQL:

```sql
INSERT INTO config_entries (key, value, user_id) 
VALUES ('service_api_key', 'your-api-key-here', NULL);
```

Or via the migration script, which checks for keys in the environment.

### Updating Keys

To update existing keys:

```sql
UPDATE config_entries 
SET value = 'your-new-api-key' 
WHERE key = 'service_api_key';
```

### User-Specific Keys

To add a user-specific API key:

```sql
INSERT INTO config_entries (key, value, user_id) 
VALUES ('service_api_key', 'user-specific-api-key', 1);
```

Replace `1` with the actual user ID.

## Troubleshooting

If you encounter issues with API keys not being recognized:

1. Check if the key exists in the database:
   ```sql
   SELECT * FROM config_entries WHERE key = 'service_api_key';
   ```

2. Verify the key is being loaded in the application context:
   ```python
   from src.utils.config import AthenaConfig
   config = AthenaConfig.load()
   print(getattr(config, 'service_api_key', None))
   ```

3. Ensure the application has the necessary attributes defined in the `AthenaConfig` class for any new keys being added.

## Technical Implementation

The API key loading is implemented in `src/utils/config.py` in the `AthenaConfig.load()` method. The method:

1. Creates a default configuration instance
2. Loads global database configuration entries
3. Loads user-specific database entries (if in a Flask app context with authenticated user)
4. Falls back to environment variables for any attributes that aren't set from the database
5. Applies special overrides for specific configurations (e.g., embedding models)

This implementation ensures backward compatibility while providing the benefits of database-stored configuration values.
