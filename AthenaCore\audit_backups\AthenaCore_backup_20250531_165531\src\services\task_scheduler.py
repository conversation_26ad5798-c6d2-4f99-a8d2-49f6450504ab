# src/services/task_scheduler.py

"""
Task scheduler service for the Athena Cross-Device API System.

This file implements:
- Scheduled task monitoring and triggering
- Support for interval, cron, event, and trigger-based scheduling
- Dynamic device targeting and selection
- Integration with task executor for task execution
"""

import json
import logging
import time
import threading
import traceback
from datetime import datetime, timedelta

from flask import current_app
from sqlalchemy import and_, or_

from croniter import croniter

from src.models import db
# Import device models directly instead of using the deprecated module
from src.models.device import (
    Device, Command, ScheduledTask,
    create_command
)
from src.services.task_executor import task_executor_service

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger("task_scheduler")

class TaskSchedulerService:
    """
    Service for managing and triggering scheduled tasks.
    """
    
    def __init__(self, app=None, check_interval=60):
        """
        Initialize the task scheduler service.
        
        Args:
            app (Flask): Flask application
            check_interval (int): How often to check for scheduled tasks (in seconds)
        """
        self.app = app
        self.check_interval = check_interval
        self.scheduler_thread = None
        self.stop_event = threading.Event()
        
        if app is not None:
            self.init_app(app)
            
    def init_app(self, app):
        """
        Initialize the service with a Flask app.
        
        Args:
            app (Flask): Flask application
        """
        self.app = app
        
        # Get configuration from app
        self.check_interval = app.config.get('SCHEDULER_CHECK_INTERVAL', 60)
        
        # Register for signals
        app.teardown_appcontext(self._teardown)
        
        # The service will be started by the endpoint in the task_executor service
        
    def _teardown(self, exception):
        """
        Teardown function called when request context is popped.
        """
        pass
        
    def start(self):
        """
        Start the task scheduler service.
        """
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            logger.warning("Task scheduler service already running")
            return
            
        # Reset the stop event
        self.stop_event.clear()
        
        # Create and start the scheduler thread
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop)
        self.scheduler_thread.daemon = True
        self.scheduler_thread.start()
        
        logger.info("Task scheduler service started")
        
    def stop(self):
        """
        Stop the task scheduler service.
        """
        if not self.scheduler_thread or not self.scheduler_thread.is_alive():
            logger.warning("Task scheduler service not running")
            return
            
        # Set the stop event
        self.stop_event.set()
        
        # Wait for the scheduler thread to finish (with timeout)
        self.scheduler_thread.join(timeout=5.0)
        
        logger.info("Task scheduler service stopped")
        
    def _scheduler_loop(self):
        """
        Main scheduler loop that processes scheduled tasks.
        """
        logger.info("Task scheduler loop started")
        
        while not self.stop_event.is_set():
            try:
                # Get current time
                current_time = datetime.utcnow()
                
                # Process different types of scheduled tasks
                with self.app.app_context():
                    # Process interval-based tasks
                    self._process_interval_tasks(current_time)
                    
                    # Process cron-based tasks
                    self._process_cron_tasks(current_time)
                    
                    # Process event-based and trigger-based tasks would normally go here
                    # but they require event system integration which is beyond our current scope
                    
                # Sleep until next check
                sleep_time = 0
                while sleep_time < self.check_interval and not self.stop_event.is_set():
                    time.sleep(1)
                    sleep_time += 1
                    
            except Exception as e:
                logger.error(f"Error in task scheduler loop: {str(e)}")
                logger.error(traceback.format_exc())
                time.sleep(5)  # Wait a bit before retrying after an error
        
        logger.info("Task scheduler loop stopped")
    
    def _process_interval_tasks(self, current_time):
        """
        Process interval-based scheduled tasks.
        
        Args:
            current_time (datetime): Current time
        """
        # Find active interval tasks that are due to run
        interval_tasks = ScheduledTask.query.filter(
            ScheduledTask.is_active == True,
            ScheduledTask.schedule_type == "interval",
            or_(
                ScheduledTask.next_run <= current_time,
                ScheduledTask.next_run.is_(None)
            )
        ).all()
        
        for task in interval_tasks:
            try:
                # Execute the task
                self._execute_scheduled_task(task)
                
                # Update last_run and calculate next_run
                task.last_run = current_time
                
                # Calculate next run time based on interval
                try:
                    schedule_data = json.loads(task.schedule_data)
                    interval_seconds = schedule_data.get("seconds", 0)
                    interval_minutes = schedule_data.get("minutes", 0)
                    interval_hours = schedule_data.get("hours", 0)
                    interval_days = schedule_data.get("days", 0)
                    
                    delta = timedelta(
                        seconds=interval_seconds,
                        minutes=interval_minutes,
                        hours=interval_hours,
                        days=interval_days
                    )
                    
                    task.next_run = current_time + delta
                except json.JSONDecodeError:
                    # If we can't parse the interval, set next_run to 1 hour from now
                    task.next_run = current_time + timedelta(hours=1)
                
                # If task is not recurring, deactivate it
                if not task.is_recurring:
                    task.is_active = False
                    
                db.session.commit()
                logger.info(f"Processed interval task {task.task_uuid}, next run: {task.next_run}")
                
            except Exception as e:
                logger.error(f"Error processing interval task {task.task_uuid}: {str(e)}")
                logger.error(traceback.format_exc())
                db.session.rollback()
    
    def _process_cron_tasks(self, current_time):
        """
        Process cron-based scheduled tasks.
        
        Args:
            current_time (datetime): Current time
        """
        # Find active cron tasks
        cron_tasks = ScheduledTask.query.filter(
            ScheduledTask.is_active == True,
            ScheduledTask.schedule_type == "cron"
        ).all()
        
        for task in cron_tasks:
            try:
                # Check if task should run
                should_run = False
                
                # Calculate next run time
                try:
                    schedule_data = json.loads(task.schedule_data)
                    cron_expression = schedule_data.get("expression")
                    
                    if cron_expression:
                        # Parse cron expression
                        cron = croniter(cron_expression, task.last_run or (current_time - timedelta(days=1)))
                        next_run = cron.get_next(datetime)
                        
                        # Check if next run time is in the past or present
                        if next_run <= current_time:
                            should_run = True
                            
                            # Calculate the next run time after current time
                            cron = croniter(cron_expression, current_time)
                            task.next_run = cron.get_next(datetime)
                        else:
                            # Next run is in the future
                            task.next_run = next_run
                            
                except (json.JSONDecodeError, ValueError) as e:
                    logger.error(f"Error parsing cron expression for task {task.task_uuid}: {str(e)}")
                    continue
                
                # Execute the task if it should run
                if should_run:
                    self._execute_scheduled_task(task)
                    
                    # Update last_run
                    task.last_run = current_time
                    
                    # If task is not recurring, deactivate it
                    if not task.is_recurring:
                        task.is_active = False
                
                db.session.commit()
                logger.info(f"Processed cron task {task.task_uuid}, next run: {task.next_run}")
                
            except Exception as e:
                logger.error(f"Error processing cron task {task.task_uuid}: {str(e)}")
                logger.error(traceback.format_exc())
                db.session.rollback()
    
    def _execute_scheduled_task(self, task):
        """
        Execute a scheduled task by creating a command and adding it to the task queue.
        
        Args:
            task (ScheduledTask): The scheduled task to execute
            
        Returns:
            Command: The created command
        """
        # Find target device
        target_device_id = None
        
        if task.target_device_id:
            # Use the specified device
            target_device_id = task.target_device_id
        else:
            # Find matching devices based on filters
            query = Device.query.filter_by(
                user_id=task.user_id,
                is_active=True
            )
            
            # Apply device type filter
            if task.device_type_filter:
                query = query.filter_by(device_type=task.device_type_filter)
                
            devices = query.all()
            
            # Apply capability filter
            if task.capability_filter:
                filtered_devices = []
                for device in devices:
                    if any(cap.capability_name == task.capability_filter for cap in device.capabilities):
                        filtered_devices.append(device)
                devices = filtered_devices
            
            if not devices:
                logger.warning(f"No matching devices found for task {task.task_uuid}")
                return None
                
            # Use the first matching device
            target_device_id = devices[0].id
        
        # Parse parameters
        parameters = task.parameters
        if not isinstance(parameters, dict):
            try:
                parameters = json.loads(parameters)
            except json.JSONDecodeError:
                parameters = {}
        
        # Create the command
        try:
            command = create_command(
                user_id=task.user_id,
                target_device_id=target_device_id,
                capability_name=task.capability_name,
                parameters=parameters,
                priority="normal",
                is_background=True,
                priority_level=task.priority_level
            )
            
            # Add to task executor
            task_executor_service.add_task(command)
            
            logger.info(f"Created command {command.command_uuid} from scheduled task {task.task_uuid}")
            return command
            
        except Exception as e:
            logger.error(f"Error creating command for task {task.task_uuid}: {str(e)}")
            raise
    
    def trigger_task(self, task_uuid):
        """
        Manually trigger a scheduled task to run immediately.
        
        Args:
            task_uuid (str): UUID of the scheduled task to trigger
            
        Returns:
            Command: The created command or None if task not found or error
        """
        try:
            with self.app.app_context():
                # Get the scheduled task
                task = ScheduledTask.query.filter_by(task_uuid=task_uuid).first()
                
                if not task:
                    logger.error(f"Scheduled task with UUID {task_uuid} not found")
                    return None
                    
                if not task.is_active:
                    logger.error(f"Cannot trigger inactive task {task_uuid}")
                    return None
                    
                # Execute the task
                command = self._execute_scheduled_task(task)
                
                if command:
                    # Update last_run
                    task.last_run = datetime.utcnow()
                    
                    # If it's a recurring interval task, calculate next run time
                    if task.is_recurring and task.schedule_type == "interval":
                        try:
                            schedule_data = json.loads(task.schedule_data)
                            interval_seconds = schedule_data.get("seconds", 0)
                            interval_minutes = schedule_data.get("minutes", 0)
                            interval_hours = schedule_data.get("hours", 0)
                            interval_days = schedule_data.get("days", 0)
                            
                            delta = timedelta(
                                seconds=interval_seconds,
                                minutes=interval_minutes,
                                hours=interval_hours,
                                days=interval_days
                            )
                            
                            task.next_run = datetime.utcnow() + delta
                        except json.JSONDecodeError:
                            # If we can't parse the interval, set next_run to 1 hour from now
                            task.next_run = datetime.utcnow() + timedelta(hours=1)
                            
                    # If task is not recurring, deactivate it
                    if not task.is_recurring:
                        task.is_active = False
                        
                    db.session.commit()
                    
                return command
                
        except Exception as e:
            logger.error(f"Error triggering task {task_uuid}: {str(e)}")
            logger.error(traceback.format_exc())
            return None


# Create a default scheduler
task_scheduler_service = TaskSchedulerService()

def init_app(app):
    """
    Initialize the task scheduler service with a Flask app.
    
    Args:
        app (Flask): Flask application
    """
    global task_scheduler_service
    task_scheduler_service.init_app(app)
