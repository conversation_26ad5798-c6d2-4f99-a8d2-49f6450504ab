"""
Fallback handler for chat functionality when API connections aren't available.
"""

import logging
import json
import random
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger("fallback_handler")

class FallbackHandler:
    """
    Provides fallback responses when API connections aren't available.
    This ensures the chat interface always works, even without valid API keys.
    """
    
    @staticmethod
    def get_fallback_response(user_input, conversation_id=None):
        """
        Generate a fallback response when API connections aren't available.
        
        Args:
            user_input: The user's input message
            conversation_id: Optional conversation ID
            
        Returns:
            A tuple of (response_text, command, python_code)
        """
        logger.info(f"Using fallback handler for message: {user_input[:30]}...")
        
        # Check for specific command patterns
        if user_input.lower().startswith(("help", "/help", "?", "what can you do")):
            return (
                "I'm <PERSON>, your AI assistant. I can help with various tasks once you've configured API connections. "
                "To get started, please go to the settings page and configure an OpenAI or other LLM provider API key. "
                "Until then, I have limited functionality, but I'm still here to chat!",
                None,
                None
            )
            
        # General fallback response
        responses = [
            f"I'd love to help with '{user_input[:30]}...', but I need an API connection configured first. "
            f"Please go to Settings and add an OpenAI API key to enable full functionality.",
            
            "To enable full AI functionality, please add an API key in Settings. "
            "I currently don't have access to language model capabilities without a configured API connection.",
            
            "I noticed you're trying to use chat functionality, but there are no API connections configured. "
            "Please visit the Settings page to add an OpenAI or other LLM provider API key.",
            
            "I'm sorry, but I can't process your request without a configured API connection. "
            "Please add an API key in the Settings page to enable full functionality."
        ]
        
        return (random.choice(responses), None, None)
