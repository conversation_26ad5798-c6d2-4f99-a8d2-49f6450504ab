// settings.js - Complete rewrite with plain JavaScript only (v5)

console.log('[AthenaAgent] settings.js loaded and running');

// Define all critical functions globally first

// Helper function to parse section IDs
window.parseSectionId = function(sectionId) {
    let actualSection = sectionId;
    let logsPage = 1;
    
    if (typeof sectionId === 'string' && sectionId.startsWith('logs-page-')) {
        actualSection = 'logs';
        const suffix = sectionId.substring('logs-page-'.length);
        const parsedPage = parseInt(suffix, 10);
        if (!isNaN(parsedPage) && parsedPage > 0) {
            logsPage = parsedPage;
        }
    }
    return { actualSection, logsPage };
};

// Create breadcrumb element
window.createBreadcrumb = function(sectionId) {
    const { actualSection } = window.parseSectionId(sectionId);
    
    // Get the nav items to find the section name
    const navItems = document.querySelectorAll('.nav-item');
    let sectionName = 'Unknown';
    
    for (let i = 0; i < navItems.length; i++) {
        if (navItems[i].dataset.section === actualSection) {
            const span = navItems[i].querySelector('span');
            if (span) {
                sectionName = span.textContent;
            }
            break;
        }
    }
    
    const breadcrumb = document.createElement('div');
    breadcrumb.className = 'settings-breadcrumb';
    breadcrumb.innerHTML = `
        <a href="/">Home</a>
        <span>/</span>
        <a href="/settings">Settings</a>
        <span>/</span>
        <span class="current">${sectionName}</span>
    `;
    
    return breadcrumb;
};

// Update URL function
window.updateURL = function(section) {
    // Update URL using History API
    const url = new URL(window.location);
    url.searchParams.set('section', section);
    window.history.pushState({ section: section }, '', url);
};

// Load section content
window.loadSection = async function(sectionId) {
    try {
        console.log('Loading section:', sectionId);
        const contentContainer = document.getElementById('content-container');
        const loadingSpinner = document.querySelector('.loading-spinner');
        
        if (!contentContainer || !loadingSpinner) {
            console.error('Required DOM elements not found');
            return;
        }
        
        // Save the previous section to allow fallback if loading fails
        const previousSection = window.currentLoadingSection;
        
        loadingSpinner.classList.add('active');
        contentContainer.classList.add('loading');

        // Handle empty or undefined sectionId
        if (!sectionId) {
            console.warn('No section ID provided, defaulting to llm');
            sectionId = 'llm';
        }

        // Parse the section ID
        const { actualSection, logsPage } = window.parseSectionId(sectionId);
        console.log('Parsed section:', actualSection, 'Page:', logsPage);

        // Store the current section being loaded
        window.currentLoadingSection = actualSection;

        // Set a timeout for the fetch request to prevent hanging
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout for slower connections
        
        try {
            // Fetch the HTML template for that section
            const response = await fetch(`/settings/${actualSection}`, {
                signal: controller.signal,
                headers: {
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                }
            });
            
            clearTimeout(timeoutId); // Clear the timeout if fetch completed
            
            if (!response.ok) {
                throw new Error(`Failed to load section: ${response.status} ${response.statusText}`);
            }
            
            const html = await response.text();
            console.log('Content loaded successfully');
            
            // Update active state of nav items
            const navItems = document.querySelectorAll('.nav-item');
            for (let i = 0; i < navItems.length; i++) {
                const item = navItems[i];
                if (item.dataset.section === 'logs' && actualSection === 'logs') {
                    item.classList.add('active');
                } else if (item.dataset.section === sectionId) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            }

            // Update the content container
            contentContainer.innerHTML = '';
            contentContainer.classList.remove('loading');
            
            // Create and add breadcrumb
            const breadcrumb = window.createBreadcrumb(sectionId);
            contentContainer.appendChild(breadcrumb);
            
            // Create and add content wrapper
            const contentWrapper = document.createElement('div');
            contentWrapper.innerHTML = html;
            contentContainer.appendChild(contentWrapper);
            
            // Save the last section to localStorage
            localStorage.setItem('lastSection', sectionId);
            
            // Reset any initialization flags to ensure proper reinitialization
            if (actualSection === 'mcp') {
                window.mcpInitialized = false;
            }
            
            // Dispatch a custom event to notify that content has been loaded
            const event = new CustomEvent('contentLoaded', { detail: { section: actualSection } });
            document.dispatchEvent(event);
            
            // Handle special sections
            setTimeout(() => {
                // Special handling for logs section
                if (actualSection === 'logs') {
                    // If logs.js is loaded, call its initialization function
                    if (typeof window.initLogsPage === 'function') {
                        window.initLogsPage(logsPage);
                    }
                }
                
                // Special handling for MCP section
                if (actualSection === 'mcp') {
                    console.log('MCP section loaded, initializing MCP page');
                    // If mcp.js is loaded, call its initialization function
                    if (typeof window.initMcpPage === 'function') {
                        window.initMcpPage();
                        
                        // Also dispatch the mcpContentLoaded event
                        const mcpEvent = new CustomEvent('mcpContentLoaded');
                        document.dispatchEvent(mcpEvent);
                    } else {
                        console.warn('initMcpPage function not found');
                    }
                }
                
                // Special handling for commands section
                if (actualSection === 'commands') {
                    console.log('Commands section loaded, initializing Commands page');
                    // If commands.js is loaded, call its initialization function
                    if (typeof window.initCommandsPage === 'function') {
                        window.initCommandsPage();
                    } else {
                        console.warn('initCommandsPage function not found');
                    }
                }
            }, 100);
            
            // Execute script tags in loaded content safely
            const scripts = contentContainer.getElementsByTagName('script');
            for (let i = 0; i < scripts.length; i++) {
                const script = scripts[i];
                try {
                    // Create a new script element
                    const newScript = document.createElement('script');

                    // Copy all attributes from the original script
                    for (let j = 0; j < script.attributes.length; j++) {
                        const attr = script.attributes[j];
                        newScript.setAttribute(attr.name, attr.value);
                    }

                    // Wrap the script content to avoid variable conflicts
                    const scriptContent = script.textContent;
                    if (scriptContent && scriptContent.trim()) {
                        // Wrap in an IIFE to avoid global variable conflicts
                        newScript.textContent = `
                            (function() {
                                try {
                                    ${scriptContent}
                                } catch (e) {
                                    console.warn('Script execution error (non-critical):', e);
                                }
                            })();
                        `;
                    }

                    // Replace the old script with the new one
                    script.parentNode.replaceChild(newScript, script);
                } catch (error) {
                    console.warn('Failed to execute script:', error);
                    // Remove the problematic script
                    if (script.parentNode) {
                        script.parentNode.removeChild(script);
                    }
                }
            }
            
        } catch (error) {
            console.error('Failed to load section:', error);
            clearTimeout(timeoutId); // Clear the timeout in case of error
            
            // Display error message in content container
            contentContainer.innerHTML = '';
            contentContainer.classList.remove('loading');
            
            // If the failed section is 'mcp', show a special message and try to revert to 'llm'
            if (actualSection === 'mcp') {
                console.warn('MCP section failed to load, falling back to LLM section');
                contentContainer.innerHTML = `
                    <div class="alert alert-warning" style="margin: 2rem;">
                        <h4>MCP Section Unavailable</h4>
                        <p>The MCP section could not be loaded. Redirecting to LLM section...</p>
                    </div>
                `;
                
                // Try to load LLM section instead after a brief delay
                setTimeout(() => {
                    // Only attempt LLM fallback if we're not already trying to load LLM
                    if (actualSection !== 'llm') {
                        localStorage.setItem('lastSection', 'llm');
                        window.loadSection('llm');
                        window.updateURL('llm');
                    } else {
                        // If LLM section also fails, show a generic error
                        showGenericError(error, contentContainer);
                    }
                }, 2000);
                return;
            }
            
            // Generic error for other sections
            showGenericError(error, contentContainer);
            
            // Helper function to show a generic error
            function showGenericError(error, container) {
                container.innerHTML = `
                    <div class="error-container" style="text-align: center; padding: 2rem;">
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <line x1="12" y1="8" x2="12" y2="12"/>
                            <line x1="12" y1="16" x2="12" y2="16"/>
                        </svg>
                        <h3>Failed to load section</h3>
                        <p>${error.message}</p>
                        <button id="retry-section" class="btn btn-primary mt-3">Retry</button>
                        <button id="fallback-section" class="btn btn-secondary mt-3 ml-2">Go to LLM section</button>
                    </div>
                `;
                
                // Add event listeners for retry and fallback buttons
                setTimeout(() => {
                    const retryButton = document.getElementById('retry-section');
                    const fallbackButton = document.getElementById('fallback-section');
                    
                    if (retryButton) {
                        retryButton.addEventListener('click', () => {
                            window.loadSection(sectionId);
                        });
                    }
                    
                    if (fallbackButton) {
                        fallbackButton.addEventListener('click', () => {
                            localStorage.setItem('lastSection', 'llm');
                            window.loadSection('llm');
                            window.updateURL('llm');
                        });
                    }
                }, 100);
            }
        }
    } catch (error) {
        console.error('Error in loadSection:', error);
    } finally {
        const loadingSpinner = document.querySelector('.loading-spinner');
        if (loadingSpinner) {
            loadingSpinner.classList.remove('active');
        }
    }
};

// Set up nav item click handlers
window.setupNavItemClickHandlers = function() {
    console.log('Setting up nav item click handlers');
    
    // Get fresh reference to nav items
    const navItems = document.querySelectorAll('.nav-item');
    
    for (let i = 0; i < navItems.length; i++) {
        const item = navItems[i];
        
        // Remove old event listeners by cloning the node
        const newItem = item.cloneNode(true);
        if (item.parentNode) {
            item.parentNode.replaceChild(newItem, item);
        }
        
        // Add new event listener to the cloned node
        newItem.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Nav item clicked:', this.dataset.section);
            
            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(navItem => {
                navItem.classList.remove('active');
            });
            
            // Add active class to clicked item
            this.classList.add('active');
            
            // Reset any initialization flags to ensure proper reinitialization
            if (this.dataset.section === 'mcp') {
                window.mcpInitialized = false;
            }
            
            if (this.dataset.section === 'logs') {
                window.loadSection('logs-page-1');
                window.updateURL('logs-page-1');
            } else {
                window.loadSection(this.dataset.section);
                window.updateURL(this.dataset.section);
            }
        });
    }
};

// Main initialization when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Loading completely rewritten settings.js - v5');
    
    // Function to ensure we have safe global functions for the missing ones
    window.ensureSafeFunctions = function() {
        // Safe parseSectionId fallback
        window.parseSectionId = window.parseSectionId || function(sectionId) {
            console.log('Using fallback parseSectionId for:', sectionId);
            let actualSection = sectionId;
            let logsPage = 1;
            
            if (typeof sectionId === 'string' && sectionId.startsWith('logs-page-')) {
                actualSection = 'logs';
                const suffix = sectionId.substring('logs-page-'.length);
                const parsedPage = parseInt(suffix, 10);
                if (!isNaN(parsedPage) && parsedPage > 0) {
                    logsPage = parsedPage;
                }
            }
            return { actualSection, logsPage };
        };
        
        // MCP fallback functions
        window.initMcpPage = window.initMcpPage || function() {
            console.log('Using fallback initMcpPage function');
            // Try to find an alerts container to show a warning
            const alertsContainer = document.querySelector('#alertsContainer') || document.querySelector('.alerts-container');
            if (alertsContainer) {
                const warningDiv = document.createElement('div');
                warningDiv.className = 'alert alert-warning';
                warningDiv.textContent = 'MCP initialization function not found. Some features may not work correctly.';
                alertsContainer.appendChild(warningDiv);
            }
            
            // Try to load servers and connections if those functions exist
            if (typeof window.loadAvailableServers === 'function') {
                window.loadAvailableServers();
            }
            
            if (typeof window.loadActiveConnections === 'function') {
                window.loadActiveConnections();
            }
        };
        
        // Create fallbacks for MCP functions
        window.loadAvailableServers = window.loadAvailableServers || function() {
            console.log('Using fallback loadAvailableServers function');
            const serversList = document.getElementById('mcpServersList');
            if (serversList) {
                serversList.innerHTML = '<tr><td colspan="5" class="text-center">Failed to load servers list. Function not available.</td></tr>';
            }
        };
        
        window.loadActiveConnections = window.loadActiveConnections || function() {
            console.log('Using fallback loadActiveConnections function');
            const connectionsList = document.getElementById('activeConnectionsList');
            if (connectionsList) {
                connectionsList.innerHTML = '<tr><td colspan="5" class="text-center">Failed to load connections list. Function not available.</td></tr>';
            }
        };
    };
    
    // Ensure all required functions are available
    window.ensureSafeFunctions();
    
    // Apply stored theme
    const theme = localStorage.getItem('theme') || 'dark';
    document.documentElement.setAttribute('data-theme', theme);
    document.body.style.opacity = '1';

    // Cache DOM elements
    const sidebar = document.querySelector('.settings-sidebar');
    const navItems = document.querySelectorAll('.nav-item');
    const contentContainer = document.getElementById('content-container');
    const loadingSpinner = document.querySelector('.loading-spinner');
    const alertsContainer = document.getElementById('alerts-container');

    console.log(`Found ${navItems ? navItems.length : 0} navigation items`);
    console.log('DOM elements available:', {
        sidebar: !!sidebar,
        navItems: !!navItems,
        contentContainer: !!contentContainer,
        loadingSpinner: !!loadingSpinner,
        alertsContainer: !!alertsContainer
    });

    // Define key functions directly on window object for global access
    // Helper to parse logs-page-# from the section string
    window.parseSectionId = function(sectionId) {
        let actualSection = sectionId;
        let logsPage = 1;
        
        if (typeof sectionId === 'string' && sectionId.startsWith('logs-page-')) {
            actualSection = 'logs';
            const suffix = sectionId.substring('logs-page-'.length);
            const parsedPage = parseInt(suffix, 10);
            if (!isNaN(parsedPage) && parsedPage > 0) {
                logsPage = parsedPage;
            }
        }
        return { actualSection, logsPage };
    };
    
    // Creates breadcrumb for the current section
    window.createBreadcrumb = function(sectionId) {
        const { actualSection } = window.parseSectionId(sectionId);
        
        let sectionName = 'Unknown';
        for (let i = 0; i < navItems.length; i++) {
            if (navItems[i].dataset.section === actualSection) {
                if (navItems[i].querySelector('span')) {
                    sectionName = navItems[i].querySelector('span').textContent;
                }
                break;
            }
        }
        
        const breadcrumb = document.createElement('div');
        breadcrumb.className = 'settings-breadcrumb';
        breadcrumb.innerHTML = `
            <a href="/">Home</a>
            <span>/</span>
            <a href="/settings">Settings</a>
            <span>/</span>
            <span class="current">${sectionName}</span>
        `;
        
        return breadcrumb;
    };
    
    // Make updateURL global
    window.updateURL = function(section) {
        // Update URL using History API
        const url = new URL(window.location);
        url.searchParams.set('section', section);
        window.history.pushState({ section: section }, '', url);
    };
    
    // Load section content - key function that was missing
    window.loadSection = async function(sectionId) {
        try {
            console.log('Loading section:', sectionId);
            loadingSpinner.classList.add('active');
            contentContainer.classList.add('loading');

            const { actualSection, logsPage } = window.parseSectionId(sectionId);
            console.log('Parsed section:', actualSection, 'Page:', logsPage);

            // Store the current section being loaded
            window.currentLoadingSection = actualSection;

            // Set a timeout for the fetch request to prevent hanging
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
            
            try {
                // Fetch the HTML template for that section
                const response = await fetch(`/settings/${actualSection}`, {
                    signal: controller.signal,
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                });
                
                clearTimeout(timeoutId); // Clear the timeout if fetch completed
                
                if (!response.ok) {
                    throw new Error(`Failed to load section: ${response.status} ${response.statusText}`);
                }
                
                const html = await response.text();
                console.log('Content loaded successfully');
                
                // Update active state of nav items
                for (let i = 0; i < navItems.length; i++) {
                    const item = navItems[i];
                    if (item.dataset.section === 'logs' && actualSection === 'logs') {
                        item.classList.add('active');
                    } else if (item.dataset.section === sectionId) {
                        item.classList.add('active');
                    } else {
                        item.classList.remove('active');
                    }
                }

                // Update the content container
                contentContainer.innerHTML = '';
                contentContainer.classList.remove('loading');
                
                // Create and add breadcrumb
                const breadcrumb = window.createBreadcrumb(sectionId);
                contentContainer.appendChild(breadcrumb);
                
                // Create and add content wrapper
                const contentWrapper = document.createElement('div');
                contentWrapper.innerHTML = html;
                contentContainer.appendChild(contentWrapper);
                
                // Save the last section to localStorage
                localStorage.setItem('lastSection', sectionId);
                
                // Reset any initialization flags to ensure proper reinitialization
                if (actualSection === 'mcp') {
                    window.mcpInitialized = false;
                }
                
                // Dispatch a custom event to notify that content has been loaded
                const event = new CustomEvent('contentLoaded', { detail: { section: actualSection } });
                document.dispatchEvent(event);
                
                // Handle special sections
                setTimeout(() => {
                    // Special handling for logs section
                    if (actualSection === 'logs') {
                        // If logs.js is loaded, call its initialization function
                        if (typeof window.initLogsPage === 'function') {
                            window.initLogsPage(logsPage);
                        }
                    }
                    
                    // Special handling for MCP section
                    if (actualSection === 'mcp') {
                        console.log('MCP section loaded, initializing MCP page');
                        // If mcp.js is loaded, call its initialization function
                        if (typeof window.initMcpPage === 'function') {
                            window.initMcpPage();
                            
                            // Also dispatch the mcpContentLoaded event
                            const mcpEvent = new CustomEvent('mcpContentLoaded');
                            document.dispatchEvent(mcpEvent);
                        } else {
                            console.warn('initMcpPage function not found');
                        }
                    }
                    
                    // Special handling for commands section
                    if (actualSection === 'commands') {
                        console.log('Commands section loaded, initializing Commands page');
                        // If commands.js is loaded, call its initialization function
                        if (typeof window.initCommandsPage === 'function') {
                            window.initCommandsPage();
                        } else {
                            console.warn('initCommandsPage function not found');
                        }
                    }
                }, 100);
                
                // Re-run <script> tags in loaded content
                const scripts = contentContainer.getElementsByTagName('script');
                for (let i = 0; i < scripts.length; i++) {
                    const script = scripts[i];
                    const newScript = document.createElement('script');
                    
                    // Copy all attributes from the original script
                    for (let j = 0; j < script.attributes.length; j++) {
                        const attr = script.attributes[j];
                        newScript.setAttribute(attr.name, attr.value);
                    }
                    
                    // Copy the content of the script
                    newScript.textContent = script.textContent;
                    
                    // Replace the old script with the new one
                    script.parentNode.replaceChild(newScript, script);
                }
                
            } catch (error) {
                console.error('Failed to load section:', error);
                clearTimeout(timeoutId); // Clear the timeout in case of error
                
                // Display error message in content container
                contentContainer.innerHTML = '';
                contentContainer.classList.remove('loading');
                contentContainer.innerHTML = `
                    <div class="error-container" style="text-align: center; padding: 2rem;">
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="12" y1="8" x2="12" y2="12"></line>
                            <line x1="12" y1="16" x2="12" y2="16"></line>
                        </svg>
                        <h3 style="margin-top: 1rem;">Failed to load section</h3>
                        <p style="margin-top: 0.5rem;">${error.message}</p>
                        <button class="retry-button" style="margin-top: 1rem; padding: 8px 16px; background: var(--accent-color); color: white; border: none; border-radius: 4px; cursor: pointer;">
                            Retry
                        </button>
                    </div>
                `;
                
                const retryButton = contentContainer.querySelector('.retry-button');
                if (retryButton) {
                    retryButton.addEventListener('click', function() {
                        window.loadSection(sectionId);
                    });
                }
            }
        } catch (error) {
            console.error('Error in loadSection:', error);
        } finally {
            loadingSpinner.classList.remove('active');
        }
    };
    
    // Set up nav item click handlers and make it global
    window.setupNavItemClickHandlers = function() {
        console.log('Setting up nav item click handlers');
        
        // Get fresh reference to nav items
        const currentNavItems = document.querySelectorAll('.nav-item');
        
        // Store reference for future use
        window.navItems = Array.from(currentNavItems);
        
        for (let i = 0; i < currentNavItems.length; i++) {
            const item = currentNavItems[i];
            
            // Remove old event listeners by cloning the node
            const newItem = item.cloneNode(true);
            if (item.parentNode) {
                item.parentNode.replaceChild(newItem, item);
            }
            
            // Add new event listener to the cloned node
            newItem.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Nav item clicked:', this.dataset.section);
                
                // Remove active class from all nav items
                document.querySelectorAll('.nav-item').forEach(navItem => {
                    navItem.classList.remove('active');
                });
                
                // Add active class to clicked item
                this.classList.add('active');
                
                // Reset any initialization flags to ensure proper reinitialization
                if (this.dataset.section === 'mcp') {
                    window.mcpInitialized = false;
                }
                
                if (this.dataset.section === 'logs') {
                    window.loadSection('logs-page-1');
                    window.updateURL('logs-page-1');
                } else {
                    window.loadSection(this.dataset.section);
                    window.updateURL(this.dataset.section);
                }
            });
        }
    };
    
    // Initialize UI interactions
    window.setupNavItemClickHandlers();
    
    // On page load, load the last visited section or default to 'llm'
    const urlParams = new URLSearchParams(window.location.search);
    const sectionParam = urlParams.get('section');
    const lastSection = sectionParam || localStorage.getItem('lastSection') || 'llm';
    
    console.log('Initial section load:', lastSection);
    const { actualSection } = window.parseSectionId(lastSection);
    
    // Set the active class on the correct nav item
    for (let i = 0; i < navItems.length; i++) {
        if (navItems[i].dataset.section === actualSection) {
            navItems[i].classList.add('active');
        } else {
            navItems[i].classList.remove('active');
        }
    }
    
    // Load the initial section
    window.loadSection(lastSection);
    window.updateURL(lastSection);
    
    // Handle back button
    window.addEventListener('popstate', function(event) {
        const section = event.state && event.state.section ? event.state.section : 'llm';
        window.loadSection(section);
    });
});

// Main initialization function
function initializeSettingsPage(sidebar, navItems, contentContainer, loadingSpinner, alertsContainer) {
    console.log('Settings.js: Initializing settings page');
    
    // Direct helper function to find by section
    function findNavItemBySection(section) {
        for (let i = 0; i < navItems.length; i++) {
            if (navItems[i].dataset.section === section) {
                return navItems[i];
            }
        }
        return null;
    }

    // Make it available globally for other scripts
    window.findNavItemBySection = findNavItemBySection;

    // Helper to parse logs-page-# from the section string
    function parseSectionId(sectionId) {
        let actualSection = sectionId;
        let logsPage = 1;
        
        if (typeof sectionId === 'string' && sectionId.startsWith('logs-page-')) {
            actualSection = 'logs';
            const suffix = sectionId.substring('logs-page-'.length);
            const parsedPage = parseInt(suffix, 10);
            if (!isNaN(parsedPage) && parsedPage > 0) {
                logsPage = parsedPage;
            }
        }
        return { actualSection, logsPage };
    }

    // Creates breadcrumb for the current section
    function createBreadcrumb(sectionId) {
        const { actualSection } = parseSectionId(sectionId);
        
        // Find matching nav item using our helper function
        const navItem = findNavItemBySection(actualSection);
        let sectionName = 'Unknown';
        
        if (navItem && navItem.querySelector('span')) {
            sectionName = navItem.querySelector('span').textContent;
        }
        
        const breadcrumb = document.createElement('div');
        breadcrumb.className = 'settings-breadcrumb';
        breadcrumb.innerHTML = `
            <a href="/">Home</a>
            <span>/</span>
            <a href="/settings">Settings</a>
            <span>/</span>
            <span class="current">${sectionName}</span>
        `;
        
        return breadcrumb;
    }

    // Make updateURL global so logs.js can call it
    window.updateURL = function(section) {
        const url = new URL(window.location);
        url.searchParams.set('section', section);
        window.history.pushState({ section }, '', url);
    };

    // Load section content
    async function loadSection(sectionId) {
        try {
            console.log('Loading section:', sectionId);
            loadingSpinner.classList.add('active');
            contentContainer.classList.add('loading');

            const { actualSection, logsPage } = parseSectionId(sectionId);
            console.log('Parsed section:', actualSection, 'Page:', logsPage);

            // Store the current section being loaded
            window.currentLoadingSection = actualSection;

            // Set a timeout for the fetch request to prevent hanging
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
            
            try {
                // Fetch the HTML template for that section
                const response = await fetch(`/settings/${actualSection}`, {
                    signal: controller.signal,
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                });
                
                clearTimeout(timeoutId); // Clear the timeout if fetch completed
                
                if (!response.ok) {
                    throw new Error(`Failed to load section: ${response.status} ${response.statusText}`);
                }
                
                const html = await response.text();
                console.log('Content loaded successfully');
                
                // Update active state of nav items
                for (let i = 0; i < navItems.length; i++) {
                    const item = navItems[i];
                    if (item.dataset.section === 'logs' && actualSection === 'logs') {
                        item.classList.add('active');
                    } else if (item.dataset.section === sectionId) {
                        item.classList.add('active');
                    } else {
                        item.classList.remove('active');
                    }
                }

                // Update the content container
                contentContainer.innerHTML = '';
                contentContainer.classList.remove('loading');
                
                // Create and add breadcrumb
                const breadcrumb = createBreadcrumb(sectionId);
                contentContainer.appendChild(breadcrumb);
                
                // Create and add content wrapper
                const contentWrapper = document.createElement('div');
                contentWrapper.innerHTML = html;
                contentContainer.appendChild(contentWrapper);
                
                // Save the last section to localStorage
                localStorage.setItem('lastSection', sectionId);
                
                // Reset any initialization flags to ensure proper reinitialization
                if (actualSection === 'mcp') {
                    window.mcpInitialized = false;
                }
                
                // Dispatch a custom event to notify that content has been loaded
                const event = new CustomEvent('contentLoaded', { detail: { section: actualSection } });
                document.dispatchEvent(event);
                
                // Wait a small amount of time for the DOM to fully update
                setTimeout(() => {
                    // Special handling for logs section
                    if (actualSection === 'logs') {
                        // If logs.js is loaded, call its initialization function
                        if (typeof window.initLogsPage === 'function') {
                            window.initLogsPage(logsPage);
                        }
                    }
                    
                    // Special handling for MCP section
                    if (actualSection === 'mcp') {
                        console.log('MCP section loaded, initializing MCP page');
                        // If mcp.js is loaded, call its initialization function
                        if (typeof window.initMcpPage === 'function') {
                            window.initMcpPage();
                            
                            // Also dispatch the mcpContentLoaded event
                            const mcpEvent = new CustomEvent('mcpContentLoaded');
                            document.dispatchEvent(mcpEvent);
                        } else {
                            console.warn('initMcpPage function not found');
                        }
                    }
                    
                    // Special handling for commands section
                    if (actualSection === 'commands') {
                        console.log('Commands section loaded, initializing Commands page');
                        // If commands.js is loaded, call its initialization function
                        if (typeof window.initCommandsPage === 'function') {
                            window.initCommandsPage();
                        } else {
                            console.warn('initCommandsPage function not found');
                        }
                    }
                }, 100); // Small delay to ensure DOM is ready
                
                // Re-run <script> tags in loaded content
                const scripts = contentContainer.getElementsByTagName('script');
                for (let i = 0; i < scripts.length; i++) {
                    const script = scripts[i];
                    const newScript = document.createElement('script');
                    
                    // Copy all attributes from the original script
                    for (let j = 0; j < script.attributes.length; j++) {
                        const attr = script.attributes[j];
                        newScript.setAttribute(attr.name, attr.value);
                    }
                    
                    // Only set textContent if it's an inline script (no src attribute)
                    if (!script.src) {
                        newScript.textContent = script.textContent;
                    }
                    
                    // Replace the old script with the new one
                    script.parentNode.replaceChild(newScript, script);
                }
            } catch (error) {
                throw error;
            }
        } catch (error) {
            console.error('Error loading section:', error);
            contentContainer.innerHTML = `
                <div style="color: var(--text-secondary); text-align: center; padding: 2rem;">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="8" x2="12" y2="12"></line>
                        <line x1="12" y1="16" x2="12.01" y2="16"></line>
                    </svg>
                    <h3 style="margin-top: 1rem;">Failed to load section</h3>
                    <p style="margin-top: 0.5rem;">${error.message}</p>
                    <button class="retry-button" style="margin-top: 1rem; padding: 8px 16px; background: var(--accent-color); color: white; border: none; border-radius: 4px; cursor: pointer;">
                        Retry
                    </button>
                </div>
            `;
            
            const retryButton = contentContainer.querySelector('.retry-button');
            if (retryButton) {
                retryButton.addEventListener('click', function() {
                    loadSection(sectionId);
                });
            }
        } finally {
            loadingSpinner.classList.remove('active');
        }
    }

    // Set up nav item click handlers
    function setupNavItemClickHandlers() {
        console.log('Setting up nav item click handlers');
        
        // Get fresh reference to nav items
        const currentNavItems = document.querySelectorAll('.nav-item');
        
        // Store reference for future use
        window.navItems = Array.from(currentNavItems);
        
        for (let i = 0; i < currentNavItems.length; i++) {
            const item = currentNavItems[i];
            
            // Remove old event listeners by cloning the node
            const newItem = item.cloneNode(true);
            if (item.parentNode) {
                item.parentNode.replaceChild(newItem, item);
            }
            
            // Add new event listener to the cloned node
            newItem.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Nav item clicked:', this.dataset.section);
                
                // Remove active class from all nav items
                document.querySelectorAll('.nav-item').forEach(navItem => {
                    navItem.classList.remove('active');
                });
                
                // Add active class to clicked item
                this.classList.add('active');
                
                // Reset any initialization flags to ensure proper reinitialization
                if (this.dataset.section === 'mcp') {
                    window.mcpInitialized = false;
                }
                
                if (this.dataset.section === 'logs') {
                    loadSection('logs-page-1');
                    window.updateURL('logs-page-1');
                } else {
                    loadSection(this.dataset.section);
                    window.updateURL(this.dataset.section);
                }
            });
        }
    }
    
    // Initial setup of nav item click handlers
    setupNavItemClickHandlers();
    
    // Make it available globally for reinitializing if needed
    window.setupNavItemClickHandlers = setupNavItemClickHandlers;

    // On page load
    const urlParams = new URLSearchParams(window.location.search);
    const sectionParam = urlParams.get('section');
    const lastSection = sectionParam || localStorage.getItem('lastSection') || 'llm';
    
    console.log('Initial section load:', lastSection);
    
    // Set the active class on the correct nav item
    const { actualSection } = parseSectionId(lastSection);
    const activeItem = findNavItemBySection(actualSection);
    if (activeItem) {
        // Remove active class from all nav items
        for (let i = 0; i < navItems.length; i++) {
            navItems[i].classList.remove('active');
        }
        // Add active class to the current nav item
        activeItem.classList.add('active');
    }
    
    loadSection(lastSection);
    window.updateURL(lastSection);

    // Handle back button
    window.addEventListener('popstate', function(event) {
        const section = event.state && event.state.section ? event.state.section : 'llm';
        loadSection(section);
    });
};

// Make functions available globally so they can be called from other scripts
window.loadSection = loadSection;
window.parseSectionId = parseSectionId;
window.createBreadcrumb = createBreadcrumb;
window.updateURL = updateURL;
window.setupNavItemClickHandlers = setupNavItemClickHandlers;

// --- AthenaAgent Patch: Section-specific JS initialization after loadSection ---
// Map section IDs to their JS initializer functions (if present)
window.sectionInitializers = window.sectionInitializers || {
    'connections': {
        init: function() {
            console.log('[AthenaAgent] Attempting to initialize Connections Page...');
            if (typeof window.initConnectionsPage === 'function') {
                window.connectionsInitialized = false; // Always re-init on load
                window.initConnectionsPage();
                console.log('[AthenaAgent] Connections Page initialized.');
            } else {
                console.error('[AthenaAgent] initConnectionsPage not found after script load!');
            }
        },
        script: '/static/js/connections.js'
    },
    'llm': {
        init: function() {
            console.log('[AthenaAgent] Attempting to initialize LLM Page...');
            if (typeof window.initLlmPage === 'function') {
                window.llmInitialized = false;
                window.initLlmPage();
                console.log('[AthenaAgent] LLM Page initialized.');
            } else {
                console.error('[AthenaAgent] initLlmPage not found after script load!');
            }
        },
        script: '/static/js/llm.js'
    },
    'devices': {
        init: function() {
            console.log('[AthenaAgent] Attempting to initialize Devices Page...');
            if (typeof window.initDevicesPage === 'function') {
                window.devicesInitialized = false;
                window.initDevicesPage();
                console.log('[AthenaAgent] Devices Page initialized.');
            } else {
                console.error('[AthenaAgent] initDevicesPage not found after script load!');
            }
        },
        script: '/static/js/devices.js'
    },
    'logs': {
        init: function() {
            console.log('[AthenaAgent] Attempting to initialize Logs Page...');
            if (typeof window.initLogsPage === 'function') {
                window.logsInitialized = false;
                window.initLogsPage();
                console.log('[AthenaAgent] Logs Page initialized.');
            } else {
                console.error('[AthenaAgent] initLogsPage not found after script load!');
            }
        },
        script: '/static/js/logs.js'
    },
    'commands': {
        init: function() {
            console.log('[AthenaAgent] Attempting to initialize Commands Page...');
            if (typeof window.initCommandsPage === 'function') {
                window.commandsInitialized = false;
                window.initCommandsPage();
                console.log('[AthenaAgent] Commands Page initialized.');
            } else {
                console.error('[AthenaAgent] initCommandsPage not found after script load!');
            }
        },
        script: '/static/js/commands.js'
    },
    'admin': {
        init: function() {
            console.log('[AthenaAgent] Attempting to initialize Admin Page...');
            if (typeof window.initAdminPage === 'function') {
                window.adminInitialized = false;
                window.initAdminPage();
                console.log('[AthenaAgent] Admin Page initialized.');
            } else {
                console.error('[AthenaAgent] initAdminPage not found after script load!');
            }
        },
        script: '/static/js/admin.js'
    }
};

function callSectionInit(section, sectionId) {
    if (typeof section.init === 'function') {
        console.log(`[AthenaAgent] Calling init for section '${sectionId}' from loader`);
        section.init();
    } else {
        console.warn(`[AthenaAgent] No init function found for section '${sectionId}'`);
    }
}
window.callSectionInit = callSectionInit;

function loadScriptIfNeeded(src, windowInitName, callback) {
    if (typeof window[windowInitName] === 'function') {
        console.log(`[AthenaAgent] Script already loaded for ${src}. Running initializer.`);
        callback();
        return;
    }
    // Add cache-busting query param
    const scriptUrl = src + '?cb=' + Date.now();
    let scriptTag = document.querySelector(`script[src^="${src}"]`);
    if (!scriptTag) {
        console.log(`[AthenaAgent] Injecting script: ${scriptUrl}`);
        scriptTag = document.createElement('script');
        scriptTag.src = scriptUrl;
        scriptTag.onload = function() {
            console.log(`[AthenaAgent] Script loaded: ${scriptUrl}`);
            if (typeof window[windowInitName] === 'function') {
                callback();
            } else {
                console.error(`[AthenaAgent] ${windowInitName} not found after script load!`);
            }
        };
        scriptTag.onerror = function() {
            console.error(`[AthenaAgent] Failed to load script: ${scriptUrl}`);
        };
        document.head.appendChild(scriptTag);
    } else {
        console.log(`[AthenaAgent] Script tag already exists for ${src}. Waiting for load.`);
        scriptTag.onload = function() {
            if (typeof window[windowInitName] === 'function') {
                callback();
            } else {
                console.error(`[AthenaAgent] ${windowInitName} not found after script (existing) load!`);
            }
        };
    }
}

function getWindowInitName(section) {
    if (section.script.includes('connections')) return 'initConnectionsPage';
    if (section.script.includes('llm')) return 'initLlmPage';
    if (section.script.includes('devices')) return 'initDevicesPage';
    if (section.script.includes('logs')) return 'initLogsPage';
    if (section.script.includes('commands')) return 'initCommandsPage';
    if (section.script.includes('admin')) return 'initAdminPage';
    return null;
}

if (!window.loadSection.__isAthenaPatched) {
    window.originalLoadSection = window.loadSection;
    window.loadSection = async function(sectionId) {
        await window.originalLoadSection.apply(this, arguments);
        const { actualSection } = window.parseSectionId(sectionId);
        const section = window.sectionInitializers[actualSection];
        if (section) {
            const windowInitName = getWindowInitName(section);
            loadScriptIfNeeded(
                section.script,
                windowInitName,
                () => window.callSectionInit(section, actualSection)
            );
        }
    };
    window.loadSection.__isAthenaPatched = true;
}

document.addEventListener('DOMContentLoaded', function() {
    let sectionId = null;
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('section')) {
        sectionId = urlParams.get('section');
        console.log(`[AthenaAgent] DOMContentLoaded: Found section from URL param: ${sectionId}`);
    } else {
        const activeNav = document.querySelector('.nav-item.active');
        if (activeNav && activeNav.dataset.section) {
            sectionId = activeNav.dataset.section;
            console.log(`[AthenaAgent] DOMContentLoaded: Found section from nav: ${sectionId}`);
        } else {
            console.log('[AthenaAgent] DOMContentLoaded: No section found in URL or nav.');
        }
    }
    if (sectionId) {
        const { actualSection } = window.parseSectionId(sectionId);
        const section = window.sectionInitializers[actualSection];
        if (section) {
            const windowInitName = getWindowInitName(section);
            console.log(`[AthenaAgent] DOMContentLoaded: Attempting to load/init section '${actualSection}' with script '${section.script}' and window init '${windowInitName}'`);
            loadScriptIfNeeded(
                section.script,
                windowInitName,
                () => window.callSectionInit(section, actualSection)
            );
        } else {
            console.log(`[AthenaAgent] DOMContentLoaded: No section initializer found for '${actualSection}'`);
        }
    }
});
// --- End AthenaAgent Patch ---
