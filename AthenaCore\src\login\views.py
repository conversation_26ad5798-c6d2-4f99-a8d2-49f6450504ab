
# ===========================================================================
# CRITICAL DEPRECATION NOTICE
# ===========================================================================
# This file is DEPRECATED and will be REMOVED in a future release.
# All functionality has been migrated to src/controllers/auth_controller.py
#
# ===========================================================================
# DEPRECATED: src/login/views.py
# ===========================================================================
# This file is DEPRECATED and will be REMOVED in a future release.
# All functionality has been migrated to src/controllers/auth_controller.py.
#
# Please update your imports to use the new locations:
#   OLD: from src.login.views import login, logout
#   NEW: from src.controllers.auth_controller import login, logout

import warnings
import logging
import os
from urllib.parse import urlparse as url_parse
from email_validator import validate_email, EmailNotValidError
import src.utils as utils

# Set up logging
logger = logging.getLogger('athena.compat')

# Show deprecation warning
warnings.warn(
    "The module src.login.views is deprecated. Please update imports to use src.controllers.auth_controller",
    category=FutureWarning,
    stacklevel=2
)

# Import from auth_controller
from src.controllers.auth_controller import (
    # Blueprint
    auth_bp,
    
    # Authentication functions
    login,
    logout,
    register,
    profile,
    password_reset_request,
    password_reset,
    change_password,
    
    # API key management
    api_keys,
    create_api_key,
    delete_api_key,
    get_token
)

# Import from admin_controller
from src.controllers.admin_controller import (
    admin_panel,
    admin_bp
)

# Import middleware decorators
from src.utils.middleware import require_auth, require_role

# Admin required decorator (for backward compatibility)
def admin_required(f):
    """Admin required decorator for backward compatibility."""
    return require_role(['admin'])(f)

# Import Flask utilities that might be used by importing code
from flask import render_template, redirect, url_for, flash, request, g, session, jsonify
from flask_login import login_user, logout_user, login_required, current_user

# Define __all__ to control what's imported with 'from src.login.views import *'
__all__ = [
    # Blueprints
    'auth_bp', 'admin_bp',
    
    # Authentication functions
    'login', 'logout', 'register', 'profile',
    'password_reset_request', 'password_reset', 'change_password',
    
    # API key management
    'api_keys', 'create_api_key', 'delete_api_key', 'get_token',
    
    # Admin functions
    'admin_panel', 'admin_required',
    
    # Decorators
    'login_required', 'require_auth', 'require_role',
    
    # Flask utilities
    'render_template', 'redirect', 'url_for', 'flash', 'request', 'g', 'session', 'jsonify',
    'login_user', 'logout_user', 'current_user'
]

# Note: End of compatibility layer

# End of compatibility layer

# Re-export everything from views_compat
__all__ = [
    # Authentication functions
    'login',
    'logout',
    'register',
    'profile',
    
    # API key management
    'api_keys',
    'create_api_key',
    'delete_api_key',
    'get_token',
    
    # Admin functions
    'admin_panel',
    'admin_required',
    
    # Blueprint
    'auth_bp'
]

# Preserve imports for backward compatibility
from flask import (
    Blueprint,
    abort,
    flash,
    redirect,
    render_template,
    request,
    session,
    url_for,
    jsonify
)
from flask_login import (
    LoginManager,
    current_user,
    login_required,
    login_user,
    logout_user,
)

from src.models import APIKey, User, UserLog, MCPApiKey, db

# Blueprint imported from compatibility layer
# auth_bp already imported from src.login.views_compat

# LoginManager imported from compatibility layer
# login_manager is defined in the new auth_controller.py

# Functions already imported from compatibility layer
# No need to redefine them here

# Add a note for future developers
"""IMPORTANT: This file is maintained for backward compatibility only.
All new development should use src.controllers.auth_controller instead."""


@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('core.home'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember = 'remember' in request.form

        user = User.query.filter_by(username=username).first()
        if user and user.check_password(password):
            # Check if user is pending or suspended
            if user.role in ['pending', 'suspend', None]:
                flash("Your account is not approved or has been suspended.", "error")
                return redirect(url_for('auth.login'))
            login_user(user, remember=remember)
            next_page = request.args.get('next')
            if not next_page or url_parse(next_page).netloc != '':
                next_page = url_for('core.home')
            flash("Login successful!", "success")
            return redirect(next_page)
        else:
            flash("Invalid username or password", "error")

    return render_template('login.html')


@auth_bp.route('/logout')
@login_required
def logout():
    logout_user()
    flash("You have been logged out", "info")
    return redirect(url_for('auth.login'))


@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    config = utils.AthenaConfig.load()
    if not config.allow_registration:
        flash("Registration is currently disabled", "warning")
        return redirect(url_for('auth.login'))
    if current_user.is_authenticated:
        return redirect(url_for('core.home'))

    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        error = None

        # Basic validation
        if not username or not email or not password or not confirm_password:
            error = "All fields are required."
        elif password != confirm_password:
            error = "Passwords do not match."
        elif len(password) < config.min_password_length:
            error = f"Password must be at least {config.min_password_length} characters long."
        else:
            try:
                validate_email(email)
            except EmailNotValidError:
                error = "Invalid email format."

        # Check if username/email already exists
        if not error:
            if User.query.filter_by(username=username).first():
                error = "Username is already taken."
            elif User.query.filter_by(email=email).first():
                error = "Email is already registered."

        # If no errors, create the user
        if error is None:
            new_user = User(username=username, email=email)
            new_user.set_password(password)
            # If no users exist in the database, assign admin role.
            if User.query.first() is None:
                new_user.role = "admin"
                flash(
                    "Registration successful! You are the first user and have been granted admin privileges.", "success")
            else:
                new_user.role = "pending"  # Subsequent users must be approved.
                flash(
                    "Registration successful! Please wait for account approval.", "success")
            db.session.add(new_user)
            db.session.commit()
            return redirect(url_for('auth.login'))
        else:
            flash(error, "error")

    return render_template('register.html')


@auth_bp.route('/settings/reset_password', methods=['GET', 'POST'])
@login_required
def reset_password():
    if request.method == 'POST':
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')

        # Validate current password
        if not current_user.check_password(current_password):
            flash("Incorrect current password.", "error")
            return redirect(url_for('auth.reset_password'))
        if new_password != confirm_password:
            flash("New passwords do not match.", "error")
            return redirect(url_for('auth.reset_password'))
        if len(new_password) < 6:
            flash("New password must be at least 6 characters long.", "error")
            return redirect(url_for('auth.reset_password'))

        # Update password
        current_user.set_password(new_password)
        db.session.commit()
        flash("Password updated successfully!", "success")
        return redirect(url_for('auth.reset_password'))

    return render_template('auth/reset_password.html')


@auth_bp.route('/settings/update_username', methods=['POST'])
@login_required
def update_username():
    new_username = request.form.get('new_username')
    if not new_username:
        flash("Please provide a new username.", "error")
        return redirect(url_for('auth.account_settings'))

    # Check if taken
    if User.query.filter_by(username=new_username).first():
        flash("Username is already taken.", "error")
        return redirect(url_for('auth.account_settings'))

    # Update username
    current_user.username = new_username
    db.session.commit()
    flash("Username updated successfully!", "success")
    return redirect(url_for('auth.account_settings'))


@auth_bp.route('/settings/account', methods=['GET', 'POST'])
@login_required
def account_settings():
    if request.method == 'POST':
        print('--- POST /settings/account ---')
        # File size limit (5MB)
        max_size = 5 * 1024 * 1024  # 5 MB
        data = request.form
        updated = False
        for field in ['display_name', 'bio', 'phone', 'location']:
            if field in data:
                print(f'Updating {field}: {data[field]}')
                setattr(current_user, field, data[field])
                updated = True
        # Handle profile picture upload
        if 'profile_picture' in request.files:
            file = request.files['profile_picture']
            print(f'File received: {file.filename}')
            if file and file.filename:
                # Check file extension
                if not allowed_image(file.filename):
                    flash('Invalid file type. Only PNG, JPG, JPEG, GIF, BMP, and WEBP images are allowed.', 'error')
                    print('Rejected file: invalid file type')
                    return redirect(url_for('core.settings', section='account'))
                # Check content type for security
                allowed_mimetypes = {'image/png', 'image/jpeg', 'image/gif', 'image/bmp', 'image/webp'}
                if file.mimetype not in allowed_mimetypes:
                    flash('Invalid image file. The file content does not match the extension.', 'error')
                    print(f'Rejected file: mismatched mimetype {file.mimetype}')
                    return redirect(url_for('core.settings', section='account'))
                file.seek(0, 2)  # Seek to end
                size = file.tell()
                file.seek(0)
                if size > max_size:
                    flash('Image too large. Max 5MB allowed.', 'error')
                    print('Rejected file: too large')
                    return redirect(url_for('core.settings', section='account'))
                ext = file.filename.rsplit('.', 1)[1].lower()
                filename = f"{current_user.username}.{ext}"
                static_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'uploads', 'profile_pics'))
                print(f'Saving to: {static_dir}')
                file_path = os.path.join(static_dir, filename)
                print(f'Full file path: {file_path}')
                # Delete old profile picture if it exists and is not the same filename
                old_path = None
                if current_user.profile_picture:
                    old_path = os.path.join(static_dir, os.path.basename(current_user.profile_picture))
                    if os.path.exists(old_path) and old_path != file_path:
                        try:
                            os.remove(old_path)
                            print(f'Removed old profile picture: {old_path}')
                        except Exception as e:
                            print(f"Could not remove old profile picture: {e}")
                try:
                    file.save(file_path)
                    print('File saved successfully.')
                except Exception as e:
                    print(f'Error saving file: {e}')
                rel_path = f"uploads/profile_pics/{filename}"
                current_user.profile_picture = rel_path
                updated = True
        if updated:
            db.session.commit()
            print('Profile updated and committed to DB.')
            flash('Profile updated.', 'success')
        else:
            print('No changes detected.')
            flash('No changes detected.', 'info')
        # Redirect to correct URL
        return redirect(url_for('core.settings', section='account'))
    return render_template('settings/account.html')


@auth_bp.route('/settings/admin', methods=['GET'])
@login_required
@admin_required
def admin_panel():
    """
    Show all users for admin management.
    """
    print(f"[DEBUG] Admin panel accessed by user: {current_user.username} (ID: {current_user.id})")

    try:
        users = User.query.all()
        print(f"[DEBUG] Found {len(users)} users in database")
        for user in users:
            print(f"[DEBUG] User: {user.username} ({user.email}) - Role: {user.role}")

        print(f"[DEBUG] Rendering template with {len(users)} users")
        return render_template('settings/admin.html', users=users)
    except Exception as e:
        print(f"[DEBUG] Error in admin_panel: {e}")
        import traceback
        traceback.print_exc()
        flash(f"Error loading admin panel: {e}", "error")
        return redirect(url_for('core.home'))


@auth_bp.route('/settings/admin/create_user', methods=['POST'])
@login_required
@admin_required
def create_user_admin():
    new_username = request.form.get('new_username')
    new_email = request.form.get('new_email')
    new_password = request.form.get('new_password')

    if not new_username or not new_email or not new_password:
        flash("All fields are required to create a user.", "error")
        return redirect(request.referrer or url_for('auth.admin_panel'))

    # Check if username/email is taken
    if User.query.filter_by(username=new_username).first():
        flash("Username is already taken.", "error")
        return redirect(request.referrer or url_for('auth.admin_panel'))
    if User.query.filter_by(email=new_email).first():
        flash("Email is already in use.", "error")
        return redirect(request.referrer or url_for('auth.admin_panel'))

    # Create the user
    new_user = User(username=new_username, email=new_email)
    new_user.set_password(new_password)
    new_user.role = "user"  # or "pending" if you require manual approval
    db.session.add(new_user)
    db.session.commit()
    flash(f"Created new user: {new_username}", "success")
    return redirect(request.referrer or url_for('auth.admin_panel'))


@auth_bp.route('/settings/admin/update_role', methods=['POST'])
@login_required
@admin_required
def update_role():
    user_id = request.form.get('user_id')
    new_role = request.form.get('role')
    user = User.query.get(user_id)
    if not user:
        flash("User not found", "error")
        return redirect(request.referrer or url_for('auth.admin_panel'))

    if new_role not in ["pending", "user", "admin", "suspend"]:
        flash("Invalid role selected", "error")
        return redirect(request.referrer or url_for('auth.admin_panel'))

    user.role = new_role
    db.session.commit()
    flash(f"Updated {user.username}'s role to {new_role}", "success")
    return redirect(request.referrer or url_for('auth.admin_panel'))


@auth_bp.route('/settings/admin/reset_user_password', methods=['POST'])
@login_required
@admin_required
def reset_user_password():
    user_id = request.form.get('user_id')
    new_password = request.form.get('new_password')
    user = User.query.get(user_id)
    if not user:
        flash("User not found", "error")
        return redirect(request.referrer or url_for('auth.admin_panel'))
    if not new_password or len(new_password) < 6:
        flash("Please provide a valid new password (min 6 characters).", "error")
        return redirect(request.referrer or url_for('auth.admin_panel'))

    user.set_password(new_password)
    db.session.commit()
    flash(f"Password reset for {user.username}", "success")
    return redirect(request.referrer or url_for('auth.admin_panel'))


@auth_bp.route('/settings/admin/delete_user', methods=['POST'])
@login_required
@admin_required
def delete_user():
    user_id = request.form.get('user_id')
    user = User.query.get(user_id)
    if user:
        if user.id == current_user.id:
            flash("You cannot delete your own account.", "error")
            return redirect(request.referrer or url_for('auth.admin_panel'))
        # Delete profile picture from disk if it exists
        if user.profile_picture:
            static_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'uploads', 'profile_pics'))
            pic_path = os.path.join(static_dir, os.path.basename(user.profile_picture))
            if os.path.exists(pic_path):
                try:
                    os.remove(pic_path)
                except Exception as e:
                    print(f"Could not remove profile picture for deleted user: {e}")
        # Delete associated API keys first
        keys = APIKey.query.filter_by(user_id=user.id).all()
        for key in keys:
            db.session.delete(key)
        db.session.delete(user)
        db.session.commit()
        flash(f"Deleted user {user.username}", "success")
    else:
        flash("User not found", "error")
    return redirect(request.referrer or url_for('auth.admin_panel'))

# -------------------------------
# EMULATION ROUTES
# -------------------------------


@auth_bp.route('/admin/emulate/<int:user_id>', methods=['POST'])
@login_required
@admin_required
def emulate_user(user_id):
    """Emulate another user. Original admin's ID is stored in session."""
    target_user = User.query.get(user_id)
    if not target_user:
        flash("User not found", "error")
        return redirect(url_for('auth.admin_panel'))
    
    # Skip if trying to emulate self
    if target_user.id == current_user.id:
        flash("You cannot emulate yourself.", "warning")
        return redirect(url_for('auth.admin_panel'))
    
    # Store the current admin's ID in session
    # Use 'admin_emulation' key to track emulation state
    session['admin_emulation'] = current_user.id
    session['emulated_user'] = target_user.id
    session.modified = True
    
    # Actually log in as the target user
    logout_user()
    login_user(target_user)
    
    # Log the emulation event
    print(f"Admin {session['admin_emulation']} is now emulating user {target_user.id}")
    flash(f"You are now emulating {target_user.username}.", "success")
    
    # IMPORTANT: Don't redirect to an admin-only route, or you'll 403
    return redirect(url_for('core.home'))


@auth_bp.route('/admin/stop_emulation')
@login_required
def stop_emulation():
    admin_id = session.pop('admin_emulation', None)
    emulated_id = session.pop('emulated_user', None)
    session.modified = True
    
    if not admin_id:
        flash("No emulation in progress.", "error")
        return redirect(url_for('core.home'))
    
    admin_user = User.query.get(admin_id)
    if not admin_user:
        flash("Original admin not found.", "error")
        return redirect(url_for('auth.logout'))
    
    # Log the end of emulation
    print(f"Admin {admin_id} stopped emulating user {emulated_id or current_user.id}")
    
    # Logout as emulated user and login as admin
    logout_user()
    login_user(admin_user)
    
    flash("Emulation stopped. You are now logged back in as admin.", "success")
    return redirect(url_for('auth.admin_panel'))

def allowed_image(filename):
    allowed = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in allowed


@auth_bp.route('/api/settings/integration', methods=['GET', 'POST'])
@login_required
def integration_settings():
    from src.models import MCPApiKey, db
    import json as _json
    if request.method == 'GET':
        service = request.args.get('service')
        if service == 'spotify':
            key_entry = MCPApiKey.query.filter_by(user_id=current_user.id, service_id='spotify').first()
            if key_entry:
                return jsonify({
                    'success': True,
                    'client_id': key_entry.spotify_client_id or '',
                    'client_secret': key_entry.spotify_client_secret or '',
                    'redirect_uri': key_entry.spotify_redirect_uri or '',
                    'username': key_entry.spotify_username or ''
                })
            else:
                return jsonify({'success': True, 'client_id': '', 'client_secret': '', 'redirect_uri': '', 'username': ''})
        return jsonify({'success': False, 'error': 'Unsupported service.'}), 400
    # POST logic
    try:
        data = request.get_json()
        service = data.get('service')
        client_id = data.get('client_id')
        client_secret = data.get('client_secret')
        redirect_uri = data.get('redirect_uri')
        username = data.get('username')
        if not service or not client_id or not client_secret:
            return jsonify({'success': False, 'error': 'Missing required fields.'}), 400
        if service == 'spotify':
            key_entry = MCPApiKey.query.filter_by(user_id=current_user.id, service_id='spotify').first()
            if key_entry:
                key_entry.spotify_client_id = client_id
                key_entry.spotify_client_secret = client_secret
                key_entry.spotify_redirect_uri = redirect_uri
                key_entry.spotify_username = username
            else:
                key_entry = MCPApiKey(user_id=current_user.id, service_id='spotify',
                                     spotify_client_id=client_id,
                                     spotify_client_secret=client_secret,
                                     spotify_redirect_uri=redirect_uri,
                                     spotify_username=username)
                db.session.add(key_entry)
            db.session.commit()
            return jsonify({'success': True})
        return jsonify({'success': False, 'error': 'Unsupported service.'}), 400
    except Exception as e:
        import traceback
        return jsonify({'success': False, 'error': str(e), 'trace': traceback.format_exc()}), 500
