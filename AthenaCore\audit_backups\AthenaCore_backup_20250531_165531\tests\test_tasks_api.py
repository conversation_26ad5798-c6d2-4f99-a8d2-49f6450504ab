# tests/test_tasks_api.py

"""
Tests for the Background Task and Scheduled Task API endpoints.

This file contains tests for:
- Creating background tasks
- Listing and retrieving background tasks
- Updating task progress
- Cancelling tasks
- Creating scheduled tasks
- Managing scheduled tasks
"""

import json
import unittest
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from flask import url_for
from flask_login import current_user

from src.models import db, User
from src.login.device_models import Device, Command, ScheduledTask
from src.services.task_executor import task_executor_service
from src.services.task_scheduler import task_scheduler_service

# Import the app and create a test client
from main import app

class TasksAPITestCase(unittest.TestCase):
    """Test case for the Tasks API endpoints."""
    
    def setUp(self):
        """Set up test client and fixtures."""
        # Configure the application for testing
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        app.config['WTF_CSRF_ENABLED'] = False
        
        # Create test client
        self.client = app.test_client()
        
        # Create application context
        self.app_context = app.app_context()
        self.app_context.push()
        
        # Create database tables
        db.create_all()
        
        # Create test user
        self.user = User(
            username="testuser",
            email="<EMAIL>"
        )
        self.user.set_password("testpassword")
        db.session.add(self.user)
        db.session.commit()
        
        # Create test device
        self.device = Device(
            device_uuid="test-device-uuid",
            name="Test Device",
            device_type="test_device",
            user_id=self.user.id,
            is_active=True
        )
        db.session.add(self.device)
        db.session.commit()
        
        # Login the test user
        with self.client.session_transaction() as session:
            session['user_id'] = self.user.id
        
    def tearDown(self):
        """Clean up after tests."""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_create_background_task(self):
        """Test creating a background task."""
        # Prepare task data
        task_data = {
            "target_device_id": self.device.id,
            "capability_name": "test_capability",
            "parameters": {"test_param": "test_value"},
            "priority_level": 7,
            "max_runtime": 1800
        }
        
        # Mock the create_command function
        with patch('src.api.tasks.create_command') as mock_create_command:
            # Create a mock command
            mock_command = MagicMock()
            mock_command.command_uuid = "test-command-uuid"
            mock_command.to_dict.return_value = {
                "command_uuid": "test-command-uuid",
                "status": "pending",
                "capability_name": "test_capability"
            }
            mock_create_command.return_value = mock_command
            
            # Send request
            response = self.client.post(
                '/api/tasks/background',
                json=task_data,
                content_type='application/json'
            )
            
            # Check response
            self.assertEqual(response.status_code, 201)
            data = json.loads(response.data)
            self.assertEqual(data['task']['command_uuid'], "test-command-uuid")
            self.assertEqual(data['message'], "Background task created successfully")
            
            # Verify mock was called with correct arguments
            mock_create_command.assert_called_once()
            args, kwargs = mock_create_command.call_args
            self.assertEqual(kwargs['user_id'], self.user.id)
            self.assertEqual(kwargs['target_device_id'], self.device.id)
            self.assertEqual(kwargs['capability_name'], "test_capability")
            self.assertEqual(kwargs['is_background'], True)
            self.assertEqual(kwargs['priority_level'], 7)
    
    def test_list_background_tasks(self):
        """Test listing background tasks."""
        # Create some test tasks
        for i in range(3):
            command = Command(
                command_uuid=f"test-command-{i}",
                user_id=self.user.id,
                target_device_id=self.device.id,
                capability_name="test_capability",
                parameters=json.dumps({"test": f"value-{i}"}),
                is_background=True,
                status="pending" if i == 0 else ("running" if i == 1 else "completed"),
                priority_level=5
            )
            db.session.add(command)
        db.session.commit()
        
        # Send request
        response = self.client.get('/api/tasks/background')
        
        # Check response
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(len(data['tasks']), 3)
        
        # Test filtering by status
        response = self.client.get('/api/tasks/background?status=running')
        data = json.loads(response.data)
        self.assertEqual(len(data['tasks']), 1)
        self.assertEqual(data['tasks'][0]['status'], "running")
    
    def test_cancel_background_task(self):
        """Test cancelling a background task."""
        # Create a test task
        command = Command(
            command_uuid="test-command-uuid",
            user_id=self.user.id,
            target_device_id=self.device.id,
            capability_name="test_capability",
            parameters=json.dumps({"test": "value"}),
            is_background=True,
            status="running",
            priority_level=5
        )
        db.session.add(command)
        db.session.commit()
        
        # Send request
        response = self.client.post(f'/api/tasks/background/{command.command_uuid}/cancel')
        
        # Check response
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['message'], "Background task cancelled successfully")
        
        # Verify task was cancelled
        updated_command = Command.query.filter_by(command_uuid=command.command_uuid).first()
        self.assertEqual(updated_command.status, "cancelled")
        self.assertIsNotNone(updated_command.completed_at)
    
    def test_update_task_progress(self):
        """Test updating a task's progress."""
        # Create a test task
        command = Command(
            command_uuid="test-command-uuid",
            user_id=self.user.id,
            target_device_id=self.device.id,
            capability_name="test_capability",
            parameters=json.dumps({"test": "value"}),
            is_background=True,
            status="running",
            priority_level=5,
            progress=0
        )
        db.session.add(command)
        db.session.commit()
        
        # Send request
        progress_data = {
            "progress": 50,
            "status": "running",
            "message": "Task is 50% complete"
        }
        response = self.client.post(
            f'/api/tasks/background/{command.command_uuid}/progress',
            json=progress_data,
            content_type='application/json'
        )
        
        # Check response
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['message'], "Background task progress updated successfully")
        
        # Verify task progress was updated
        updated_command = Command.query.filter_by(command_uuid=command.command_uuid).first()
        self.assertEqual(updated_command.progress, 50)
        self.assertEqual(updated_command.status, "running")
    
    def test_create_scheduled_task(self):
        """Test creating a scheduled task."""
        # Prepare task data
        task_data = {
            "name": "Test Scheduled Task",
            "capability_name": "test_capability",
            "parameters": {"test_param": "test_value"},
            "schedule_type": "interval",
            "schedule_data": {"minutes": 30},
            "target_device_id": self.device.id,
            "priority_level": 6,
            "is_recurring": True
        }
        
        # Mock the create_scheduled_task function
        with patch('src.api.tasks.create_scheduled_task') as mock_create_scheduled_task:
            # Create a mock task
            mock_task = MagicMock()
            mock_task.task_uuid = "test-task-uuid"
            mock_task.to_dict.return_value = {
                "task_uuid": "test-task-uuid",
                "name": "Test Scheduled Task",
                "schedule_type": "interval"
            }
            mock_create_scheduled_task.return_value = mock_task
            
            # Send request
            response = self.client.post(
                '/api/tasks/scheduled',
                json=task_data,
                content_type='application/json'
            )
            
            # Check response
            self.assertEqual(response.status_code, 201)
            data = json.loads(response.data)
            self.assertEqual(data['task']['task_uuid'], "test-task-uuid")
            self.assertEqual(data['message'], "Scheduled task created successfully")
            
            # Verify mock was called with correct arguments
            mock_create_scheduled_task.assert_called_once()
            args, kwargs = mock_create_scheduled_task.call_args
            self.assertEqual(kwargs['user_id'], self.user.id)
            self.assertEqual(kwargs['name'], "Test Scheduled Task")
            self.assertEqual(kwargs['capability_name'], "test_capability")
            self.assertEqual(kwargs['target_device_id'], self.device.id)
            self.assertEqual(kwargs['schedule_type'], "interval")
            self.assertEqual(kwargs['priority_level'], 6)
    
    def test_list_scheduled_tasks(self):
        """Test listing scheduled tasks."""
        # Create some test scheduled tasks
        for i in range(3):
            task = ScheduledTask(
                task_uuid=f"test-task-{i}",
                user_id=self.user.id,
                name=f"Test Task {i}",
                capability_name="test_capability",
                parameters=json.dumps({"test": f"value-{i}"}),
                schedule_type="interval",
                schedule_data=json.dumps({"minutes": 30}),
                is_active=True if i < 2 else False,
                priority_level=5
            )
            db.session.add(task)
        db.session.commit()
        
        # Send request for all tasks
        response = self.client.get('/api/tasks/scheduled?active_only=false')
        
        # Check response
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(len(data['tasks']), 3)
        
        # Test filtering by active_only (default=true)
        response = self.client.get('/api/tasks/scheduled')
        data = json.loads(response.data)
        self.assertEqual(len(data['tasks']), 2)
    
    def test_trigger_scheduled_task(self):
        """Test manually triggering a scheduled task."""
        # Create a test scheduled task
        task = ScheduledTask(
            task_uuid="test-task-uuid",
            user_id=self.user.id,
            name="Test Task",
            capability_name="test_capability",
            parameters=json.dumps({"test": "value"}),
            schedule_type="interval",
            schedule_data=json.dumps({"minutes": 30}),
            is_active=True,
            priority_level=5
        )
        db.session.add(task)
        db.session.commit()
        
        # Mock the task execution
        with patch('src.api.tasks._execute_scheduled_task') as mock_execute_task:
            # Create a mock command
            mock_command = MagicMock()
            mock_command.command_uuid = "test-command-uuid"
            mock_command.to_dict.return_value = {
                "command_uuid": "test-command-uuid",
                "status": "pending",
                "capability_name": "test_capability"
            }
            mock_execute_task.return_value = mock_command
            
            # Send request
            response = self.client.post(f'/api/tasks/scheduled/{task.task_uuid}/trigger')
            
            # Check response
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            self.assertEqual(data['message'], "Scheduled task triggered successfully")
            self.assertEqual(data['command']['command_uuid'], "test-command-uuid")
            
            # Verify mock was called
            mock_execute_task.assert_called_once()

if __name__ == '__main__':
    unittest.main()
