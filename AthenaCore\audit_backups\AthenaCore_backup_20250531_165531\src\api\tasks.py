# src/api/tasks.py

"""
API endpoints for background tasks and scheduled tasks in the Cross-Device API System.

This file implements:
- Background task creation and management
- Scheduled task creation and management
- Task status checking and reporting
- Task cancellation and scheduling
"""

import json
import logging
import uuid
from datetime import datetime, timedelta

from flask import Blueprint, jsonify, request, current_app
from flask_login import current_user, login_required
from functools import wraps

# Import socket service for real-time updates
from src.services.socket import emit_task_update, emit_task_progress, emit_notification

from src.models import db, User
# Import device models directly instead of using the deprecated module
from src.models.device import (
    Device, Command, CommandLog, ScheduledTask,
    create_command, add_command_log, get_device_or_404,
    create_scheduled_task
)

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger("tasks_api")

# Create blueprint
tasks_bp = Blueprint("tasks", __name__, url_prefix="/api/tasks")

# Add a general task endpoint to handle query parameters
@tasks_bp.route("", methods=["GET"])
@login_required
def get_tasks():
    """
    Get tasks based on query parameters.
    
    Query Parameters:
        status (str, optional): Filter by status ('active' or 'completed')
        limit (int, optional): Maximum number of tasks to return (default: 50)
        
    Returns:
        JSON response with tasks matching the criteria
    """
    status = request.args.get('status')
    limit = int(request.args.get('limit', 50))
    
    # Build query based on status
    query = Command.query.filter_by(user_id=current_user.id, is_background=True)
    
    if status == 'active':
        query = query.filter(Command.status.in_(["pending", "running"]))
    elif status == 'completed':
        query = query.filter(Command.status.in_(["completed", "failed", "cancelled"]))
    
    # Apply limit and sort
    tasks = query.order_by(Command.created_at.desc()).limit(limit).all()
    
    # Return tasks as JSON
    return jsonify({
        "count": len(tasks),
        "tasks": [task.to_dict() for task in tasks]
    })

# Helper Functions

def parse_schedule_data(schedule_type, data):
    """
    Parse and validate schedule data based on schedule type.
    
    Args:
        schedule_type (str): The type of schedule ("interval", "cron", "event", "trigger")
        data (dict): The schedule configuration data
        
    Returns:
        tuple: (is_valid, schedule_data, error_message)
    """
    if schedule_type == "interval":
        # Validate interval data
        if not any(key in data for key in ["seconds", "minutes", "hours", "days"]):
            return False, None, "Interval schedule must specify seconds, minutes, hours, or days"
        
        # Convert all values to integers
        for key in ["seconds", "minutes", "hours", "days"]:
            if key in data:
                try:
                    data[key] = int(data[key])
                except (ValueError, TypeError):
                    return False, None, f"Invalid {key} value, must be an integer"
        
        return True, data, None
        
    elif schedule_type == "cron":
        # Validate cron expression
        if "expression" not in data:
            return False, None, "Cron schedule must include an expression"
        
        # Here you would validate the cron expression format
        # For simplicity, we'll just check if it exists
        return True, data, None
        
    elif schedule_type == "event":
        # Validate event trigger
        if "event_type" not in data:
            return False, None, "Event schedule must include an event_type"
        
        return True, data, None
        
    elif schedule_type == "trigger":
        # Validate trigger condition
        if "condition" not in data:
            return False, None, "Trigger schedule must include a condition"
        
        return True, data, None
        
    else:
        return False, None, f"Unknown schedule type: {schedule_type}"

# Background Tasks API Endpoints

@tasks_bp.route("/background", methods=["POST"])
@login_required
def create_background_task():
    """
    Create a new background task.
    
    Request Body:
        target_device_id (int): ID of the device to execute the task
        capability_name (str): Name of the capability to invoke
        parameters (dict): Parameters for the task
        source_device_id (int, optional): ID of the device sending the task
        priority_level (int, optional): Priority level (1-10, default: 5)
        max_runtime (int, optional): Maximum runtime in seconds (0 for unlimited)
        parent_command_id (int, optional): ID of the parent command if this is a sub-task
        
    Returns:
        JSON response with the created task
    """
    logger.info(f"Background task creation request received from user: {current_user.id}")
    
    data = request.json or {}
    
    # Validate required fields
    required_fields = ["target_device_id", "capability_name", "parameters"]
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        return jsonify({
            "error": f"Missing required fields: {', '.join(missing_fields)}"
        }), 400
    
    # Check if target device exists and belongs to user
    target_device = Device.query.filter_by(
        id=data["target_device_id"], 
        user_id=current_user.id
    ).first()
    
    if not target_device:
        return jsonify({
            "error": f"Target device with ID {data['target_device_id']} not found"
        }), 404
    
    # Check if capability is supported by target device
    capability_name = data["capability_name"]
    device_capabilities = [cap.capability_name for cap in target_device.capabilities]
    
    if capability_name not in device_capabilities:
        return jsonify({
            "error": f"Target device does not support capability: {capability_name}",
            "supported_capabilities": device_capabilities
        }), 400
    
    # Get optional parameters
    source_device_id = data.get("source_device_id")
    priority_level = data.get("priority_level", 5)
    max_runtime = data.get("max_runtime", 3600)
    parent_command_id = data.get("parent_command_id")
    
    # Create the background command
    try:
        command = create_command(
            user_id=current_user.id,
            target_device_id=data["target_device_id"],
            capability_name=capability_name,
            parameters=data["parameters"],
            source_device_id=source_device_id,
            priority="normal",  # Use priority_level for finer control
            expires_in_hours=24,
            is_background=True,
            parent_command_id=parent_command_id,
            priority_level=priority_level
        )
        
        # Add additional background task properties
        command.max_runtime = max_runtime
        command.progress = 0
        
        db.session.commit()
        
        logger.info(f"Background task created: {command.command_uuid}")
        
        return jsonify({
            "task": command.to_dict(),
            "message": "Background task created successfully"
        }), 201
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error creating background task: {str(e)}")
        return jsonify({
            "error": f"Failed to create background task: {str(e)}"
        }), 500

@tasks_bp.route("/background", methods=["GET"])
@login_required
def list_background_tasks():
    """
    Get a list of background tasks for the current user.
    
    Query Parameters:
        status (str, optional): Filter by status
        device_uuid (str, optional): Filter by device UUID
        limit (int, optional): Maximum number of tasks to return (default: 20)
        
    Returns:
        JSON response with list of background tasks
    """
    status = request.args.get("status")
    device_uuid = request.args.get("device_uuid")
    limit = int(request.args.get("limit", 20))
    
    # Build query
    query = Command.query.filter_by(
        user_id=current_user.id,
        is_background=True
    )
    
    # Apply filters
    if status:
        query = query.filter_by(status=status)
        
    if device_uuid:
        device = Device.query.filter_by(
            device_uuid=device_uuid,
            user_id=current_user.id
        ).first()
        
        if device:
            query = query.filter_by(target_device_id=device.id)
        else:
            return jsonify({
                "error": f"Device with UUID {device_uuid} not found"
            }), 404
    
    # Order by most recently created
    query = query.order_by(Command.created_at.desc())
    
    # Apply limit
    if limit > 0:
        query = query.limit(limit)
        
    tasks = query.all()
    
    return jsonify({
        "tasks": [task.to_dict() for task in tasks],
        "count": len(tasks)
    })

@tasks_bp.route("/background/<string:command_uuid>", methods=["GET"])
@login_required
def get_background_task(command_uuid):
    """
    Get details for a specific background task.
    
    Path Parameters:
        command_uuid (str): UUID of the task
        
    Returns:
        JSON response with task details and logs
    """
    command = Command.query.filter_by(
        command_uuid=command_uuid,
        user_id=current_user.id,
        is_background=True
    ).first_or_404(description=f"Background task with UUID {command_uuid} not found")
    
    # Get command logs
    logs = CommandLog.query.filter_by(command_id=command.id).order_by(CommandLog.timestamp.asc()).all()
    
    return jsonify({
        "task": command.to_dict(),
        "logs": [log.to_dict() for log in logs]
    })

@tasks_bp.route("/background/<string:command_uuid>/cancel", methods=["POST"])
@login_required
def cancel_background_task(command_uuid):
    """
    Cancel a background task.
    
    Path Parameters:
        command_uuid (str): UUID of the task to cancel
        
    Returns:
        JSON response confirming cancellation
    """
    command = Command.query.filter_by(
        command_uuid=command_uuid,
        user_id=current_user.id,
        is_background=True
    ).first_or_404(description=f"Background task with UUID {command_uuid} not found")
    
    # Check if task can be cancelled
    if command.status in ["completed", "failed", "expired"]:
        return jsonify({
            "error": f"Cannot cancel task with status: {command.status}"
        }), 400
    
    # Cancel the task
    command.status = "cancelled"
    command.completed_at = datetime.utcnow()
    
    # Add log entry
    add_command_log(
        command=command,
        status="cancelled",
        message="Task cancelled by user",
        log_data={"cancelled_at": datetime.utcnow().isoformat()}
    )
    
    db.session.commit()
    
    # Emit task update event via socket
    emit_task_update(command)
    emit_notification(current_user.id, "Task Cancelled", 
                   f"Task '{command.capability_name}' has been cancelled.", "warning")
    
    logger.info(f"Background task cancelled: {command_uuid}")
    
    return jsonify({
        "message": "Background task cancelled successfully",
        "task": command.to_dict()
    })

@tasks_bp.route("/background/<string:command_uuid>/progress", methods=["POST"])
@login_required
def update_task_progress(command_uuid):
    """
    Update the progress of a background task.
    
    Path Parameters:
        command_uuid (str): UUID of the task
        
    Request Body:
        progress (int): Progress value (0-100)
        status (str, optional): New status for the task
        message (str, optional): Progress message
        
    Returns:
        JSON response confirming the update
    """
    data = request.json or {}
    
    # Validate progress value
    if "progress" not in data:
        return jsonify({
            "error": "Progress value is required"
        }), 400
    
    try:
        progress = int(data["progress"])
        if progress < 0 or progress > 100:
            return jsonify({
                "error": "Progress must be between 0 and 100"
            }), 400
    except (ValueError, TypeError):
        return jsonify({
            "error": "Progress must be an integer"
        }), 400
    
    # Get the task
    command = Command.query.filter_by(
        command_uuid=command_uuid,
        user_id=current_user.id,
        is_background=True
    ).first_or_404(description=f"Background task with UUID {command_uuid} not found")
    
    # Update progress
    command.progress = progress
    
    # Update status if provided
    if "status" in data:
        status = data["status"]
        valid_statuses = ["pending", "delivered", "running", "completed", "failed"]
        
        if status not in valid_statuses:
            return jsonify({
                "error": f"Invalid status: {status}. Must be one of: {', '.join(valid_statuses)}"
            }), 400
        
        command.status = status
        
        # Update timestamps based on status
        if status == "delivered":
            command.delivered_at = datetime.utcnow()
        elif status in ["completed", "failed"]:
            command.completed_at = datetime.utcnow()
    
    # Add log entry
    add_command_log(
        command=command,
        status=data.get("status", command.status),
        message=data.get("message", f"Progress updated to {progress}%"),
        log_data={"progress": progress}
    )
    
    db.session.commit()
    
    # Emit task progress update via socket
    emit_task_progress(command, data.get("message"), progress)
    
    logger.info(f"Background task progress updated: {command_uuid} ({progress}%)")
    
    return jsonify({
        "message": "Background task progress updated successfully",
        "task": command.to_dict()
    })

# Task Logs API Endpoints

@tasks_bp.route("/background/<string:command_uuid>/logs", methods=["GET"])
@login_required
def get_task_logs(command_uuid):
    """
    Get logs for a specific background task.
    
    Path Parameters:
        command_uuid (str): UUID of the task
        
    Query Parameters:
        limit (int, optional): Maximum number of logs to return (default: 50)
        
    Returns:
        JSON response with task logs
    """
    # Get limit from query parameters (default: 50)
    try:
        limit = int(request.args.get("limit", 50))
    except ValueError:
        limit = 50
        
    # Find the command
    command = Command.query.filter_by(
        command_uuid=command_uuid,
        user_id=current_user.id
    ).first_or_404()
    
    # Get logs for the command
    logs = CommandLog.query.filter_by(
        command_id=command.id
    ).order_by(
        CommandLog.timestamp.asc()
    ).limit(limit).all()
    
    # Return logs as JSON
    return jsonify({
        "logs": [log.to_dict() for log in logs]
    })

@tasks_bp.route("/background/active", methods=["GET"])
@login_required
def get_active_tasks():
    """
    Get active background tasks for the current user.
    
    Returns:
        JSON response with active tasks and count
    """
    # Get active tasks
    active_tasks = Command.query.filter_by(
        user_id=current_user.id,
        is_background=True
    ).filter(
        Command.status.in_(["pending", "running"])
    ).order_by(
        Command.created_at.desc()
    ).all()
    
    # Return tasks as JSON
    return jsonify({
        "count": len(active_tasks),
        "tasks": [task.to_dict() for task in active_tasks]
    })

@tasks_bp.route("/background/completed", methods=["GET"])
@login_required
def get_completed_tasks():
    """
    Get completed background tasks for the current user.
    
    Query Parameters:
        limit (int, optional): Maximum number of tasks to return (default: 10)
        
    Returns:
        JSON response with completed tasks
    """
    # Get limit from query parameters (default: 10)
    try:
        limit = int(request.args.get("limit", 10))
    except ValueError:
        limit = 10
        
    # Get completed tasks
    completed_tasks = Command.query.filter_by(
        user_id=current_user.id,
        is_background=True
    ).filter(
        Command.status.in_(["completed", "failed", "cancelled", "expired"])
    ).order_by(
        Command.completed_at.desc()
    ).limit(limit).all()
    
    # Return tasks as JSON
    return jsonify({
        "count": len(completed_tasks),
        "tasks": [task.to_dict() for task in completed_tasks]
    })

# Scheduled Tasks API Endpoints

@tasks_bp.route("/scheduled", methods=["POST"])
@login_required
def create_scheduled_task_endpoint():
    """
    Create a new scheduled task.
    
    Request Body:
        name (str): Name for the scheduled task
        capability_name (str): Name of the capability to invoke
        parameters (dict): Parameters for the task
        schedule_type (str): Type of schedule ("interval", "cron", "event", "trigger")
        schedule_data (dict): Schedule configuration
        target_device_id (int, optional): ID of a specific device to target
        device_type_filter (str, optional): Filter to a specific device type
        capability_filter (str, optional): Filter to devices with this capability
        trigger_condition (dict, optional): Conditions to trigger this task
        priority_level (int, optional): Priority level (1-10, default: 5)
        is_recurring (bool, optional): Whether the task repeats after execution
        
    Returns:
        JSON response with the created scheduled task
    """
    logger.info(f"Scheduled task creation request received from user: {current_user.id}")
    
    data = request.json or {}
    
    # Validate required fields
    required_fields = ["name", "capability_name", "parameters", "schedule_type", "schedule_data"]
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        return jsonify({
            "error": f"Missing required fields: {', '.join(missing_fields)}"
        }), 400
    
    # Parse and validate schedule data
    schedule_type = data["schedule_type"]
    schedule_data = data["schedule_data"]
    
    is_valid, parsed_schedule_data, error_message = parse_schedule_data(schedule_type, schedule_data)
    if not is_valid:
        return jsonify({
            "error": error_message
        }), 400
    
    # Validate device targeting (must have at least one targeting method)
    target_device_id = data.get("target_device_id")
    device_type_filter = data.get("device_type_filter")
    capability_filter = data.get("capability_filter")
    
    if not any([target_device_id, device_type_filter, capability_filter]):
        return jsonify({
            "error": "Task must have at least one device targeting method: target_device_id, device_type_filter, or capability_filter"
        }), 400
    
    # Check if target_device_id exists and belongs to user
    if target_device_id:
        target_device = Device.query.filter_by(
            id=target_device_id,
            user_id=current_user.id
        ).first()
        
        if not target_device:
            return jsonify({
                "error": f"Target device with ID {target_device_id} not found"
            }), 404
            
        # Check if capability is supported by target device
        if not any(cap.capability_name == data["capability_name"] for cap in target_device.capabilities):
            return jsonify({
                "error": f"Target device does not support capability: {data['capability_name']}"
            }), 400
    
    # Create the scheduled task
    try:
        scheduled_task = create_scheduled_task(
            user_id=current_user.id,
            name=data["name"],
            capability_name=data["capability_name"],
            parameters=data["parameters"],
            schedule_type=schedule_type,
            schedule_data=parsed_schedule_data,
            target_device_id=target_device_id,
            device_type_filter=device_type_filter,
            capability_filter=capability_filter,
            trigger_condition=data.get("trigger_condition"),
            priority_level=data.get("priority_level", 5),
            is_recurring=data.get("is_recurring", True)
        )
        
        db.session.commit()
        
        logger.info(f"Scheduled task created: {scheduled_task.task_uuid}")
        
        return jsonify({
            "task": scheduled_task.to_dict(),
            "message": "Scheduled task created successfully"
        }), 201
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error creating scheduled task: {str(e)}")
        return jsonify({
            "error": f"Failed to create scheduled task: {str(e)}"
        }), 500

@tasks_bp.route("/scheduled", methods=["GET"])
@login_required
def list_scheduled_tasks():
    """
    Get a list of scheduled tasks for the current user.
    
    Query Parameters:
        active_only (bool): If true, only return active tasks (default: true)
        schedule_type (str, optional): Filter by schedule type
        limit (int, optional): Maximum number of tasks to return (default: 20)
        
    Returns:
        JSON response with list of scheduled tasks
    """
    active_only = request.args.get("active_only", "true").lower() == "true"
    schedule_type = request.args.get("schedule_type")
    limit = int(request.args.get("limit", 20))
    
    # Build query
    query = ScheduledTask.query.filter_by(user_id=current_user.id)
    
    # Apply filters
    if active_only:
        query = query.filter_by(is_active=True)
        
    if schedule_type:
        query = query.filter_by(schedule_type=schedule_type)
    
    # Order by most recently created
    query = query.order_by(ScheduledTask.created_at.desc())
    
    # Apply limit
    if limit > 0:
        query = query.limit(limit)
        
    tasks = query.all()
    
    return jsonify({
        "tasks": [task.to_dict() for task in tasks],
        "count": len(tasks)
    })

@tasks_bp.route("/scheduled/<string:task_uuid>", methods=["GET"])
@login_required
def get_scheduled_task(task_uuid):
    """
    Get details for a specific scheduled task.
    
    Path Parameters:
        task_uuid (str): UUID of the scheduled task
        
    Returns:
        JSON response with task details
    """
    scheduled_task = ScheduledTask.query.filter_by(
        task_uuid=task_uuid,
        user_id=current_user.id
    ).first_or_404(description=f"Scheduled task with UUID {task_uuid} not found")
    
    return jsonify(scheduled_task.to_dict())

@tasks_bp.route("/scheduled/<string:task_uuid>", methods=["PUT"])
@login_required
def update_scheduled_task(task_uuid):
    """
    Update a scheduled task.
    
    Path Parameters:
        task_uuid (str): UUID of the scheduled task
        
    Request Body:
        name (str, optional): New name for the task
        parameters (dict, optional): New parameters
        schedule_data (dict, optional): New schedule configuration
        target_device_id (int, optional): New target device ID
        device_type_filter (str, optional): New device type filter
        capability_filter (str, optional): New capability filter
        trigger_condition (dict, optional): New trigger condition
        priority_level (int, optional): New priority level
        is_active (bool, optional): Whether the task is active
        is_recurring (bool, optional): Whether the task repeats after execution
        
    Returns:
        JSON response with the updated task
    """
    data = request.json or {}
    
    if not data:
        return jsonify({
            "error": "No update data provided"
        }), 400
    
    # Get the task
    scheduled_task = ScheduledTask.query.filter_by(
        task_uuid=task_uuid,
        user_id=current_user.id
    ).first_or_404(description=f"Scheduled task with UUID {task_uuid} not found")
    
    # Update the fields
    if "name" in data:
        scheduled_task.name = data["name"]
        
    if "parameters" in data:
        if isinstance(data["parameters"], dict):
            scheduled_task.parameters = json.dumps(data["parameters"])
        else:
            scheduled_task.parameters = data["parameters"]
    
    if "schedule_data" in data and "schedule_type" in data:
        # Validate schedule data
        is_valid, parsed_schedule_data, error_message = parse_schedule_data(data["schedule_type"], data["schedule_data"])
        if not is_valid:
            return jsonify({
                "error": error_message
            }), 400
            
        scheduled_task.schedule_type = data["schedule_type"]
        scheduled_task.schedule_data = json.dumps(parsed_schedule_data)
        
        # Calculate next run time for interval schedules
        if data["schedule_type"] == "interval":
            interval_seconds = parsed_schedule_data.get("seconds", 0)
            interval_minutes = parsed_schedule_data.get("minutes", 0)
            interval_hours = parsed_schedule_data.get("hours", 0)
            interval_days = parsed_schedule_data.get("days", 0)
            
            delta = timedelta(
                seconds=interval_seconds,
                minutes=interval_minutes,
                hours=interval_hours,
                days=interval_days
            )
            
            scheduled_task.next_run = datetime.utcnow() + delta
    
    # Update device targeting
    if "target_device_id" in data:
        target_device_id = data["target_device_id"]
        if target_device_id:
            # Check if device exists and belongs to user
            target_device = Device.query.filter_by(
                id=target_device_id,
                user_id=current_user.id
            ).first()
            
            if not target_device:
                return jsonify({
                    "error": f"Target device with ID {target_device_id} not found"
                }), 404
                
            # Check if capability is supported by target device
            if not any(cap.capability_name == scheduled_task.capability_name for cap in target_device.capabilities):
                return jsonify({
                    "error": f"Target device does not support capability: {scheduled_task.capability_name}"
                }), 400
                
        scheduled_task.target_device_id = target_device_id
    
    if "device_type_filter" in data:
        scheduled_task.device_type_filter = data["device_type_filter"]
        
    if "capability_filter" in data:
        scheduled_task.capability_filter = data["capability_filter"]
        
    if "trigger_condition" in data:
        if isinstance(data["trigger_condition"], dict):
            scheduled_task.trigger_condition = json.dumps(data["trigger_condition"])
        else:
            scheduled_task.trigger_condition = data["trigger_condition"]
    
    if "priority_level" in data:
        scheduled_task.priority_level = int(data["priority_level"])
        
    if "is_active" in data:
        scheduled_task.is_active = bool(data["is_active"])
        
    if "is_recurring" in data:
        scheduled_task.is_recurring = bool(data["is_recurring"])
    
    # After all updates, check if at least one targeting method exists
    if not any([scheduled_task.target_device_id, scheduled_task.device_type_filter, scheduled_task.capability_filter]):
        return jsonify({
            "error": "Task must have at least one device targeting method: target_device_id, device_type_filter, or capability_filter"
        }), 400
    
    try:
        db.session.commit()
        
        logger.info(f"Scheduled task updated: {task_uuid}")
        
        return jsonify({
            "task": scheduled_task.to_dict(),
            "message": "Scheduled task updated successfully"
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error updating scheduled task: {str(e)}")
        return jsonify({
            "error": f"Failed to update scheduled task: {str(e)}"
        }), 500

@tasks_bp.route("/scheduled/<string:task_uuid>/trigger", methods=["POST"])
@login_required
def trigger_scheduled_task(task_uuid):
    """
    Manually trigger a scheduled task to run immediately.
    
    Path Parameters:
        task_uuid (str): UUID of the scheduled task to trigger
        
    Returns:
        JSON response with the triggered command
    """
    # Get the scheduled task
    scheduled_task = ScheduledTask.query.filter_by(
        task_uuid=task_uuid,
        user_id=current_user.id
    ).first_or_404(description=f"Scheduled task with UUID {task_uuid} not found")
    
    if not scheduled_task.is_active:
        return jsonify({
            "error": "Cannot trigger inactive task"
        }), 400
    
    # Determine target device
    target_device_id = None
    
    if scheduled_task.target_device_id:
        # Use specified device
        target_device_id = scheduled_task.target_device_id
    else:
        # Find a matching device based on filters
        query = Device.query.filter_by(
            user_id=current_user.id,
            is_active=True
        )
        
        if scheduled_task.device_type_filter:
            query = query.filter_by(device_type=scheduled_task.device_type_filter)
            
        devices = query.all()
        
        if scheduled_task.capability_filter:
            # Filter devices by capability
            filtered_devices = []
            for device in devices:
                if any(cap.capability_name == scheduled_task.capability_filter for cap in device.capabilities):
                    filtered_devices.append(device)
            devices = filtered_devices
        
        if not devices:
            return jsonify({
                "error": "No matching devices found to execute this task"
            }), 404
            
        # Use the first matching device
        target_device_id = devices[0].id
    
    # Create command for this task
    try:
        # Parse parameters
        parameters = scheduled_task.parameters
        if not isinstance(parameters, dict):
            try:
                parameters = json.loads(parameters)
            except json.JSONDecodeError:
                parameters = {}
        
        # Create the command
        command = create_command(
            user_id=current_user.id,
            target_device_id=target_device_id,
            capability_name=scheduled_task.capability_name,
            parameters=parameters,
            priority="normal",
            is_background=True,
            priority_level=scheduled_task.priority_level
        )
        
        # Update task's last run time
        scheduled_task.last_run = datetime.utcnow()
        
        # If it's a recurring interval task, calculate next run time
        if scheduled_task.is_recurring and scheduled_task.schedule_type == "interval":
            try:
                schedule_data = json.loads(scheduled_task.schedule_data)
                interval_seconds = schedule_data.get("seconds", 0)
                interval_minutes = schedule_data.get("minutes", 0)
                interval_hours = schedule_data.get("hours", 0)
                interval_days = schedule_data.get("days", 0)
                
                delta = timedelta(
                    seconds=interval_seconds,
                    minutes=interval_minutes,
                    hours=interval_hours,
                    days=interval_days
                )
                
                scheduled_task.next_run = datetime.utcnow() + delta
            except json.JSONDecodeError:
                # If we can't parse the interval, just set next_run to 1 hour from now
                scheduled_task.next_run = datetime.utcnow() + timedelta(hours=1)
        
        db.session.commit()
        
        logger.info(f"Scheduled task triggered: {task_uuid}, command: {command.command_uuid}")
        
        return jsonify({
            "message": "Scheduled task triggered successfully",
            "command": command.to_dict(),
            "task": scheduled_task.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error triggering scheduled task: {str(e)}")
        return jsonify({
            "error": f"Failed to trigger scheduled task: {str(e)}"
        }), 500

@tasks_bp.route("/scheduled/<string:task_uuid>", methods=["DELETE"])
@login_required
def delete_scheduled_task(task_uuid):
    """
    Delete a scheduled task.
    
    Path Parameters:
        task_uuid (str): UUID of the scheduled task to delete
        
    Returns:
        JSON response confirming deletion
    """
    scheduled_task = ScheduledTask.query.filter_by(
        task_uuid=task_uuid,
        user_id=current_user.id
    ).first_or_404(description=f"Scheduled task with UUID {task_uuid} not found")
    
    try:
        db.session.delete(scheduled_task)
        db.session.commit()
        
        logger.info(f"Scheduled task deleted: {task_uuid}")
        
        return jsonify({
            "message": "Scheduled task deleted successfully",
            "task_uuid": task_uuid
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error deleting scheduled task: {str(e)}")
        return jsonify({
            "error": f"Failed to delete scheduled task: {str(e)}"
        }), 500
