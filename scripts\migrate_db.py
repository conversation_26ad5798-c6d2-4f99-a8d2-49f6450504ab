import sqlite3
import os
import shutil
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   filename='db_migration.log')
logger = logging.getLogger('db_migration')

# Console handler for immediate feedback
console = logging.StreamHandler()
console.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console.setFormatter(formatter)
logger.addHandler(console)

logger.info("Database migration script started")

# Backup existing databases
def backup_db(db_path):
    if os.path.exists(db_path):
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        backup_path = f"{db_path}.{timestamp}.bak"
        shutil.copy2(db_path, backup_path)
        logger.info(f"Backed up {db_path} to {backup_path}")
        return True
    return False

# Create instance directory if it doesn't exist
if not os.path.exists("instance"):
    os.makedirs("instance")
    logger.info("Created instance directory")

# Backup existing databases
backup_db("athena.db")
backup_db("database.db")
backup_db("instance/athena.db")

# Function to merge databases
def merge_tables(source_db, target_db):
    if not os.path.exists(source_db):
        logger.info(f"Source database {source_db} not found, skipping")
        return False
        
    source_conn = sqlite3.connect(source_db)
    target_conn = sqlite3.connect(target_db)
    
    # Get tables from source database
    source_cursor = source_conn.cursor()
    source_cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = source_cursor.fetchall()
    
    logger.info(f"Found {len(tables)} tables in {source_db}")
    
    # Copy each table to target database if it doesn't exist
    for table in tables:
        table_name = table[0]
        if table_name.startswith("sqlite_"):
            continue
            
        # Check if table exists in target
        target_cursor = target_conn.cursor()
        target_cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}';")
        if not target_cursor.fetchone():
            # Table doesn't exist in target, create it
            source_cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table_name}';")
            create_table_sql = source_cursor.fetchone()[0]
            target_cursor.execute(create_table_sql)
            logger.info(f"Created table {table_name} in target database")
            
            # Copy data
            source_cursor.execute(f"SELECT * FROM {table_name}")
            rows = source_cursor.fetchall()
            if rows:
                columns = [description[0] for description in source_cursor.description]
                placeholders = ",".join(["?" for _ in columns])
                insert_sql = f"INSERT INTO {table_name} VALUES ({placeholders})"
                target_cursor.executemany(insert_sql, rows)
                logger.info(f"Copied {len(rows)} rows from {table_name}")
        else:
            # Table exists, try to merge data without conflicts
            logger.info(f"Table {table_name} already exists in target database, attempting to merge data")
            
            # Get primary key columns
            source_cursor.execute(f"PRAGMA table_info({table_name})")
            columns_info = source_cursor.fetchall()
            pk_columns = [col[1] for col in columns_info if col[5] > 0]  # col[5] > 0 indicates a primary key
            
            if pk_columns:
                # Table has primary keys, use them for conflict resolution
                source_cursor.execute(f"SELECT * FROM {table_name}")
                rows = source_cursor.fetchall()
                
                if rows:
                    # Get all column names
                    columns = [description[0] for description in source_cursor.description]
                    
                    # Generate INSERT OR IGNORE statement
                    placeholders = ",".join(["?" for _ in columns])
                    insert_sql = f"INSERT OR IGNORE INTO {table_name} VALUES ({placeholders})"
                    
                    # Execute batch insert
                    target_cursor.executemany(insert_sql, rows)
                    logger.info(f"Merged {target_cursor.rowcount} rows into {table_name} using primary keys")
            else:
                logger.warning(f"Table {table_name} has no primary key, skipping data merge to avoid duplicates")
            
    target_conn.commit()
    source_conn.close()
    target_conn.close()
    return True

# Create or use instance/athena.db as the target
if not os.path.exists("instance/athena.db"):
    logger.info("Creating new instance/athena.db")
    open("instance/athena.db", "a").close()

# Merge databases
logger.info("Merging athena.db into instance/athena.db...")
merge_tables("athena.db", "instance/athena.db")

logger.info("Merging database.db into instance/athena.db...")
merge_tables("database.db", "instance/athena.db")

logger.info("Database migration completed!")
print("\nMigration completed successfully! Please update the configuration files as described in the next steps.")
