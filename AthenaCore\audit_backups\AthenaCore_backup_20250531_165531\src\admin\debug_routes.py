"""
Debug settings routes - Admin interface for controlling debug output

This module provides routes for the admin debug panel, allowing
developers to enable/disable debug logging for specific system components.
"""

import logging
from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required, current_user

from src.core.debug_manager import get_debug_manager

# Setup logging
logger = logging.getLogger(__name__)

# Create blueprint
debug_bp = Blueprint("debug", __name__)


@debug_bp.route("/settings/debug", methods=["GET", "POST"])
@login_required
def debug_settings():
    """
    Handle debug settings interface.
    
    GET: Display the debug settings interface with current settings
    POST: Update debug settings based on form submission
    
    Access restricted to admin users for security.
    """
    # Only allow admins to access debug settings
    if not current_user.is_authenticated or current_user.role != 'admin':
        flash("You do not have permission to access debug settings.", "error")
        return redirect(url_for("core.settings"))
    
    debug_manager = get_debug_manager()
    
    if request.method == "POST":
        # Update debug settings from form
        debug_manager.update_from_request(request.form)
        flash("Debug settings updated successfully.", "success")
        return redirect(url_for("debug.debug_settings"))
    
    # Get current settings for template
    debug_settings = debug_manager.get_settings_dict()
    
    return render_template("settings/debug.html", debug_settings=debug_settings)
