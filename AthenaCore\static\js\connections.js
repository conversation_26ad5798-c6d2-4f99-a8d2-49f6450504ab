// Direct Connections Management JavaScript

// Debug flag - use window property to avoid redeclaration errors
window.CONNECTIONS_DEBUG = window.CONNECTIONS_DEBUG || true;

// Initialization flag - use window property to avoid redeclaration errors
window.connectionsInitialized = window.connectionsInitialized || false;

// Global variables for new UI
window.directConnections = [];
window.editingConnectionId = null;

// Provider configurations
const PROVIDER_CONFIGS = {
    openai: {
        name: "OpenAI",
        defaultUrl: "https://api.openai.com/v1/chat/completions",
        defaultModels: "gpt-4o,gpt-4-turbo,gpt-4,gpt-3.5-turbo",
        defaultEmbedding: "text-embedding-ada-002",
        keyHelp: "Get your API key from https://platform.openai.com/api-keys",
        urlHelp: "Use the default URL for standard OpenAI API"
    },
    anthropic: {
        name: "Anthropic",
        defaultUrl: "https://api.anthropic.com/v1/messages",
        defaultModels: "claude-3-opus-20240229,claude-3-sonnet-20240229,claude-3-haiku-20240307",
        defaultEmbedding: "",
        keyHelp: "Get your API key from https://console.anthropic.com/",
        urlHelp: "Use the default URL for Anthropic Claude API"
    },
    google: {
        name: "Google",
        defaultUrl: "https://generativelanguage.googleapis.com/v1beta/models",
        defaultModels: "gemini-pro,gemini-pro-vision",
        defaultEmbedding: "",
        keyHelp: "Get your API key from Google AI Studio",
        urlHelp: "Use the default URL for Google Generative AI"
    },
    azure: {
        name: "Azure OpenAI",
        defaultUrl: "https://your-resource.openai.azure.com/openai/deployments/your-deployment/chat/completions?api-version=2023-12-01-preview",
        defaultModels: "gpt-4,gpt-35-turbo",
        defaultEmbedding: "text-embedding-ada-002",
        keyHelp: "Get your API key from Azure OpenAI Service",
        urlHelp: "Replace 'your-resource' and 'your-deployment' with your actual values"
    },
    smithery: {
        name: "Smithery",
        defaultUrl: "https://smithery.ai/api/v1",
        defaultModels: "",
        defaultEmbedding: "",
        keyHelp: "Get your API key from Smithery Registry",
        urlHelp: "Use the default URL for Smithery MCP Registry"
    }
};

// Debug logging function
function log(message) {
    if (window.CONNECTIONS_DEBUG) {
        console.log(`[Connections] ${message}`);
    }
}

// Initialize function - called when page loads
function initConnectionsPage() {
    // Skip if already initialized
    if (window.connectionsInitialized) {
        log("Page already initialized, skipping duplicate initialization");
        return;
    }

    log("Connections page initializing...");

    // Set up event delegation for the connections table
    setupConnectionsTable();
    
    // Set up the connection form
    setupConnectionForm();

    // Global initialization state
    window.connectionsInitialized = true;
    log("Connections initialization complete");
}

// Setup event delegation for the connections table
function setupConnectionsTable() {
    const tableContainer = document.getElementById('connections-table-container');
    if (!tableContainer) {
        log("Connections table container not found");
        return;
    }

    // Use event delegation to handle all button clicks in the table
    tableContainer.addEventListener('click', function(e) {
        // Handle toggle buttons
        if (e.target.classList.contains('toggle-connection-btn') || 
            e.target.closest('.toggle-connection-btn')) {
            
            const button = e.target.classList.contains('toggle-connection-btn') ? 
                e.target : e.target.closest('.toggle-connection-btn');
            
            const connectionId = button.dataset.connectionId;
            const currentState = button.dataset.enabled === 'true';
            
            log(`Toggle button clicked for connection ${connectionId}, current state: ${currentState}`);
            toggleConnection(connectionId, !currentState, button);
        }
        
        // Handle delete buttons
        if (e.target.classList.contains('delete-connection-btn') || 
            e.target.closest('.delete-connection-btn')) {
            
            const button = e.target.classList.contains('delete-connection-btn') ? 
                e.target : e.target.closest('.delete-connection-btn');
            
            const connectionId = button.dataset.connectionId;
            log(`Delete button clicked for connection ${connectionId}`);
            deleteConnection(connectionId);
        }
        
        // Handle edit buttons
        if (e.target.classList.contains('edit-connection-btn') || 
            e.target.closest('.edit-connection-btn')) {
            
            const button = e.target.classList.contains('edit-connection-btn') ? 
                e.target : e.target.closest('.edit-connection-btn');
            
            const connectionId = button.dataset.connectionId;
            log(`Edit button clicked for connection ${connectionId}`);
            editConnection(connectionId);
        }
        
        // Handle test/validate buttons
        if (e.target.classList.contains('test-connection-btn') || 
            e.target.closest('.test-connection-btn')) {
            
            const button = e.target.classList.contains('test-connection-btn') ? 
                e.target : e.target.closest('.test-connection-btn');
            
            const connectionId = button.dataset.connectionId;
            log(`Test button clicked for connection ${connectionId}`);
            testConnection(connectionId);
        }
        
        // Handle fetch models buttons
        if (e.target.classList.contains('fetch-models-btn') || 
            e.target.closest('.fetch-models-btn')) {
            
            const button = e.target.classList.contains('fetch-models-btn') ? 
                e.target : e.target.closest('.fetch-models-btn');
            
            const connectionId = button.dataset.connectionId;
            log(`Fetch models button clicked for connection ${connectionId}`);
            fetchModels(connectionId);
        }
    });
}

// Toggle connection enabled state
function toggleConnection(connectionId, newState, buttonElement) {
    if (!connectionId) {
        showError("Connection ID is required to toggle state");
        return;
    }
    
    // Show loading state
    if (buttonElement) {
        buttonElement.disabled = true;
        buttonElement.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div>';
    }
    
    log(`Toggling connection ${connectionId} to ${newState ? 'enabled' : 'disabled'}`);
    
    // Make API request to toggle the connection
    fetch(`/api/v1/connections/${connectionId}/toggle`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            enabled: newState
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        log(`Toggle successful: ${data.message}`);
        
        // Update the button state and text
        if (buttonElement) {
            buttonElement.disabled = false;
            buttonElement.dataset.enabled = String(newState);
            
            if (newState) {
                buttonElement.textContent = 'Disable';
                buttonElement.classList.remove('btn-success');
                buttonElement.classList.add('btn-danger');
            } else {
                buttonElement.textContent = 'Enable';
                buttonElement.classList.remove('btn-danger');
                buttonElement.classList.add('btn-success');
            }
        }
        
        // Update row styling
        const row = buttonElement?.closest('tr');
        if (row) {
            if (!newState) {
                row.classList.add('disabled-connection');
            } else {
                row.classList.remove('disabled-connection');
            }
        }
        
        // Show success message
        showMessage(data.message, 'success');
        
        // Refresh the models dropdown if this is being used in the UI
        try {
            if (typeof updateModelsDropdown === 'function') {
                updateModelsDropdown();
            }
        } catch (e) {
            log('Failed to refresh models dropdown, function may not exist');
        }
    })
    .catch(error => {
        log(`Toggle error: ${error.message}`);
        if (buttonElement) {
            buttonElement.disabled = false;
            // Reset to previous state
            buttonElement.textContent = newState ? 'Disable' : 'Enable';
        }
        showError(`Failed to toggle connection: ${error.message}`);
    });
}

// Setup connection form
function setupConnectionForm() {
    const form = document.getElementById('connection-form');
    if (!form) {
        log("Connection form not found");
        return;
    }
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        submitConnectionForm(form);
    });
    
    // Provider type change handler
    const providerSelect = document.getElementById('provider-type');
    if (providerSelect) {
        providerSelect.addEventListener('change', function() {
            updateFormFields(providerSelect.value);
        });
        
        // Initial update
        updateFormFields(providerSelect.value);
    }
}

// Update form fields based on provider type
function updateFormFields(providerType) {
    log(`Updating form fields for provider type: ${providerType}`);
    // Add provider-specific logic here if needed
}

// Submit connection form
function submitConnectionForm(form) {
    const formData = new FormData(form);
    const jsonData = {};
    
    for (const [key, value] of formData.entries()) {
        jsonData[key] = value;
    }
    
    const isEditing = form.dataset.connectionId !== undefined;
    const url = isEditing ? 
        `/api/v1/connections/${form.dataset.connectionId}` : 
        '/api/v1/connections';
    
    log(`${isEditing ? 'Updating' : 'Creating'} connection: ${JSON.stringify(jsonData)}`);
    
    // Disable form
    const submitButton = form.querySelector('button[type="submit"]');
    if (submitButton) {
        submitButton.disabled = true;
        submitButton.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div> Saving...';
    }
    
    fetch(url, {
        method: isEditing ? 'PUT' : 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(jsonData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        log(`Connection ${isEditing ? 'updated' : 'created'} successfully`);
        showMessage(`Connection ${isEditing ? 'updated' : 'created'} successfully`, 'success');
        
        // Reset form
        form.reset();
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.textContent = isEditing ? 'Update Connection' : 'Create Connection';
        }
        
        // Refresh the connections list
        refreshConnectionsList();
        
        // Close modal if it exists
        const modal = document.getElementById('connection-modal');
        if (modal && typeof bootstrap !== 'undefined') {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
        }
    })
    .catch(error => {
        log(`Form submission error: ${error.message}`);
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.textContent = isEditing ? 'Update Connection' : 'Create Connection';
        }
        showError(`Failed to ${isEditing ? 'update' : 'create'} connection: ${error.message}`);
    });
}

// Delete connection
function deleteConnection(connectionId) {
    if (!confirm('Are you sure you want to delete this connection? This action cannot be undone.')) {
        return;
    }
    
    log(`Deleting connection ${connectionId}`);
    
    fetch(`/api/v1/connections/${connectionId}`, {
        method: 'DELETE'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        log(`Connection deleted successfully`);
        showMessage('Connection deleted successfully', 'success');
        
        // Refresh the connections list
        refreshConnectionsList();
    })
    .catch(error => {
        log(`Delete error: ${error.message}`);
        showError(`Failed to delete connection: ${error.message}`);
    });
}

// Edit connection
function editConnection(connectionId) {
    log(`Loading connection ${connectionId} for editing`);
    
    fetch(`/api/v1/connections/${connectionId}`)
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        log(`Connection data loaded: ${JSON.stringify(data)}`);
        
        // Fill form with connection data
        const form = document.getElementById('connection-form');
        if (!form) {
            throw new Error('Connection form not found');
        }
        
        // Set form to editing mode
        form.dataset.connectionId = connectionId;
        
        // Fill form fields
        for (const [key, value] of Object.entries(data)) {
            const field = form.elements[key];
            if (field) {
                field.value = value;
            }
        }
        
        // Update submit button
        const submitButton = form.querySelector('button[type="submit"]');
        if (submitButton) {
            submitButton.textContent = 'Update Connection';
        }
        
        // Show modal if it exists
        const modal = document.getElementById('connection-modal');
        if (modal && typeof bootstrap !== 'undefined') {
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
        }
    })
    .catch(error => {
        log(`Edit error: ${error.message}`);
        showError(`Failed to load connection data: ${error.message}`);
    });
}

// Test connection
function testConnection(connectionId) {
    log(`Testing connection ${connectionId}`);
    
    // Show loading indicator
    const button = document.querySelector(`.test-connection-btn[data-connection-id="${connectionId}"]`);
    if (button) {
        button.disabled = true;
        button.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div> Testing...';
    }
    
    fetch(`/api/v1/connections/${connectionId}/test`)
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        log(`Connection test result: ${JSON.stringify(data)}`);
        
        // Reset button
        if (button) {
            button.disabled = false;
            button.textContent = 'Test';
        }
        
        if (data.success) {
            showMessage('Connection test successful!', 'success');
        } else {
            showError(`Connection test failed: ${data.error || 'Unknown error'}`);
        }
    })
    .catch(error => {
        log(`Test error: ${error.message}`);
        if (button) {
            button.disabled = false;
            button.textContent = 'Test';
        }
        showError(`Failed to test connection: ${error.message}`);
    });
}

// Fetch models
function fetchModels(connectionId) {
    log(`Fetching models for connection ${connectionId}`);
    
    // Show loading indicator
    const button = document.querySelector(`.fetch-models-btn[data-connection-id="${connectionId}"]`);
    if (button) {
        button.disabled = true;
        button.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div> Fetching...';
    }
    
    fetch(`/api/v1/connections/${connectionId}/models`)
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        log(`Models fetched successfully: ${JSON.stringify(data)}`);
        
        // Reset button
        if (button) {
            button.disabled = false;
            button.textContent = 'Fetch Models';
        }
        
        if (data.success) {
            showMessage(`Successfully fetched ${data.models?.length || 0} models!`, 'success');
            refreshConnectionsList(); // Refresh to show updated models
        } else {
            showError(`Failed to fetch models: ${data.error || 'Unknown error'}`);
        }
    })
    .catch(error => {
        log(`Fetch models error: ${error.message}`);
        if (button) {
            button.disabled = false;
            button.textContent = 'Fetch Models';
        }
        showError(`Failed to fetch models: ${error.message}`);
    });
}

// Refresh connections list
function refreshConnectionsList() {
    log('Refreshing connections list');
    
    const tableContainer = document.getElementById('connections-table-container');
    if (!tableContainer) {
        log("Connections table container not found");
        return;
    }
    
    // Show loading state
    tableContainer.innerHTML = `
        <div class="text-center p-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading connections...</p>
        </div>
    `;
    
    fetch('/api/v1/connections')
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        log(`Loaded ${data.connections?.length || 0} connections`);
        
        if (!data.connections || data.connections.length === 0) {
            tableContainer.innerHTML = `
                <div class="alert alert-info" role="alert">
                    No connections found. Create a new connection to get started.
                </div>
            `;
            return;
        }
        
        // Build table HTML
        let tableHtml = `
            <table class="table connections-table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>URL</th>
                        <th>Provider Type</th>
                        <th>Models</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        data.connections.forEach(conn => {
            const isEnabled = conn.enabled;
            const rowClass = isEnabled ? '' : 'disabled-connection';
            const toggleBtnClass = isEnabled ? 'btn-danger' : 'btn-success';
            const toggleBtnText = isEnabled ? 'Disable' : 'Enable';
            
            tableHtml += `
                <tr class="${rowClass}">
                    <td>${conn.name}</td>
                    <td><span class="text-truncate d-inline-block" style="max-width: 200px;">${conn.url}</span></td>
                    <td>${conn.api_type || 'default'}</td>
                    <td>
                        <span class="text-truncate d-inline-block" style="max-width: 200px;">
                            ${conn.model_ids || 'None'}
                        </span>
                    </td>
                    <td>
                        <span class="badge ${isEnabled ? 'bg-success' : 'bg-danger'}">
                            ${isEnabled ? 'Enabled' : 'Disabled'}
                        </span>
                    </td>
                    <td>
                        <div class="btn-group">
                            <button class="btn btn-sm ${toggleBtnClass} toggle-connection-btn" 
                                    data-connection-id="${conn.id}" 
                                    data-enabled="${isEnabled}">
                                ${toggleBtnText}
                            </button>
                            <button class="btn btn-sm btn-primary fetch-models-btn" 
                                    data-connection-id="${conn.id}">
                                Fetch Models
                            </button>
                            <button class="btn btn-sm btn-info test-connection-btn" 
                                    data-connection-id="${conn.id}">
                                Test
                            </button>
                            <button class="btn btn-sm btn-secondary edit-connection-btn" 
                                    data-connection-id="${conn.id}">
                                Edit
                            </button>
                            <button class="btn btn-sm btn-danger delete-connection-btn" 
                                    data-connection-id="${conn.id}">
                                Delete
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });
        
        tableHtml += `
                </tbody>
            </table>
        `;
        
        tableContainer.innerHTML = tableHtml;
    })
    .catch(error => {
        log(`Refresh error: ${error.message}`);
        tableContainer.innerHTML = `
            <div class="alert alert-danger" role="alert">
                Error loading connections: ${error.message}
                <button class="btn btn-outline-danger btn-sm ms-3" onclick="refreshConnectionsList()">
                    Retry
                </button>
            </div>
        `;
    });
}

// Show error message
function showError(message) {
    const alertsContainer = document.getElementById('alerts-container');
    if (!alertsContainer) {
        log(`Error: ${message} (alerts container not found)`);
        return;
    }
    
    const alert = document.createElement('div');
    alert.className = 'alert alert-danger alert-dismissible fade show';
    alert.role = 'alert';
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    alertsContainer.appendChild(alert);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        alert.classList.remove('show');
        setTimeout(() => {
            alertsContainer.removeChild(alert);
        }, 150);
    }, 5000);
}

// Show message
function showMessage(message, type = 'info') {
    const alertsContainer = document.getElementById('alerts-container');
    if (!alertsContainer) {
        log(`Message (${type}): ${message} (alerts container not found)`);
        return;
    }
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.role = 'alert';
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    alertsContainer.appendChild(alert);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        alert.classList.remove('show');
        setTimeout(() => {
            alertsContainer.removeChild(alert);
        }, 150);
    }, 5000);
}

// Add styling for disabled connections
function addCustomStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .disabled-connection {
            opacity: 0.6;
            background-color: rgba(0, 0, 0, 0.05);
        }
    `;
    document.head.appendChild(style);
}

// New provider-specific functions
function openProviderModal(provider) {
    const config = PROVIDER_CONFIGS[provider];
    if (!config) {
        console.error("Unknown provider:", provider);
        return;
    }

    const modal = document.getElementById("providerModal");
    const title = document.getElementById("providerTitle");
    const content = document.getElementById("providerContent");

    if (!modal || !title || !content) {
        log("Provider modal elements not found, falling back to generic modal");
        openConnectionModal();
        return;
    }

    title.textContent = `Setup ${config.name}`;

    content.innerHTML = `
        <div class="provider-setup">
            <div class="setup-step">
                <span class="step-number">1</span>
                <strong>Get your API key</strong>
                <p>${config.keyHelp}</p>
            </div>

            <div class="setup-step">
                <span class="step-number">2</span>
                <strong>Configure your connection</strong>
                <form id="providerForm" onsubmit="saveProviderConnection(event, '${provider}')">
                    <div class="form-group">
                        <label for="providerName">Connection Name:</label>
                        <input type="text" id="providerName" class="form-control"
                               value="${config.name}" required>
                    </div>

                    <div class="form-group">
                        <label for="providerApiKey">API Key:</label>
                        <input type="password" id="providerApiKey" class="form-control"
                               placeholder="Enter your ${config.name} API key" required>
                    </div>

                    ${provider === 'azure' ? `
                    <div class="form-group">
                        <label for="providerUrl">Azure Endpoint:</label>
                        <input type="url" id="providerUrl" class="form-control"
                               value="${config.defaultUrl}" required>
                        <small class="form-text text-muted">${config.urlHelp}</small>
                    </div>
                    ` : `
                    <div class="form-group">
                        <label for="providerUrl">API URL:</label>
                        <input type="url" id="providerUrl" class="form-control"
                               value="${config.defaultUrl}" required readonly>
                        <small class="form-text text-muted">${config.urlHelp}</small>
                    </div>
                    `}

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Connection
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeProviderModal()">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    modal.style.display = "flex";
}

function closeProviderModal() {
    const modal = document.getElementById("providerModal");
    if (modal) {
        modal.style.display = "none";
    }
}

async function saveProviderConnection(event, provider) {
    event.preventDefault();

    const config = PROVIDER_CONFIGS[provider];
    const name = document.getElementById("providerName").value.trim();
    const apiKey = document.getElementById("providerApiKey").value.trim();
    const url = document.getElementById("providerUrl").value.trim();

    const payload = {
        name: name,
        url: url,
        api_key: apiKey,
        prefix: provider,
        model_ids: config.defaultModels,
        embedding_model: config.defaultEmbedding,
        enabled: true
    };

    try {
        await createConnection(payload);
        closeProviderModal();

        // Refresh connections list
        if (typeof loadConnections === 'function') {
            loadConnections();
        } else if (typeof refreshConnectionsList === 'function') {
            refreshConnectionsList();
        }

        showNotification(`${config.name} connection created successfully!`, 'success');
    } catch (error) {
        console.error("Failed to save provider connection:", error);
        showNotification(`Failed to save connection: ${error.message}`, 'error');
    }
}

// New connection management functions for the new UI
async function loadConnections() {
    try {
        const response = await fetch("/api/v1/connections", {
            credentials: "same-origin"
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        window.directConnections = data.connections || [];
        renderConnections();
    } catch (error) {
        console.error("Failed to load connections:", error);
        const container = document.getElementById("connectionsList");
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    Failed to load connections: ${error.message}
                </div>
            `;
        }
    }
}

function renderConnections() {
    const container = document.getElementById("connectionsList");
    if (!container) return;

    if (window.directConnections.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-plug fa-3x mb-3"></i>
                <h5>No connections configured</h5>
                <p>Add your first AI provider connection to get started.</p>
            </div>
        `;
        return;
    }

    container.innerHTML = window.directConnections.map(conn => `
        <div class="connection-item ${conn.enabled ? 'enabled' : 'disabled'}">
            <div class="connection-header">
                <div class="connection-name">${escapeHtml(conn.name || 'Unnamed Connection')}</div>
                <div class="connection-status ${conn.enabled ? 'status-enabled' : 'status-disabled'}">
                    ${conn.enabled ? 'Enabled' : 'Disabled'}
                </div>
            </div>
            <div class="connection-details">
                <div><strong>URL:</strong> ${escapeHtml(conn.url || 'Not set')}</div>
                <div><strong>API Key:</strong> ${conn.api_key ? '••••••••' : 'Not set'}</div>
                <div><strong>Models:</strong> ${escapeHtml(conn.model_ids || 'Auto-detect')}</div>
                <div><strong>Created:</strong> ${new Date(conn.created_at).toLocaleDateString()}</div>
            </div>
            <div class="connection-actions">
                <button class="btn btn-sm ${conn.enabled ? 'btn-warning' : 'btn-success'}"
                        onclick="toggleConnectionNew(${conn.id})">
                    ${conn.enabled ? 'Disable' : 'Enable'}
                </button>
                <button class="btn btn-sm btn-primary" onclick="editConnectionNew(${conn.id})">
                    Edit
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteConnectionNew(${conn.id})">
                    Delete
                </button>
            </div>
        </div>
    `).join('');
}

// Helper functions
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function showNotification(message, type = 'info') {
    // Try to use existing notification system
    if (typeof showMessage === 'function') {
        showMessage(message, type);
        return;
    }

    // Fallback notification
    const alertClass = type === 'error' ? 'alert-danger' :
                      type === 'success' ? 'alert-success' : 'alert-info';

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Wrapper functions for new UI
function toggleConnectionNew(connId) {
    const conn = window.directConnections.find(c => c.id == connId);
    if (conn) {
        toggleConnection(connId, !conn.enabled);
    }
}

function editConnectionNew(connId) {
    openConnectionModal(connId);
}

function deleteConnectionNew(connId) {
    if (confirm("Are you sure you want to delete this connection?")) {
        deleteConnection(connId);
    }
}

async function createConnection(payload) {
    const res = await fetch("/api/v1/connections", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "same-origin",
        body: JSON.stringify(payload),
    });
    if (!res.ok) throw new Error(`HTTP ${res.status}`);
    const data = await res.json();
    if (data.error) throw new Error(data.error);
}

async function updateConnection(connId, payload) {
    const res = await fetch(`/api/v1/connections/${connId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        credentials: "same-origin",
        body: JSON.stringify(payload),
    });
    if (!res.ok) throw new Error(`HTTP ${res.status}`);
    const data = await res.json();
    if (data.error) throw new Error(data.error);
}

function openConnectionModal(connId = null) {
    window.editingConnectionId = connId;
    const modal = document.getElementById("connectionFormModal");
    const title = document.getElementById("formTitle");

    if (!modal || !title) {
        log("Connection modal elements not found");
        return;
    }

    // Reset form
    const form = document.getElementById("connectionForm");
    if (form) {
        form.reset();
        const providerSelect = document.getElementById("connectionProvider");
        if (providerSelect) {
            providerSelect.value = "";
            updateConnectionForm();
        }
    }

    if (!connId) {
        title.textContent = "Add Custom Connection";
    } else {
        title.textContent = "Edit Connection";
        const conn = window.directConnections.find(c => c.id == connId);
        if (conn && form) {
            document.getElementById("connectionName").value = conn.name || "";
            document.getElementById("connectionUrl").value = conn.url || "";
            document.getElementById("connectionKey").value = conn.api_key ? "********" : "";
            document.getElementById("connectionPrefix").value = conn.prefix || "";
            document.getElementById("connectionModels").value = conn.model_ids || "";
            const embeddingInput = document.getElementById("connectionEmbeddingModel");
            if (embeddingInput) {
                embeddingInput.value = conn.embedding_model || "";
            }
        }
    }

    modal.style.display = "flex";
}

function closeConnectionModal() {
    window.editingConnectionId = null;
    const modal = document.getElementById("connectionFormModal");
    if (modal) {
        modal.style.display = "none";
    }
}

function updateConnectionForm() {
    const provider = document.getElementById("connectionProvider")?.value;
    const config = PROVIDER_CONFIGS[provider];

    if (config) {
        document.getElementById("connectionName").value = config.name;
        document.getElementById("connectionUrl").value = config.defaultUrl;
        const embeddingInput = document.getElementById("connectionEmbeddingModel");
        if (embeddingInput) {
            embeddingInput.value = config.defaultEmbedding;
        }
        document.getElementById("connectionModels").value = config.defaultModels;
        const urlHelp = document.getElementById("urlHelp");
        const keyHelp = document.getElementById("keyHelp");
        if (urlHelp) urlHelp.textContent = config.urlHelp;
        if (keyHelp) keyHelp.textContent = config.keyHelp;
    } else {
        const urlHelp = document.getElementById("urlHelp");
        const keyHelp = document.getElementById("keyHelp");
        if (urlHelp) urlHelp.textContent = "Enter the API endpoint URL";
        if (keyHelp) keyHelp.textContent = "Enter your API key";
    }
}

async function saveConnection(event) {
    event.preventDefault();

    const nameInput = document.getElementById("connectionName");
    const urlInput = document.getElementById("connectionUrl");
    const keyInput = document.getElementById("connectionKey");
    const prefixInput = document.getElementById("connectionPrefix");
    const modelsInput = document.getElementById("connectionModels");
    const embeddingInput = document.getElementById("connectionEmbeddingModel");

    let payload = {
        name: nameInput.value.trim(),
        url: urlInput.value.trim(),
        prefix: prefixInput.value.trim(),
        embedding_model: embeddingInput ? embeddingInput.value.trim() : "",
        model_ids: modelsInput.value.trim() ? modelsInput.value.split(",").map(m => m.trim()) : undefined
    };

    if (keyInput && keyInput.value.trim() && keyInput.value.trim() !== "********") {
        payload.api_key = keyInput.value.trim();
    }

    try {
        if (!window.editingConnectionId) {
            await createConnection(payload);
        } else {
            await updateConnection(window.editingConnectionId, payload);
        }
        closeConnectionModal();
        loadConnections();
        showNotification("Connection saved successfully!", 'success');
    } catch (error) {
        console.error("Failed to save connection:", error);
        showNotification(`Failed to save connection: ${error.message}`, 'error');
    }
}

// Export functions for global usage
window.toggleConnection = toggleConnection;
window.deleteConnection = deleteConnection;
window.editConnection = editConnection;
window.testConnection = testConnection;
window.fetchModels = fetchModels;
window.refreshConnectionsList = refreshConnectionsList;

// Export new functions
window.openProviderModal = openProviderModal;
window.closeProviderModal = closeProviderModal;
window.saveProviderConnection = saveProviderConnection;
window.loadConnections = loadConnections;
window.renderConnections = renderConnections;
window.openConnectionModal = openConnectionModal;
window.closeConnectionModal = closeConnectionModal;
window.updateConnectionForm = updateConnectionForm;
window.saveConnection = saveConnection;

window.initConnectionsPage = initConnectionsPage;
console.log('[AthenaAgent] initConnectionsPage defined');

// Initialize the page when loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a page with connections UI
    if (document.getElementById('connections-table-container') ||
        document.getElementById('connection-form') ||
        document.getElementById('connectionsList') ||
        document.getElementById('providerModal')) {

        log('Connections interface detected, initializing');

        // Add custom styles
        addCustomStyles();

        // Initialize the page
        initConnectionsPage();

        // Load connections on initial load
        if (typeof refreshConnectionsList === 'function') {
            refreshConnectionsList();
        } else if (typeof loadConnections === 'function') {
            loadConnections();
        }
    } else {
        log('No connections interface detected, skipping initialization');
    }
});

// Re-initialize when the page content changes (for dynamic loading via AJAX)
document.addEventListener('contentLoaded', function(e) {
    // Check if the loaded content contains connections UI
    if (document.getElementById('connections-table-container') ||
        document.getElementById('connection-form') ||
        document.getElementById('connectionsList') ||
        document.getElementById('providerModal')) {

        log('Connections interface detected after content load, initializing');

        // Reset initialization flag
        window.connectionsInitialized = false;

        // Initialize the page
        initConnectionsPage();

        // Load connections
        if (typeof refreshConnectionsList === 'function') {
            refreshConnectionsList();
        } else if (typeof loadConnections === 'function') {
            loadConnections();
        }
    }
});

// Add a specific initialization function for the new connections page
window.initNewConnectionsPage = function() {
    log('Initializing new connections page');

    // Check if we have the new UI elements
    if (document.getElementById('connectionsList')) {
        loadConnections();
    }

    // Set up event listeners for modals
    const providerModal = document.getElementById('providerModal');
    const connectionModal = document.getElementById('connectionFormModal');

    if (providerModal) {
        // Close modal when clicking outside
        providerModal.addEventListener('click', function(e) {
            if (e.target === providerModal) {
                closeProviderModal();
            }
        });
    }

    if (connectionModal) {
        // Close modal when clicking outside
        connectionModal.addEventListener('click', function(e) {
            if (e.target === connectionModal) {
                closeConnectionModal();
            }
        });
    }
};
