/**
 * Admin Page JavaScript
 * Handles admin-specific functionality for user management
 */

// Global initialization state
window.adminInitialized = false;

/**
 * Initialize the admin page
 */
window.initAdminPage = function() {
    // If already initialized, don't initialize again
    if (window.adminInitialized) {
        console.log('[Admin] Admin page already initialized, skipping initialization');
        return;
    }

    console.log('[Admin] Initializing admin page');

    try {
        // Set up form validation
        setupFormValidation();
        
        // Set up confirmation dialogs
        setupConfirmationDialogs();
        
        // Set up form submission handlers
        setupFormHandlers();
        
        // Set up role change handlers
        setupRoleChangeHandlers();
        
        // Mark as initialized
        window.adminInitialized = true;
        console.log('[Admin] Admin page initialization complete');
        
    } catch (error) {
        console.error('[Admin] Error during admin page initialization:', error);
        showAdminError('Failed to initialize admin page: ' + error.message);
    }
};

/**
 * Set up form validation
 */
function setupFormValidation() {
    console.log('[Admin] Setting up form validation');
    
    const createUserForm = document.getElementById('createUserForm');
    if (createUserForm) {
        createUserForm.addEventListener('submit', function(e) {
            const username = document.getElementById('new_username').value.trim();
            const email = document.getElementById('new_email').value.trim();
            const password = document.getElementById('new_password').value;
            
            // Validate username
            if (username.length < 3) {
                e.preventDefault();
                showAdminError('Username must be at least 3 characters long');
                return false;
            }
            
            // Validate email
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                showAdminError('Please enter a valid email address');
                return false;
            }
            
            // Validate password
            if (password.length < 6) {
                e.preventDefault();
                showAdminError('Password must be at least 6 characters long');
                return false;
            }
            
            console.log('[Admin] Create user form validation passed');
        });
    }
}

/**
 * Set up confirmation dialogs for destructive actions
 */
function setupConfirmationDialogs() {
    console.log('[Admin] Setting up confirmation dialogs');
    
    // Delete user confirmations
    document.addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('btn-danger')) {
            const form = e.target.closest('form');
            if (form && form.action.includes('delete_user')) {
                const confirmed = confirm('Are you sure you want to delete this user? This action cannot be undone.');
                if (!confirmed) {
                    e.preventDefault();
                    return false;
                }
            }
        }
    });
    
    // Reset password confirmations
    document.addEventListener('submit', function(e) {
        if (e.target && e.target.action.includes('reset_user_password')) {
            const passwordInput = e.target.querySelector('input[name="new_password"]');
            if (passwordInput && passwordInput.value.length < 6) {
                e.preventDefault();
                showAdminError('Password must be at least 6 characters long');
                return false;
            }
            
            const confirmed = confirm('Are you sure you want to reset this user\'s password?');
            if (!confirmed) {
                e.preventDefault();
                return false;
            }
        }
    });
}

/**
 * Set up form submission handlers
 */
function setupFormHandlers() {
    console.log('[Admin] Setting up form handlers');
    
    // Add loading states to forms
    document.addEventListener('submit', function(e) {
        const form = e.target;
        const submitButton = form.querySelector('button[type="submit"]');
        
        if (submitButton) {
            const originalText = submitButton.textContent;
            submitButton.textContent = 'Processing...';
            submitButton.disabled = true;
            
            // Re-enable after a delay (in case of errors)
            setTimeout(() => {
                submitButton.textContent = originalText;
                submitButton.disabled = false;
            }, 5000);
        }
    });
}

/**
 * Set up role change handlers
 */
function setupRoleChangeHandlers() {
    console.log('[Admin] Setting up role change handlers');
    
    document.addEventListener('change', function(e) {
        if (e.target && e.target.name === 'role') {
            const newRole = e.target.value;
            const form = e.target.closest('form');
            const userIdInput = form.querySelector('input[name="user_id"]');
            
            if (userIdInput) {
                const userId = userIdInput.value;
                console.log(`[Admin] Role change requested: User ${userId} -> ${newRole}`);
                
                // Add confirmation for admin role changes
                if (newRole === 'admin') {
                    const confirmed = confirm('Are you sure you want to grant admin privileges to this user?');
                    if (!confirmed) {
                        e.preventDefault();
                        // Reset the select to previous value
                        e.target.selectedIndex = 0;
                        return false;
                    }
                }
                
                // Add confirmation for suspend
                if (newRole === 'suspend') {
                    const confirmed = confirm('Are you sure you want to suspend this user?');
                    if (!confirmed) {
                        e.preventDefault();
                        // Reset the select to previous value
                        e.target.selectedIndex = 0;
                        return false;
                    }
                }
            }
        }
    });
}

/**
 * Show admin error message
 */
function showAdminError(message) {
    console.error('[Admin] Error:', message);
    
    // Try to find an existing alert container
    let alertContainer = document.querySelector('.alert-container');
    if (!alertContainer) {
        // Create one if it doesn't exist
        alertContainer = document.createElement('div');
        alertContainer.className = 'alert-container';
        
        const adminContainer = document.querySelector('.admin-container');
        if (adminContainer) {
            adminContainer.insertBefore(alertContainer, adminContainer.firstChild);
        }
    }
    
    // Create error alert
    const alert = document.createElement('div');
    alert.className = 'alert alert-error';
    alert.innerHTML = `
        <strong>Error:</strong> ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()">&times;</button>
    `;
    
    alertContainer.appendChild(alert);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alert.parentElement) {
            alert.remove();
        }
    }, 5000);
}

/**
 * Show admin success message
 */
function showAdminSuccess(message) {
    console.log('[Admin] Success:', message);
    
    // Try to find an existing alert container
    let alertContainer = document.querySelector('.alert-container');
    if (!alertContainer) {
        // Create one if it doesn't exist
        alertContainer = document.createElement('div');
        alertContainer.className = 'alert-container';
        
        const adminContainer = document.querySelector('.admin-container');
        if (adminContainer) {
            adminContainer.insertBefore(alertContainer, adminContainer.firstChild);
        }
    }
    
    // Create success alert
    const alert = document.createElement('div');
    alert.className = 'alert alert-success';
    alert.innerHTML = `
        <strong>Success:</strong> ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()">&times;</button>
    `;
    
    alertContainer.appendChild(alert);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (alert.parentElement) {
            alert.remove();
        }
    }, 3000);
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if we're on the admin page
    if (document.getElementById('admin-section')) {
        console.log('[Admin] Admin section detected, initializing');
        window.initAdminPage();
    }
});

// Also initialize when the admin section is loaded dynamically
document.addEventListener('sectionLoaded', function(event) {
    if (event.detail && event.detail.section === 'admin') {
        console.log('[Admin] Admin section loaded event detected, initializing');
        // Reset initialization flag to ensure proper initialization
        window.adminInitialized = false;
        // Give a small delay to ensure DOM is updated
        setTimeout(() => {
            window.initAdminPage();
        }, 100);
    }
});

// Make functions globally available
window.showAdminError = showAdminError;
window.showAdminSuccess = showAdminSuccess;
