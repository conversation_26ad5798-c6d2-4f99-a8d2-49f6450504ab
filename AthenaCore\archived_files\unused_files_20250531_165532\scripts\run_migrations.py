#!/usr/bin/env python3
"""
Simple database migration runner.

This script provides a simplified way to apply database migrations without
relying on complex imports from the application structure.
"""

import os
import sys
import sqlite3
import importlib.util
import argparse
from pathlib import Path
from datetime import datetime

# Set up basic configuration
PROJECT_ROOT = Path(__file__).parent.parent
MIGRATIONS_DIR = PROJECT_ROOT / "migrations"
DATABASE_PATH = PROJECT_ROOT / "database.db"

def ensure_migrations_table(conn):
    """
    Ensure the migrations tracking table exists.
    
    Args:
        conn: SQLite database connection
    """
    cursor = conn.cursor()
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS migrations_applied (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version INTEGER NOT NULL UNIQUE,
            name TEXT NOT NULL,
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    conn.commit()

def get_applied_migrations(conn):
    """
    Get a list of applied migrations.
    
    Args:
        conn: SQLite database connection
        
    Returns:
        List of applied migration versions
    """
    cursor = conn.cursor()
    cursor.execute("SELECT version FROM migrations_applied ORDER BY version")
    return [row[0] for row in cursor.fetchall()]

def get_all_migrations():
    """
    Get a list of all available migrations.
    
    Returns:
        List of migration info dictionaries
    """
    migrations = []
    
    if not MIGRATIONS_DIR.exists():
        MIGRATIONS_DIR.mkdir(parents=True)
        return migrations
    
    migration_files = list(MIGRATIONS_DIR.glob("v*_*.py"))
    for file_path in migration_files:
        # Skip non-migration files
        if not file_path.name.startswith("v"):
            continue
        
        try:
            # Extract version from filename
            version_str = file_path.name.split("_")[0][1:]
            version = int(version_str)
            
            # Load the module to extract more information
            spec = importlib.util.spec_from_file_location("migration", file_path)
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                # Extract migration metadata
                description = getattr(module, "description", file_path.name)
                
                migrations.append({
                    "version": version,
                    "name": file_path.stem,
                    "description": description,
                    "file_path": file_path,
                    "module": module
                })
        except Exception as e:
            print(f"Error processing migration {file_path}: {str(e)}")
    
    # Sort migrations by version
    migrations.sort(key=lambda m: m["version"])
    return migrations

def apply_migration(conn, migration):
    """
    Apply a single migration.
    
    Args:
        conn: SQLite database connection
        migration: Migration info dictionary
        
    Returns:
        True if successful, False otherwise
    """
    try:
        print(f"Applying migration {migration['name']}...")
        
        # Create a simple database wrapper for the migration
        class DBWrapper:
            def __init__(self, conn):
                self.conn = conn
                self.engine = self
                
            def execute(self, sql, *args):
                cursor = self.conn.cursor()
                if args and len(args) == 1 and isinstance(args[0], tuple):
                    cursor.execute(sql, args[0])
                else:
                    cursor.execute(sql, args if args else ())
                return cursor
        
        # Create db wrapper
        db_wrapper = DBWrapper(conn)
        
        # Apply the migration
        migration["module"].upgrade(db_wrapper)
        
        # Record the migration
        cursor = conn.cursor()
        cursor.execute(
            "INSERT INTO migrations_applied (version, name, applied_at) VALUES (?, ?, ?)",
            (migration["version"], migration["name"], datetime.now().isoformat())
        )
        conn.commit()
        
        print(f"Successfully applied migration {migration['name']}")
        return True
    except Exception as e:
        print(f"Error applying migration {migration['name']}: {str(e)}")
        conn.rollback()
        return False

def run_migrations(database_path, target_version=None):
    """
    Run all pending migrations or up to a target version.
    
    Args:
        database_path: Path to the SQLite database
        target_version: Optional target version to migrate to
        
    Returns:
        Number of migrations applied
    """
    # Connect to the database
    conn = sqlite3.connect(database_path)
    
    # Ensure migrations table exists
    ensure_migrations_table(conn)
    
    # Get applied migrations
    applied_versions = get_applied_migrations(conn)
    print(f"Found {len(applied_versions)} previously applied migrations")
    
    # Get all migrations
    all_migrations = get_all_migrations()
    print(f"Found {len(all_migrations)} total migrations")
    
    # Filter pending migrations
    if target_version is not None:
        pending_migrations = [m for m in all_migrations if m["version"] <= target_version and m["version"] not in applied_versions]
    else:
        pending_migrations = [m for m in all_migrations if m["version"] not in applied_versions]
    
    print(f"Found {len(pending_migrations)} pending migrations to apply")
    
    # Apply pending migrations
    applied_count = 0
    for migration in pending_migrations:
        if apply_migration(conn, migration):
            applied_count += 1
        else:
            print(f"Stopping migrations due to error with {migration['name']}")
            break
    
    # Close connection
    conn.close()
    
    return applied_count

def main():
    """Main function to run the migration script."""
    parser = argparse.ArgumentParser(description="Athena Core Simple Migration Runner")
    parser.add_argument("--database", "-d", default=str(DATABASE_PATH),
                        help=f"Path to the SQLite database (default: {DATABASE_PATH})")
    parser.add_argument("--target", "-t", type=int, help="Target migration version to apply")
    parser.add_argument("--list", "-l", action="store_true", help="List all migrations")
    args = parser.parse_args()
    
    # Create migrations directory if it doesn't exist
    if not MIGRATIONS_DIR.exists():
        MIGRATIONS_DIR.mkdir(parents=True)
        print(f"Created migrations directory: {MIGRATIONS_DIR}")
    
    # Handle list command
    if args.list:
        all_migrations = get_all_migrations()
        if not all_migrations:
            print("No migrations found.")
            return
        
        print(f"{'Version':<8} {'Name':<40} {'Description'}")
        print(f"{'-'*8} {'-'*40} {'-'*40}")
        
        for migration in all_migrations:
            version = f"v{migration['version']}"
            name = migration['name']
            description = migration["description"]
            
            print(f"{version:<8} {name[:40]:<40} {description[:40]}")
        
        return
    
    # Run migrations
    db_path = args.database
    print(f"Using database: {db_path}")
    
    applied_count = run_migrations(db_path, args.target)
    print(f"Applied {applied_count} migrations")

if __name__ == "__main__":
    main()
