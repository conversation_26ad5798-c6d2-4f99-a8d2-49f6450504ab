# AthenaAgent Core Environment Variables
#
# NOTE: API keys are now managed through the web interface and stored in the database.
# The DirectConnections system provides a unified way to manage all API keys.
# These environment variables are no longer used for API key configuration.
#
# To configure API keys:
# 1. Start the Athena server
# 2. Log in to the web interface
# 3. Go to Settings > DirectConnections
# 4. Add your API keys there
#
# Legacy API Key Configuration (DEPRECATED - DO NOT USE)
# These are kept for reference only - they will not be used by the application
# OPENAI_API_KEY=your_openai_api_key_here
# ANTHROPIC_API_KEY=your_anthropic_api_key_here
# GOOGLE_API_KEY=your_google_api_key_here
# AZURE_OPENAI_API_KEY=your_azure_openai_key_here
# AZURE_OPENAI_ENDPOINT=your_azure_endpoint_here

# Database configuration
DATABASE_URL=sqlite:///athena.db

# Debug mode (true/false)
DEBUG=false

# Server configuration
HOST=0.0.0.0
PORT=5000

# Security
SECRET_KEY=your_secret_key_here_change_this_to_something_secure
