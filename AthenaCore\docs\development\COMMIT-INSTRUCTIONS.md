# Commit Instructions for Background Task Tracking System

## Files to Commit
1. **New Files:**
   - `src/api/demo.py` - Demo task implementation
   - `src/services/socket.py` - WebSocket event handling
   - `static/css/task-tracker.css` - CSS for notification bell and task panel
   - `static/js/task-tracker.js` - Client-side task management
   - `docs/background-task-system.md` - Technical documentation
   - `README-BACKGROUND-TASKS.md` - High-level documentation
   - `CHANGELOG-BACKGROUND-TASKS.md` - Changes summary

2. **Modified Files:**
   - `requirements.txt` - Added Flask-SocketIO dependency
   - `src/api/tasks.py` - Enhanced task management endpoints
   - `src/services/__init__.py` - Socket service initialization
   - `src/services/task_executor.py` - Improved background thread handling
   - `templates/index.html` - Added notification bell and task panel
   - `static/css/solo-leveling-theme.css` - Fixed z-index and pointer-events

## Commit Strategy

### 1. Backup Current State
```bash
# Create a backup branch before making any changes
git checkout -b backup-before-task-system
git add .
git commit -m "Backup before merging background task system"
git checkout main
```

### 2. Update Remote Changes
```bash
# Pull latest changes from remote repository
git fetch origin
git pull origin main
```

### 3. Handle Conflicts
If conflicts occur during the pull, follow these steps:
- Examine each conflicted file
- Keep both changes when they affect different parts of the code
- For the same code sections:
  - Prioritize preserving existing functionality
  - Ensure our background task tracking system is properly integrated
  - Follow established code patterns

### 4. Stage and Commit Changes
```bash
# Add new files
git add src/api/demo.py
git add src/services/socket.py
git add static/css/task-tracker.css
git add static/js/task-tracker.js
git add docs/background-task-system.md
git add README-BACKGROUND-TASKS.md
git add CHANGELOG-BACKGROUND-TASKS.md

# Add modified files
git add requirements.txt
git add src/api/tasks.py
git add src/services/__init__.py
git add src/services/task_executor.py
git add templates/index.html
git add static/css/solo-leveling-theme.css

# Commit with detailed message
git commit -m "Add background task tracking system

- Implemented real-time notifications via WebSockets
- Added task panel UI with progress tracking
- Created demo tasks for testing
- Fixed task status display issues
- Added comprehensive documentation"
```

### 5. Push to Repository
```bash
git push origin main
```

## Conflict Resolution Guidelines

### 1. Requirements.txt
If conflicts occur in requirements.txt, ensure these dependencies are included:
```
Flask-SocketIO==5.5.1
```

### 2. Template Changes
For conflicts in templates/index.html:
- Ensure the notification bell is properly placed in the system-status-header
- Verify the task panel HTML structure is preserved
- Maintain any new UI components added by the developer

### 3. JavaScript and CSS
If the developer added new JS/CSS files:
- Ensure our task-tracker.js and task-tracker.css are included in the HTML
- Check for conflicts in script initialization
- Verify that CSS selectors don't override each other

### 4. Server-Side Code
For conflicts in Python files:
- Maintain proper Flask app context handling in background threads
- Ensure socket.io initialization in app startup
- Preserve API endpoints for task management

## Testing After Merge
1. Start the Athena server
2. Verify WebSocket connection in browser console
3. Test creating a demo task
4. Confirm notification bell shows active tasks
5. Check that task panel displays properly
6. Verify tasks transition correctly from "Running" to "Completed"
7. Test any new features added by the developer

## Rollback Plan
If issues occur after merging:
```bash
# Revert to backup
git checkout backup-before-task-system

# Create a new branch for fixing issues
git checkout -b fix-task-system-integration
```
