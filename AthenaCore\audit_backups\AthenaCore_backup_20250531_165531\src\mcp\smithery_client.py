import base64
import json
import logging
import os
from typing import Dict, List, Optional, Any, Union

import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class SmitheryClient:
    """Client for interacting with the Smithery Registry API for MCP servers."""

    def __init__(self, api_key: Optional[str] = None, base_url: str = "https://registry.smithery.ai", config=None):
        """Initialize the Smithery API client.

        Args:
            api_key: The API key for authentication. If not provided, will attempt
                     to load from config or SMITHERY_API_KEY environment variable.
            base_url: The base URL for the Smithery Registry API.
            config: AthenaConfig object that can be passed directly (optional)
        """
        # Try to get API key from function parameter first
        self.api_key = api_key

        # If config object is passed directly, try to get API key from it
        if not self.api_key and config is not None:
            if hasattr(config, 'smithery_api_key'):
                self.api_key = config.smithery_api_key
                logger.info("Using Smithery API key from provided config object")

        # If still not provided, try to import the config and get the smithery_api_key
        if not self.api_key:
            try:
                # Dynamically import the config to avoid circular imports
                from src.utils.config import load_config
                config_data = load_config()
                self.api_key = config_data.get("smithery_api_key")
                logger.info("Using Smithery API key from loaded config file")
            except Exception as e:
                logger.warning(f"Could not load API key from config: {e}")

        # If still not found, try environment variable
        if not self.api_key:
            self.api_key = os.getenv("SMITHERY_API_KEY")
            if self.api_key:
                logger.info("Using Smithery API key from environment variable")

        if not self.api_key:
            logger.warning("No Smithery API key provided. Authentication will fail.")
        else:
            # Log partial key for debugging (first 4 chars and last 4 chars)
            if len(self.api_key) > 8:
                masked_key = f"{self.api_key[:4]}...{self.api_key[-4:]}"
                logger.info(f"Initialized Smithery client with API key: {masked_key}")

        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        })

    @staticmethod
    def normalize_server_name(server_name: str) -> str:
        """Normalize server name format.

        The Smithery API returns server names prefixed with '@' but
        expects the name without '@' in the URL path.

        Args:
            server_name: Server name with or without '@' prefix

        Returns:
            Normalized server name without '@' prefix
        """
        return server_name[1:] if server_name.startswith('@') else server_name

    @staticmethod
    def format_display_name(server_name: str) -> str:
        """Format server name for display purposes.

        Ensures the server name has the '@' prefix for display.

        Args:
            server_name: Server name with or without '@' prefix

        Returns:
            Display name with '@' prefix
        """
        return server_name if server_name.startswith('@') else f'@{server_name}'

    def list_servers(self, query: str = "", page: int = 1, page_size: int = 10) -> Dict[str, Any]:
        """Search for MCP servers in the Smithery Registry.

        Args:
            query: Search query string. Supports filtering syntax like owner:username, repo:name, etc.
            page: Page number (default: 1).
            page_size: Number of items per page (default: 10).

        Returns:
            Dictionary with servers list and pagination info.
        """
        params = {
            "q": query,
            "page": page,
            "pageSize": page_size
        }

        try:
            response = self.session.get(f"{self.base_url}/servers", params=params)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"Failed to list servers: {e}")
            return {"servers": [], "pagination": {"totalCount": 0}}

    def search_servers(self, query: str, page: int = 1, page_size: int = 10) -> List[Dict[str, Any]]:
        """Search for MCP servers in the Smithery Registry and return results.

        Args:
            query: Search query string. Supports filtering syntax like owner:username, repo:name, etc.
            page: Page number (default: 1).
            page_size: Number of items per page (default: 10).

        Returns:
            List of server objects
        """
        # Call the existing list_servers method
        result = self.list_servers(query, page, page_size)

        # Extract servers
        servers = result.get("servers", [])

        # Log what we're returning for debugging
        logger.info(f"Found {len(servers)} servers matching query: {query}")

        return servers

    def get_server(self, qualified_name: str) -> Optional[Dict[str, Any]]:
        """Get details about a specific MCP server.

        Args:
            qualified_name: The qualified name of the server (e.g., 'owner/repo' or '@owner/repo').

        Returns:
            Server details or None if not found/error.
        """
        # Normalize the server name (remove '@' prefix if present)
        normalized_name = self.normalize_server_name(qualified_name)

        try:
            response = self.session.get(f"{self.base_url}/servers/{normalized_name}")
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"Failed to get server {qualified_name}: {e}")
            return None

    @staticmethod
    def create_websocket_url(deployment_url: str, config: Dict[str, Any]) -> str:
        """Create a WebSocket URL for connecting to an MCP server.

        Args:
            deployment_url: The deployment URL from the server details.
            config: Configuration object matching the server's schema.

        Returns:
            Formatted WebSocket URL with encoded config.
        """
        config_json = json.dumps(config)
        config_b64 = base64.b64encode(config_json.encode('utf-8')).decode('utf-8')

        # Handle URLs with or without trailing slashes
        base_url = deployment_url.rstrip('/')
        return f"{base_url}/ws?config={config_b64}"

    def check_connection(self) -> bool:
        """Check if the connection to Smithery Registry is working.

        Returns:
            True if connected successfully, False otherwise.
        """
        try:
            print(f"Checking connection to Smithery Registry at {self.base_url}")
            print(f"Using API key: {self.api_key[:6]}...{self.api_key[-4:]}")
            response = self.session.get(f"{self.base_url}/servers", params={"page": 1, "pageSize": 1})
            if response.status_code == 200:
                print(f"Connection successful, response status: {response.status_code}")
                try:
                    data = response.json()
                    server_count = data.get('pagination', {}).get('totalCount', 0)
                    print(f"Successfully retrieved server list. Total servers: {server_count}")
                except Exception as json_err:
                    print(f"Warning: Could not parse JSON response: {str(json_err)}")
                return True
            else:
                print(f"Connection failed, response status: {response.status_code}")
                print(f"Response text: {response.text[:200]}")
                return False
        except requests.RequestException as e:
            print(f"Connection error: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        except Exception as e:
            print(f"Unexpected error during connection check: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
