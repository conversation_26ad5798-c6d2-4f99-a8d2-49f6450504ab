# Utilities Package

This directory contains utility functions and helpers used throughout the Athena application.

## Overview

The `utils` package provides reusable functionality that doesn't belong to a specific domain. As part of the refactoring process, various utility functions from across the codebase are being migrated here to improve organization and reusability.

## Modules

- **refactoring.py**: Utilities to help with the refactoring process, including compatibility imports and deprecation warnings.
- (More modules will be added as refactoring progresses)

## Usage Guidelines

When adding utilities to this package:

1. **Scope**: Only add functionality that is truly generic and used across multiple parts of the application.
2. **Organization**: Group related functions in the same module.
3. **Documentation**: Thoroughly document all functions with docstrings.
4. **Testing**: Write unit tests for all utility functions.
5. **Dependencies**: Minimize dependencies on other parts of the application.

## Migration Process

When moving utilities from elsewhere in the codebase:

1. Move the code to an appropriate module in this package
2. Create proxy functions/classes at the original location using `moved_module` decorator
3. Update the migration tracker
4. Update any direct imports (if known)

## Naming Conventions

- Use descriptive, clear function names that indicate what the function does
- Follow Python's snake_case naming convention
- Use verbs for functions that perform actions (e.g., `format_date`, not `date_formatter`)
- Group related utilities in modules with clear names
