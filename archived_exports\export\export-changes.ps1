# Create necessary directories
New-Item -Path "c:\Projects\AthenaNew\export\src\api" -ItemType Directory -Force
New-Item -Path "c:\Projects\AthenaNew\export\src\services" -ItemType Directory -Force
New-Item -Path "c:\Projects\AthenaNew\export\static\css" -ItemType Directory -Force
New-Item -Path "c:\Projects\AthenaNew\export\static\js" -ItemType Directory -Force
New-Item -Path "c:\Projects\AthenaNew\export\docs" -ItemType Directory -Force
New-Item -Path "c:\Projects\AthenaNew\export\templates" -ItemType Directory -Force

# Copy new files
Copy-Item -Path "c:\Projects\AthenaNew\AthenaCore\src\api\demo.py" -Destination "c:\Projects\AthenaNew\export\src\api\" -Force
Copy-Item -Path "c:\Projects\AthenaNew\AthenaCore\src\services\socket.py" -Destination "c:\Projects\AthenaNew\export\src\services\" -Force
Copy-Item -Path "c:\Projects\AthenaNew\AthenaCore\static\css\task-tracker.css" -Destination "c:\Projects\AthenaNew\export\static\css\" -Force
Copy-Item -Path "c:\Projects\AthenaNew\AthenaCore\static\js\task-tracker.js" -Destination "c:\Projects\AthenaNew\export\static\js\" -Force

# Copy documentation
Copy-Item -Path "c:\Projects\AthenaNew\AthenaCore\docs\background-task-system.md" -Destination "c:\Projects\AthenaNew\export\docs\" -Force
Copy-Item -Path "c:\Projects\AthenaNew\AthenaCore\README-BACKGROUND-TASKS.md" -Destination "c:\Projects\AthenaNew\export\" -Force
Copy-Item -Path "c:\Projects\AthenaNew\AthenaCore\CHANGELOG-BACKGROUND-TASKS.md" -Destination "c:\Projects\AthenaNew\export\" -Force
Copy-Item -Path "c:\Projects\AthenaNew\AthenaCore\PULL_REQUEST_TEMPLATE.md" -Destination "c:\Projects\AthenaNew\export\" -Force
Copy-Item -Path "c:\Projects\AthenaNew\AthenaCore\INTEGRATION-CHECKLIST.md" -Destination "c:\Projects\AthenaNew\export\" -Force

# Copy modified files
Copy-Item -Path "c:\Projects\AthenaNew\AthenaCore\requirements.txt" -Destination "c:\Projects\AthenaNew\export\" -Force
Copy-Item -Path "c:\Projects\AthenaNew\AthenaCore\src\api\tasks.py" -Destination "c:\Projects\AthenaNew\export\src\api\" -Force
Copy-Item -Path "c:\Projects\AthenaNew\AthenaCore\src\services\__init__.py" -Destination "c:\Projects\AthenaNew\export\src\services\" -Force
Copy-Item -Path "c:\Projects\AthenaNew\AthenaCore\src\services\task_executor.py" -Destination "c:\Projects\AthenaNew\export\src\services\" -Force
Copy-Item -Path "c:\Projects\AthenaNew\AthenaCore\templates\index.html" -Destination "c:\Projects\AthenaNew\export\templates\" -Force
Copy-Item -Path "c:\Projects\AthenaNew\AthenaCore\static\css\solo-leveling-theme.css" -Destination "c:\Projects\AthenaNew\export\static\css\" -Force

Write-Host "All files have been exported to c:\Projects\AthenaNew\export\"
Write-Host "You can now manually copy these files to a new branch in the repository."
