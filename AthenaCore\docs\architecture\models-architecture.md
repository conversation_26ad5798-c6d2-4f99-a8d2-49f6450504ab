# Athena Core Models

This directory contains the database models for Athena Core, centralized in one location for better organization and maintainability.

## Model Organization

The models are organized by domain:

- **User models**: `user.py`, `user_log.py`
- **API models**: `api_key.py`
- **Configuration models**: `configuration.py`
- **Task models**: `task.py`
- **LLM models**: `llm_provider_setting.py`
- **Command models**: `command_toggle.py`
- **Connection models**: `direct_connection.py`, `mcp_api_key.py`, `mcp_server_template.py`
- **Conversation models**: `conversation.py`, `message.py`
- **Logging models**: `log_entry.py`

## Usage

### Importing Models

All models can be imported directly from the `src.models` package:

```python
from src.models import User, APIKey, Configuration

# Use models
user = User.query.get(user_id)
```

### Database Instance

The shared database instance is also available from the models package:

```python
from src.models import db

# Use database instance
db.session.add(model_instance)
db.session.commit()
```

## Backward Compatibility

For backward compatibility with code that still imports from the original locations, compatibility layers have been implemented. However, new code should always import directly from `src.models`.

## Model Relationships

Models maintain relationships with other models using SQLAlchemy relationships. For example:

- `User` has one-to-many relationships with `APIKey`, `UserLog`, and other user-related models
- `Conversation` has a one-to-many relationship with `Message`
- Most models have a relationship back to `User` for ownership

## Creating New Models

When creating new models:

1. Create a new file in the `src/models` directory
2. Import the database instance from `src.models`
3. Define your model class with appropriate table name, columns, and relationships
4. Import your model in `__init__.py` and add it to the `__all__` list

Example:

```python
from src.models import db

class NewModel(db.Model):
    __tablename__ = "new_models"
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=False)
    
    # Relationships
    user = db.relationship("User", backref=db.backref("new_models", lazy=True))
```

## Database Migrations

When changing model schemas, always create a migration script in the `migrations` directory to ensure database consistency across deployments.
