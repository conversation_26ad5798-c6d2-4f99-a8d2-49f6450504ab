# src/services/__init__.py

"""
Services package for Athena Core.

This package contains services that implement the application's business logic,
following the service layer pattern established in the refactoring plan. Services
provide a clean interface between controllers and data access layers.
"""

from .task_executor import task_executor_service, init_app as init_task_executor
from .task_scheduler import task_scheduler_service, init_app as init_task_scheduler
from .socket import init_app as init_socket_service
from .base_service import BaseService, ServiceRegistry, get_service
from .config_service import ConfigurationService
from .auth_service import AuthenticationService
from .user_service import UserService
from .task_service import TaskService

def init_app(app):
    """
    Initialize all services with the Flask application.
    
    Args:
        app (Flask): The Flask application
    """
    # Initialize service registry and register all services
    from .base_service import service_registry
    
    # Register all service classes with the registry
    service_registry.register(ConfigurationService)
    service_registry.register(AuthenticationService)
    service_registry.register(UserService)
    service_registry.register(TaskService)
    
    # Initialize background services
    init_task_executor(app)
    init_task_scheduler(app)
    init_socket_service(app)
    
    # Log initialization
    app.logger.info("Athena services initialized")


def init_socketio(socketio):
    """
    Initialize SocketIO service with the SocketIO application.
    This is called after the app and services initialization.
    
    Args:
        socketio (SocketIO): The SocketIO application
    """
    from .socket import setup_socketio
    setup_socketio(socketio)
