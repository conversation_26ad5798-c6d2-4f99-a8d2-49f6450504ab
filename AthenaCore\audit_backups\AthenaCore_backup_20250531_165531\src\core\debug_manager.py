"""
Debug Manager - Centralized debugging component for Athena

This module provides a unified approach to debugging across all Athena components,
allowing developers to enable/disable debug output for specific subsystems without
needing to constantly add and remove debug statements from the codebase.
"""

import json
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Set, Union

# Configure logging
logger = logging.getLogger(__name__)

class DebugManager:
    """
    Centralized debug management system for Athena components.
    
    This class handles debug settings across the application, allowing
    developers to enable/disable debug output for specific subsystems
    through the admin interface rather than code changes.
    """
    
    # Default settings path
    SETTINGS_PATH = os.path.join("data", "debug_settings.json")
    
    # Available debug components
    CORE_COMPONENTS = ["kb", "llm", "api", "auth", "commands", "mcp"]
    INTEGRATIONS = ["devices", "vector_db", "external_apis"]
    
    # Singleton instance
    _instance = None
    
    def __new__(cls):
        """Implement singleton pattern to ensure consistent debug settings."""
        if cls._instance is None:
            cls._instance = super(DebugManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Initialize the debug manager if not already initialized."""
        if self._initialized:
            return
            
        self._initialized = True
        
        # Default debug settings
        self.enabled = False
        self.save_logs = False
        self.global_level = "INFO"
        
        # Component-specific debug flags
        self.components = {component: False for component in self.CORE_COMPONENTS}
        self.integrations = {integration: False for integration in self.INTEGRATIONS}
        
        # Set of currently active debug components
        self.active_components: Set[str] = set()
        
        # Load settings if available
        self.load_settings()
    
    def load_settings(self):
        """Load debug settings from file."""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.SETTINGS_PATH), exist_ok=True)
            
            # Check if settings file exists
            if not os.path.exists(self.SETTINGS_PATH):
                logger.info("No debug settings file found, using defaults")
                return
                
            # Load settings from file
            with open(self.SETTINGS_PATH, "r") as f:
                settings = json.load(f)
            
            # Apply settings
            self.enabled = settings.get("enabled", False)
            self.save_logs = settings.get("save_logs", False)
            self.global_level = settings.get("global_level", "INFO")
            
            # Load component settings
            components_dict = settings.get("components", {})
            for component in self.CORE_COMPONENTS:
                if component in components_dict:
                    self.components[component] = components_dict[component]
            
            # Load integration settings
            integrations_dict = settings.get("integrations", {})
            for integration in self.INTEGRATIONS:
                if integration in integrations_dict:
                    self.integrations[integration] = integrations_dict[integration]
            
            # Update active components
            self._update_active_components()
            
            logger.info(f"Loaded debug settings: {len(self.active_components)} active components")
        except Exception as e:
            logger.error(f"Error loading debug settings: {str(e)}")
    
    def save_settings(self):
        """Save current debug settings to file."""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.SETTINGS_PATH), exist_ok=True)
            
            # Prepare settings dict
            settings = {
                "enabled": self.enabled,
                "save_logs": self.save_logs,
                "global_level": self.global_level,
                "components": self.components,
                "integrations": self.integrations,
                "last_updated": datetime.now().isoformat()
            }
            
            # Save to file
            with open(self.SETTINGS_PATH, "w") as f:
                json.dump(settings, f, indent=2)
            
            logger.info("Debug settings saved successfully")
        except Exception as e:
            logger.error(f"Error saving debug settings: {str(e)}")
    
    def update_from_request(self, form_data):
        """Update debug settings from form submission."""
        # Update global settings
        self.enabled = "debug_enabled" in form_data
        self.save_logs = "save_debug_logs" in form_data
        self.global_level = form_data.get("global_debug_level", "INFO")
        
        # Update component settings
        for component in self.CORE_COMPONENTS:
            self.components[component] = f"debug_{component}" in form_data
        
        # Update integration settings
        for integration in self.INTEGRATIONS:
            self.integrations[integration] = f"debug_{integration}" in form_data
        
        # Update active components list
        self._update_active_components()
        
        # Save updated settings
        self.save_settings()
        
        # Apply logging level changes
        self._apply_logging_levels()
        
        return self.get_settings_dict()
    
    def _update_active_components(self):
        """Update the set of active debug components."""
        self.active_components = set()
        
        # Only track active components if debug mode is enabled
        if not self.enabled:
            return
            
        # Add active core components
        for component, enabled in self.components.items():
            if enabled:
                self.active_components.add(component)
        
        # Add active integrations
        for integration, enabled in self.integrations.items():
            if enabled:
                self.active_components.add(integration)
    
    def _apply_logging_levels(self):
        """Apply the current global logging level."""
        level = getattr(logging, self.global_level, logging.INFO)
        logging.getLogger().setLevel(level)
        logger.info(f"Set global logging level to {self.global_level}")
    
    def is_debug_enabled(self, component: str) -> bool:
        """Check if debugging is enabled for a specific component."""
        # Debug is disabled globally
        if not self.enabled:
            return False
            
        # Check if component is in the active set
        return component in self.active_components
    
    def debug_log(self, component: str, message: str, *args, **kwargs):
        """
        Log a debug message for a specific component if debugging is enabled.
        
        Args:
            component: The component name (kb, llm, api, etc.)
            message: The log message
            *args, **kwargs: Additional arguments for the logger
        """
        if self.is_debug_enabled(component):
            logger.debug(f"[{component.upper()}] {message}", *args, **kwargs)
    
    def get_settings_dict(self):
        """Get the current debug settings as a dictionary for templates."""
        return {
            "enabled": self.enabled,
            "save_logs": self.save_logs,
            "global_level": self.global_level,
            "components": self.components,
            "integrations": self.integrations,
            "active_components": sorted(list(self.active_components))
        }

# Global instance for easy access
debug_manager = DebugManager()

def get_debug_manager() -> DebugManager:
    """Get the global debug manager instance."""
    return debug_manager

# Convenience functions for use throughout the codebase
def is_debug_enabled(component: str) -> bool:
    """Check if debugging is enabled for a specific component."""
    return debug_manager.is_debug_enabled(component)

def debug_log(component: str, message: str, *args, **kwargs):
    """Log a debug message for a component if debugging is enabled."""
    debug_manager.debug_log(component, message, *args, **kwargs)
