/* task-tracker.css - Styles for background task tracking system */

/* Notification Bell */
.notification-bell {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    width: 32px;
    height: 32px;
    margin-right: 8px;
    transition: transform 0.2s ease;
    z-index: 1010; /* Ensure it's above other elements */
    pointer-events: auto; /* Force pointer events to be enabled */
}

.notification-bell:hover {
    transform: scale(1.1);
}

.notification-bell i {
    font-size: 1.2rem;
    color: var(--primary-color);
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--accent-color);
    color: var(--accent-text);
    font-size: 0.7rem;
    font-weight: bold;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0);
    transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.notification-badge.active {
    transform: scale(1);
}

.notification-bell.notification-new {
    animation: bell-shake 1s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes bell-shake {
    0%, 100% { transform: rotate(0); }
    20%, 60% { transform: rotate(5deg); }
    40%, 80% { transform: rotate(-5deg); }
}

/* Task Panel */
.task-panel {
    position: fixed;
    top: 60px;
    right: -350px;
    width: 320px;
    max-height: calc(100vh - 80px);
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    transition: right 0.3s ease;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.task-panel.open {
    right: 20px;
}

.task-panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.task-panel-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.task-filter {
    display: flex;
    align-items: center;
    margin-left: auto;
    margin-right: 12px;
}

.task-filter button {
    background: none;
    border: none;
    font-size: 0.8rem;
    padding: 2px 8px;
    cursor: pointer;
    color: var(--text-secondary);
    border-radius: 12px;
}

.task-filter button.active {
    background-color: var(--primary-color);
    color: var(--primary-text);
}

.close-panel {
    background: none;
    border: none;
    font-size: 1.2rem;
    line-height: 1;
    padding: 0;
    cursor: pointer;
    color: var(--text-secondary);
}

.task-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
}

.task-item {
    margin: 8px;
    border-radius: 6px;
    overflow: hidden;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    transition: box-shadow 0.2s ease;
}

.task-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-header {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    cursor: pointer;
}

.task-name {
    flex: 1;
    font-size: 0.9rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.task-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    margin: 0 8px;
    font-weight: 500;
}

.task-status.pending {
    background-color: var(--status-pending-bg, #fff8e1);
    color: var(--status-pending-text, #f57c00);
}

.task-status.running {
    background-color: var(--status-running-bg, #e8f5e9);
    color: var(--status-running-text, #43a047);
}

.task-status.completed {
    background-color: var(--status-completed-bg, #e3f2fd);
    color: var(--status-completed-text, #1976d2);
}

.task-status.failed {
    background-color: var(--status-failed-bg, #ffebee);
    color: var(--status-failed-text, #e53935);
}

.task-progress {
    width: 60px;
    height: 6px;
    background-color: var(--bg-secondary);
    border-radius: 3px;
    overflow: hidden;
    margin-right: 8px;
}

.progress-bar {
    height: 100%;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.expand-task {
    background: none;
    border: none;
    font-size: 1rem;
    padding: 0;
    cursor: pointer;
    color: var(--text-secondary);
    transition: transform 0.2s ease;
}

.task-item.expanded .expand-task {
    transform: rotate(180deg);
}

.task-details {
    height: 0;
    overflow: hidden;
    transition: height 0.3s ease;
}

.task-item.expanded .task-details {
    height: auto;
    max-height: 200px;
    overflow-y: auto;
}

.task-logs {
    padding: 8px 12px;
    border-top: 1px solid var(--border-color);
}

.log-entry {
    margin: 6px 0;
    font-size: 0.8rem;
    display: flex;
    align-items: flex-start;
}

.log-time {
    color: var(--text-secondary);
    margin-right: 8px;
    font-size: 0.7rem;
    min-width: 45px;
}

.log-message {
    flex: 1;
}

/* Toast Notification */
.toast-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
    z-index: 10000;
}

.toast {
    padding: 12px 16px;
    border-radius: 8px;
    min-width: 250px;
    max-width: 320px;
    background-color: var(--card-bg);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: flex-start;
    border-left: 4px solid var(--primary-color);
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast.info {
    border-color: var(--info-color, #2196f3);
}

.toast.success {
    border-color: var(--success-color, #4caf50);
}

.toast.warning {
    border-color: var(--warning-color, #ff9800);
}

.toast.error {
    border-color: var(--error-color, #f44336);
}

.toast-icon {
    margin-right: 12px;
    font-size: 1.2rem;
}

.toast.info .toast-icon {
    color: var(--info-color, #2196f3);
}

.toast.success .toast-icon {
    color: var(--success-color, #4caf50);
}

.toast.warning .toast-icon {
    color: var(--warning-color, #ff9800);
}

.toast.error .toast-icon {
    color: var(--error-color, #f44336);
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: 600;
    margin-bottom: 3px;
    font-size: 0.9rem;
}

.toast-message {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.toast-close {
    background: none;
    border: none;
    font-size: 1rem;
    line-height: 1;
    padding: 0;
    cursor: pointer;
    color: var(--text-secondary);
    margin-left: 12px;
}

/* Solo Leveling Theme Integration */
.solo-leveling-theme .notification-bell i {
    color: var(--sl-purple);
}

.solo-leveling-theme .notification-badge {
    background-color: var(--sl-purple-bright);
}

.solo-leveling-theme .task-progress .progress-bar {
    background-color: var(--sl-purple);
}

.solo-leveling-theme .task-filter button.active {
    background-color: var(--sl-purple);
}
