"""
Task management service module.

This module provides services for managing background tasks, including creation,
tracking, and notification. It follows the service layer pattern established in
the refactoring plan.
"""

import logging
import threading
import time
import uuid
import json
from typing import Dict, List, Any, Optional, Callable, Union
from datetime import datetime
from flask_socketio import emit

from .base_service import BaseService
from src.models.task import Task
from src.models import db

# Configure logging
logger = logging.getLogger(__name__)

# Keep this for backward compatibility during migration
class InMemoryTask:
    """Legacy class representing an in-memory background task."""
    
    def __init__(self, task_id: str, name: str, description: str = "", total_steps: int = 100):
        """
        Initialize a task.
        
        Args:
            task_id: Unique identifier for the task
            name: Name of the task
            description: Description of the task
            total_steps: Total number of steps in the task
        """
        self.id = task_id
        self.name = name
        self.description = description
        self.total_steps = total_steps
        self.current_step = 0
        self.status = "pending"  # pending, running, completed, failed
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        self.progress = 0  # 0 to 100
        self.logs = []
        self.result = None
        self.error = None
        self._log_data = []
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the task to a dictionary.
        
        Returns:
            Dictionary representation of the task
        """
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "status": self.status,
            "progress": self.progress,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "logs": self.logs,
            "result": self.result,
            "error": self.error
        }
    
    def start(self) -> None:
        """Mark the task as running."""
        self.status = "running"
        self.started_at = datetime.now()
        self.log("Task started")
    
    def complete(self, result: Any = None) -> None:
        """
        Mark the task as completed.
        
        Args:
            result: Result of the task
        """
        self.status = "completed"
        self.completed_at = datetime.now()
        self.progress = 100
        self.result = result
        self.log("Task completed")
    
    def fail(self, error: str) -> None:
        """
        Mark the task as failed.
        
        Args:
            error: Error message
        """
        self.status = "failed"
        self.completed_at = datetime.now()
        self.error = error
        self.log(f"Task failed: {error}", level="error")
    
    def update_progress(self, step: int, message: Optional[str] = None) -> None:
        """
        Update the task progress.
        
        Args:
            step: Current step
            message: Optional progress message
        """
        self.current_step = min(step, self.total_steps)
        self.progress = int((self.current_step / self.total_steps) * 100)
        
        if message:
            self.log(message)
    
    def log(self, message: str, level: str = "info") -> None:
        """
        Add a log message to the task.
        
        Args:
            message: Log message
            level: Log level (info, warning, error)
        """
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "message": message,
            "level": level
        }
        
        self.logs.append(log_entry)
        self._log_data.append(log_entry)
        logger.info(f"Task {self.id} ({self.name}): {message}")
        if level == "info":
            logger.info(f"Task {self.id} - {message}")
        elif level == "warning":
            logger.warning(f"Task {self.id} - {message}")
        elif level == "error":
            logger.error(f"Task {self.id} - {message}")


class TaskService(BaseService):
    """Service for managing background tasks.
    
    This service provides a centralized system for creating, running, and monitoring
    background tasks. It manages task lifecycle, provides real-time updates, and
    ensures proper error handling and recovery.
    """
    
    # Task priority levels
    PRIORITY_LOW = 0
    PRIORITY_NORMAL = 5
    PRIORITY_HIGH = 10
    
    def initialize(self) -> None:
        """Initialize the task service."""
        self._active_threads = {}
        self._pending_tasks = []
        self._socketio = None
        # Maximum number of concurrent tasks
        self._max_concurrent_tasks = 5
        # Task execution interval in seconds
        self._task_check_interval = 1.0
        # Whether the task scheduler is running
        self._scheduler_running = False
        # Legacy in-memory tasks (to be deprecated)
        self._tasks = {}
        
        # Start the task scheduler
        self._start_scheduler()
        
        logger.info("Task service initialized")
        
    def _start_scheduler(self) -> None:
        """Start the task scheduler in a background thread."""
        if self._scheduler_running:
            return
            
        self._scheduler_running = True
        thread = threading.Thread(target=self._scheduler_loop)
        thread.daemon = True
        thread.start()
        logger.info("Task scheduler started")
    
    def _scheduler_loop(self) -> None:
        """Run the task scheduler loop."""
        try:
            while self._scheduler_running:
                try:
                    self._process_pending_tasks()
                    time.sleep(self._task_check_interval)
                except Exception as e:
                    logger.error(f"Error in task scheduler loop: {str(e)}")
                    time.sleep(5)  # Wait longer after an error
        except Exception as e:
            logger.error(f"Task scheduler exited unexpectedly: {str(e)}")
            self._scheduler_running = False
    
    def set_socketio(self, socketio) -> None:
        """
        Set the SocketIO instance for real-time notifications.
        
        Args:
            socketio: SocketIO instance
        """
        self._socketio = socketio
    
    def _process_pending_tasks(self) -> None:
        """Process pending tasks based on priority and available threads."""
        if not self._pending_tasks:
            return
            
        # Sort pending tasks by priority (highest first)
        self._pending_tasks.sort(key=lambda t: t["priority"], reverse=True)
        
        # Check how many tasks we can start
        available_slots = self._max_concurrent_tasks - len(self._active_threads)
        if available_slots <= 0:
            return
            
        # Start as many tasks as we can
        tasks_to_remove = []
        for i, task_info in enumerate(self._pending_tasks):
            if i >= available_slots:
                break
                
            # Start the task
            try:
                task = task_info["task"]
                func = task_info["func"]
                args = task_info.get("args", ())
                kwargs = task_info.get("kwargs", {})
                
                # Run the task
                self._run_task_immediately(task, func, args, kwargs)
                tasks_to_remove.append(task_info)
                logger.info(f"Started scheduled task {task.id}: {task.name}")
            except Exception as e:
                logger.error(f"Error starting task {task_info['task'].id}: {str(e)}")
                tasks_to_remove.append(task_info)
                
        # Remove started tasks from the pending list
        for task_info in tasks_to_remove:
            self._pending_tasks.remove(task_info)
    
    def create_task(self, name: str, description: str = "", total_steps: int = 100, user_id: Optional[int] = None, 
                   priority: int = None, metadata: Dict[str, Any] = None) -> Union[Task, InMemoryTask]:
        """
        Create a new task.
        
        Args:
            name: Name of the task
            description: Description of the task
            total_steps: Total number of steps in the task
            user_id: Optional user ID associated with the task
            priority: Task priority (0-10, higher is more important)
            metadata: Optional metadata to attach to the task
            
        Returns:
            The created task
        """
        # Set default priority if not specified
        if priority is None:
            priority = self.PRIORITY_NORMAL
            
        # Validate priority
        if not isinstance(priority, int) or priority < 0 or priority > 10:
            raise ValueError("Priority must be an integer between 0 and 10")
            
        # Prepare metadata
        if metadata is None:
            metadata = {}
            
        # Add priority to metadata
        metadata["priority"] = priority
        
        # Create a database-backed task
        task = Task.create(name=name, description=description, user_id=user_id, metadata=metadata)
        
        # Also create an in-memory task for backward compatibility
        in_memory_task = InMemoryTask(task.id, name, description, total_steps)
        self._tasks[task.id] = in_memory_task
        
        # Emit task created event
        self._emit_task_update(task)
        
        return task
    
    def get_task(self, task_id: str) -> Optional[Union[Task, InMemoryTask]]:
        """
        Get a task by ID.
        
        Args:
            task_id: ID of the task to get
            
        Returns:
            The task if found, None otherwise
        """
        # First try to get from database
        db_task = Task.get_by_id(task_id)
        
        # If found in database, return it
        if db_task:
            # Check if we also have an in-memory version
            if task_id in self._tasks:
                # Update the in-memory task based on the database task
                self._sync_task_with_db(task_id, db_task)
            return db_task
        
        # Fallback to in-memory tasks (legacy mode)
        return self._tasks.get(task_id)
    
    def _sync_task_with_db(self, task_id: str, db_task: Task) -> None:
        """
        Sync an in-memory task with its database counterpart.
        
        Args:
            task_id: ID of the task to sync
            db_task: Database task to sync with
        """
        if task_id in self._tasks:
            in_memory_task = self._tasks[task_id]
            in_memory_task.status = db_task.status
            in_memory_task.progress = db_task.progress
            in_memory_task.result = db_task.result
            in_memory_task.error = db_task.error
            
            # Update timestamps
            if db_task.started_at:
                in_memory_task.started_at = db_task.started_at
            if db_task.completed_at:
                in_memory_task.completed_at = db_task.completed_at
    
    def get_all_tasks(self) -> List[Union[Task, InMemoryTask]]:
        """
        Get all tasks.
        
        Returns:
            List of all tasks
        """
        # Get tasks from database
        db_tasks = Task.get_all()
        
        # Return database tasks (they take precedence)
        return db_tasks
    
    def get_active_tasks(self) -> List[Union[Task, InMemoryTask]]:
        """
        Get all active (pending or running) tasks.
        
        Returns:
            List of active tasks
        """
        # Get active tasks from database
        return Task.get_all(status=Task.STATUS_PENDING) + Task.get_all(status=Task.STATUS_RUNNING)
    
    def get_completed_tasks(self) -> List[Union[Task, InMemoryTask]]:
        """
        Get all completed tasks.
        
        Returns:
            List of completed tasks
        """
        # Get completed tasks from database
        return Task.get_all(status=Task.STATUS_COMPLETED) + Task.get_all(status=Task.STATUS_FAILED)
    
    def update_task_progress(self, task_id: str, step: int, message: Optional[str] = None) -> Optional[Union[Task, InMemoryTask]]:
        """
        Update a task's progress.
        
        Args:
            task_id: ID of the task to update
            step: Current step
            message: Optional progress message
            
        Returns:
            The updated task if found, None otherwise
        """
        # Get the task (database or in-memory)
        task = self.get_task(task_id)
        
        if not task:
            return None
            
        # Handle database task
        if isinstance(task, Task):
            # Calculate progress percentage
            progress = min(100, max(0, int(step)))
            task.update_progress(progress)
            
            # If message is provided, store it in result field
            if message:
                task.result = message
                db.session.commit()
        # Handle in-memory task (legacy)
        else:
            task.update_progress(step, message)
            
        # Notify via websocket
        self._emit_task_update(task)
        return task
    
    def complete_task(self, task_id: str, result: Any = None) -> Optional[Union[Task, InMemoryTask]]:
        """
        Mark a task as completed.
        
        Args:
            task_id: ID of the task to complete
            result: Optional result data
            
        Returns:
            The completed task if found, None otherwise
        """
        # Get the task (database or in-memory)
        task = self.get_task(task_id)
        
        if not task:
            return None
            
        # Handle database task
        if isinstance(task, Task):
            task.complete(result)
        # Handle in-memory task (legacy)
        else:
            task.complete(result)
            
        # Notify via websocket
        self._emit_task_update(task)
        return task
    
    def fail_task(self, task_id: str, error: str) -> Optional[Union[Task, InMemoryTask]]:
        """
        Mark a task as failed.
        
        Args:
            task_id: ID of the task to fail
            error: Error message
            
        Returns:
            The failed task if found, None otherwise
        """
        # Get the task (database or in-memory)
        task = self.get_task(task_id)
        
        if not task:
            return None
            
        # Handle database task
        if isinstance(task, Task):
            task.fail(error)
        # Handle in-memory task (legacy)
        else:
            task.fail(error)
            
        # Notify via websocket
        self._emit_task_update(task)
        return task
    
    def _run_task_immediately(self, task_or_id: Any, func: Callable, args: tuple = (), kwargs: Dict[str, Any] = None) -> Union[Task, InMemoryTask]:
        """
        Run a task immediately in a background thread.
        
        Args:
            task_or_id: Task instance or task ID
            func: Function to run
            args: Arguments to pass to the function
            kwargs: Keyword arguments to pass to the function
            
        Returns:
            The task
        """
        # Handle various task_or_id types
        if isinstance(task_or_id, str):
            task = self.get_task(task_or_id)
            if not task:
                raise ValueError(f"Task ID {task_or_id} not found")
        elif isinstance(task_or_id, (Task, InMemoryTask)):
            task = task_or_id
        else:
            raise TypeError(f"Expected Task, InMemoryTask, or str, got {type(task_or_id)}")
        
        # Prepare kwargs
        if kwargs is None:
            kwargs = {}
        
        # Get task ID for thread tracking
        task_id = task.id
        
        # Wrap the function to handle task status
        def wrapped_func(*args, **kwargs):
            try:
                # Start the task
                if isinstance(task, Task):
                    task.start()
                else:
                    # Get fresh database task if available
                    db_task = Task.get_by_id(task_id)
                    if db_task:
                        db_task.start()
                    else:
                        # Fallback to in-memory task
                        task.start()
                
                self._emit_task_update(task)
                
                # Run the function
                result = func(*args, **kwargs)
                
                # Complete the task
                self.complete_task(task_id, result)
                
                return result
            except Exception as e:
                # Fail the task
                self.fail_task(task_id, str(e))
                logger.exception(f"Task {task_id} failed: {str(e)}")
                raise
            finally:
                # Clean up thread reference
                if task_id in self._active_threads:
                    del self._active_threads[task_id]
        
        # Run the wrapped function in a thread
        thread = threading.Thread(target=wrapped_func, args=args, kwargs=kwargs)
        thread.daemon = True
        thread.start()
        
        # Store the thread
        self._active_threads[task_id] = thread
        
        return task
    
    def run_task(self, task_or_id: Any, func: Callable, args: tuple = (), kwargs: Dict[str, Any] = None, 
                 schedule: bool = False, priority: int = None) -> Union[Task, InMemoryTask]:
        """
        Run a task in a background thread.
        
        Args:
            task_or_id: Task instance or task ID
            func: Function to run
            args: Arguments to pass to the function
            kwargs: Keyword arguments to pass to the function
            schedule: Whether to schedule the task or run it immediately
            priority: Priority for scheduled tasks (0-10, higher is more important)
            
        Returns:
            The task
        """
        # Handle various task_or_id types
        if isinstance(task_or_id, str):
            task = self.get_task(task_or_id)
            if not task:
                raise ValueError(f"Task ID {task_or_id} not found")
        elif isinstance(task_or_id, (Task, InMemoryTask)):
            task = task_or_id
        else:
            raise TypeError(f"Expected Task, InMemoryTask, or str, got {type(task_or_id)}")
            
        # Extract priority from task if not specified
        if priority is None and isinstance(task, Task) and task.metadata and "priority" in task.metadata:
            try:
                priority = int(task.metadata["priority"])
            except (ValueError, TypeError):
                priority = self.PRIORITY_NORMAL
        elif priority is None:
            priority = self.PRIORITY_NORMAL
        
        # Validate priority
        if not isinstance(priority, int) or priority < 0 or priority > 10:
            raise ValueError("Priority must be an integer between 0 and 10")
        
        # Prepare kwargs
        if kwargs is None:
            kwargs = {}
            
        # If scheduling is requested, add to pending tasks queue
        if schedule:
            # Create task info
            task_info = {
                "task": task,
                "func": func,
                "args": args,
                "kwargs": kwargs,
                "priority": priority
            }
            
            # Add to pending tasks
            self._pending_tasks.append(task_info)
            logger.info(f"Scheduled task {task.id} with priority {priority}")
            
            # Make sure scheduler is running
            self._start_scheduler()
            
            return task
        else:
            # Run immediately
            return self._run_task_immediately(task, func, args, kwargs)
    
    def _emit_task_update(self, task: Union[Task, InMemoryTask]) -> None:
        """
        Emit a task update event via SocketIO.
        
        Args:
            task: The task that was updated
        """
        if not self._socketio:
            return
            
        # Convert task to dictionary format for SocketIO
        task_data = task.to_dict()
        
        # Ensure consistent format for both task types
        if isinstance(task, Task) and 'logs' not in task_data:
            # Add empty logs array for consistency with frontend expectations
            task_data['logs'] = []
            
        # Emit the event
        self._socketio.emit("task_update", task_data)
    
    def get_task_stats(self) -> Dict[str, Any]:
        """
        Get statistics about tasks in the system.
        
        Returns:
            Dictionary containing task statistics
        """
        try:
            # Get counts from database
            pending_count = Task.query.filter_by(status=Task.STATUS_PENDING).count()
            running_count = Task.query.filter_by(status=Task.STATUS_RUNNING).count()
            completed_count = Task.query.filter_by(status=Task.STATUS_COMPLETED).count()
            failed_count = Task.query.filter_by(status=Task.STATUS_FAILED).count()
            canceled_count = Task.query.filter_by(status=Task.STATUS_CANCELED).count()
            
            # Calculate active threads
            active_threads = len(self._active_threads)
            
            # Calculate pending tasks
            pending_tasks = len(self._pending_tasks)
            
            # Return stats
            return {
                "pending": pending_count,
                "running": running_count,
                "completed": completed_count,
                "failed": failed_count,
                "canceled": canceled_count,
                "active_threads": active_threads,
                "pending_tasks": pending_tasks,
                "max_concurrent_tasks": self._max_concurrent_tasks,
                "scheduler_running": self._scheduler_running
            }
        except Exception as e:
            logger.error(f"Error getting task stats: {str(e)}")
            return {
                "error": str(e)
            }
    
    def set_max_concurrent_tasks(self, max_concurrent_tasks: int) -> None:
        """
        Set the maximum number of concurrent tasks.
        
        Args:
            max_concurrent_tasks: Maximum number of concurrent tasks
        """
        if not isinstance(max_concurrent_tasks, int) or max_concurrent_tasks < 1:
            raise ValueError("Maximum concurrent tasks must be a positive integer")
            
        self._max_concurrent_tasks = max_concurrent_tasks
        logger.info(f"Maximum concurrent tasks set to {max_concurrent_tasks}")
    
    def cancel_task(self, task_id: str, reason: str = "Canceled by user") -> Optional[Union[Task, InMemoryTask]]:
        """
        Cancel a pending or running task.
        
        Args:
            task_id: ID of the task to cancel
            reason: Reason for cancellation
            
        Returns:
            The canceled task if found, None otherwise
        """
        # Get the task
        task = self.get_task(task_id)
        
        if not task:
            return None
            
        # Check if the task can be canceled (only pending or running)
        if isinstance(task, Task):
            if task.status not in [Task.STATUS_PENDING, Task.STATUS_RUNNING]:
                logger.warning(f"Cannot cancel task {task_id} with status {task.status}")
                return task
        else:  # InMemoryTask
            if task.status not in ["pending", "running"]:
                logger.warning(f"Cannot cancel task {task_id} with status {task.status}")
                return task
                
        # Cancel the task
        if isinstance(task, Task):
            task.cancel(reason)
        else:  # InMemoryTask
            task.status = "canceled"
            task.completed_at = datetime.now()
            task.error = reason
            
        # Remove from pending tasks if present
        for task_info in list(self._pending_tasks):
            if task_info["task"].id == task_id:
                self._pending_tasks.remove(task_info)
                break
                
        # Remove from active threads if present (can't really stop it, but we can untrack it)
        if task_id in self._active_threads:
            del self._active_threads[task_id]
            
        # Notify of cancellation
        self._emit_task_update(task)
        logger.info(f"Task {task_id} canceled: {reason}")
        
        return task
    
    def cleanup_old_tasks(self, max_age_days: int = 7) -> int:
        """
        Clean up old completed, failed, or canceled tasks.
        
        Args:
            max_age_days: Maximum age of tasks to keep in days
            
        Returns:
            Number of tasks removed
        """
        from datetime import timedelta
        import sqlalchemy
        
        # Calculate cutoff date
        cutoff_date = datetime.now() - timedelta(days=max_age_days)
        count = 0
        
        # Clean up database tasks
        try:
            # Find completed/failed/canceled tasks older than cutoff date
            old_tasks = Task.query.filter(
                Task.status.in_([Task.STATUS_COMPLETED, Task.STATUS_FAILED, Task.STATUS_CANCELED]),
                Task.completed_at < cutoff_date
            ).all()
            
            # Delete the tasks
            for task in old_tasks:
                db.session.delete(task)
            
            # Commit the changes
            db.session.commit()
            count = len(old_tasks)
            
            # Log the cleanup
            logger.info(f"Removed {count} old tasks from database")
        except Exception as e:
            logger.error(f"Error cleaning up old database tasks: {str(e)}")
            db.session.rollback()
        
        # Clean up in-memory tasks (legacy)
        in_memory_count = 0
        for task_id, task in list(self._tasks.items()):
            if (task.status in ["completed", "failed", "canceled"] and 
                task.completed_at and 
                task.completed_at < cutoff_date):
                del self._tasks[task_id]
                in_memory_count += 1
        
        if in_memory_count > 0:
            logger.info(f"Removed {in_memory_count} old in-memory tasks")
            
        return count + in_memory_count
    
    def demo_tasks(self) -> List[Union[Task, InMemoryTask]]:
        """
        Create demo tasks for testing.
        
        Returns:
            List of created demo tasks
        """
        # Create a few demo tasks with descriptive information
        demo_tasks = [
            self.create_task(
                name="Demo Task 1", 
                description="This is a demo task that completes quickly"
            ),
            self.create_task(
                name="Demo Task 2", 
                description="This is a demo task that takes some time",
                total_steps=10
            ),
            self.create_task(
                name="Demo Task 3", 
                description="This is a demo task that will fail"
            )
        ]
        
        # Set up a quick-completing task
        self.run_task(demo_tasks[0], lambda: "Task completed successfully")
        
        # Set up a task that takes some time
        def long_running_process():
            total_steps = 10
            for i in range(total_steps):
                # Update progress
                self.update_task_progress(demo_tasks[1].id, i + 1, f"Step {i+1}/{total_steps}")
                # Sleep for a bit
                time.sleep(1)
            return "Long running task completed successfully"
        
        self.run_task(demo_tasks[1], long_running_process)
        
        # Set up a failing task
        def failing_process():
            self.update_task_progress(demo_tasks[2].id, 3, "About to fail...")
            time.sleep(1)
            raise ValueError("This task failed intentionally for demonstration purposes")
        
        self.run_task(demo_tasks[2], failing_process)
        
        # Create a pending task that doesn't start automatically
        pending_task = self.create_task(
            name="Demo Pending Task", 
            description="This is a demo task that remains in pending state"
        )
        demo_tasks.append(pending_task)
        
        return demo_tasks
