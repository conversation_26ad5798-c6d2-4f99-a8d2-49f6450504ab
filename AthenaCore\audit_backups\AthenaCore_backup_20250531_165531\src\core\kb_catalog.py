"""
Knowledge Base Catalog Module.

This module provides functionality for cataloging and managing knowledge base documents.
It allows tracking document metadata, sources, and relationships.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import threading
from datetime import datetime

# Configure logging
logger = logging.getLogger("athena.kb_catalog")

class KnowledgeBaseCatalog:
    """
    Manages a catalog of knowledge base documents and their metadata.
    Provides methods for document tracking, searching, and metadata management.
    """
    
    def __init__(self, catalog_path: Union[str, Path] = None):
        """
        Initialize the knowledge base catalog.
        
        Args:
            catalog_path: Path to the catalog file. If not provided, uses a default path.
        """
        self.lock = threading.RLock()
        
        if catalog_path is None:
            # Default path is in the same directory as the knowledge base
            from src.utils.config import AthenaConfig
            config = AthenaConfig.load()
            base_dir = Path(config.persistent_dir) if hasattr(config, 'persistent_dir') else Path.cwd()
            self.catalog_path = base_dir / "kb_catalog.json"
        else:
            self.catalog_path = Path(catalog_path)
            
        # Create parent directories if they don't exist
        self.catalog_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize empty catalog or load existing one
        self.catalog = self._load_catalog()
        logger.info(f"Knowledge Base Catalog initialized at {self.catalog_path}")
        
    def _load_catalog(self) -> Dict:
        """Load the catalog from disk or create a new one if it doesn't exist."""
        if self.catalog_path.exists():
            try:
                with open(self.catalog_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading catalog: {e}")
                return {"documents": {}, "metadata": {"version": "1.0"}}
        else:
            return {"documents": {}, "metadata": {"version": "1.0"}}
            
    def _save_catalog(self):
        """Save the catalog to disk."""
        with self.lock:
            try:
                with open(self.catalog_path, 'w', encoding='utf-8') as f:
                    json.dump(self.catalog, f, indent=2, ensure_ascii=False)
            except json.JSONDecodeError as e:
                logger.error(f"Error saving catalog due to JSON decode error: {e}")
            except Exception as e:
                logger.error(f"Error saving catalog: {e}")
    
    def add_document(self, doc_id: str, metadata: Dict) -> bool:
        """
        Add a document to the catalog.
        
        Args:
            doc_id: Document ID
            metadata: Document metadata
            
        Returns:
            bool: Success status
        """
        try:
            # Ensure metadata is a dictionary
            if not isinstance(metadata, dict):
                logger.warning(f"Received non-dict metadata of type {type(metadata).__name__}. Converting to dictionary.")
                if isinstance(metadata, str):
                    try:
                        # Try to parse as JSON if it's a string
                        metadata = json.loads(metadata)
                    except json.JSONDecodeError:
                        # If not valid JSON, create a simple dict
                        metadata = {"content": metadata}
                else:
                    # For any other type, create a simple dict
                    metadata = {"value": str(metadata)}
            
            # Deep serialization to handle nested structures
            def deep_serialize(obj):
                if isinstance(obj, dict):
                    return {k: deep_serialize(v) for k, v in obj.items()}
                elif isinstance(obj, (list, tuple, set)):
                    return [deep_serialize(item) for item in obj]
                elif isinstance(obj, (str, int, float, bool)) or obj is None:
                    return obj
                else:
                    # Convert anything else to string
                    return str(obj)
            
            # Ensure all metadata is serializable to prevent unhashable type errors
            try:
                # First, ensure metadata is a proper dictionary
                if not isinstance(metadata, dict):
                    logger.warning(f"Converting non-dict metadata of type {type(metadata).__name__} to dictionary")
                    if isinstance(metadata, str):
                        # If it's a string that might be JSON, try to parse it
                        try:
                            metadata = json.loads(metadata)
                        except json.JSONDecodeError:
                            # Not JSON, create a simple metadata dict
                            metadata = {"content": metadata}
                    else:
                        # Convert any other type to a simple metadata dict
                        metadata = {"content": str(metadata)}
                
                # Now serialize the properly formatted metadata dictionary
                serialized_metadata = deep_serialize(metadata)
                
                # As a safety check, convert to JSON and back to ensure serializability
                try:
                    json_str = json.dumps(serialized_metadata)
                    serialized_metadata = json.loads(json_str)
                    logger.debug(f"Successfully serialized metadata for document {doc_id}")
                except (TypeError, ValueError) as e:
                    logger.error(f"JSON serialization check failed: {e}, falling back to string conversion")
                    # Fall back to string conversion for all values
                    serialized_metadata = {k: str(v) for k, v in metadata.items()}
            except Exception as e:
                logger.error(f"All serialization strategies failed: {e}")
                # Create a simplified metadata dictionary with basic string values
                serialized_metadata = {
                    "filename": str(metadata.get("filename", "")) if isinstance(metadata, dict) else "",
                    "source": str(metadata.get("source", "")) if isinstance(metadata, dict) else "",
                    "timestamp": datetime.now().isoformat()
                }
            
            # Add user_id from metadata if available for proper separation
            user_id = metadata.get("user_id", "system")
            
            with self.lock:
                self.catalog["documents"][doc_id] = {
                    "metadata": serialized_metadata,
                    "user_id": user_id,
                    "created_at": metadata.get("timestamp", ""),
                    "last_updated": metadata.get("timestamp", "")
                }
                # Save the catalog to disk to persist the change
                self._save_catalog()
            return True
        except Exception as e:
            logger.error(f"Error adding document {doc_id} to catalog: {e}")
            return False
            
    def remove_document(self, doc_id: str) -> bool:
        """
        Remove a document from the catalog.
        
        Args:
            doc_id: Unique identifier for the document
            
        Returns:
            bool: True if successful, False otherwise
        """
        with self.lock:
            if doc_id in self.catalog["documents"]:
                del self.catalog["documents"][doc_id]
                self._save_catalog()
                return True
            return False
            
    def get_document(self, doc_id: str) -> Optional[Dict]:
        """
        Get document metadata from the catalog.
        
        Args:
            doc_id: Unique identifier for the document
            
        Returns:
            Optional[Dict]: Document metadata if found, None otherwise
        """
        with self.lock:
            return self.catalog["documents"].get(doc_id)
            
    def assign_document_to_user(self, doc_id: str, user_id: str) -> bool:
        """
        Assign a document to a specific user.
        
        Args:
            doc_id: The document ID to assign
            user_id: The user ID to assign the document to
            
        Returns:
            bool: True if successful, False otherwise
        """
        with self.lock:
            if doc_id in self.catalog["documents"]:
                # Update the user_id in the catalog
                self.catalog["documents"][doc_id]["user_id"] = user_id
                # Also update the metadata user_id for consistency
                if "metadata" in self.catalog["documents"][doc_id]:
                    self.catalog["documents"][doc_id]["metadata"]["user_id"] = user_id
                self._save_catalog()
                
                # Try to update the document in the knowledge base as well
                try:
                    from src.api.routes import kb
                    # Use a method that ensures the metadata in ChromaDB is updated too
                    kb.update_document_metadata(doc_id, {"user_id": user_id})
                except Exception as e:
                    logger.error(f"Error updating document metadata in KB: {e}")
                
                return True
            return False
            
    def list_documents(self, user_id: Optional[str] = None, include_system: bool = False, is_admin: bool = False) -> List[Dict]:
        """
        List all documents in the catalog, optionally filtered by user ID.
        
        Args:
            user_id: Optional user ID to filter by
            include_system: Whether to include system documents
            is_admin: Whether the requester is an admin (can see all documents)
            
        Returns:
            List[Dict]: List of document metadata
        """
        with self.lock:
            if user_id:
                return [
                    {"id": doc_id, **doc}
                    for doc_id, doc in self.catalog["documents"].items()
                    if doc.get("user_id") == user_id
                ]
            elif is_admin:
                return [
                    {"id": doc_id, **doc} 
                    for doc_id, doc in self.catalog["documents"].items()
                ]
            else:
                return [
                    {"id": doc_id, **doc} 
                    for doc_id, doc in self.catalog["documents"].items()
                    if doc.get("user_id") != "system" or include_system
                ]
            
    def process_knowledgebase_folder(self, folder_path: Union[str, Path], current_user_id: str = None) -> Dict[str, int]:
        """
        Process all documents in the Knowledgebase folder and import them into the knowledge base.
        
        Args:
            folder_path: Path to the Knowledgebase folder
            current_user_id: Optional user ID to assign to the documents (default: system)
            
        Returns:
            Dict[str, int]: Statistics about the import process
        """
        # Initialize statistics
        stats = {
            "imported": 0,
            "skipped": 0,
            "failed": 0,
            "total": 0
        }
        
        folder_path = Path(folder_path)
        if not folder_path.exists() or not folder_path.is_dir():
            logger.warning(f"Knowledgebase folder not found: {folder_path}")
            return stats
        
        # Supported file types
        supported_extensions = {
            ".pdf", ".txt", ".md", ".docx", ".html", ".csv", ".json"
        }
        
        # Process all files in the folder
        for file_path in folder_path.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                stats["total"] += 1
                try:
                    # Import the document into the knowledge base
                    from src.core.document_processor import process_document
                    # Access the KB instance
                    from src.core.knowledge_db import kb
                    
                    # Determine the user ID to assign
                    # If a specific user ID is provided, use it
                    # Otherwise, use the current user's ID if available, or fall back to "system"
                    user_id = current_user_id or "system"
                    
                    # Create metadata for the document
                    relative_path = file_path.name
                    metadata = {
                        "title": file_path.name,
                        "source": "knowledgebase_folder",
                        "source_path": relative_path,
                        "type": file_path.suffix.lstrip(".").lower(),
                        "user_id": user_id,
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    # Process and add the document - note the new return signature with metadata
                    doc_id, text, processed_metadata = process_document(file_path, metadata)
                    if doc_id:
                        # Use the processed metadata returned from the document processor
                        # This already has the metadata we created merged in
                        
                        # Make sure processed_metadata is a dictionary and not another type
                        if not isinstance(processed_metadata, dict):
                            logger.warning(f"Processed metadata is not a dictionary: {type(processed_metadata)}, converting")
                            processed_metadata = dict(metadata)  # Fall back to original metadata
                        
                        # Convert all metadata to serializable format to prevent unhashable dict errors
                        serialized_metadata = {}
                        for key, value in processed_metadata.items():
                            # For complex types, convert to JSON string
                            if isinstance(value, (dict, list, set, tuple)):
                                serialized_metadata[key] = json.dumps(value)
                            # For simple types, use as-is or convert to string
                            elif isinstance(value, (str, int, float, bool)) or value is None:
                                serialized_metadata[key] = value
                            else:
                                # For any other types, convert to string
                                serialized_metadata[key] = str(value)
                        
                        try:
                            # Ensure metadata is fully serializable before adding to KB
                            # First, make a deep copy to avoid modifying the original
                            import copy
                            clean_metadata = copy.deepcopy(serialized_metadata)
                            
                            # Custom JSON encoder for handling non-serializable types
                            class SafeJSONEncoder(json.JSONEncoder):
                                def default(self, obj):
                                    return str(obj)
                            
                            # Validate by round-tripping through JSON
                            json_string = json.dumps(clean_metadata, cls=SafeJSONEncoder)
                            clean_metadata = json.loads(json_string)
                            
                            # Add to knowledge base with the validated metadata
                            kb.add_document(doc_id, text, clean_metadata)
                            
                            # Add to catalog with the same clean metadata
                            success = self.add_document(doc_id, clean_metadata)
                            
                            if success:
                                logger.info(f"Successfully imported: {file_path}")
                                stats["imported"] += 1
                            else:
                                logger.error(f"Failed to add document to catalog: {file_path}")
                                stats["failed"] += 1
                                
                        except Exception as e:
                            logger.error(f"Error processing document {file_path}: {e}")
                            
                            # Try alternate serialization strategy
                            try:
                                # Create completely flat string metadata
                                str_metadata = {}
                                for k, v in serialized_metadata.items():
                                    if isinstance(v, (dict, list, tuple, set)):
                                        str_metadata[k] = json.dumps(v, cls=SafeJSONEncoder)
                                    else:
                                        str_metadata[k] = str(v)
                                
                                # Try again with string-only metadata
                                kb.add_document(doc_id, text, str_metadata)
                                success = self.add_document(doc_id, str_metadata)
                                
                                if success:
                                    logger.info(f"Successfully imported with alternate serialization: {file_path}")
                                    stats["imported"] += 1
                                else:
                                    logger.error(f"Alternate catalog add failed: {file_path}")
                                    stats["failed"] += 1
                                    
                            except Exception as inner_e:
                                logger.error(f"All serialization strategies failed: {inner_e}")
                                stats["failed"] += 1
                    else:
                        logger.warning(f"Failed to process document: {file_path}")
                        stats["failed"] += 1
                except Exception as e:
                    logger.error(f"Error processing file {file_path}: {e}")
                    stats["failed"] += 1
                    
        return stats

# Singleton instance
_catalog_instance = None

def get_catalog_instance() -> KnowledgeBaseCatalog:
    """
    Get the singleton instance of the knowledge base catalog.
    
    Returns:
        KnowledgeBaseCatalog: The catalog instance
    """
    global _catalog_instance
    if _catalog_instance is None:
        _catalog_instance = KnowledgeBaseCatalog()
    return _catalog_instance
