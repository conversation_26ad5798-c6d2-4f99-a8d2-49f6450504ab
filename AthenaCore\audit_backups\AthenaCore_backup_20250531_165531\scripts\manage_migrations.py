#!/usr/bin/env python3
"""
Migration management utility.

This script provides command-line utilities for creating and running database
migrations during the refactoring process.
"""

import os
import sys
import argparse
import importlib.util
from pathlib import Path

# Get the absolute path to the project directory
project_dir = Path(__file__).parent.parent.absolute()

# Add the project directory to the Python path
sys.path.insert(0, str(project_dir))
os.environ['PYTHONPATH'] = str(project_dir)

# Print debug information
print(f"Project directory: {project_dir}")
print(f"Python path: {sys.path}")

# Import the application factory
from src.app_factory import create_app

def main():
    """Main function to run the migration manager CLI."""
    parser = argparse.ArgumentParser(description="Athena Core Migration Manager")
    subparsers = parser.add_subparsers(dest="command", help="Command to run")
    
    # Create migration command
    create_parser = subparsers.add_parser("create", help="Create a new migration")
    create_parser.add_argument("name", help="Name of the migration")
    create_parser.add_argument("--description", "-d", help="Description of the migration")
    
    # Run migration command
    run_parser = subparsers.add_parser("run", help="Run a migration")
    run_parser.add_argument("version", type=int, nargs="?", help="Version of the migration to run (omit to run all pending migrations)")
    
    # List migrations command
    list_parser = subparsers.add_parser("list", help="List all migrations")
    list_parser.add_argument("--pending", "-p", action="store_true", help="Only show pending migrations")
    list_parser.add_argument("--applied", "-a", action="store_true", help="Only show applied migrations")
    
    # Parse arguments
    args = parser.parse_args()
    
    # Create Flask app with database
    app = create_app()
    
    # Handle commands
    if args.command == "create":
        create_migration(app, args.name, args.description)
    elif args.command == "run":
        run_migration(app, args.version)
    elif args.command == "list":
        list_migrations(app, args.pending, args.applied)
    else:
        parser.print_help()

def create_migration(app, name, description=None):
    """
    Create a new migration.
    
    Args:
        app: Flask application instance
        name: Name of the migration
        description: Optional description of the migration
    """
    # Get the migration manager from the app
    migration_manager = app.migration_manager
    
    # Create the migration file
    success, message, migration_info = migration_manager.create_migration_file(name, description)
    
    if success:
        print(f"Success: {message}")
        if migration_info:
            print(f"Created migration v{migration_info['version']}: {migration_info['name']}")
            print(f"File: {migration_info['file_path']}")
    else:
        print(f"Error: {message}")
        sys.exit(1)

def run_migration(app, version=None):
    """
    Run a migration or all pending migrations.
    
    Args:
        app: Flask application instance
        version: Optional version of the migration to run
    """
    # Get the migration manager from the app
    migration_manager = app.migration_manager
    
    if version is not None:
        # Run the specified migration
        success, message = migration_manager.run_migration(version)
        
        if success:
            print(f"Success: {message}")
        else:
            print(f"Error: {message}")
            sys.exit(1)
    else:
        # Run all pending migrations
        success, message, executed_migrations = migration_manager.run_all_pending_migrations()
        
        if success:
            print(f"Success: {message}")
            
            if executed_migrations:
                print("\nExecuted migrations:")
                for migration in executed_migrations:
                    print(f"- v{migration['version']}: {migration['name']}")
        else:
            print(f"Error: {message}")
            sys.exit(1)

def list_migrations(app, pending_only=False, applied_only=False):
    """
    List all migrations.
    
    Args:
        app: Flask application instance
        pending_only: Only show pending migrations
        applied_only: Only show applied migrations
    """
    # Get the migration manager from the app
    migration_manager = app.migration_manager
    
    # Get all migrations
    all_migrations = migration_manager.get_all_migrations()
    
    # Get applied migrations
    with app.app_context():
        applied_migrations = migration_manager._get_applied_migrations()
    
    applied_versions = [m["version"] for m in applied_migrations]
    
    if not all_migrations:
        print("No migrations found.")
        return
    
    # Filter migrations based on flags
    if pending_only:
        migrations = [m for m in all_migrations if m["version"] not in applied_versions]
        status_label = "Pending"
    elif applied_only:
        migrations = [m for m in all_migrations if m["version"] in applied_versions]
        status_label = "Applied"
    else:
        migrations = all_migrations
        status_label = "Status"
    
    # Print migrations
    print(f"{'Version':<8} {'Name':<30} {status_label:<10} {'Description'}")
    print(f"{'-'*8} {'-'*30} {'-'*10} {'-'*30}")
    
    for migration in migrations:
        version = f"v{migration['version']}"
        name = migration['name']
        status = "Applied" if migration["version"] in applied_versions else "Pending"
        description = migration["description"]
        
        print(f"{version:<8} {name[:30]:<30} {status:<10} {description[:50]}")

if __name__ == "__main__":
    main()
