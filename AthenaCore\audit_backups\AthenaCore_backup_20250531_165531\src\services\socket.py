# src/services/socket.py

"""
WebSocket service for Athena Cross-Device API System.

This module implements WebSocket functionality to provide real-time
updates for background tasks, notifications, and other features
requiring immediate client communication.
"""

import logging
from flask import current_app
from flask_login import current_user
from flask_socketio import emit, join_room, leave_room

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger("socket_service")

# Store a reference to the Flask app
_app = None
# Store a reference to the SocketIO instance
_socketio = None

def init_app(app):
    """
    Initialize the socket service with the Flask application.
    
    Args:
        app (Flask): The Flask application
    """
    global _app
    _app = app
    logger.info("Socket service initialized")

def setup_socketio(socketio):
    """
    Set up SocketIO event handlers.
    
    Args:
        socketio (SocketIO): The SocketIO instance
    """
    global _socketio
    _socketio = socketio

    @socketio.on('connect')
    def handle_connect():
        """Handle client connection."""
        if current_user.is_authenticated:
            # Join a room specific to this user for private messages
            user_room = f'user_{current_user.id}'
            join_room(user_room)
            logger.debug(f"Client connected and joined room: {user_room}")
            
            # Send current active tasks on connect
            emit_active_tasks()
        else:
            logger.debug("Anonymous client connected")

    @socketio.on('disconnect')
    def handle_disconnect():
        """Handle client disconnect."""
        if current_user.is_authenticated:
            user_room = f'user_{current_user.id}'
            leave_room(user_room)
            logger.debug(f"Client disconnected from room: {user_room}")
        else:
            logger.debug("Anonymous client disconnected")

    @socketio.on('join')
    def on_join(data):
        """
        Handle client joining a room.
        
        Args:
            data (dict): Dictionary containing room name to join
        """
        if current_user.is_authenticated and 'room' in data:
            room = data['room']
            # Only allow joining rooms the user has permission to join
            if room == f"user_{current_user.id}" or room == "global":
                join_room(room)
                logger.debug(f"User {current_user.id} joined room: {room}")
    
    @socketio.on('leave')
    def on_leave(data):
        """
        Handle client leaving a room.
        
        Args:
            data (dict): Dictionary containing room name to leave
        """
        if 'room' in data:
            room = data['room']
            leave_room(room)
            logger.debug(f"User left room: {room}")

    logger.info("SocketIO event handlers registered")

def emit_task_update(task):
    """
    Emit a task update to the client.
    
    Args:
        task (Command): The task that was updated
    """
    if not _socketio:
        logger.warning("SocketIO not initialized, can't emit task update")
        return

    # Emit to the user's room
    user_room = f'user_{task.user_id}'
    _socketio.emit('task_update', task.to_dict(), room=user_room)

def emit_task_progress(task, message, progress=None, log_data=None):
    """
    Emit a task progress update to the client.
    
    Args:
        task (Command): The task being updated
        message (str): Progress message
        progress (int): Progress percentage (0-100)
        log_data (dict): Additional log data
    """
    if not _socketio:
        logger.warning("SocketIO not initialized, can't emit task progress")
        return

    # Create the progress data
    progress_data = {
        'command_uuid': task.command_uuid,
        'message': message,
        'timestamp': task.created_at.timestamp()
    }
    
    if progress is not None:
        progress_data['progress'] = progress
        
    if log_data:
        progress_data['log_data'] = log_data

    # Emit to the user's room
    user_room = f'user_{task.user_id}'
    _socketio.emit('task_progress', progress_data, room=user_room)

def emit_active_tasks():
    """
    Emit the currently active tasks for the authenticated user.
    This is called when a client connects to get the initial state.
    """
    if not _app or not _socketio or not current_user.is_authenticated:
        return
        
    with _app.app_context():
        from src.login.device_models import Command
        
        # Get active tasks for the user
        active_tasks = Command.query.filter_by(
            user_id=current_user.id,
            is_background=True
        ).filter(
            Command.status.in_(['pending', 'running'])
        ).all()
        
        # Emit the active tasks count and list
        user_room = f'user_{current_user.id}'
        _socketio.emit('active_tasks', {
            'count': len(active_tasks),
            'tasks': [task.to_dict() for task in active_tasks]
        }, room=user_room)

def emit_notification(user_id, title, message, type_='info'):
    """
    Emit a notification to a specific user.
    
    Args:
        user_id (int): ID of the user to notify
        title (str): Notification title
        message (str): Notification message
        type_ (str): Notification type ('info', 'success', 'warning', 'error')
    """
    if not _socketio:
        logger.warning("SocketIO not initialized, can't emit notification")
        return
        
    # Create the notification data
    notification_data = {
        'title': title,
        'message': message,
        'type': type_,
        'timestamp': int(__import__('time').time())
    }
    
    # Emit to the user's room
    user_room = f'user_{user_id}'
    _socketio.emit('notification', notification_data, room=user_room)
