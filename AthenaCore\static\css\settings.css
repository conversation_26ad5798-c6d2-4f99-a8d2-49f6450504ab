/* CSS stylesheet for the settings page */

/* ------------------------------------------------------------------
   Theme Variables (aligned with main.css)
   ------------------------------------------------------------------ */
   :root {
    /* Light theme – refined enterprise palette */
    --primary-color:         #3b4c64;
    --background-color:      #f4f4f5;
    --sidebar-color:         #e0e2e6;
    --card-bg:               #ffffff;
    --text-primary:          #111827;
    --text-secondary:        #6b7280;
    --border-color:          #d1d5db;
    --hover-color:           #64748b;
    --message-ai-bg:         #ffffff;
    --message-user-bg:       #3b4c64;
    --input-bg:              #ffffff;
    --code-bg:               #e0e2e6;
    --overlay-bg:            rgba(0, 0, 0, 0.4);
    --tooltip-bg:            rgba(0, 0, 0, 0.85);

    /* Scrollbar (light) */
    --scrollbar-track-color: #f3f4f6;
    --scrollbar-thumb-color: #64748b;
    --scrollbar-thumb-hover: #3b4c64;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);

    /* Status colors */
    --success-color:         #10b981;
    --warning-color:         #f59f1b;
    --danger-color:          #ef4444;
}

[data-theme="dark"] {
    /* Dark theme – matched to Athena logo’s deep blue */
    --primary-color:         #0E57D0;
    --background-color:      #0B0E13;
    --sidebar-color:         #10131A;
    --card-bg:               #131728;
    --text-primary:          #E0E0E0;
    --text-secondary:        #A0A0A5;
    --border-color:          #1A1E29;
    --hover-color:           #0E57D0;

    /* Slightly lighter bubble than the card */
    --message-ai-bg:         #1e2433;
    --message-user-bg:       #0E57D0;
    --input-bg:              #131728;
    --code-bg:               #181C25;
    --overlay-bg:            rgba(0, 0, 0, 0.6);
    --tooltip-bg:            rgba(0, 0, 0, 0.85);

    /* Scrollbar (dark) */
    --scrollbar-track-color: #131728;
    --scrollbar-thumb-color: #0E57D0;
    --scrollbar-thumb-hover: #0C4CC3;

    /* Shadows & status (same) */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --success-color: #10b981;
    --warning-color: #f59f1b;
    --danger-color:  #ef4444;
}

/* ------------------------------------------------------------------
   Reset & Base
   ------------------------------------------------------------------ */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    color: var(--text-primary);
    line-height: 1.5;
    background-color: var(--background-color);
    transition: background-color 0.3s, color 0.3s;
    min-height: 100vh;
    display: flex;
}

/* ------------------------------------------------------------------
   Settings Sidebar
   ------------------------------------------------------------------ */
.settings-sidebar {
    width: 280px;
    background-color: var(--sidebar-color);
    border-right: 1px solid var(--border-color);
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    z-index: 100;
}

.settings-logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 1rem;
    padding-bottom: 0.5rem;
    margin-bottom: 0.25rem;
}

.settings-logo-img {
    width: 80px;
    height: 80px;
    max-width: 80px;
    max-height: 80px;
    border-radius: 50%;
    box-shadow: 0 2px 12px rgba(0,0,0,0.13);
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 1rem;
    padding-left: 2.1rem;
    margin-top: 0.25rem;
    margin-bottom: 1.2rem;
    min-height: 56px;
}

.back-button {
    background: none;
    border: none;
    outline: none;
    cursor: pointer;
    padding: 0.15rem 0.4rem;
    margin-right: 0.1rem;
    display: flex;
    align-items: center;
    color: var(--text-secondary);
    transition: color 0.2s;
    height: 40px;
    width: 40px;
    justify-content: center;
}
.back-button svg {
    display: block;
    margin: auto;
}
.back-button:hover {
    color: var(--primary-color);
}

.settings-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.nav-menu {
    flex: 1 1 auto;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    overflow-y: auto;
    overflow-x: visible; /* for tooltips to show horizontally */
    min-height: 0; /* Allow flex item to shrink below content size */
    max-height: calc(100vh - 200px); /* Ensure space for header and footer */
}

/* Custom scrollbar for nav-menu */
.nav-menu::-webkit-scrollbar {
    width: 6px;
}

.nav-menu::-webkit-scrollbar-track {
    background: var(--scrollbar-track-color, #f3f4f6);
    border-radius: 3px;
}

.nav-menu::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb-color, #64748b);
    border-radius: 3px;
}

.nav-menu::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover, #3b4c64);
}

/* ------------------------------------------------------------------
   Navigation Items
   ------------------------------------------------------------------ */
.nav-item {
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border-radius: 8px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 4px;
}
.nav-item:hover {
    background-color: var(--hover-color);
    transform: translateX(2px);
}
.nav-item.active {
    background-color: var(--hover-color);
    color: var(--text-primary);
    border-left: 3px solid var(--primary-color);
    font-weight: 500;
}
.nav-item.active::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background-color: var(--primary-color);
    border-radius: 0 2px 2px 0;
}

.nav-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    color: inherit;
}

/* Tooltip for collapsed nav */
.nav-item:hover span::after {
    content: attr(data-tooltip);
    position: fixed;
    left: 280px;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 200;
    box-shadow: var(--shadow-sm);
}

/* ------------------------------------------------------------------
   Main Content Area
   ------------------------------------------------------------------ */
.main-content {
    margin-left: 280px;
    padding: 2rem;
    flex: 1;
    display: flex;
    justify-content: center;
    overflow-x: hidden;
    min-height: 100vh;
}

#content-container {
    width: 100%;
    max-width: 1200px;
    padding: 0 15px;
    animation: fadeIn 0.3s ease-out;
    transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
}
#content-container.loading {
    opacity: 0;
    transform: translateY(10px);
}

/* ------------------------------------------------------------------
   Breadcrumb Navigation
   ------------------------------------------------------------------ */
.settings-breadcrumb {
    display: flex;
    align-items: center;
    padding: 8px 15px;
    margin-bottom: 20px;
    background-color: var(--card-bg);
    border-radius: 6px;
    box-shadow: var(--shadow-sm);
}
.settings-breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
}
.settings-breadcrumb span {
    color: var(--text-secondary);
    margin: 0 10px;
}
.settings-breadcrumb .current {
    color: var(--text-primary);
    font-weight: 500;
}

/* ------------------------------------------------------------------
   Sidebar Footer (Logout)
   ------------------------------------------------------------------ */
.sidebar-footer {
    flex-shrink: 0;
    margin-top: auto;
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: center;
}
.logout-button {
    background-color: var(--primary-color);
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: opacity 0.2s;
}
.logout-button:hover {
    opacity: 0.9;
}

/* ------------------------------------------------------------------
   Status Messages
   ------------------------------------------------------------------ */
.status-message {
    padding: 10px;
    margin-top: 15px;
    border-radius: 4px;
}
.status-message.success {
    background-color: rgba(16,185,129,0.2);
    border: 1px solid #10b981;
    color: #10b981;
}
.status-message.error {
    background-color: rgba(239,68,68,0.2);
    border: 1px solid #ef4444;
    color: #ef4444;
}
.status-message.info {
    background-color: rgba(59,130,246,0.2);
    border: 1px solid #3b82f6;
    color: #3b82f6;
}
.status-message.hidden {
    display: none;
}

/* ------------------------------------------------------------------
   Emulation Banner (Admin → User)
   ------------------------------------------------------------------ */
.emulation-banner {
    background-color: #ff5722;
    color: #fff;
    font-weight: 500;
    text-align: center;
    padding: 10px 15px;
    position: fixed;
    top: 0; left: 0; right: 0;
    z-index: 300;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    display: flex;
    justify-content: center;
    align-items: center;
}
.stop-emulation-link {
    background-color: white;
    color: #ff5722;
    padding: 5px 15px;
    border-radius: 4px;
    font-weight: bold;
    text-decoration: none;
    margin-left: 15px;
    transition: all 0.2s ease;
}
.stop-emulation-link:hover {
    background-color: #f5f5f5;
    box-shadow: 0 0 8px rgba(255,255,255,0.8);
}
body.with-emulation-banner .settings-sidebar,
body.with-emulation-banner .main-content {
    margin-top: 40px;
}

/* ------------------------------------------------------------------
   Thinking Section (in-page progress)
   ------------------------------------------------------------------ */
.thinking-section {
    margin: 10px 0;
    padding: 10px;
    background-color: rgba(0,0,0,0.15);
    border-radius: 6px;
    border-left: 3px solid #fab387;
}
.thinking-label {
    font-weight: bold;
    color: #fab387;
    display: block;
    margin-bottom: 5px;
}
.thinking-content {
    color: var(--text-secondary);
    font-style: italic;
    white-space: pre-wrap;
}

/* ------------------------------------------------------------------
   Animations & Responsive
   ------------------------------------------------------------------ */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to   { opacity: 1; transform: translateY(0); }
}
@keyframes spin {
    to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .settings-sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }
    .main-content {
        margin-left: 0;
        padding: 1rem;
    }
    body {
        flex-direction: column;
    }
}
