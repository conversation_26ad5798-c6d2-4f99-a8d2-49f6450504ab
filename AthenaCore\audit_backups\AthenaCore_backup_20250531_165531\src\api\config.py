"""
Configuration management API endpoints.

This module provides API endpoints for managing application configuration
stored in the database. It supports listing, creating, updating, and deleting
configuration entries through a RESTful interface.
"""

from flask import Blueprint, request, current_app, g
from flask_login import login_required, current_user

from src.models import Configuration, db
from src.services import get_service, ConfigurationService
from src.utils.api_response import success_response, error_response
from src.utils.exceptions import ConfigurationError, ValidationError, ResourceNotFoundError
from src.utils.middleware import require_auth, require_role, api_route

config_api = Blueprint("config_api", __name__)

@config_api.route("/api/config", methods=["GET"])
@require_auth
@require_role(["admin"])
def get_configurations():
    """
    Get all configuration entries.
    
    Returns:
        API response with configuration entries
    """
    try:
        # Get the configuration service
        config_service = get_service(ConfigurationService)
        
        # Get all configurations
        configs = config_service.get_all_values(include_sensitive=True)
        
        return success_response(
            data={"configurations": configs},
            message="Configuration entries retrieved successfully"
        )
    except ConfigurationError as e:
        return error_response(str(e), status_code=500)

@config_api.route("/api/config/<string:key>", methods=["GET"])
@require_auth
def get_configuration(key):
    """
    Get a specific configuration entry.
    
    Args:
        key: Configuration key
        
    Returns:
        API response with the configuration entry
    """
    try:
        # Get the configuration service
        config_service = get_service(ConfigurationService)
        
        # Check if the current user is an admin
        is_admin = g.user.role == "admin"
        
        # Get the configuration
        value = config_service.get_value(key)
        if value is None:
            return error_response(
                message=f"Configuration '{key}' not found", 
                status_code=404
            )
        
        # Get the configuration object to check sensitivity
        config = Configuration.query.filter_by(key=key).first()
        
        # Check permission for sensitive configurations
        if config and config.is_sensitive and not is_admin:
            return error_response(
                message="You do not have permission to access this configuration", 
                status_code=403
            )
        
        return success_response(
            data={"configuration": config.to_dict(include_sensitive=is_admin)},
            message=f"Configuration '{key}' retrieved successfully"
        )
    except ConfigurationError as e:
        return error_response(str(e), status_code=500)

@config_api.route("/api/config", methods=["POST"])
@require_auth
@require_role(["admin"])
def create_configuration():
    """
    Create a new configuration entry.
    
    Returns:
        API response with the created configuration entry
    """
    try:
        # Get the configuration service
        config_service = get_service(ConfigurationService)
        
        # Get request data
        data = request.get_json()
        if not data:
            return error_response("Invalid request data", status_code=400)
        
        # Validate required fields
        required_fields = ["key", "value"]
        for field in required_fields:
            if field not in data:
                return error_response(f"Missing required field: {field}", status_code=400)
        
        # Check if the key already exists
        existing = Configuration.query.filter_by(key=data["key"]).first()
        if existing:
            return error_response("Configuration key already exists", status_code=409)
        
        # Create the configuration
        config = config_service.set_value(
            key=data["key"],
            value=data["value"],
            value_type=data.get("value_type"),
            description=data.get("description"),
            is_sensitive=data.get("is_sensitive", False)
        )
        
        return success_response(
            data={"configuration": config.to_dict(include_sensitive=True)},
            message="Configuration created successfully",
            status_code=201
        )
    except ValidationError as e:
        return error_response(str(e), status_code=400)
    except ConfigurationError as e:
        return error_response(str(e), status_code=500)

@config_api.route("/api/config/<string:key>", methods=["PUT"])
@require_auth
@require_role(["admin"])
def update_configuration(key):
    """
    Update an existing configuration entry.
    
    Args:
        key: Configuration key
        
    Returns:
        API response with the updated configuration entry
    """
    try:
        # Get the configuration service
        config_service = get_service(ConfigurationService)
        
        # Get request data
        data = request.get_json()
        if not data:
            return error_response("Invalid request data", status_code=400)
        
        # Check if the configuration exists
        config = Configuration.query.filter_by(key=key).first()
        if not config:
            return error_response(f"Configuration '{key}' not found", status_code=404)
        
        # Update the configuration
        config = config_service.set_value(
            key=key,
            value=data.get("value", config.value),
            value_type=data.get("value_type", config.value_type),
            description=data.get("description", config.description),
            is_sensitive=data.get("is_sensitive", config.is_sensitive)
        )
        
        return success_response(
            data={"configuration": config.to_dict(include_sensitive=True)},
            message=f"Configuration '{key}' updated successfully"
        )
    except ResourceNotFoundError as e:
        return error_response(str(e), status_code=404)
    except ValidationError as e:
        return error_response(str(e), status_code=400)
    except ConfigurationError as e:
        return error_response(str(e), status_code=500)

@config_api.route("/api/config/<string:key>", methods=["DELETE"])
@require_auth
@require_role(["admin"])
def delete_configuration(key):
    """
    Delete a configuration entry.
    
    Args:
        key: Configuration key
        
    Returns:
        API response indicating success
    """
    try:
        # Get the configuration service
        config_service = get_service(ConfigurationService)
        
        # Delete the configuration
        success = config_service.delete_value(key)
        if not success:
            return error_response(
                message=f"Configuration '{key}' not found or could not be deleted", 
                status_code=404
            )
        
        return success_response(
            message=f"Configuration '{key}' deleted successfully"
        )
    except ResourceNotFoundError as e:
        return error_response(str(e), status_code=404)
    except ConfigurationError as e:
        return error_response(str(e), status_code=500)

@config_api.route("/api/config/test-env", methods=["GET"])
@require_auth
@require_role(["admin"])
def test_environment_variables():
    """
    Test access to environment variables vs. database config.
    This endpoint is for testing during the transition period.
    
    Returns:
        API response with test results
    """
    try:
        # Get the configuration service
        config_service = get_service(ConfigurationService)
        
        # Test keys to check
        test_keys = [
            "FLASK_ENV", 
            "FLASK_DEBUG", 
            "APP_PORT", 
            "DATABASE_URI",
            "OPENAI_API_KEY",
            "SMITHERY_API_KEY"
        ]
        
        # Get values using both environment variables and config service
        test_results = {}
        for key in test_keys:
            # Get the raw environment variable (if available)
            import os
            env_value = os.getenv(key)
            if key.endswith("_KEY") and env_value:
                env_value = "[REDACTED]"  # Don't expose API keys
            
            # Get the value using the config service
            try:
                config_value = config_service.get_value(key)
                if key.endswith("_KEY") and config_value:
                    config_value = "[REDACTED]"  # Don't expose API keys
            except Exception:
                config_value = None
            
            # Get the database value (if available)
            db_config = Configuration.query.filter_by(key=key).first()
            db_value = db_config.value if db_config else None
            if key.endswith("_KEY") and db_value:
                db_value = "[REDACTED]"  # Don't expose API keys
            
            # Record the results
            test_results[key] = {
                "environment": env_value,
                "database": db_value,
                "config_service": config_value,
                "source": "database" if db_value is not None else "environment" if env_value is not None else "default"
            }
        
        return success_response(
            data={"test_results": test_results},
            message="Environment variables and database configuration tested successfully"
        )
    except ConfigurationError as e:
        return error_response(str(e), status_code=500)
