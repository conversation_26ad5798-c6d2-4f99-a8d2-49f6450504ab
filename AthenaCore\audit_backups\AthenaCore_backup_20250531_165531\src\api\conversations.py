"""
API endpoints for managing conversations and messages.
"""

import json
import logging
import uuid
from datetime import datetime
from flask import Blueprint, jsonify, request
from flask_login import current_user, login_required
from src.api.conversation_handler import handle_missing_conversation

from src.models import db, Conversation, Message

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger("conversations_api")

# Create blueprint
conversations_bp = Blueprint("conversations", __name__, url_prefix="/api/conversations")


@conversations_bp.route("", methods=["GET"])
@login_required
def list_conversations():
    """
    Get a list of the user's conversations.

    Query Parameters:
        limit (int): Maximum number of conversations to return (default: 20)
        offset (int): Offset for pagination (default: 0)
        active_only (bool): If true, only return active conversations (default: true)

    Returns:
        JSON response with list of conversations
    """
    limit = request.args.get("limit", 20, type=int)
    offset = request.args.get("offset", 0, type=int)
    active_only = request.args.get("active_only", "true").lower() == "true"

    query = Conversation.query.filter_by(user_id=current_user.id)

    if active_only:
        query = query.filter_by(is_active=True)

    total = query.count()

    conversations = (
        query.order_by(Conversation.updated_at.desc())
        .limit(limit)
        .offset(offset)
        .all()
    )

    return jsonify({
        "conversations": [conv.to_dict() for conv in conversations],
        "total": total,
        "limit": limit,
        "offset": offset
    })


@conversations_bp.route("", methods=["POST"])
@login_required
def create_conversation():
    """
    Create a new conversation.

    Request Body:
        title (str, optional): Title for the conversation
        initial_message (str, optional): First message to add to the conversation

    Returns:
        JSON response with the created conversation
    """
    data = request.json or {}
    title = data.get("title")
    initial_message = data.get("initial_message")

    # Create new conversation
    conversation = Conversation(
        user_id=current_user.id,
        title=title,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        is_active=True
    )

    db.session.add(conversation)
    db.session.flush()  # Get the ID without committing

    # Add initial message if provided
    if initial_message:
        message = Message(
            conversation_id=conversation.id,
            role="user",
            content=initial_message,
            timestamp=datetime.utcnow()
        )
        db.session.add(message)

    db.session.commit()

    logger.info(f"Created new conversation {conversation.id} for user {current_user.id}")

    return jsonify({
        "conversation": conversation.to_dict(),
        "message": "Conversation created successfully"
    }), 201


@conversations_bp.route("/<int:conversation_id>", methods=["GET"])
@login_required
def get_conversation_int(conversation_id):
    """
    Get a specific conversation with its messages.

    Path Parameters:
        conversation_id (int): ID of the conversation to retrieve

    Query Parameters:
        limit (int): Maximum number of messages to return (default: 100)
        offset (int): Offset for pagination (default: 0)

    Returns:
        JSON response with conversation details and messages
    """
    limit = request.args.get("limit", 100, type=int)
    offset = request.args.get("offset", 0, type=int)

    # Get conversation with ownership check
    conversation = Conversation.query.filter_by(
        id=conversation_id, user_id=current_user.id
    ).first_or_404()

    # Get messages with pagination
    messages = (
        Message.query.filter_by(conversation_id=conversation_id)
        .order_by(Message.timestamp)
        .limit(limit)
        .offset(offset)
        .all()
    )

    total_messages = Message.query.filter_by(conversation_id=conversation_id).count()

    return jsonify({
        "conversation": conversation.to_dict(),
        "messages": [msg.to_dict() for msg in messages],
        "total_messages": total_messages,
        "limit": limit,
        "offset": offset
    })


@conversations_bp.route("/<conversation_id>", methods=["GET"])
@login_required
def get_conversation_uuid(conversation_id):
    """
    Get a specific conversation with its messages.

    Path Parameters:
        conversation_id (str): ID of the conversation to retrieve

    Query Parameters:
        limit (int): Maximum number of messages to return (default: 100)
        offset (int): Offset for pagination (default: 0)

    Returns:
        JSON response with conversation details and messages
    """
    limit = request.args.get("limit", 100, type=int)
    offset = request.args.get("offset", 0, type=int)

    try:
        uuid.UUID(conversation_id)
    except ValueError:
        logger.warning(f"Invalid conversation ID format: {conversation_id}")
        return jsonify({
            "conversation": {
                "id": conversation_id,
                "title": "New conversation", 
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
                "user_id": current_user.id,
                "is_active": True
            },
            "messages": [],
            "total_messages": 0,
            "limit": limit,
            "offset": offset
        })

    # Get conversation with ownership check
    conversation = Conversation.query.filter_by(
        id=conversation_id, user_id=current_user.id
    ).first()
    
    # If conversation doesn't exist, return a default empty conversation instead of 404
    if not conversation:
        logger.info(f"Conversation {conversation_id} not found, returning empty conversation")
        return jsonify({
            "conversation": {
                "id": conversation_id,
                "title": "New conversation", 
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
                "user_id": current_user.id,
                "is_active": True
            },
            "messages": [],
            "total_messages": 0,
            "limit": limit,
            "offset": offset
        })

    # Get messages with pagination
    messages = (
        Message.query.filter_by(conversation_id=conversation_id)
        .order_by(Message.timestamp)
        .limit(limit)
        .offset(offset)
        .all()
    )

    total_messages = Message.query.filter_by(conversation_id=conversation_id).count()

    return jsonify({
        "conversation": conversation.to_dict(),
        "messages": [msg.to_dict() for msg in messages],
        "total_messages": total_messages,
        "limit": limit,
        "offset": offset
    })


@conversations_bp.route("/<int:conversation_id>", methods=["PATCH"])
@login_required
def update_conversation_int(conversation_id):
    """
    Update a conversation's metadata.

    Path Parameters:
        conversation_id (int): ID of the conversation to update

    Request Body:
        title (str, optional): New title for the conversation
        is_active (bool, optional): Whether the conversation is active

    Returns:
        JSON response with the updated conversation
    """
    data = request.json or {}

    # Get conversation with ownership check
    conversation = Conversation.query.filter_by(
        id=conversation_id, user_id=current_user.id
    ).first_or_404()

    # Update fields if provided
    if "title" in data:
        conversation.title = data["title"]

    if "is_active" in data:
        conversation.is_active = bool(data["is_active"])

    conversation.updated_at = datetime.utcnow()
    db.session.commit()

    logger.info(f"Updated conversation {conversation_id} for user {current_user.id}")

    return jsonify({
        "conversation": conversation.to_dict(),
        "message": "Conversation updated successfully"
    })


@conversations_bp.route("/<conversation_id>", methods=["PATCH"])
@login_required
def update_conversation_uuid(conversation_id):
    """
    Update a conversation's metadata.

    Path Parameters:
        conversation_id (str): ID of the conversation to update

    Request Body:
        title (str, optional): New title for the conversation
        is_active (bool, optional): Whether the conversation is active

    Returns:
        JSON response with the updated conversation
    """
    data = request.json or {}

    try:
        uuid.UUID(conversation_id)
    except ValueError:
        return jsonify({"error": "Invalid conversation ID"}), 400

    # Get conversation with ownership check
    conversation = Conversation.query.filter_by(
        id=conversation_id, user_id=current_user.id
    ).first_or_404()

    # Update fields if provided
    if "title" in data:
        conversation.title = data["title"]

    if "is_active" in data:
        conversation.is_active = bool(data["is_active"])

    conversation.updated_at = datetime.utcnow()
    db.session.commit()

    logger.info(f"Updated conversation {conversation_id} for user {current_user.id}")

    return jsonify({
        "conversation": conversation.to_dict(),
        "message": "Conversation updated successfully"
    })


@conversations_bp.route("/<int:conversation_id>", methods=["DELETE"])
@login_required
def delete_conversation_int(conversation_id):
    """
    Delete a conversation (soft delete by default).

    Path Parameters:
        conversation_id (int): ID of the conversation to delete

    Query Parameters:
        permanent (bool): If true, permanently delete the conversation (default: false)

    Returns:
        JSON response confirming deletion
    """
    permanent = request.args.get("permanent", "false").lower() == "true"

    # Get conversation with ownership check
    conversation = Conversation.query.filter_by(
        id=conversation_id, user_id=current_user.id
    ).first_or_404()

    if permanent:
        # Permanently delete
        db.session.delete(conversation)
        message = "Conversation permanently deleted"
    else:
        # Soft delete
        conversation.is_active = False
        conversation.updated_at = datetime.utcnow()
        message = "Conversation archived"

    db.session.commit()

    logger.info(f"Deleted conversation {conversation_id} for user {current_user.id} (permanent={permanent})")

    return jsonify({
        "message": message,
        "conversation_id": conversation_id
    })


@conversations_bp.route("/<conversation_id>", methods=["DELETE"])
@login_required
def delete_conversation_uuid(conversation_id):
    """
    Delete a conversation (soft delete by default).

    Path Parameters:
        conversation_id (str): ID of the conversation to delete

    Query Parameters:
        permanent (bool): If true, permanently delete the conversation (default: false)

    Returns:
        JSON response confirming deletion
    """
    permanent = request.args.get("permanent", "false").lower() == "true"

    try:
        uuid.UUID(conversation_id)
    except ValueError:
        return jsonify({"error": "Invalid conversation ID"}), 400

    # Get conversation with ownership check
    conversation = Conversation.query.filter_by(
        id=conversation_id, user_id=current_user.id
    ).first_or_404()

    if permanent:
        # Permanently delete
        db.session.delete(conversation)
        message = "Conversation permanently deleted"
    else:
        # Soft delete
        conversation.is_active = False
        conversation.updated_at = datetime.utcnow()
        message = "Conversation archived"

    db.session.commit()

    logger.info(f"Deleted conversation {conversation_id} for user {current_user.id} (permanent={permanent})")

    return jsonify({
        "message": message,
        "conversation_id": conversation_id
    })


@conversations_bp.route("/<int:conversation_id>/messages", methods=["POST"])
@login_required
def add_message(conversation_id):
    """
    Add a new message to a conversation.

    Path Parameters:
        conversation_id (int): ID of the conversation to add the message to

    Request Body:
        role (str): Role of the message sender ('user', 'assistant', 'system')
        content (str): Content of the message
        metadata (dict, optional): Additional metadata for the message

    Returns:
        JSON response with the created message
    """
    data = request.json or {}

    # Validate required fields
    if "role" not in data or "content" not in data:
        return jsonify({
            "error": "Missing required fields: role, content"
        }), 400

    # Get conversation with ownership check
    conversation = Conversation.query.filter_by(
        id=conversation_id, user_id=current_user.id
    ).first_or_404()

    # Create new message
    message = Message(
        conversation_id=conversation_id,
        role=data["role"],
        content=data["content"],
        timestamp=datetime.utcnow()
    )

    # Add metadata if provided
    if "metadata" in data and data["metadata"]:
        message.message_metadata = json.dumps(data["metadata"])

    # Update conversation timestamp
    conversation.updated_at = datetime.utcnow()

    db.session.add(message)
    db.session.commit()

    logger.info(f"Added message to conversation {conversation_id} for user {current_user.id}")

    return jsonify({
        "message": message.to_dict(),
        "conversation": conversation.to_dict()
    }), 201


@conversations_bp.route("/<conversation_id>/messages", methods=["POST"])
@login_required
def add_message_uuid(conversation_id):
    """
    Add a new message to a conversation.

    Path Parameters:
        conversation_id (str): ID of the conversation to add the message to

    Request Body:
        role (str): Role of the message sender ('user', 'assistant', 'system')
        content (str): Content of the message
        metadata (dict, optional): Additional metadata for the message

    Returns:
        JSON response with the created message
    """
    data = request.json or {}

    # Validate required fields
    if "role" not in data or "content" not in data:
        return jsonify({
            "error": "Missing required fields: role, content"
        }), 400

    try:
        uuid.UUID(conversation_id)
    except ValueError:
        return jsonify({"error": "Invalid conversation ID"}), 400

    # Get conversation with ownership check
    conversation = Conversation.query.filter_by(
        id=conversation_id, user_id=current_user.id
    ).first_or_404()

    # Create new message
    message = Message(
        conversation_id=conversation_id,
        role=data["role"],
        content=data["content"],
        timestamp=datetime.utcnow()
    )

    # Add metadata if provided
    if "metadata" in data and data["metadata"]:
        message.message_metadata = json.dumps(data["metadata"])

    # Update conversation timestamp
    conversation.updated_at = datetime.utcnow()

    db.session.add(message)
    db.session.commit()

    logger.info(f"Added message to conversation {conversation_id} for user {current_user.id}")

    return jsonify({
        "message": message.to_dict(),
        "conversation": conversation.to_dict()
    }), 201


@conversations_bp.route("/<int:conversation_id>/messages/<int:message_id>", methods=["DELETE"])
@login_required
def delete_message(conversation_id, message_id):
    """
    Delete a message from a conversation.

    Path Parameters:
        conversation_id (int): ID of the conversation
        message_id (int): ID of the message to delete

    Returns:
        JSON response confirming deletion
    """
    # Get conversation with ownership check
    conversation = Conversation.query.filter_by(
        id=conversation_id, user_id=current_user.id
    ).first_or_404()

    # Get message with conversation check
    message = Message.query.filter_by(
        id=message_id, conversation_id=conversation_id
    ).first_or_404()

    db.session.delete(message)

    # Update conversation timestamp
    conversation.updated_at = datetime.utcnow()

    db.session.commit()

    logger.info(f"Deleted message {message_id} from conversation {conversation_id} for user {current_user.id}")

    return jsonify({
        "message": "Message deleted successfully",
        "message_id": message_id,
        "conversation_id": conversation_id
    })
