# src/services/__init__.py

"""
Services package for Athena Cross-Device API System.

This package contains services that run in the background to support
autonomous operations, task execution, scheduled task processing,
and other core functionality of the Cross-Device API System.
"""

from .task_executor import task_executor_service, init_app as init_task_executor
from .task_scheduler import task_scheduler_service, init_app as init_task_scheduler
from .socket import init_app as init_socket_service

def init_app(app):
    """
    Initialize all services with the Flask application.
    
    Args:
        app (Flask): The Flask application
    """
    # Initialize task executor service
    init_task_executor(app)
    
    # Initialize task scheduler service
    init_task_scheduler(app)
    
    # Initialize socket service (without socketio object)
    init_socket_service(app)
    
    # Log initialization
    app.logger.info("Athena services initialized")


def init_socketio(socketio):
    """
    Initialize SocketIO service with the SocketIO application.
    This is called after the app and services initialization.
    
    Args:
        socketio (SocketIO): The SocketIO application
    """
    from .socket import setup_socketio
    setup_socketio(socketio)
