"""
Compatibility module for login models.

This module provides backward compatibility for code that imports models from
the original location in src/login/models.py. As part of the refactoring process,
these models are being migrated to src/models/.

This compatibility layer will be maintained throughout the transition period to
ensure existing code continues to work while encouraging migration to the new imports.
"""

import warnings
import sys
import logging
import inspect

# Set up logging
logger = logging.getLogger('athena.deprecation')

# Issue a warning about the deprecated location
warnings.warn(
    "Importing models from src.login.models is deprecated. "
    "Please update your imports to use src.models instead.",
    category=FutureWarning,
    stacklevel=2
)

# Import database instance
from src.models import db

# Import User models
from src.models.user import User, Role, UserRole, UserLog

# Import API models
from src.models.api import APIKey

# Import Config models
from src.models.configuration import ConfigEntry

# Import LLM models
from src.models.llm import LLMProviderSetting

# Import Command models
from src.models.command import CommandToggle

# Import Connection models
from src.models.connection import DirectConnection, MCPApiKey, MCPServerTemplate

# Import Conversation models
from src.models.conversation import Conversation, Message

# Import Logging models
from src.models.logging import LogEntry

# Log usage of this compatibility layer
frame = sys._getframe(1)
caller_module = frame.f_globals.get('__name__', 'unknown')
caller_function = frame.f_code.co_name
logger.info(f"Deprecated model imports used by {caller_module}.{caller_function}")

# Add __all__ to explicitly define what's exported
__all__ = [
    'db',
    'User', 'Role', 'UserRole', 'UserLog',
    'APIKey',
    'ConfigEntry',
    'LLMProviderSetting',
    'CommandToggle',
    'DirectConnection', 'MCPApiKey', 'MCPServerTemplate',
    'Conversation', 'Message',
    'LogEntry'
]
