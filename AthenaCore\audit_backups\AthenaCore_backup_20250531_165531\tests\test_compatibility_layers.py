"""
Test script to verify that compatibility layers are working correctly.
This helps ensure we can safely proceed with deprecated file removal.
"""

import os
import sys
import inspect
import logging
from datetime import datetime

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("CompatibilityTest")

def log_header(message):
    """Print a header message to the log."""
    logger.info("=" * 80)
    logger.info(message)
    logger.info("=" * 80)

def test_login_views_compat():
    """Test the compatibility layer for src.login.views."""
    log_header("Testing src.login.views compatibility")
    
    try:
        # Import from deprecated location
        from src.login.views import login, logout, register, admin_panel, admin_required
        
        # Verify the imports worked
        logger.info(f"Successfully imported login from {inspect.getmodule(login).__name__}")
        logger.info(f"Successfully imported logout from {inspect.getmodule(logout).__name__}")
        logger.info(f"Successfully imported register from {inspect.getmodule(register).__name__}")
        logger.info(f"Successfully imported admin_panel from {inspect.getmodule(admin_panel).__name__}")
        logger.info(f"Successfully imported admin_required from {inspect.getmodule(admin_required).__name__}")
        
        # Also check the location the compatibility layer is forwarding to
        logger.info(f"login function's real location: {login.__module__}.{login.__name__}")
        
        return True
    except Exception as e:
        logger.error(f"Error testing views compatibility: {str(e)}")
        return False

def test_login_models_compat():
    """Test the compatibility layer for src.login.models."""
    log_header("Testing src.login.models compatibility")
    
    try:
        # Import from deprecated location
        from src.login.models import User, Role, UserRole, APIKey, ConfigEntry
        
        # Verify the imports worked
        logger.info(f"Successfully imported User from {inspect.getmodule(User).__name__}")
        logger.info(f"Successfully imported Role from {inspect.getmodule(Role).__name__}")
        logger.info(f"Successfully imported UserRole from {inspect.getmodule(UserRole).__name__}")
        logger.info(f"Successfully imported APIKey from {inspect.getmodule(APIKey).__name__}")
        logger.info(f"Successfully imported ConfigEntry from {inspect.getmodule(ConfigEntry).__name__}")
        
        # Also check the location the compatibility layer is forwarding to
        logger.info(f"User model's real location: {User.__module__}")
        
        return True
    except Exception as e:
        logger.error(f"Error testing models compatibility: {str(e)}")
        return False

def check_import_warnings():
    """
    Check if using deprecated imports produces appropriate warnings.
    We've set up the deprecated modules to log warnings when imported directly.
    """
    log_header("Checking deprecation warnings")
    
    try:
        # These imports should trigger deprecation warnings
        import src.login.views
        import src.login.models
        
        logger.info("Successfully imported deprecated modules directly")
        return True
    except Exception as e:
        logger.error(f"Error importing deprecated modules: {str(e)}")
        return False

def test_migration_script():
    """Test that the migration script with updated imports works correctly."""
    log_header("Testing migration script with updated imports")
    
    try:
        # Import the migration script directly
        from migrations.add_configurations_table import run_migration
        
        logger.info("Successfully imported migration script")
        
        # We don't actually run the migration here, just verify it can be imported
        return True
    except Exception as e:
        logger.error(f"Error importing migration script: {str(e)}")
        return False

def run_all_tests():
    """Run all compatibility tests and report results."""
    log_header(f"Starting compatibility tests at {datetime.now().isoformat()}")
    
    tests = [
        ("Views Compatibility", test_login_views_compat),
        ("Models Compatibility", test_login_models_compat),
        ("Import Warnings", check_import_warnings),
        ("Migration Script", test_migration_script)
    ]
    
    results = {}
    all_passed = True
    
    for test_name, test_func in tests:
        logger.info(f"Running test: {test_name}")
        result = test_func()
        results[test_name] = result
        if not result:
            all_passed = False
    
    # Print summary
    log_header("Test Results Summary")
    for test_name, result in results.items():
        logger.info(f"{test_name}: {'PASSED' if result else 'FAILED'}")
    
    logger.info(f"Overall Result: {'PASSED' if all_passed else 'FAILED'}")
    
    return all_passed

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
