# Athena Developer Setup Guide

## Getting Started with MCP Integration

This document provides instructions for setting up the Athena project with MCP integration.

## Initial Setup

1. **<PERSON><PERSON> & Pull Repository**
   ```bash
   git clone https://github.com/RealShocky/AthenaAgent_Core.git
   # OR if you already have the repo
   git pull
   ```

2. **Install Dependencies**
   ```bash
   # Setup AthenaCore dependencies
   cd AthenaCore
   pip install -r requirements.txt
   
   # Setup AthenaAgent dependencies
   cd ../AthenaAgent
   pip install -r requirements.txt
   ```

3. **Environment Configuration**
   - Copy the `.env.example` file to `.env` in the AthenaCore directory
   - Update with your API keys (important for MCP functionality)
   - At minimum, set the `SMITHERY_API_KEY` for MCP server discovery

4. **Database Migration**
   ```bash
   cd AthenaCore
   # Run the migration script to add MCP API keys table
   python migrations/add_mcp_api_keys.py
   ```

## MCP Configuration

### Required API Keys

To fully utilize the MCP integration, you'll need:

- **Smithery API Key**: Register at https://smithery.dev to obtain a key
- **OpenAI API Key**: For core functionality
- Other model provider keys as needed (Anthropic, Google, etc.)

### Testing MCP Connectivity

After setting up:

1. Start AthenaCore:
   ```bash
   cd AthenaCore
   python main.py
   ```

2. Start AthenaAgent:
   ```bash
   cd AthenaAgent
   python src/main.py
   ```

3. In the Athena GUI, click on Settings → MCP Settings to open the MCP configuration dialog

4. The Server Browser tab will show available MCP servers (requires Smithery API key)

5. Click on the status indicator in the top-right to manually refresh connection status

## Database Notes

- The migration script adds a new `mcp_api_keys` table for user-specific MCP API keys
- Existing database data is preserved (no need to delete your database)
- User preferences and settings will be maintained

## Troubleshooting

### Status Always Shows "Refreshing..."
- Check that AthenaCore is running and accessible
- Verify your API keys are set correctly in `.env`
- Try restarting both AthenaCore and AthenaAgent

### MCP Servers Not Showing
- Verify your Smithery API key is valid
- Check internet connectivity
- Look for error messages in the AthenaCore logs

### Database Migration Issues
- If the migration script fails, you can manually add the table using SQLite:
  ```sql
  CREATE TABLE mcp_api_keys (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    service_id TEXT,
    api_key TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(user_id) REFERENCES users(id)
  );
  ```
