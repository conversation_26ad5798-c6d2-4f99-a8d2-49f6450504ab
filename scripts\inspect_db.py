import sqlite3
import os

def main():
    db_path = os.path.join('<PERSON><PERSON>ore', 'athena.db')
    print(f"Checking database at {os.path.abspath(db_path)}")
    
    if not os.path.exists(db_path):
        print(f"Database file not found at {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # List all tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    
    print("\nTables in the database:")
    for table in tables:
        print(f"- {table[0]}")
        
        # List columns for each table
        try:
            cursor.execute(f"PRAGMA table_info({table[0]})")
            columns = cursor.fetchall()
            print(f"  Columns in {table[0]}:")
            for col in columns:
                print(f"    - {col[1]} ({col[2]})")
                
            # If this looks like a settings table, print a sample row
            if 'setting' in table[0].lower() or 'config' in table[0].lower() or 'key' in ' '.join([col[1] for col in columns]):
                try:
                    cursor.execute(f"SELECT * FROM {table[0]} LIMIT 1")
                    sample = cursor.fetchone()
                    if sample:
                        print(f"  Sample row: {sample}")
                except Exception as e:
                    print(f"  Error getting sample: {e}")
        except Exception as e:
            print(f"  Error getting columns: {e}")
    
    conn.close()

if __name__ == "__main__":
    main()
