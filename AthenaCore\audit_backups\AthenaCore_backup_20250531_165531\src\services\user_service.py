"""
User service module.

This module provides services for user management, authentication, and authorization.
It follows the service layer pattern established in the refactoring plan.
"""

import logging
from typing import Optional, List, Dict, Any
from flask_login import current_user
from datetime import datetime, timedelta
import uuid

from src.models import User, <PERSON><PERSON>ey
from src.models import db
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from src.models.user import User  # This ensures proper type checking
from .base_service import BaseService

# Configure logging
logger = logging.getLogger(__name__)

class UserService(BaseService):
    """Service for user-related operations."""
    
    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """
        Get a user by ID.
        
        Args:
            user_id: User ID to look up
            
        Returns:
            User object if found, None otherwise
        """
        return User.query.get(user_id)
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """
        Get a user by username.
        
        Args:
            username: Username to look up
            
        Returns:
            User object if found, None otherwise
        """
        return User.query.filter_by(username=username).first()
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """
        Get a user by email.
        
        Args:
            email: Email to look up
            
        Returns:
            User object if found, None otherwise
        """
        return User.query.filter_by(email=email).first()
    
    def create_user(self, username: str, email: str, password: str, role: str = "user") -> User:
        """
        Create a new user.
        
        Args:
            username: Username for the new user
            email: Email for the new user
            password: Password for the new user
            role: Role for the new user (default: "user")
            
        Returns:
            Newly created User object
            
        Raises:
            ValueError: If a user with the username or email already exists
        """
        # Check if username already exists
        if self.get_user_by_username(username):
            raise ValueError(f"Username '{username}' already exists")
        
        # Check if email already exists
        if self.get_user_by_email(email):
            raise ValueError(f"Email '{email}' already exists")
        
        # Create the user
        user = User(username=username, email=email, role=role)
        user.set_password(password)
        
        # Save to database
        db.session.add(user)
        db.session.commit()
        
        logger.info(f"Created new user: {username} (ID: {user.id})")
        return user
    
    def update_user(self, user_id: int, **kwargs) -> Optional[User]:
        """
        Update a user's attributes.
        
        Args:
            user_id: ID of the user to update
            **kwargs: Attributes to update
            
        Returns:
            Updated User object if found, None otherwise
        """
        user = self.get_user_by_id(user_id)
        if not user:
            return None
        
        # Handle special case for password
        if 'password' in kwargs:
            user.set_password(kwargs.pop('password'))
        
        # Update other attributes
        for key, value in kwargs.items():
            if hasattr(user, key):
                setattr(user, key, value)
        
        # Save changes
        db.session.commit()
        logger.info(f"Updated user: {user.username} (ID: {user.id})")
        
        return user
    
    def delete_user(self, user_id: int) -> bool:
        """
        Delete a user.
        
        Args:
            user_id: ID of the user to delete
            
        Returns:
            True if the user was deleted, False otherwise
        """
        user = self.get_user_by_id(user_id)
        if not user:
            return False
        
        # Delete the user
        db.session.delete(user)
        db.session.commit()
        
        logger.info(f"Deleted user: {user.username} (ID: {user.id})")
        return True
    
    def authenticate(self, username_or_email: str, password: str) -> Optional[User]:
        """
        Authenticate a user with username/email and password.
        
        Args:
            username_or_email: Username or email to authenticate
            password: Password to authenticate
            
        Returns:
            User object if authentication successful, None otherwise
        """
        # Try to find the user by username or email
        user = self.get_user_by_username(username_or_email)
        if not user:
            # Try by email if not found by username
            user = self.get_user_by_email(username_or_email)
        
        # Check if user exists and password is correct
        if user and user.check_password(password):
            logger.info(f"User authenticated: {user.username} (ID: {user.id})")
            return user
        
        logger.warning(f"Authentication failed for: {username_or_email}")
        return None
    
    def is_admin(self, user: Optional[User] = None) -> bool:
        """
        Check if the user is an admin.
        
        Args:
            user: User to check (defaults to current_user)
            
        Returns:
            True if the user is an admin, False otherwise
        """
        if user is None:
            user = current_user
        
        # Anonymous users are not admins
        if not hasattr(user, 'role'):
            return False
        
        return user.role == "admin"
    
    def get_all_users(self, page: int = 1, per_page: int = 20) -> Dict[str, Any]:
        """
        Get all users with pagination.
        
        Args:
            page: Page number (1-indexed)
            per_page: Number of users per page
            
        Returns:
            Dictionary with users and pagination information
        """
        # Get paginated users
        pagination = User.query.paginate(page=page, per_page=per_page, error_out=False)
        
        # Return users and pagination info
        return {
            "users": pagination.items,
            "total": pagination.total,
            "pages": pagination.pages,
            "page": page,
            "per_page": per_page,
            "has_next": pagination.has_next,
            "has_prev": pagination.has_prev
        }
    
    def create_api_key(self, user_id: int, name: Optional[str] = None) -> Optional[APIKey]:
        """
        Create a new API key for a user.
        
        Args:
            user_id: ID of the user to create an API key for
            name: Optional name for the API key
            
        Returns:
            Newly created APIKey object if successful, None otherwise
        """
        user = self.get_user_by_id(user_id)
        if not user:
            return None
        
        # Generate a unique API key
        api_key_id = str(uuid.uuid4())
        
        # Create the API key
        api_key = APIKey(
            id=api_key_id,
            user_id=user.id,
            name=name or f"API Key {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        )
        
        # Save to database
        db.session.add(api_key)
        db.session.commit()
        
        logger.info(f"Created API key for user: {user.username} (ID: {user.id})")
        return api_key
    
    def get_api_keys(self, user_id: int) -> List[APIKey]:
        """
        Get all API keys for a user.
        
        Args:
            user_id: ID of the user to get API keys for
            
        Returns:
            List of APIKey objects
        """
        return APIKey.query.filter_by(user_id=user_id).all()
    
    def validate_api_key(self, api_key_id: str) -> Optional[User]:
        """
        Validate an API key and return the associated user.
        
        Args:
            api_key_id: API key to validate
            
        Returns:
            User associated with the API key if valid, None otherwise
        """
        api_key = APIKey.query.get(api_key_id)
        if not api_key:
            return None
        
        # Update last used timestamp
        api_key.last_used = datetime.now()
        db.session.commit()
        
        # Return the associated user
        return api_key.user
    
    def delete_api_key(self, api_key_id: str) -> bool:
        """
        Delete an API key.
        
        Args:
            api_key_id: ID of the API key to delete
            
        Returns:
            True if the API key was deleted, False otherwise
        """
        api_key = APIKey.query.get(api_key_id)
        if not api_key:
            return False
        
        # Delete the API key
        db.session.delete(api_key)
        db.session.commit()
        
        logger.info(f"Deleted API key: {api_key_id}")
        return True
        
    def verify_password(self, user: User, password: str) -> bool:
        """
        Verify a user's password.
        
        Args:
            user: User object to verify password for
            password: Password to verify
            
        Returns:
            True if the password is correct, False otherwise
        """
        if not user or not password:
            return False
            
        return user.check_password(password)
