"""
Main application entry point.

This module serves as the main entry point for the Athena Core application.
It creates the application instance and runs the web server.
"""

import os
from src.app_factory import create_app, socketio
from src.config.service import config_service

# Create the application instance
app = create_app()

# Add context processors
@app.context_processor
def utility_processor():
    """
    Add utility functions and variables to the template context.
    
    Returns:
        Dictionary of utility functions and variables
    """
    return {
        'app_name': config_service.get('APP_NAME', 'Athena'),
        'app_version': config_service.get('APP_VERSION', '1.0.0'),
    }

def main():
    """
    Run the application.
    """
    host = config_service.get('APP_HOST', '0.0.0.0')
    port = config_service.get('APP_PORT', 5000, 'int')
    debug = config_service.get('FLASK_DEBUG', False, 'bool')
    
    # Print startup information
    print(f"Starting {config_service.get('APP_NAME', 'Athena')} v{config_service.get('APP_VERSION', '1.0.0')}")
    print(f"Server running at http://{host}:{port}/")
    print(f"Debug mode: {'enabled' if debug else 'disabled'}")
    
    # Run the application with SocketIO
    socketio.run(app, host=host, port=port, debug=debug)

if __name__ == "__main__":
    main()
