{"auditcodebase": "auditcodebase.py", "backups.login.models": "backups\\login\\models.py", "backups.login.views": "backups\\login\\views.py", "main": "main.py", "migrations.add_config_entries": "migrations\\add_config_entries.py", "migrations.add_configurations_table": "migrations\\add_configurations_table.py", "migrations.v1_add_configurations_table": "migrations\\v1_add_configurations_table.py", "migrations.v2_add_tasks_table": "migrations\\v2_add_tasks_table.py", "migrations.v3_add_authentication_tables": "migrations\\v3_add_authentication_tables.py", "migrations.v4_model_refactoring": "migrations\\v4_model_refactoring.py", "scripts.codebase_analyzer": "scripts\\codebase_analyzer.py", "scripts.identify_obsolete_files": "scripts\\identify_obsolete_files.py", "scripts.manage_migrations": "scripts\\manage_migrations.py", "scripts.migrate_imports": "scripts\\migrate_imports.py", "scripts.prepare_for_removal": "scripts\\prepare_for_removal.py", "scripts.run_migrations": "scripts\\run_migrations.py", "scripts.track_refactoring_progress": "scripts\\track_refactoring_progress.py", "scripts.update_imports": "scripts\\update_imports.py", "scripts.verify_dependencies": "scripts\\verify_dependencies.py", "src.admin": "src\\admin\\__init__.py", "src.admin.debug_routes": "src\\admin\\debug_routes.py", "src.api": "src\\api\\__init__.py", "src.api.api_v1": "src\\api\\api_v1.py", "src.api.attachments": "src\\api\\attachments.py", "src.api.commands": "src\\api\\commands.py", "src.api.config": "src\\api\\config.py", "src.api.conversation_compat": "src\\api\\conversation_compat.py", "src.api.conversation_handler": "src\\api\\conversation_handler.py", "src.api.conversations": "src\\api\\conversations.py", "src.api.demo": "src\\api\\demo.py", "src.api.device_auth": "src\\api\\device_auth.py", "src.api.devices": "src\\api\\devices.py", "src.api.error_handlers": "src\\api\\error_handlers.py", "src.api.routes": "src\\api\\routes.py", "src.api.search": "src\\api\\search.py", "src.api.specific_conversation": "src\\api\\specific_conversation.py", "src.api.system_health": "src\\api\\system_health.py", "src.api.tasks": "src\\api\\tasks.py", "src.app_factory": "src\\app_factory.py", "src.config": "src\\config\\__init__.py", "src.config.service": "src\\config\\service.py", "src.controllers": "src\\controllers\\__init__.py", "src.controllers.admin_controller": "src\\controllers\\admin_controller.py", "src.controllers.api_controller": "src\\controllers\\api_controller.py", "src.controllers.auth_controller": "src\\controllers\\auth_controller.py", "src.controllers.config_controller": "src\\controllers\\config_controller.py", "src.controllers.main_controller": "src\\controllers\\main_controller.py", "src.controllers.task_controller": "src\\controllers\\task_controller.py", "src.core": "src\\core\\__init__.py", "src.core.athena": "src\\core\\athena.py", "src.core.command_processor": "src\\core\\command_processor.py", "src.core.commands": "src\\core\\commands\\__init__.py", "src.core.commands.base_command": "src\\core\\commands\\base_command.py", "src.core.commands.kb": "src\\core\\commands\\kb\\__init__.py", "src.core.commands.kb.kb_control": "src\\core\\commands\\kb\\kb_control.py", "src.core.commands.obsidian": "src\\core\\commands\\obsidian\\__init__.py", "src.core.commands.obsidian.obsidian_control": "src\\core\\commands\\obsidian\\obsidian_control.py", "src.core.commands.spotify": "src\\core\\commands\\spotify\\__init__.py", "src.core.commands.spotify.spotify_control": "src\\core\\commands\\spotify\\spotify_control.py", "src.core.commands.system": "src\\core\\commands\\system\\__init__.py", "src.core.commands.system.system_control": "src\\core\\commands\\system\\system_control.py", "src.core.debug_manager": "src\\core\\debug_manager.py", "src.core.document_processor": "src\\core\\document_processor.py", "src.core.fallback_handler": "src\\core\\fallback_handler.py", "src.core.kb_catalog": "src\\core\\kb_catalog.py", "src.core.kb_context": "src\\core\\kb_context.py", "src.core.kb_initializer": "src\\core\\kb_initializer.py", "src.core.kb_search": "src\\core\\kb_search.py", "src.core.knowledge_db": "src\\core\\knowledge_db.py", "src.core.logger": "src\\core\\logger.py", "src.core.state_manager": "src\\core\\state_manager.py", "src.core.vector_db": "src\\core\\vector_db.py", "src.db": "src\\db\\__init__.py", "src.db.default_config": "src\\db\\default_config.py", "src.db.fix_openai_url": "src\\db\\fix_openai_url.py", "src.db.migrations": "src\\db\\migrations.py", "src.login": "src\\login\\__init__.py", "src.login.compat": "src\\login\\compat.py", "src.login.device_models": "src\\login\\device_models.py", "src.login.device_models_compat": "src\\login\\device_models_compat.py", "src.login.extensions": "src\\login\\extensions.py", "src.login.models": "src\\login\\models.py", "src.login.models_compat": "src\\login\\models_compat.py", "src.login.views": "src\\login\\views.py", "src.login.views_compat": "src\\login\\views_compat.py", "src.main": "src\\main.py", "src.mcp": "src\\mcp\\__init__.py", "src.mcp.api": "src\\mcp\\api.py", "src.mcp.api_endpoints": "src\\mcp\\api_endpoints.py", "src.mcp.athena_mcp": "src\\mcp\\athena_mcp.py", "src.mcp.connection": "src\\mcp\\connection.py", "src.mcp.connection_manager": "src\\mcp\\connection_manager.py", "src.mcp.deployment": "src\\mcp\\deployment.py", "src.mcp.server_creation": "src\\mcp\\server_creation.py", "src.mcp.smithery_client": "src\\mcp\\smithery_client.py", "src.mcp.template_manager": "src\\mcp\\template_manager.py", "src.models": "src\\models\\__init__.py", "src.models.api": "src\\models\\api.py", "src.models.api_key": "src\\models\\api_key.py", "src.models.command": "src\\models\\command.py", "src.models.command_toggle": "src\\models\\command_toggle.py", "src.models.configuration": "src\\models\\configuration.py", "src.models.conversation": "src\\models\\conversation.py", "src.models.device": "src\\models\\device.py", "src.models.direct_connection": "src\\models\\direct_connection.py", "src.models.llm": "src\\models\\llm.py", "src.models.llm_provider_setting": "src\\models\\llm_provider_setting.py", "src.models.log_entry": "src\\models\\log_entry.py", "src.models.logging": "src\\models\\logging.py", "src.models.mcp_api_key": "src\\models\\mcp_api_key.py", "src.models.mcp_server_template": "src\\models\\mcp_server_template.py", "src.models.message": "src\\models\\message.py", "src.models.task": "src\\models\\task.py", "src.models.user": "src\\models\\user.py", "src.models.user_log": "src\\models\\user_log.py", "src.routes.knowledge_routes": "src\\routes\\knowledge_routes.py", "src.services": "src\\services\\__init__.py", "src.services.auth_service": "src\\services\\auth_service.py", "src.services.base_service": "src\\services\\base_service.py", "src.services.config_service": "src\\services\\config_service.py", "src.services.socket": "src\\services\\socket.py", "src.services.task_executor": "src\\services\\task_executor.py", "src.services.task_scheduler": "src\\services\\task_scheduler.py", "src.services.task_service": "src\\services\\task_service.py", "src.services.user_service": "src\\services\\user_service.py", "src.utils": "src\\utils\\__init__.py", "src.utils.api_response": "src\\utils\\api_response.py", "src.utils.auth_extensions": "src\\utils\\auth_extensions.py", "src.utils.config": "src\\utils\\config.py", "src.utils.document_processor": "src\\utils\\document_processor.py", "src.utils.error_handlers": "src\\utils\\error_handlers.py", "src.utils.exceptions": "src\\utils\\exceptions.py", "src.utils.helpers": "src\\utils\\helpers.py", "src.utils.middleware": "src\\utils\\middleware.py", "src.utils.migration_tracker": "src\\utils\\migration_tracker.py", "src.utils.refactoring": "src\\utils\\refactoring.py", "src.utils.token_count": "src\\utils\\token_count.py", "start_athena": "start_athena.py", "tests": "tests\\__init__.py", "tests.conftest": "tests\\conftest.py", "tests.simple_compat_test": "tests\\simple_compat_test.py", "tests.start_services": "tests\\start_services.py", "tests.test_attachments_api": "tests\\test_attachments_api.py", "tests.test_compatibility_layers": "tests\\test_compatibility_layers.py", "tests.test_cross_device_models": "tests\\test_cross_device_models.py", "tests.test_kb": "tests\\test_kb.py", "tests.test_tasks_api": "tests\\test_tasks_api.py", "tests.unit.config.test_config_service": "tests\\unit\\config\\test_config_service.py", "tests.unit.services.test_user_service": "tests\\unit\\services\\test_user_service.py"}