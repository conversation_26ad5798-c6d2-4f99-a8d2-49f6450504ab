"""
Unit tests for the Configuration Service.

These tests verify that the database-driven configuration service works correctly,
including fallback to environment variables during the transition period.
"""

import os
import pytest
import json
from unittest.mock import patch

from src.config.service import ConfigService
from src.models.configuration import Configuration

class TestConfigService:
    """Test suite for the ConfigService class."""
    
    def test_get_from_database(self, app, session):
        """Test retrieving a configuration value from the database."""
        # Set up test data
        config = Configuration(
            key="TEST_KEY",
            value="test_value",
            value_type="string",
            description="Test configuration"
        )
        session.add(config)
        session.commit()
        
        # Create service
        service = ConfigService(app)
        
        # Test retrieval
        value = service.get("TEST_KEY")
        assert value == "test_value"
    
    def test_get_with_type_conversion(self, app, session):
        """Test that values are properly converted based on their type."""
        # Set up test data
        configs = [
            Configuration(key="TEST_INT", value="42", value_type="int"),
            Configuration(key="TEST_FLOAT", value="3.14", value_type="float"),
            Configuration(key="TEST_BOOL", value="true", value_type="bool"),
            Configuration(key="TEST_JSON", value='{"key": "value"}', value_type="json")
        ]
        for config in configs:
            session.add(config)
        session.commit()
        
        # Create service
        service = ConfigService(app)
        
        # Test retrieval with type conversion
        assert service.get("TEST_INT") == 42
        assert service.get("TEST_FLOAT") == 3.14
        assert service.get("TEST_BOOL") is True
        assert service.get("TEST_JSON") == {"key": "value"}
    
    def test_get_with_default(self, app, session):
        """Test retrieving a non-existent value with a default."""
        service = ConfigService(app)
        
        # Test with default values of different types
        assert service.get("NONEXISTENT_KEY", "default") == "default"
        assert service.get("NONEXISTENT_KEY", 42) == 42
        assert service.get("NONEXISTENT_KEY", True) is True
        assert service.get("NONEXISTENT_KEY", {"key": "value"}) == {"key": "value"}
    
    @patch.dict(os.environ, {"ENV_TEST_KEY": "env_value"})
    def test_fallback_to_env_var(self, app, session):
        """Test fallback to environment variables for missing configs."""
        service = ConfigService(app)
        
        # Should retrieve from environment variable
        assert service.get("ENV_TEST_KEY") == "env_value"
    
    @patch.dict(os.environ, {"ENV_TEST_INT": "42", "ENV_TEST_BOOL": "true"})
    def test_fallback_with_type_conversion(self, app, session):
        """Test fallback to environment variables with type conversion."""
        service = ConfigService(app)
        
        # Should retrieve from environment variable with conversion
        assert service.get("ENV_TEST_INT", value_type="int") == 42
        assert service.get("ENV_TEST_BOOL", value_type="bool") is True
    
    def test_set_config(self, app, session):
        """Test setting a configuration value."""
        service = ConfigService(app)
        
        # Set a new configuration
        success = service.set("NEW_KEY", "new_value", "string", "New configuration")
        assert success is True
        
        # Verify it was saved to the database
        config = session.query(Configuration).filter_by(key="NEW_KEY").first()
        assert config is not None
        assert config.value == "new_value"
        assert config.value_type == "string"
        assert config.description == "New configuration"
        assert config.is_sensitive is False
    
    def test_set_sensitive_config(self, app, session):
        """Test setting a sensitive configuration value."""
        service = ConfigService(app)
        
        # Set a sensitive configuration
        success = service.set(
            "API_KEY", 
            "secret_value", 
            "string", 
            "API key",
            is_sensitive=True
        )
        assert success is True
        
        # Verify it was saved with sensitive flag
        config = session.query(Configuration).filter_by(key="API_KEY").first()
        assert config is not None
        assert config.is_sensitive is True
    
    def test_update_existing_config(self, app, session):
        """Test updating an existing configuration."""
        # Create initial config
        config = Configuration(
            key="UPDATE_KEY",
            value="initial_value",
            value_type="string"
        )
        session.add(config)
        session.commit()
        
        service = ConfigService(app)
        
        # Update the configuration
        success = service.set("UPDATE_KEY", "updated_value")
        assert success is True
        
        # Verify it was updated
        session.refresh(config)
        assert config.value == "updated_value"
    
    def test_delete_config(self, app, session):
        """Test deleting a configuration value."""
        # Create config to delete
        config = Configuration(
            key="DELETE_KEY",
            value="delete_me",
            value_type="string"
        )
        session.add(config)
        session.commit()
        
        service = ConfigService(app)
        
        # Delete the configuration
        success = service.delete("DELETE_KEY")
        assert success is True
        
        # Verify it was deleted
        config = session.query(Configuration).filter_by(key="DELETE_KEY").first()
        assert config is None
    
    def test_clear_cache(self, app, session):
        """Test clearing the configuration cache."""
        # Create test config
        config = Configuration(
            key="CACHE_KEY",
            value="cached_value",
            value_type="string"
        )
        session.add(config)
        session.commit()
        
        service = ConfigService(app)
        
        # Access once to cache
        value1 = service.get("CACHE_KEY")
        assert value1 == "cached_value"
        
        # Update directly in database
        config.value = "updated_without_service"
        session.commit()
        
        # Should still return cached value
        value2 = service.get("CACHE_KEY")
        assert value2 == "cached_value"
        
        # Clear cache
        service.clear_cache()
        
        # Should now return updated value
        value3 = service.get("CACHE_KEY")
        assert value3 == "updated_without_service"
