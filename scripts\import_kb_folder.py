#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
import_kb_folder.py

This standalone script imports documents from the Knowledgebase folder 
into Athena's knowledge base without requiring the server to be running.

Usage:
    python import_kb_folder.py [--folder PATH]
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("kb_folder_import")

# Add AthenaCore to path so we can import modules properly
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
ATHENA_CORE_DIR = os.path.join(SCRIPT_DIR, "AthenaCore")
sys.path.append(ATHENA_CORE_DIR)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Import documents from a folder into Athena's knowledge base")
    parser.add_argument("--folder", type=str, default=None, 
                        help="Path to the folder containing documents (default: Knowledgebase folder in project root)")
    return parser.parse_args()

def get_openai_api_key():
    """Get the OpenAI API key from multiple sources"""
    # Try config.json first
    try:
        from src.utils.config import AthenaConfig
        cfg = AthenaConfig.load()
        if hasattr(cfg, "OPENAI_API_KEY") and cfg.OPENAI_API_KEY:
            logger.info("Found OpenAI API key in config.json")
            return cfg.OPENAI_API_KEY
    except Exception as e:
        logger.warning(f"Error accessing config.json: {e}")
    
    # Try DirectConnection
    try:
        # Try to use SQLAlchemy models if available
        try:
            from src.login.models import DirectConnection
            from src.login.models import db
            
            dc = DirectConnection.query.filter_by(name="OpenAI", enabled=True).first()
            if dc and dc.api_key:
                logger.info("Found OpenAI API key in DirectConnection")
                return dc.api_key
        except Exception:
            # Fall back to direct SQLite access
            import sqlite3
            db_path = os.path.join(ATHENA_CORE_DIR, "athena.db")
            if os.path.exists(db_path):
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT api_key FROM direct_connections WHERE name = 'OpenAI' AND enabled = 1 LIMIT 1")
                result = cursor.fetchone()
                conn.close()
                if result and result[0]:
                    logger.info("Found OpenAI API key in SQLite DirectConnection")
                    return result[0]
    except Exception as e:
        logger.warning(f"Error accessing DirectConnection: {e}")
    
    # Finally, check environment variable
    api_key = os.environ.get("OPENAI_API_KEY")
    if api_key:
        logger.info("Found OpenAI API key in environment variable")
        return api_key
    
    return None

def main():
    args = parse_args()
    
    # Set OpenAI API key in environment if found
    api_key = get_openai_api_key()
    if api_key:
        os.environ["OPENAI_API_KEY"] = api_key
        masked_key = api_key[:4] + "***" + api_key[-4:] if len(api_key) > 8 else "****"
        logger.info(f"Using OpenAI API key: {masked_key}")
    else:
        logger.warning("No OpenAI API key found - will use fallback embedding methods")
    
    # Set up paths
    if args.folder:
        kb_folder = os.path.abspath(args.folder)
    else:
        kb_folder = os.path.join(SCRIPT_DIR, "Knowledgebase")
    
    # Check if folder exists
    if not os.path.exists(kb_folder):
        logger.info(f"Knowledgebase folder not found, creating: {kb_folder}")
        os.makedirs(kb_folder, exist_ok=True)
        
        # Create a README file in the Knowledgebase folder
        readme_path = os.path.join(kb_folder, "README.txt")
        with open(readme_path, "w") as f:
            f.write("Athena Knowledgebase Folder\n")
            f.write("===========================\n\n")
            f.write("Place your documents in this folder to have them automatically imported into Athena's knowledge base.\n")
            f.write("Supported file types: PDF, TXT, MD (Markdown), DOCX, HTML, CSV, JSON\n\n")
            f.write("To import documents, run:\n")
            f.write("    python import_kb_folder.py\n\n")
            f.write("Files will be automatically processed and added to the knowledge base.\n")
        
        logger.info(f"Created README.txt in {kb_folder}")
        logger.info("Add some documents to the folder and run this script again to import them.")
        return 0
    
    # Check if folder has files
    import glob
    supported_extensions = ['*.pdf', '*.txt', '*.md', '*.markdown', '*.docx', '*.html', '*.htm', '*.csv', '*.json']
    files = []
    for ext in supported_extensions:
        files.extend(glob.glob(os.path.join(kb_folder, '**', ext), recursive=True))
    
    if not files:
        logger.info(f"No documents found in {kb_folder}")
        logger.info("Add some documents to the folder and run this script again to import them.")
        return 0
    
    # Import the documents
    logger.info(f"Found {len(files)} documents in {kb_folder}")
    
    try:
        # Import the necessary modules
        from src.utils.folder_import import import_documents_to_kb
        from src.core.knowledge_db import KnowledgeDatabase
        
        # Create a new instance of KnowledgeDatabase
        kb = KnowledgeDatabase()
        
        # Import documents from folder with fallback option
        logger.info(f"Importing documents from {kb_folder}...")
        stats = import_documents_to_kb(kb, kb_folder, skip_existing=True, use_fallback=True)
        
        if stats.get('success', False):
            logger.info(f"Import complete: {stats['imported']} imported, {stats['skipped']} skipped, {stats['failed']} failed")
            return 0
        else:
            logger.error(f"Import failed: {stats.get('error', 'Unknown error')}")
            return 1
    except Exception as e:
        logger.error(f"Error importing documents: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())
