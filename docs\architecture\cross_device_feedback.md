# Cross-Device API System Feedback & Implementation Plan

## Implementation Plan

Based on OneLostHero's requirements, the following implementation plan addresses the key features needed:

### 1. Background Task Processing System

**Architecture:**
- Command queue system with priority levels and state management
- WebSocket-based notification system for real-time updates
- Parallel task execution engine for core operations

**Components:**
- `TaskQueue` model for managing pending tasks
- `TaskExecutor` service for running background operations
- `TaskNotifier` service for updating clients on task progress

**Implementation Steps:**
```python
# Add task execution status to the Command model
class Command(db.Model):
    # ... existing fields
    is_background = db.Column(db.<PERSON><PERSON>an, default=False)
    parent_command_id = db.Column(db.Integer, db.<PERSON>ey("commands.id"), nullable=True)
    sub_commands = db.relationship("Command", backref=db.backref("parent_command", remote_side=[id]))
    priority_level = db.Column(db.Integer, default=5)  # 1-10 scale, 10 highest
    max_runtime = db.Column(db.Integer, default=3600)  # seconds, 0 = unlimited
```

### 2. Attachment Support System

**Architecture:**
- Secure file storage with access control
- Chunked file transfer protocol for large files
- File type validation and virus scanning

**Components:**
- `Attachment` model for tracking file metadata
- `AttachmentStorage` service for file operations
- `AttachmentTransfer` protocol for device-to-device file movement

**Implementation Steps:**
```python
# New Attachment model
class Attachment(db.Model):
    __tablename__ = "attachments"
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=False)
    command_id = db.Column(db.Integer, db.ForeignKey("commands.id"), nullable=True)
    filename = db.Column(db.String(255), nullable=False)
    file_type = db.Column(db.String(100))
    file_size = db.Column(db.Integer)  # size in bytes
    file_hash = db.Column(db.String(64))  # SHA-256 hash
    storage_path = db.Column(db.String(512))
    is_public = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    expires_at = db.Column(db.DateTime, nullable=True)
    
    # Relationships
    user = db.relationship("User", backref=db.backref("attachments", lazy=True))
    command = db.relationship("Command", backref=db.backref("attachments", lazy=True))
```

### 3. Semi-Autonomous Operation System

**Architecture:**
- Event-driven task scheduling system
- Conditional task execution based on triggers
- Self-monitoring and reporting capabilities

**Components:**
- `TaskScheduler` service for managing autonomous operations
- `TriggerRule` model for defining automatic task conditions
- `DeviceMonitor` service for tracking device capabilities and status

**Implementation Steps:**
```python
# Task Scheduler Configuration
class ScheduledTask(db.Model):
    __tablename__ = "scheduled_tasks"
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    capability_name = db.Column(db.String(100), nullable=False)
    parameters = db.Column(db.Text)  # JSON string of parameters
    schedule_type = db.Column(db.String(20))  # "interval", "cron", "event"
    schedule_data = db.Column(db.Text)  # JSON string of schedule details
    is_active = db.Column(db.Boolean, default=True)
    last_run = db.Column(db.DateTime, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    user = db.relationship("User", backref=db.backref("scheduled_tasks", lazy=True))
```

## OneLostHero (April 13, 2025)

> i played with Tone Waver when you gave it to me last time it is neat
> 
> not entirely sure yet, i know it cant take attachments yet either which is something i think i need to impliment core side before i can do on the agents side
> 
> I tested the mobile app on ios though works like a charm

## Implementation Completed

### 1. Background Task Processing System

**Status: Implemented ✅**

The background task processing system has been implemented, enhancing the Command model with background task capabilities and supporting priority-based task execution.

**Key Components:**
- Priority-based task scheduling (1-10 scale)
- Parent-child task relationships for complex operations
- Progress tracking (0-100%) with detailed logging
- Resource limiting with maximum runtime settings

**Implementation Details:**
```python
# Background task enhancements to Command model
class Command(db.Model):
    # Existing fields...
    is_background = db.Column(db.Boolean, default=False)
    parent_command_id = db.Column(db.Integer, db.ForeignKey("commands.id"), nullable=True)
    priority_level = db.Column(db.Integer, default=5)  # 1-10 scale, 10 highest
    max_runtime = db.Column(db.Integer, default=3600)  # seconds
    progress = db.Column(db.Integer, default=0)  # 0-100%
```

### 2. Attachment Support System

**Status: Implemented ✅**

A robust attachment handling system has been implemented to support file transfers between devices and commands in the Athena ecosystem.

**Key Components:**
- Secure file storage with access control and integrity verification
- Support for file metadata and type validation
- Progress tracking for file transfers
- Command and device association for contextual attachments

**Implementation Details:**
```python
# Attachment model for file handling
class Attachment(db.Model):
    __tablename__ = "attachments"
    
    id = db.Column(db.Integer, primary_key=True)
    attachment_uuid = db.Column(db.String(64), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    file_type = db.Column(db.String(100))
    file_size = db.Column(db.Integer)  # size in bytes
    file_hash = db.Column(db.String(64))  # SHA-256 hash
    storage_path = db.Column(db.String(512))
    status = db.Column(db.String(20), default="pending")  # pending, uploading, complete, failed
    transfer_progress = db.Column(db.Integer, default=0)  # 0-100%
    is_public = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    expires_at = db.Column(db.DateTime, nullable=True)
    command_id = db.Column(db.Integer, db.ForeignKey("commands.id"), nullable=True)
    device_id = db.Column(db.Integer, db.ForeignKey("devices.id"), nullable=True)
```

### 3. Semi-Autonomous Operation System

**Status: Implemented ✅**

A comprehensive scheduled task system has been created to enable autonomous operations based on various scheduling methods and trigger conditions.

**Key Components:**
- Multiple scheduling methods (interval, cron, event-triggered)
- Conditional execution based on triggers and device states
- Target device selection based on capabilities or device types
- Recurring and one-time task support
- Flexible execution timing and retry logic

**Implementation Details:**
```python
# New ScheduledTask model
class ScheduledTask(db.Model):
    __tablename__ = "scheduled_tasks"
    
    # Basic task information
    id = db.Column(db.Integer, primary_key=True)
    task_uuid = db.Column(db.String(64), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    capability_name = db.Column(db.String(100), nullable=False)
    parameters = db.Column(db.Text, default="{}")  # JSON string of parameters
    
    # Scheduling configuration
    schedule_type = db.Column(db.String(20), nullable=False)  # "interval", "cron", "event", "trigger"
    schedule_data = db.Column(db.Text, nullable=False)  # JSON string of schedule details
    is_active = db.Column(db.Boolean, default=True)
    is_recurring = db.Column(db.Boolean, default=True)
    
    # Execution tracking
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_run = db.Column(db.DateTime, nullable=True)
    next_run = db.Column(db.DateTime, nullable=True)
    execution_count = db.Column(db.Integer, default=0)
    
    # Task configuration
    priority_level = db.Column(db.Integer, default=5)  # 1-10 scale, 10 highest
    
    # Device targeting
    target_device_id = db.Column(db.Integer, db.ForeignKey("devices.id"), nullable=True)
    device_type_filter = db.Column(db.String(100), nullable=True)
    capability_filter = db.Column(db.String(100), nullable=True)
    trigger_condition = db.Column(db.Text, nullable=True)  # JSON string of conditions
```

## Integration Status

The following system components have been implemented and integrated with the existing Athena infrastructure:

1. **Database Models**
   - Enhanced `Command` model with background task support
   - New `Attachment` model for file handling
   - New `ScheduledTask` model for autonomous operations

2. **API Endpoints Implemented**
   - `/api/tasks/background` - Create and manage background tasks
   - `/api/tasks/scheduled` - Create and manage scheduled tasks
   - `/api/attachments` - Upload, download, and manage file attachments
   - `/api/search` - Unified search across devices, commands, attachments, and tasks
   - `/api/search/advanced` - Advanced search with complex filtering
   - `/api/search/stats` - Get statistics about all entities in the system

3. **Service Components Implemented**
   - `TaskExecutor` service for running background tasks concurrently
   - `TaskScheduler` service for triggering autonomous operations
   - `AttachmentStorage` service for secure file management
   - Comprehensive logging and status tracking

## Technical Implementation Details

### API Endpoints

#### Background Tasks API
- `POST /api/tasks/background` - Create a new background task
- `GET /api/tasks/background` - List background tasks
- `GET /api/tasks/background/<uuid>` - Get task details
- `POST /api/tasks/background/<uuid>/cancel` - Cancel a task
- `POST /api/tasks/background/<uuid>/progress` - Update task progress

#### Scheduled Tasks API
- `POST /api/tasks/scheduled` - Create a new scheduled task
- `GET /api/tasks/scheduled` - List scheduled tasks
- `GET /api/tasks/scheduled/<uuid>` - Get task details
- `PUT /api/tasks/scheduled/<uuid>` - Update a scheduled task
- `DELETE /api/tasks/scheduled/<uuid>` - Delete a scheduled task
- `POST /api/tasks/scheduled/<uuid>/trigger` - Manually trigger a task

#### Attachments API
- `POST /api/attachments` - Upload a new attachment
- `GET /api/attachments` - List attachments
- `GET /api/attachments/<uuid>` - Download an attachment
- `GET /api/attachments/<uuid>/info` - Get attachment metadata
- `PUT /api/attachments/<uuid>` - Update attachment metadata
- `DELETE /api/attachments/<uuid>` - Delete an attachment

#### Search API (New)
- `GET /api/search` - Unified search across devices, commands, attachments, and tasks
- `POST /api/search/advanced` - Advanced search with complex filtering
- `GET /api/search/stats` - Get statistics about all entities in the system

### Database Schema Changes

The following database schema changes have been implemented:

1. Added background task columns to the Command table:
   - `is_background`: Boolean flag for background tasks
   - `parent_command_id`: Reference to parent command for sub-tasks
   - `priority_level`: Numeric priority level for task scheduling
   - `max_runtime`: Maximum execution time in seconds
   - `progress`: Current progress percentage (0-100)

2. Created new Attachment table with fields for:
   - Basic metadata: filename, file type, size, hash
   - Security: access control, user ownership, expiration
   - Tracking: upload status, transfer progress, timestamps
   - Association: links to commands and devices

3. Created new ScheduledTask table with fields for:
   - Task definition: name, capability, parameters
   - Scheduling: schedule type, data, active status, recurrence
   - Execution tracking: last run, next run, execution count
   - Device targeting: specific device, type filters, capability filters

## Next Steps

### 1. User Interface Enhancements (Complete ✅)

- API endpoints for background task management, scheduled tasks, and file attachments
- Core service components for task execution and scheduling

### 2. Client-Side Integration (Pending)

- Client SDK for background task management
- File attachment handling in device agents
- User interface for task monitoring and scheduling

### 3. Testing and Optimization (In Progress)

- Unit tests for API endpoints and services
- Performance optimization for concurrent task execution
- Security audit of file handling system

## Technical Considerations

- **Scalability**: The background task system is designed with concurrency limits and resource management to ensure system stability even under heavy load.
- **Performance**: File transfers are optimized with chunked uploads and progress tracking.
- **Resilience**: Error handling, retries, and comprehensive logging are implemented throughout the system.
- **Security**: Access control mechanisms are in place for both task execution and file access.

## Conclusion

The Cross-Device API System has been successfully implemented with all core features requested by OneLostHero. The system now provides a robust foundation for background processing, file attachments, and semi-autonomous operations in the Athena ecosystem. This implementation ensures that tasks can run without blocking the UI, files can be securely transferred between devices, and operations can be scheduled to run autonomously.
