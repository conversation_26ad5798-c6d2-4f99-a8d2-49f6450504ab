"""
Compatibility module for login views.

This module provides backward compatibility for code that imports views from
the original location in src/login/views.py. As part of the refactoring process,
these views are being migrated to src/controllers/auth_controller.py.

This compatibility layer will be maintained throughout the transition period to
ensure existing code continues to work while encouraging migration to the new imports.
"""

import warnings
import sys
import logging
import importlib.util

# Set up logging
logger = logging.getLogger('athena.deprecation')

# Issue a warning about the deprecated location
warnings.warn(
    "Importing views from src.login.views is deprecated. "
    "Please update your imports to use src.controllers.auth_controller instead.",
    category=FutureWarning,
    stacklevel=2
)

# Log usage of this compatibility layer
caller = sys._getframe(1).f_globals.get('__name__', 'unknown')
logger.info(f"Deprecated view imports used by {caller}")

# Import from the new location to forward functionality
from src.controllers.auth_controller import (
    # Authentication functions
    login,
    logout,
    register,
    profile,
    
    # API key management
    api_keys,
    create_api_key,
    delete_api_key,
    get_token,
    
    # Blueprint
    auth_bp
)

# Import additional utilities that might be used
from flask_login import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    current_user,
    login_required,
    login_user,
    logout_user,
)

# If the auth_controller doesn't have certain functions from the original views.py,
# we can add them here with a more explicit deprecation warning

# For admin functionality that's not yet migrated
def admin_panel():
    """
    Compatibility function for admin_panel.
    
    DEPRECATED: This function is provided for backward compatibility only.
    Please update your code to use src.controllers.admin_controller instead.
    """
    warnings.warn(
        "admin_panel() is deprecated. Use src.controllers.admin_controller instead.",
        category=FutureWarning,
        stacklevel=2
    )
    
    # Forward to the new implementation
    from src.controllers.admin_controller import admin_panel as new_admin_panel
    return new_admin_panel()

# Define other compatibility functions for any remaining functionality
# that hasn't been migrated yet

# Admin role decorator for backward compatibility
def admin_required(f):
    """
    Compatibility decorator for admin_required.
    
    DEPRECATED: This decorator is provided for backward compatibility only.
    Please update your code to use src.utils.middleware.require_role instead.
    """
    warnings.warn(
        "admin_required decorator is deprecated. Use src.utils.middleware.require_role instead.",
        category=FutureWarning,
        stacklevel=2
    )
    
    # Forward to the new implementation
    from src.utils.middleware import require_role
    
    # Apply the new require_role decorator with admin role
    return require_role(['admin'])(f)
