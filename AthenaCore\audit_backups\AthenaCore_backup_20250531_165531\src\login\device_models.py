# ===========================================================================
# DEPRECATED: src/login/device_models.py
# ===========================================================================
# This file is DEPRECATED and will be REMOVED in a future release.
# All functionality has been migrated to the src/models/device.py file.
#
# Please update your imports to use the new locations:
#   OLD: from src.login.device_models import Device, Command
#   NEW: from src.models.device import Device, Command
#
# This file defines the database models for the Cross-Device API System.

import warnings
import sys
import logging

# Set up logging
logger = logging.getLogger('athena.compat')

# Show deprecation warning
warnings.warn(
    "The module src.login.device_models is deprecated. Please update imports to use src.models.device",
    category=FutureWarning,
    stacklevel=2
)

# Log usage of this deprecated module
frame = sys._getframe(1)
caller_module = frame.f_globals.get('__name__', 'unknown')
caller_function = frame.f_code.co_name
logger.info(f"Deprecated module src.login.device_models used by {caller_module}.{caller_function}")

# Import from the new location to maintain backward compatibility
from src.models import db
from src.models.device import (
    Device, DeviceCapability, Command, CommandLog, 
    Attachment, ScheduledTask, create_device, create_command, 
    create_attachment, add_command_log, get_device_or_404, 
    create_scheduled_task
)

# Export all imported symbols
__all__ = [
    'Device',
    'DeviceCapability',
    'Command',
    'CommandLog',
    'Attachment',
    'ScheduledTask',
    'create_device',
    'create_command',
    'create_attachment',
    'add_command_log',
    'get_device_or_404',
    'create_scheduled_task',
    'db'  # Export db for backward compatibility
]
