"""
Migration v2: Add Tasks Table

Create tasks table for tracking background tasks and their status.

Generated: 2025-05-22
"""

# Migration version and metadata
version = 2
description = "Create tasks table for tracking background tasks and their status"
dependencies = []  # List of migration versions this depends on

def upgrade(db):
    """
    Apply the migration.
    
    Args:
        db: SQLAlchemy database instance
    """
    # Create the tasks table
    db.engine.execute("""
        CREATE TABLE IF NOT EXISTS tasks (
            id VARCHAR(36) PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            status VARCHAR(50) NOT NULL DEFAULT 'pending',
            progress INTEGER DEFAULT 0,
            result TEXT,
            error TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            started_at TIMESTAMP,
            completed_at TIMESTAMP,
            user_id INTEGER,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
        )
    """)
    
    # Create indexes for faster lookups
    db.engine.execute("""
        CREATE INDEX IF NOT EXISTS idx_tasks_status
        ON tasks (status)
    """)
    
    db.engine.execute("""
        CREATE INDEX IF NOT EXISTS idx_tasks_user_id
        ON tasks (user_id)
    """)

def downgrade(db):
    """
    Revert the migration.
    
    Args:
        db: SQLAlchemy database instance
    """
    # Drop the tasks table
    db.engine.execute("DROP TABLE IF EXISTS tasks")
