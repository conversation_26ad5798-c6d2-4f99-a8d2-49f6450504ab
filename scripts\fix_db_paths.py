#!/usr/bin/env python
"""
Database Path Fixer for Athena

This script creates or modifies the instance folder and ensures the database
file has proper permissions.
"""
import os
import sys
import sqlite3
from pathlib import Path

def main():
    """Fix database paths and permissions."""
    print("Database Path Fixer for Athena")
    print("-" * 40)
    
    # Get the project root directory
    project_root = Path(__file__).parent.absolute()
    print(f"Project root: {project_root}")
    
    # Ensure instance directory exists
    instance_dir = project_root / "instance"
    instance_dir.mkdir(exist_ok=True)
    print(f"Instance directory: {instance_dir}")
    
    # Path to database file
    db_path = instance_dir / "athena.db"
    print(f"Database path: {db_path}")
    
    # Create a fresh empty database file with proper permissions
    if db_path.exists():
        print(f"Backing up existing database to {db_path}.bak")
        if Path(f"{db_path}.bak").exists():
            Path(f"{db_path}.bak").unlink()
        Path(db_path).rename(f"{db_path}.bak")
    
    # Create a new database file
    try:
        conn = sqlite3.connect(str(db_path))
        conn.execute("CREATE TABLE IF NOT EXISTS athena_config (key TEXT PRIMARY KEY, value TEXT)")
        conn.commit()
        conn.close()
        print(f"Created new database file with test table")
    except Exception as e:
        print(f"Error creating database: {e}")
        return 1
    
    # Update config.json with absolute paths
    config_path = project_root / "config.json"
    if config_path.exists():
        try:
            import json
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            # Update database paths to use absolute paths with forward slashes for SQLAlchemy
            abs_db_path = str(db_path).replace('\\', '/')
            config["db_uri"] = f"sqlite:///{abs_db_path}"
            config["DATABASE_URI"] = f"sqlite:///{abs_db_path}"
            
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2)
            
            print(f"Updated config.json with absolute database path: {abs_db_path}")
        except Exception as e:
            print(f"Error updating config.json: {e}")
    
    print("\nDatabase path fix completed!")
    print("You can now start Athena with: python AthenaCore/start_athena.py")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
