"""
API response utilities for Athena Core.

This module provides standardized API response formatting for the application,
ensuring consistent response structures across different API endpoints.
"""

from flask import jsonify
from typing import Any, Dict, List, Optional, Union
import time

def success_response(
    data: Any = None,
    message: str = "Success",
    status_code: int = 200,
    meta: Optional[Dict[str, Any]] = None
) -> tuple:
    """
    Create a standardized success response.
    
    Args:
        data: The data to include in the response
        message: A success message
        status_code: HTTP status code
        meta: Optional metadata to include in the response
        
    Returns:
        Tuple of (JSON response, status code)
    """
    response = {
        "success": True,
        "message": message,
        "data": data or {},
        "timestamp": int(time.time())
    }
    
    if meta:
        response["meta"] = meta
    
    return jsonify(response), status_code

def error_response(
    message: str = "An error occurred",
    error_code: str = "unknown_error",
    status_code: int = 400,
    errors: Optional[List[Dict[str, Any]]] = None,
    meta: Optional[Dict[str, Any]] = None
) -> tuple:
    """
    Create a standardized error response.
    
    Args:
        message: An error message
        error_code: A code identifying the error
        status_code: HTTP status code
        errors: Optional list of specific errors
        meta: Optional metadata to include in the response
        
    Returns:
        Tuple of (JSON response, status code)
    """
    response = {
        "success": False,
        "message": message,
        "error_code": error_code,
        "timestamp": int(time.time())
    }
    
    if errors:
        response["errors"] = errors
    
    if meta:
        response["meta"] = meta
    
    return jsonify(response), status_code

def validation_error_response(
    errors: Dict[str, List[str]],
    message: str = "Validation failed",
    status_code: int = 422
) -> tuple:
    """
    Create a standardized validation error response.
    
    Args:
        errors: Dictionary mapping field names to lists of error messages
        message: An error message
        status_code: HTTP status code
        
    Returns:
        Tuple of (JSON response, status code)
    """
    # Format errors for the response
    formatted_errors = []
    for field, messages in errors.items():
        for msg in messages:
            formatted_errors.append({
                "field": field,
                "message": msg
            })
    
    return error_response(
        message=message,
        error_code="validation_error",
        status_code=status_code,
        errors=formatted_errors
    )

def pagination_meta(
    page: int,
    per_page: int,
    total: int,
    total_pages: int
) -> Dict[str, Any]:
    """
    Create standardized pagination metadata.
    
    Args:
        page: Current page number (1-indexed)
        per_page: Number of items per page
        total: Total number of items
        total_pages: Total number of pages
        
    Returns:
        Dictionary with pagination metadata
    """
    return {
        "pagination": {
            "page": page,
            "per_page": per_page,
            "total": total,
            "total_pages": total_pages,
            "has_next": page < total_pages,
            "has_prev": page > 1
        }
    }
