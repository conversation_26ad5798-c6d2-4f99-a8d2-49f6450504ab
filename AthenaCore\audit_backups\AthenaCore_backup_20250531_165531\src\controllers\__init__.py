"""
Controllers package for Athena Core.

This package contains all controller modules that handle routing and
request processing for the application. Controllers connect the user interface
with the underlying business logic and data models.
"""

from .main_controller import register_blueprints as register_main_blueprints
from .auth_controller import register_blueprints as register_auth_blueprints
from .config_controller import register_blueprints as register_config_blueprints
from .task_controller import register_blueprints as register_task_blueprints
from .api_controller import register_blueprints as register_api_blueprints

# Import knowledge routes blueprint
from src.routes.knowledge_routes import knowledge_bp

# Define list of blueprint registration functions
registration_functions = [
    register_main_blueprints,
    register_auth_blueprints,
    register_config_blueprints,
    register_task_blueprints,
    register_api_blueprints
]

def register_all_blueprints(app):
    """
    Register all controller blueprints with the Flask app.
    Register all blueprints with the Flask application.
    
    Args:
        app: Flask application instance
    """
    # Register all controller blueprints
    for register_func in registration_functions:
        register_func(app)
    
    # Register knowledge blueprint directly
    app.register_blueprint(knowledge_bp)
