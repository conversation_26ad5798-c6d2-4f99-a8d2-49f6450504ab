"""
Compatibility module for login models.

This module provides backward compatibility for code that imports models from
the original location in src/login/models.py. As part of the refactoring process,
these models are being migrated to src/models/.

This module is being consolidated with models_compat.py for a cleaner codebase.
It now forwards all imports to models_compat.py to maintain backward compatibility.

DEPRECATED: Use src.models directly instead of this compatibility layer.
"""

import warnings
import sys
import logging

# Set up logging
logger = logging.getLogger('athena.deprecation')

# Issue a warning about the deprecated location
warnings.warn(
    "This compatibility module (src.login.compat) is deprecated and will be removed. "
    "Please update your imports to use src.models directly.",
    category=FutureWarning,
    stacklevel=2
)

# Forward all imports to models_compat.py
from src.login.models_compat import *

# Log usage of this compatibility layer
caller = sys._getframe(1).f_globals.get('__name__', 'unknown')
logger.info(f"Deprecated compatibility layer (src.login.compat) used by {caller}")

# Export all imported models to make them available to importers
__all__ = [
    'db',
    'User', 'UserLog',
    'APIKey',
    'LLMProviderSetting',
    'CommandToggle',
    'DirectConnection', 'MCPApiKey', 'MCPServerTemplate',
    'Conversation', 'Message',
    'LogEntry'
]
