# src/api/demo.py

"""
Demo endpoints for testing features like the background task tracking system.
"""

import logging
import time
import threading
import uuid
import json
from datetime import datetime, timedelta

from flask import Blueprint, jsonify, request, current_app
from flask_login import current_user, login_required

from src.models import db
# Updated to use direct imports from models.device instead of deprecated src.login.device_models
from src.models.device import Command, CommandLog, Device

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger("demo_api")

# Create blueprint
demo_bp = Blueprint("demo", __name__, url_prefix="/api/demo")

# Store Flask app reference
flask_app = None

@demo_bp.record_once
def on_register(state):
    """Store Flask app when blueprint is registered"""
    global flask_app
    flask_app = state.app

@demo_bp.route("/task", methods=["POST"])
@login_required
def create_demo_task():
    """
    Create a demo background task that runs for a specified duration.
    
    Request Body:
        duration (int, optional): Duration in seconds (default: 30)
        task_type (str, optional): Type of task ("success", "fail", "random") (default: "success")
        
    Returns:
        JSON response with the created task
    """
    data = request.json or {}
    
    # Get duration from request body
    try:
        duration = int(data.get("duration", 30))
    except ValueError:
        duration = 30
        
    duration = max(5, min(120, duration))  # Limit to 5-120 seconds
    
    # Get task type from request body
    task_type = data.get("task_type", "success")
    if task_type not in ["success", "fail", "random"]:
        task_type = "success"
    
    # Get or create a device for the task
    device = Device.query.filter_by(user_id=current_user.id).first()

    if not device:
        # If no device exists, create a demo device
        device_uuid = str(uuid.uuid4())
        device = Device(
            device_uuid=device_uuid,
            user_id=current_user.id,
            name="Demo Device",
            device_type="demo",
            os_info="Athena Demo Environment",
            last_active=datetime.utcnow(),
            created_at=datetime.utcnow(),
            is_active=True
        )
        db.session.add(device)
        db.session.flush()  # Flush to get device ID but don't commit yet

    # Create a new command
    command_uuid = str(uuid.uuid4())
    command = Command(
        command_uuid=command_uuid,
        user_id=current_user.id,
        source_device_id=device.id,
        target_device_id=device.id,
        capability_name=f"demo_{task_type}_task",
        parameters=json.dumps({"duration": duration}),
        priority="normal",
        status="pending",
        is_background=True,
        parent_command_id=None,
        priority_level=5,
        max_runtime=duration + 10,  # Give a little extra time
        progress=0
    )
    
    db.session.add(command)
    db.session.commit()  # Commit the command to get an ID
    
    # Add initial log
    log_entry = CommandLog(
        command_id=command.id,  # Now command.id exists
        status="pending",
        message=f"Demo task created. Will run for {duration} seconds.",
        timestamp=datetime.utcnow()
    )
    db.session.add(log_entry)
    db.session.commit()
    
    # Start task in background thread
    task_thread = threading.Thread(
        target=run_demo_task,
        args=(command.id, duration, task_type, current_app._get_current_object())
    )
    task_thread.daemon = True
    task_thread.start()
    
    logger.info(f"Demo task created: {command_uuid}")
    
    return jsonify({
        "message": "Demo task created successfully",
        "task": command.to_dict()
    })

def run_demo_task(command_id, duration, task_type, app):
    """
    Run a demo task in the background.
    
    Args:
        command_id (int): ID of the command
        duration (int): Duration in seconds
        task_type (str): Type of task ("success", "fail", "random")
    """
    from src.services.socket import emit_task_update, emit_task_progress
    
    # Sleep a bit to simulate startup time
    time.sleep(1)
    
    try:
        with app.app_context():
            # Get command from database
            command = Command.query.get(command_id)
            
            if not command:
                logger.error(f"Command {command_id} not found")
                return
                
            # Update status to running
            command.status = "running"
            
            # Add log entry
            log_entry = CommandLog(
                command_id=command.id,
                status="running",
                message="Demo task started",
                timestamp=datetime.utcnow()
            )
            db.session.add(log_entry)
            db.session.commit()
            
            # Emit task update
            emit_task_update(command)
            
            # Set step count based on duration
            steps = max(5, min(20, duration // 2))
            sleep_per_step = duration / steps
            
            # Run steps
            import random
            
            for step in range(1, steps + 1):
                # Sleep to simulate work
                time.sleep(sleep_per_step)
                
                # Calculate progress
                progress = int((step / steps) * 100)
                
                # Handle "fail" type - fail at 60-80% progress
                should_fail = False
                if task_type == "fail":
                    should_fail = (progress >= 60)
                elif task_type == "random":
                    should_fail = (progress > 50 and random.random() < 0.3)
                
                if should_fail:
                    with app.app_context():
                        # Update command to failed
                        command = Command.query.get(command_id)
                        if command:
                            command.status = "failed"
                            command.completed_at = datetime.utcnow()
                            
                            # Add log entry
                            log_entry = CommandLog(
                                command_id=command.id,
                                status="failed",
                                message=f"Demo task failed at {progress}% completion",
                                timestamp=datetime.utcnow()
                            )
                            db.session.add(log_entry)
                            db.session.commit()
                            
                            # Emit task update
                            emit_task_update(command)
                    
                    logger.info(f"Demo task {command_id} failed at {progress}% progress")
                    return
                
                # Update progress in database
                with app.app_context():
                    command = Command.query.get(command_id)
                    if command:
                        command.progress = progress
                        
                        # Add log entry (only for certain steps)
                        if step == 1 or step == steps or step % 3 == 0:
                            message = f"Demo task progress: {progress}%"
                            
                            # Make the logs more interesting
                            if step == 1:
                                message = "Initializing demo task..."
                            elif progress > 25 and progress < 30:
                                message = "Processing input data..."
                            elif progress > 50 and progress < 55:
                                message = "Performing main computation..."
                            elif progress > 75 and progress < 80:
                                message = "Finalizing results..."
                            elif progress == 100:
                                message = "Demo task completed successfully!"
                            
                            log_entry = CommandLog(
                                command_id=command.id,
                                status="running",
                                message=message,
                                timestamp=datetime.utcnow()
                            )
                            db.session.add(log_entry)
                        
                        db.session.commit()
                        
                        # Emit task progress update
                        if step == 1 or step == steps or step % 3 == 0:
                            emit_task_progress(command, log_entry.message, progress)
                
            # Complete the task
            with app.app_context():
                command = Command.query.get(command_id)
                if command:
                    command.status = "completed"
                    command.progress = 100
                    command.completed_at = datetime.utcnow()
                    
                    # Add log entry
                    log_entry = CommandLog(
                        command_id=command.id,
                        status="completed",
                        message="Demo task completed successfully",
                        timestamp=datetime.utcnow()
                    )
                    db.session.add(log_entry)
                    db.session.commit()
                    
                    # Emit task update
                    emit_task_update(command)
            
            logger.info(f"Demo task {command_id} completed successfully")
    
    except Exception as e:
        logger.error(f"Error running demo task: {str(e)}")
        
        # Update task to failed
        try:
            with app.app_context():
                command = Command.query.get(command_id)
                if command:
                    command.status = "failed"
                    command.completed_at = datetime.utcnow()
                    
                    # Add log entry
                    log_entry = CommandLog(
                        command_id=command.id,
                        status="failed",
                        message=f"Demo task failed with error: {str(e)}",
                        timestamp=datetime.utcnow()
                    )
                    db.session.add(log_entry)
                    db.session.commit()
                    
                    # Emit task update
                    emit_task_update(command)
        except Exception as inner_e:
            logger.error(f"Error updating failed task: {str(inner_e)}")
