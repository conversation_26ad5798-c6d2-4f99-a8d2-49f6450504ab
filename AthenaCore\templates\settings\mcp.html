<!-- MCP (Model Context Protocol) Settings Page -->
<div class="settings-section"
     data-section="mcp"
     id="mcp-section">

    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <h2>MCP (Model Context Protocol)</h2>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Model Context Protocol:</strong> Configure MCP servers and tools to extend AI capabilities with external data sources and tools.
                </div>
                
                <!-- MCP Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">MCP Status</h5>
                    </div>
                    <div class="card-body">
                        <div id="mcp-status">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                                <p>Loading MCP status...</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- MCP Servers -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">MCP Servers</h5>
                        <button class="btn btn-primary" onclick="addMCPServer()">
                            <i class="fas fa-plus"></i> Add Server
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="mcp-servers-list">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-server fa-3x mb-3"></i>
                                <h5>No MCP servers configured</h5>
                                <p>Add MCP servers to extend AI capabilities with external tools and data sources.</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Smithery Registry -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Smithery Registry</h5>
                        <p class="text-muted mb-0">Browse and install MCP tools from the Smithery Registry</p>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="smithery-api-key">Smithery API Key:</label>
                                    <input type="password" id="smithery-api-key" class="form-control" 
                                           placeholder="Enter your Smithery API key">
                                    <small class="form-text text-muted">
                                        Get your API key from <a href="https://smithery.ai" target="_blank">Smithery Registry</a>
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button class="btn btn-primary" onclick="saveSmitheryKey()">
                                            <i class="fas fa-save"></i> Save API Key
                                        </button>
                                        <button class="btn btn-outline-secondary ml-2" onclick="browseSmithery()">
                                            <i class="fas fa-external-link-alt"></i> Browse Registry
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div id="smithery-tools" class="mt-4">
                            <!-- Smithery tools will be loaded here -->
                        </div>
                    </div>
                </div>
                
                <!-- MCP Tools -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Available Tools</h5>
                        <p class="text-muted mb-0">Tools available through configured MCP servers</p>
                    </div>
                    <div class="card-body">
                        <div id="mcp-tools-list">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-tools fa-3x mb-3"></i>
                                <h5>No tools available</h5>
                                <p>Configure MCP servers to see available tools.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- MCP Server Modal -->
<div id="mcpServerModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h4>Add MCP Server</h4>
            <span class="close" onclick="closeMCPServerModal()">&times;</span>
        </div>
        <div class="modal-body">
            <form id="mcpServerForm" onsubmit="saveMCPServer(event)">
                <div class="form-group">
                    <label for="serverName">Server Name:</label>
                    <input type="text" id="serverName" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="serverCommand">Command:</label>
                    <input type="text" id="serverCommand" class="form-control" required>
                    <small class="form-text text-muted">Command to start the MCP server</small>
                </div>
                
                <div class="form-group">
                    <label for="serverArgs">Arguments:</label>
                    <input type="text" id="serverArgs" class="form-control">
                    <small class="form-text text-muted">Command line arguments (optional)</small>
                </div>
                
                <div class="form-group">
                    <label for="serverEnv">Environment Variables:</label>
                    <textarea id="serverEnv" class="form-control" rows="3" 
                              placeholder="KEY1=value1&#10;KEY2=value2"></textarea>
                    <small class="form-text text-muted">One per line in KEY=value format</small>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">Add Server</button>
                    <button type="button" class="btn btn-secondary" onclick="closeMCPServerModal()">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 15px;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
}

.close:hover {
    color: #000;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #495057;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.form-control:focus {
    border-color: #007bff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.form-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    margin-right: 10px;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn-outline-secondary {
    border: 1px solid #6c757d;
    color: #6c757d;
    background-color: transparent;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    color: white;
}

.mcp-server-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    background-color: #f8f9fa;
}

.mcp-server-item.running {
    border-color: #28a745;
    background-color: #d4edda;
}

.mcp-server-item.stopped {
    border-color: #dc3545;
    background-color: #f8d7da;
}

.server-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-running {
    background-color: #28a745;
    color: white;
}

.status-stopped {
    background-color: #dc3545;
    color: white;
}

.tool-item {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 10px;
    background-color: #ffffff;
}

.tool-name {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.tool-description {
    color: #6c757d;
    font-size: 14px;
}
</style>

<script>
// MCP management functions
function addMCPServer() {
    const modal = document.getElementById('mcpServerModal');
    if (modal) {
        modal.style.display = 'flex';
    }
}

function closeMCPServerModal() {
    const modal = document.getElementById('mcpServerModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Add click outside to close functionality for template modal
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('mcpServerModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeMCPServerModal();
            }
        });

        // Add escape key handler for template modal
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && modal.style.display === 'flex') {
                closeMCPServerModal();
            }
        });
    }
});

function saveMCPServer(event) {
    event.preventDefault();
    // Implementation for saving MCP server
    console.log('Save MCP server functionality to be implemented');
    closeMCPServerModal();
}

function saveSmitheryKey() {
    const apiKey = document.getElementById('smithery-api-key').value;
    if (apiKey) {
        // Save to DirectConnections or Configuration
        console.log('Save Smithery API key functionality to be implemented');
    }
}

function browseSmithery() {
    window.open('https://smithery.ai', '_blank');
}

// Initialize MCP section
function initMCPSection() {
    console.log('Initializing MCP section');
    // Load MCP status and servers
    loadMCPStatus();
    loadMCPServers();
    loadMCPTools();
}

function loadMCPStatus() {
    // Implementation for loading MCP status
    const statusContainer = document.getElementById('mcp-status');
    if (statusContainer) {
        statusContainer.innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                MCP functionality is currently under development.
            </div>
        `;
    }
}

function loadMCPServers() {
    // Implementation for loading MCP servers
    console.log('Loading MCP servers');
}

function loadMCPTools() {
    // Implementation for loading MCP tools
    console.log('Loading MCP tools');
}

// Initialize when section becomes visible
(function() {
    function initMCPWhenVisible() {
        var mcpSection = document.getElementById('mcp-section');
        if (mcpSection) {
            var observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                        if (mcpSection.style.display !== 'none') {
                            initMCPSection();
                        }
                    }
                });
            });
            observer.observe(mcpSection, { attributes: true });
        }
    }

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initMCPWhenVisible);
    } else {
        initMCPWhenVisible();
    }
})();
</script>
