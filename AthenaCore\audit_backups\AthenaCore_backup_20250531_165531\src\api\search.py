# src/api/search.py

"""
API endpoints for searching across the Cross-Device API System.

This file implements:
- Unified search across devices, commands, attachments, and scheduled tasks
- Advanced filtering and sorting options
- Search result aggregation and pagination
"""

import json
import logging
from datetime import datetime, timedelta
from sqlalchemy import or_, and_, func

from flask import Blueprint, jsonify, request, current_app
from flask_login import current_user, login_required

from src.models import db, User
# Updated to use direct imports from models.device instead of deprecated src.login.device_models
from src.models.device import (
    Device, Command, Attachment, ScheduledTask, 
    DeviceCapability, CommandLog
)

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger("search_api")

# Create blueprint
search_bp = Blueprint("search", __name__, url_prefix="/api/search")

@search_bp.route("", methods=["GET"])
@login_required
def unified_search():
    """
    Perform a unified search across devices, commands, attachments, and scheduled tasks.
    
    Query Parameters:
        query (str): Search term
        entities (str): Comma-separated list of entity types to search (devices,commands,attachments,tasks)
        limit (int): Maximum number of results per entity type (default: 10)
        offset (int): Offset for pagination (default: 0)
        sort_by (str): Field to sort by
        sort_dir (str): Sort direction (asc or desc)
        
    Returns:
        JSON response with search results grouped by entity type
    """
    # Get search parameters
    query = request.args.get("query", "").strip()
    entities = request.args.get("entities", "devices,commands,attachments,tasks")
    limit = int(request.args.get("limit", 10))
    offset = int(request.args.get("offset", 0))
    sort_by = request.args.get("sort_by", "created_at")
    sort_dir = request.args.get("sort_dir", "desc")
    
    # Parse entities to search
    entity_types = [entity.strip() for entity in entities.split(",") if entity.strip()]
    
    if not query and not entity_types:
        return jsonify({
            "error": "Please provide a search query or entity types to search"
        }), 400
    
    result = {
        "query": query,
        "total_results": 0
    }
    
    # Search devices
    if "devices" in entity_types:
        devices = _search_devices(query, limit, offset, sort_by, sort_dir)
        result["devices"] = devices
        result["total_results"] += len(devices)
    
    # Search commands
    if "commands" in entity_types:
        commands = _search_commands(query, limit, offset, sort_by, sort_dir)
        result["commands"] = commands
        result["total_results"] += len(commands)
    
    # Search attachments
    if "attachments" in entity_types:
        attachments = _search_attachments(query, limit, offset, sort_by, sort_dir)
        result["attachments"] = attachments
        result["total_results"] += len(attachments)
    
    # Search scheduled tasks
    if "tasks" in entity_types:
        tasks = _search_scheduled_tasks(query, limit, offset, sort_by, sort_dir)
        result["tasks"] = tasks
        result["total_results"] += len(tasks)
    
    return jsonify(result)

def _search_devices(query, limit, offset, sort_by, sort_dir):
    """Search devices by name, type, or capability."""
    # Base query
    device_query = Device.query.filter_by(user_id=current_user.id)
    
    # Apply search filter if query is provided
    if query:
        device_query = device_query.filter(
            or_(
                Device.name.ilike(f"%{query}%"),
                Device.device_type.ilike(f"%{query}%"),
                Device.device_uuid.ilike(f"%{query}%"),
                # Search by capability
                Device.id.in_(
                    db.session.query(DeviceCapability.device_id)
                    .filter(DeviceCapability.capability_name.ilike(f"%{query}%"))
                )
            )
        )
    
    # Apply sorting
    if sort_by == "name":
        order_col = Device.name
    elif sort_by == "type":
        order_col = Device.device_type
    elif sort_by == "last_active":
        order_col = Device.last_active
    else:
        order_col = Device.created_at
    
    if sort_dir.lower() == "asc":
        device_query = device_query.order_by(order_col.asc())
    else:
        device_query = device_query.order_by(order_col.desc())
    
    # Apply pagination
    devices = device_query.limit(limit).offset(offset).all()
    
    # Format results
    return [
        {
            "id": device.id,
            "device_uuid": device.device_uuid,
            "name": device.name,
            "device_type": device.device_type,
            "is_active": device.is_active,
            "last_active": device.last_active.isoformat() if device.last_active else None,
            "capabilities": [cap.capability_name for cap in device.capabilities] if hasattr(device, "capabilities") else [],
            "entity_type": "device"
        }
        for device in devices
    ]

def _search_commands(query, limit, offset, sort_by, sort_dir):
    """Search commands by UUID, capability, or parameters."""
    # Base query
    command_query = Command.query.filter_by(user_id=current_user.id)
    
    # Apply search filter if query is provided
    if query:
        command_query = command_query.filter(
            or_(
                Command.command_uuid.ilike(f"%{query}%"),
                Command.capability_name.ilike(f"%{query}%"),
                Command.parameters.ilike(f"%{query}%"),
                Command.status.ilike(f"%{query}%")
            )
        )
    
    # Apply sorting
    if sort_by == "status":
        order_col = Command.status
    elif sort_by == "priority":
        order_col = Command.priority_level
    elif sort_by == "progress":
        order_col = Command.progress
    else:
        order_col = Command.created_at
    
    if sort_dir.lower() == "asc":
        command_query = command_query.order_by(order_col.asc())
    else:
        command_query = command_query.order_by(order_col.desc())
    
    # Apply pagination
    commands = command_query.limit(limit).offset(offset).all()
    
    # Format results
    return [
        {
            "id": command.id,
            "command_uuid": command.command_uuid,
            "capability_name": command.capability_name,
            "status": command.status,
            "is_background": command.is_background,
            "progress": command.progress if hasattr(command, "progress") else None,
            "created_at": command.created_at.isoformat() if command.created_at else None,
            "entity_type": "command"
        }
        for command in commands
    ]

def _search_attachments(query, limit, offset, sort_by, sort_dir):
    """Search attachments by filename, type, or hash."""
    # Base query
    attachment_query = Attachment.query.filter_by(user_id=current_user.id)
    
    # Apply search filter if query is provided
    if query:
        attachment_query = attachment_query.filter(
            or_(
                Attachment.filename.ilike(f"%{query}%"),
                Attachment.file_type.ilike(f"%{query}%"),
                Attachment.file_hash.ilike(f"%{query}%"),
                Attachment.attachment_uuid.ilike(f"%{query}%")
            )
        )
    
    # Apply sorting
    if sort_by == "filename":
        order_col = Attachment.filename
    elif sort_by == "file_type":
        order_col = Attachment.file_type
    elif sort_by == "file_size":
        order_col = Attachment.file_size
    else:
        order_col = Attachment.created_at
    
    if sort_dir.lower() == "asc":
        attachment_query = attachment_query.order_by(order_col.asc())
    else:
        attachment_query = attachment_query.order_by(order_col.desc())
    
    # Apply pagination
    attachments = attachment_query.limit(limit).offset(offset).all()
    
    # Format results
    return [
        {
            "id": attachment.id,
            "attachment_uuid": attachment.attachment_uuid,
            "filename": attachment.filename,
            "file_type": attachment.file_type,
            "file_size": attachment.file_size,
            "is_public": attachment.is_public,
            "created_at": attachment.created_at.isoformat() if attachment.created_at else None,
            "entity_type": "attachment"
        }
        for attachment in attachments
    ]

def _search_scheduled_tasks(query, limit, offset, sort_by, sort_dir):
    """Search scheduled tasks by name, capability, or parameters."""
    # Base query
    task_query = ScheduledTask.query.filter_by(user_id=current_user.id)
    
    # Apply search filter if query is provided
    if query:
        task_query = task_query.filter(
            or_(
                ScheduledTask.name.ilike(f"%{query}%"),
                ScheduledTask.capability_name.ilike(f"%{query}%"),
                ScheduledTask.parameters.ilike(f"%{query}%"),
                ScheduledTask.task_uuid.ilike(f"%{query}%")
            )
        )
    
    # Apply sorting
    if sort_by == "name":
        order_col = ScheduledTask.name
    elif sort_by == "schedule_type":
        order_col = ScheduledTask.schedule_type
    elif sort_by == "next_run":
        order_col = ScheduledTask.next_run
    else:
        order_col = ScheduledTask.created_at
    
    if sort_dir.lower() == "asc":
        task_query = task_query.order_by(order_col.asc())
    else:
        task_query = task_query.order_by(order_col.desc())
    
    # Apply pagination
    tasks = task_query.limit(limit).offset(offset).all()
    
    # Format results
    return [
        {
            "id": task.id,
            "task_uuid": task.task_uuid,
            "name": task.name,
            "capability_name": task.capability_name,
            "schedule_type": task.schedule_type,
            "is_active": task.is_active,
            "next_run": task.next_run.isoformat() if task.next_run else None,
            "entity_type": "scheduled_task"
        }
        for task in tasks
    ]

@search_bp.route("/advanced", methods=["POST"])
@login_required
def advanced_search():
    """
    Perform an advanced search with complex filtering criteria.
    
    Request Body:
        entity_type (str): Type of entity to search (device, command, attachment, task)
        filters (list): List of filter conditions
        sort (dict): Sorting criteria
        pagination (dict): Pagination options
        
    Returns:
        JSON response with search results
    """
    data = request.json or {}
    
    # Validate request
    if "entity_type" not in data:
        return jsonify({
            "error": "Entity type is required"
        }), 400
    
    entity_type = data["entity_type"]
    filters = data.get("filters", [])
    sort = data.get("sort", {"field": "created_at", "direction": "desc"})
    pagination = data.get("pagination", {"limit": 10, "offset": 0})
    
    # Call the appropriate search function based on entity type
    if entity_type == "device":
        result = _advanced_search_devices(filters, sort, pagination)
    elif entity_type == "command":
        result = _advanced_search_commands(filters, sort, pagination)
    elif entity_type == "attachment":
        result = _advanced_search_attachments(filters, sort, pagination)
    elif entity_type == "task":
        result = _advanced_search_scheduled_tasks(filters, sort, pagination)
    else:
        return jsonify({
            "error": f"Invalid entity type: {entity_type}"
        }), 400
    
    return jsonify(result)

def _advanced_search_devices(filters, sort, pagination):
    """Perform advanced search on devices."""
    # Base query
    query = Device.query.filter_by(user_id=current_user.id)
    
    # Apply filters
    for filter_condition in filters:
        field = filter_condition.get("field")
        operator = filter_condition.get("operator")
        value = filter_condition.get("value")
        
        if not all([field, operator, value is not None]):
            continue
        
        if field == "name":
            if operator == "contains":
                query = query.filter(Device.name.ilike(f"%{value}%"))
            elif operator == "equals":
                query = query.filter(Device.name == value)
        elif field == "device_type":
            if operator == "contains":
                query = query.filter(Device.device_type.ilike(f"%{value}%"))
            elif operator == "equals":
                query = query.filter(Device.device_type == value)
        elif field == "is_active":
            query = query.filter(Device.is_active == value)
        elif field == "capability":
            # Filter by capability name
            query = query.filter(
                Device.id.in_(
                    db.session.query(DeviceCapability.device_id)
                    .filter(DeviceCapability.capability_name.ilike(f"%{value}%"))
                )
            )
    
    # Apply sorting
    sort_field = sort.get("field", "created_at")
    sort_direction = sort.get("direction", "desc")
    
    if sort_field == "name":
        order_col = Device.name
    elif sort_field == "device_type":
        order_col = Device.device_type
    elif sort_field == "last_active":
        order_col = Device.last_active
    else:
        order_col = Device.created_at
    
    if sort_direction.lower() == "asc":
        query = query.order_by(order_col.asc())
    else:
        query = query.order_by(order_col.desc())
    
    # Count total results
    total_count = query.count()
    
    # Apply pagination
    limit = pagination.get("limit", 10)
    offset = pagination.get("offset", 0)
    results = query.limit(limit).offset(offset).all()
    
    # Format results
    formatted_results = [
        {
            "id": device.id,
            "device_uuid": device.device_uuid,
            "name": device.name,
            "device_type": device.device_type,
            "is_active": device.is_active,
            "last_active": device.last_active.isoformat() if device.last_active else None,
            "capabilities": [cap.capability_name for cap in device.capabilities] if hasattr(device, "capabilities") else [],
            "entity_type": "device"
        }
        for device in results
    ]
    
    return {
        "results": formatted_results,
        "total": total_count,
        "limit": limit,
        "offset": offset
    }

def _advanced_search_commands(filters, sort, pagination):
    """Perform advanced search on commands."""
    # Implementation similar to devices but for commands
    # Base query
    query = Command.query.filter_by(user_id=current_user.id)
    
    # Apply filters
    for filter_condition in filters:
        field = filter_condition.get("field")
        operator = filter_condition.get("operator")
        value = filter_condition.get("value")
        
        if not all([field, operator, value is not None]):
            continue
        
        if field == "capability_name":
            if operator == "contains":
                query = query.filter(Command.capability_name.ilike(f"%{value}%"))
            elif operator == "equals":
                query = query.filter(Command.capability_name == value)
        elif field == "status":
            if operator == "equals":
                query = query.filter(Command.status == value)
        elif field == "is_background":
            query = query.filter(Command.is_background == value)
        elif field == "priority_level":
            if operator == "equals":
                query = query.filter(Command.priority_level == value)
            elif operator == "greater_than":
                query = query.filter(Command.priority_level > value)
            elif operator == "less_than":
                query = query.filter(Command.priority_level < value)
    
    # Apply sorting
    sort_field = sort.get("field", "created_at")
    sort_direction = sort.get("direction", "desc")
    
    if sort_field == "status":
        order_col = Command.status
    elif sort_field == "priority_level":
        order_col = Command.priority_level
    elif sort_field == "progress":
        order_col = Command.progress
    else:
        order_col = Command.created_at
    
    if sort_direction.lower() == "asc":
        query = query.order_by(order_col.asc())
    else:
        query = query.order_by(order_col.desc())
    
    # Count total results
    total_count = query.count()
    
    # Apply pagination
    limit = pagination.get("limit", 10)
    offset = pagination.get("offset", 0)
    results = query.limit(limit).offset(offset).all()
    
    # Format results
    formatted_results = [
        {
            "id": command.id,
            "command_uuid": command.command_uuid,
            "capability_name": command.capability_name,
            "status": command.status,
            "is_background": command.is_background,
            "progress": command.progress if hasattr(command, "progress") else None,
            "created_at": command.created_at.isoformat() if command.created_at else None,
            "entity_type": "command"
        }
        for command in results
    ]
    
    return {
        "results": formatted_results,
        "total": total_count,
        "limit": limit,
        "offset": offset
    }

def _advanced_search_attachments(filters, sort, pagination):
    """Perform advanced search on attachments."""
    # Implementation similar to commands but for attachments
    # Base query
    query = Attachment.query.filter_by(user_id=current_user.id)
    
    # Apply filters
    for filter_condition in filters:
        field = filter_condition.get("field")
        operator = filter_condition.get("operator")
        value = filter_condition.get("value")
        
        if not all([field, operator, value is not None]):
            continue
        
        if field == "filename":
            if operator == "contains":
                query = query.filter(Attachment.filename.ilike(f"%{value}%"))
            elif operator == "equals":
                query = query.filter(Attachment.filename == value)
        elif field == "file_type":
            if operator == "contains":
                query = query.filter(Attachment.file_type.ilike(f"%{value}%"))
            elif operator == "equals":
                query = query.filter(Attachment.file_type == value)
        elif field == "is_public":
            query = query.filter(Attachment.is_public == value)
        elif field == "file_size":
            if operator == "equals":
                query = query.filter(Attachment.file_size == value)
            elif operator == "greater_than":
                query = query.filter(Attachment.file_size > value)
            elif operator == "less_than":
                query = query.filter(Attachment.file_size < value)
    
    # Apply sorting
    sort_field = sort.get("field", "created_at")
    sort_direction = sort.get("direction", "desc")
    
    if sort_field == "filename":
        order_col = Attachment.filename
    elif sort_field == "file_type":
        order_col = Attachment.file_type
    elif sort_field == "file_size":
        order_col = Attachment.file_size
    else:
        order_col = Attachment.created_at
    
    if sort_direction.lower() == "asc":
        query = query.order_by(order_col.asc())
    else:
        query = query.order_by(order_col.desc())
    
    # Count total results
    total_count = query.count()
    
    # Apply pagination
    limit = pagination.get("limit", 10)
    offset = pagination.get("offset", 0)
    results = query.limit(limit).offset(offset).all()
    
    # Format results
    formatted_results = [
        {
            "id": attachment.id,
            "attachment_uuid": attachment.attachment_uuid,
            "filename": attachment.filename,
            "file_type": attachment.file_type,
            "file_size": attachment.file_size,
            "is_public": attachment.is_public,
            "created_at": attachment.created_at.isoformat() if attachment.created_at else None,
            "entity_type": "attachment"
        }
        for attachment in results
    ]
    
    return {
        "results": formatted_results,
        "total": total_count,
        "limit": limit,
        "offset": offset
    }

def _advanced_search_scheduled_tasks(filters, sort, pagination):
    """Perform advanced search on scheduled tasks."""
    # Implementation similar to other entities but for scheduled tasks
    # Base query
    query = ScheduledTask.query.filter_by(user_id=current_user.id)
    
    # Apply filters
    for filter_condition in filters:
        field = filter_condition.get("field")
        operator = filter_condition.get("operator")
        value = filter_condition.get("value")
        
        if not all([field, operator, value is not None]):
            continue
        
        if field == "name":
            if operator == "contains":
                query = query.filter(ScheduledTask.name.ilike(f"%{value}%"))
            elif operator == "equals":
                query = query.filter(ScheduledTask.name == value)
        elif field == "capability_name":
            if operator == "contains":
                query = query.filter(ScheduledTask.capability_name.ilike(f"%{value}%"))
            elif operator == "equals":
                query = query.filter(ScheduledTask.capability_name == value)
        elif field == "schedule_type":
            if operator == "equals":
                query = query.filter(ScheduledTask.schedule_type == value)
        elif field == "is_active":
            query = query.filter(ScheduledTask.is_active == value)
        elif field == "is_recurring":
            query = query.filter(ScheduledTask.is_recurring == value)
    
    # Apply sorting
    sort_field = sort.get("field", "created_at")
    sort_direction = sort.get("direction", "desc")
    
    if sort_field == "name":
        order_col = ScheduledTask.name
    elif sort_field == "schedule_type":
        order_col = ScheduledTask.schedule_type
    elif sort_field == "next_run":
        order_col = ScheduledTask.next_run
    else:
        order_col = ScheduledTask.created_at
    
    if sort_direction.lower() == "asc":
        query = query.order_by(order_col.asc())
    else:
        query = query.order_by(order_col.desc())
    
    # Count total results
    total_count = query.count()
    
    # Apply pagination
    limit = pagination.get("limit", 10)
    offset = pagination.get("offset", 0)
    results = query.limit(limit).offset(offset).all()
    
    # Format results
    formatted_results = [
        {
            "id": task.id,
            "task_uuid": task.task_uuid,
            "name": task.name,
            "capability_name": task.capability_name,
            "schedule_type": task.schedule_type,
            "is_active": task.is_active,
            "next_run": task.next_run.isoformat() if task.next_run else None,
            "entity_type": "scheduled_task"
        }
        for task in results
    ]
    
    return {
        "results": formatted_results,
        "total": total_count,
        "limit": limit,
        "offset": offset
    }

@search_bp.route("/stats", methods=["GET"])
@login_required
def entity_stats():
    """
    Get statistics about entities in the Cross-Device system.
    
    Returns:
        JSON response with entity counts and statistics
    """
    # Count devices
    device_count = Device.query.filter_by(user_id=current_user.id).count()
    active_device_count = Device.query.filter_by(user_id=current_user.id, is_active=True).count()
    
    # Count commands
    total_commands = Command.query.filter_by(user_id=current_user.id).count()
    background_commands = Command.query.filter_by(user_id=current_user.id, is_background=True).count()
    completed_commands = Command.query.filter_by(user_id=current_user.id, status="completed").count()
    
    # Count attachments
    total_attachments = Attachment.query.filter_by(user_id=current_user.id).count()
    public_attachments = Attachment.query.filter_by(user_id=current_user.id, is_public=True).count()
    
    # Calculate total file size
    total_file_size = db.session.query(func.sum(Attachment.file_size)) \
        .filter(Attachment.user_id == current_user.id) \
        .scalar() or 0
    
    # Count scheduled tasks
    total_tasks = ScheduledTask.query.filter_by(user_id=current_user.id).count()
    active_tasks = ScheduledTask.query.filter_by(user_id=current_user.id, is_active=True).count()
    
    # Get device types distribution
    device_types = db.session.query(
        Device.device_type, 
        func.count(Device.id).label("count")
    ).filter(
        Device.user_id == current_user.id
    ).group_by(
        Device.device_type
    ).all()
    
    device_type_distribution = {dt[0]: dt[1] for dt in device_types}
    
    return jsonify({
        "devices": {
            "total": device_count,
            "active": active_device_count,
            "type_distribution": device_type_distribution
        },
        "commands": {
            "total": total_commands,
            "background": background_commands,
            "completed": completed_commands
        },
        "attachments": {
            "total": total_attachments,
            "public": public_attachments,
            "total_size_bytes": total_file_size
        },
        "scheduled_tasks": {
            "total": total_tasks,
            "active": active_tasks
        }
    })
