#!/usr/bin/env python
"""
Database Migration Script: Create config_entries table and migrate API keys from .env

This script:
1. Creates the config_entries table if it doesn't exist
2. Migrates API keys from .env file to the database
3. Preserves existing functionality while prioritizing database storage
"""

import os
import sys
import sqlite3
import logging
from pathlib import Path
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('migrations')

# Add the project root directory to the Python path
base_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(base_dir))

# Load environment variables from .env file
env_file = base_dir / '.env'
if env_file.exists():
    load_dotenv(env_file)
    logger.info(f"Loaded environment variables from {env_file}")

# Connect to SQLite database
db_path = base_dir / 'athena.db'
if not db_path.exists():
    logger.error(f"Database file not found at {db_path}")
    sys.exit(1)

logger.info(f"Connecting to database at {db_path}")
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

def create_config_entries_table():
    """Create the config_entries table if it doesn't exist."""
    try:
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS config_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key TEXT UNIQUE NOT NULL,
            value TEXT,
            user_id INTEGER,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )
        """)
        conn.commit()
        logger.info("Created config_entries table")
        
        # Create index for better performance
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_config_entries_key ON config_entries(key)")
        conn.commit()
        logger.info("Created index on config_entries(key)")
        
        return True
    except Exception as e:
        logger.error(f"Error creating config_entries table: {e}")
        return False

def migrate_api_keys():
    """Migrate API keys from .env file to the database."""
    # Define the API keys to migrate (key in .env, key in database)
    api_keys_to_migrate = [
        ('OPENAI_API_KEY', 'openai_api_key'),
        ('SMITHERY_API_KEY', 'smithery_api_key'),
        ('ANTHROPIC_API_KEY', 'anthropic_api_key'),
        ('GOOGLE_API_KEY', 'google_api_key'),
        ('AZURE_OPENAI_API_KEY', 'azure_openai_api_key'),
        ('AZURE_OPENAI_ENDPOINT', 'azure_openai_endpoint')
    ]
    
    migrated_count = 0
    
    for env_key, db_key in api_keys_to_migrate:
        value = os.environ.get(env_key)
        if value:
            # Check if key already exists in database
            cursor.execute("SELECT id FROM config_entries WHERE key = ?", (db_key,))
            existing = cursor.fetchone()
            
            if existing:
                # Update existing entry
                cursor.execute("UPDATE config_entries SET value = ? WHERE key = ?", (value, db_key))
                logger.info(f"Updated existing config entry for {db_key}")
            else:
                # Insert new entry
                cursor.execute("INSERT INTO config_entries (key, value, user_id) VALUES (?, ?, NULL)", (db_key, value))
                logger.info(f"Added new config entry for {db_key}")
            
            migrated_count += 1
    
    # Also migrate Spotify credentials which might be in the .env
    spotify_keys = [
        ('CLIENT_ID', 'spotify_client_id'),
        ('CLIENT_SECRET', 'spotify_client_secret'),
        ('REDIRECT_URI', 'spotify_redirect_uri'),
        ('SPOTIFY_USERNAME', 'spotify_username')
    ]
    
    for env_key, db_key in spotify_keys:
        value = os.environ.get(env_key)
        if value and value.strip():
            cursor.execute("SELECT id FROM config_entries WHERE key = ?", (db_key,))
            existing = cursor.fetchone()
            
            if existing:
                cursor.execute("UPDATE config_entries SET value = ? WHERE key = ?", (value, db_key))
                logger.info(f"Updated existing config entry for {db_key}")
            else:
                cursor.execute("INSERT INTO config_entries (key, value, user_id) VALUES (?, ?, NULL)", (db_key, value))
                logger.info(f"Added new config entry for {db_key}")
            
            migrated_count += 1
    
    # Set the initial fallback model
    cursor.execute("SELECT id FROM config_entries WHERE key = ?", ('default_model',))
    if not cursor.fetchone():
        cursor.execute("INSERT INTO config_entries (key, value, user_id) VALUES (?, ?, NULL)", ('default_model', 'gpt-4o'))
        logger.info("Added default model setting")
    
    conn.commit()
    return migrated_count

def display_config_entries():
    """Display all config entries in the database."""
    cursor.execute("SELECT key, value FROM config_entries")
    entries = cursor.fetchall()
    
    if entries:
        logger.info("Current configuration entries:")
        for key, value in entries:
            # Mask most of the value for sensitive keys
            if 'api_key' in key.lower() or 'secret' in key.lower():
                if value and len(value) > 8:
                    masked_value = f"{value[:4]}...{value[-4:]}"
                else:
                    masked_value = "(masked)"
                logger.info(f"  {key}: {masked_value}")
            else:
                logger.info(f"  {key}: {value}")
    else:
        logger.info("No configuration entries found")

if __name__ == "__main__":
    logger.info("Starting config_entries migration")
    
    if create_config_entries_table():
        migrated = migrate_api_keys()
        logger.info(f"Migrated {migrated} API keys from .env to database")
        
        display_config_entries()
        
        logger.info("""
        Migration complete! API keys have been migrated to the database.
        
        Next steps:
        1. Review the code in AthenaConfig.load() to ensure it prioritizes database values
        2. Make sure the Athena class in athena.py also prioritizes database values
        3. Restart the AthenaCore server to apply changes
        """)
    else:
        logger.error("Migration failed")
    
    conn.close()
