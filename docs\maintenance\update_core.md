# Athena Core Roadmap

This document outlines the planned features and enhancements for the Athena Core system.

## Completed Features

### Core Infrastructure
- ✅ Basic chat interface
- ✅ Session management
- ✅ API key management
- ✅ History tracking
- ✅ Basic prompt engineering
- ✅ Improved chat bubble system
- ✅ Support for extended context windows
- ✅ Vector database integration
- ✅ SQLite database for user data
- ✅ Response streaming
- ✅ Model parameter adjustment support
- ✅ Error handling for rate limits and quota
- ✅ Conversation history management
- ✅ Multimodal support (if model supports it)
- ✅ Persistent Chat Functionality
  - ✅ Implement database models for conversations and messages
  - ✅ Create API endpoints for conversation management
  - ✅ Update chat interface to support conversation history
  - ✅ Add conversation sidebar for easy navigation
  - ✅ Enable renaming and deleting conversations

### API Infrastructure
- ✅ Basic API endpoints
- ✅ OpenAI API integration
- ✅ Google Gemini API integration
- ✅ Anthropic Claude API integration
- ✅ Agent design framework
- ✅ Custom agent deployment
- ✅ Conversation persistence
- ✅ Smithery Registry integration

### MCP Integration
- ✅ Implement MCP server integration
  - ✅ Integrate Smithery Registry API for MCP server discovery
  - ✅ Add support for WebSocket connections to MCP servers
  - ✅ Add ability to route chat conversations to MCP servers
  - ✅ Enhance UI to display available MCP servers and configurations
  - ✅ Add support for configuring MCP server parameters
  - ✅ Implement efficient server status checking with caching

### Cross-Device API System
- ✅ Implement central API communication layer
  - ✅ Design RESTful API with standardized endpoints
    - ✅ `/api/devices` - Device registration and management
    - ✅ `/api/commands` - Command submission and routing
    - ✅ `/api/status` - Command and device status checking
    - ✅ `/api/capabilities` - Device capability registration and discovery
  - ✅ Implement JWT authentication system with device-specific tokens
    - ✅ Ensure tokens include device ID, user ID, and capability scopes
    - ✅ Add token refresh mechanism with appropriate expiration policies
    - ✅ Implement revocation system for compromised devices
  - ✅ Create command routing infrastructure
    - ✅ Design universal command schema with target device specification
    - ✅ Implement priority levels for commands (immediate, scheduled, background)
    - ✅ Add command validation and permission checking
    - ✅ Create command history and audit logging
  - ✅ Develop device registration protocol
    - ✅ Automatic capability discovery during registration
    - ✅ Periodic heartbeat system to track device online status
    - ✅ Device metadata storage (OS, version, installed capabilities)
    - ✅ Support for device groups and targeting by capability
  - ✅ Enhanced database models for device management
    - ✅ Device model with capability tracking
    - ✅ Command model with routing information
    - ✅ Device capability model for feature discovery

- ✅ Background Task Processing System
  - ✅ Enhanced Command model with background execution support
  - ✅ Parent-child task relationships for complex workflows
  - ✅ Task priority management (1-10 scale plus categorical)
  - ✅ Progress tracking for long-running operations
  - ✅ Task runtime limits and resource controls
  - ✅ Background task execution engine

- ✅ Attachment Handling System
  - ✅ Attachment model for file metadata and tracking
  - ✅ Secure file storage with access control
  - ✅ File integrity verification with hashing
  - ✅ Command and device attachment associations
  - ✅ File transfer service implementation

- ✅ Semi-Autonomous Operation System
  - ✅ ScheduledTask model for automated operations
  - ✅ Multiple scheduling methods (interval, cron, event, trigger)
  - ✅ Conditional execution based on triggers
  - ✅ Target device selection by capability
  - ✅ Task scheduler service implementation

- ✅ Cross-Device Search API System
  - ✅ Unified search across devices, commands, attachments, and tasks
  - ✅ Advanced filtering with complex query support
  - ✅ System statistics and monitoring endpoints
  - ✅ Integration with Developer API documentation

## Planned Features

### Core Enhancements
- 🚀 Improved Settings UI
  - 🚀 Unified settings panel
  - 🚀 Model-specific configurations
  - 🚀 Customizable UI themes
- 🚀 Enhanced Conversation Management
  - 🚀 Full conversation search
  - 🚀 Conversation tagging
  - 🚀 Conversation export/import
  - 🚀 Conversation sharing
- 🚀 Add limit on reasoning for reasoning models 
- 🚀 Support for additional operating systems
  - 🚀 Linux command control
  - 🚀 Mac support

### LLM Capabilities
- 🚀 Multi-model conversations
  - 🚀 Switch models mid-conversation
  - 🚀 Compare model responses
  - 🚀 Ensemble model responses
- 🚀 Fine-tuning interface
  - 🚀 Dataset preparation tools
  - 🚀 Fine-tuning job management
  - 🚀 Fine-tuned model evaluation
- 🚀 Integration with additional model providers (Cohere, Mistral AI, etc.)

### Developer Tools
- 🚀 Advanced Prompt Engineering Tools
  - 🚀 Prompt template library
  - 🚀 Visual prompt builder
  - 🚀 Prompt version control
  - 🚀 A/B testing for prompts
- 🚀 Agent Development Framework
  - 🚀 Visual agent designer
  - 🚀 Agent testing environment
  - 🚀 Agent marketplace integration
  - 🚀 Agent versioning

### Custom Personas
- 🚀 Implement custom personas for users to interact with

### MCP Integration
- 🚀 Enable Athena to generate MCPs if possible
- 🚀 Add capability to scrape documentation for the MCP server

### Security and Privacy Enhancements
- 🚀 End-to-end encryption for conversations
- 🚀 Local model support for privacy-sensitive operations
- 🚀 Enhanced API key management
- 🚀 Role-based access control
- 🚀 Data retention policies and management
- 🚀 Limit Athena's system write access
- 🚀 Require prompt for permission when accessing outside of root directory

### Performance Optimizations
- 🚀 Cloud sync options
- 🚀 Performance optimizations for resource usage
- 🚀 External service integration
  - 🚀 Create UI prompts for Spotify API credentials instead of using .env file
  - 🚀 Consider MCP integration options for external services
  - 🚀 Gradually eliminate reliance on .env files for improved security
