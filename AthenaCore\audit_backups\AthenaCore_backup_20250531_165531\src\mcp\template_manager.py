import os
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

from src.models import User, db, MCPServerTemplate
from src.utils.config import AthenaConfig

logger = logging.getLogger(__name__)

class MCPTemplateManager:
    """Manager for MCP server templates."""
    
    def __init__(self, config: AthenaConfig):
        """Initialize the template manager.
        
        Args:
            config: Athena configuration
        """
        self.config = config
        # Set default template directory
        default_template_dir = os.path.join(os.path.dirname(os.path.dirname(
                                        os.path.dirname(__file__))), 'server_templates')
        
        # Use the template directory from config if it exists, otherwise use default
        self.template_dir = Path(default_template_dir)
        
        # Ensure template directory exists
        os.makedirs(self.template_dir, exist_ok=True)
        
    def get_system_templates(self) -> List[Dict[str, Any]]:
        """Get all system templates.
        
        Returns:
            List of system templates
        """
        templates = []
        
        try:
            # Get all JSON files in template directory
            template_files = list(self.template_dir.glob('*.json'))
            
            for template_file in template_files:
                with open(template_file, 'r') as f:
                    template_data = json.load(f)
                    
                    # Add template info
                    templates.append({
                        'id': template_file.stem,
                        'name': template_data.get('name', 'Unnamed Template'),
                        'description': template_data.get('description', ''),
                        'type': 'system'
                    })
        except Exception as e:
            logger.error(f"Error loading system templates: {str(e)}")
        
        return templates
    
    def get_user_templates(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all templates for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            List of user templates
        """
        templates = []
        
        try:
            # Get templates from database
            db_templates = MCPServerTemplate.query.filter_by(user_id=user_id).all()
            
            for template in db_templates:
                templates.append({
                    'id': template.id,
                    'name': template.name,
                    'description': template.description,
                    'created_at': template.created_at.isoformat(),
                    'updated_at': template.updated_at.isoformat(),
                    'type': 'user'
                })
        except Exception as e:
            logger.error(f"Error loading user templates: {str(e)}")
        
        return templates
    
    def get_template(self, template_id: str, user_id: Optional[int] = None) -> Optional[Dict[str, Any]]:
        """Get a template by ID.
        
        Args:
            template_id: Template ID or filename (for system templates)
            user_id: User ID (for user templates)
            
        Returns:
            Template data or None if not found
        """
        # First try to load as a system template
        try:
            template_path = self.template_dir / f"{template_id}.json"
            if template_path.exists():
                with open(template_path, 'r') as f:
                    template_data = json.load(f)
                    template_data['id'] = template_id
                    template_data['type'] = 'system'
                    return template_data
        except Exception as e:
            logger.error(f"Error loading system template: {str(e)}")
        
        # If not found or error, try user template
        if user_id is not None:
            try:
                template = MCPServerTemplate.query.filter_by(id=template_id, user_id=user_id).first()
                if template:
                    # Parse server definition from JSON string
                    template_data = json.loads(template.server_definition)
                    # Add template metadata
                    template_data['id'] = template.id
                    template_data['name'] = template.name
                    template_data['description'] = template.description
                    template_data['type'] = 'user'
                    template_data['created_at'] = template.created_at.isoformat()
                    template_data['updated_at'] = template.updated_at.isoformat()
                    return template_data
            except Exception as e:
                logger.error(f"Error loading user template: {str(e)}")
        
        return None
    
    def create_template(self, user_id: int, name: str, description: str, 
                       server_definition: Dict[str, Any]) -> Tuple[bool, str, Optional[int]]:
        """Create a new user template.
        
        Args:
            user_id: User ID
            name: Template name
            description: Template description
            server_definition: Server definition
            
        Returns:
            Tuple of (success, message, template_id)
        """
        try:
            # Create template in database
            template = MCPServerTemplate(
                user_id=user_id,
                name=name,
                description=description,
                server_definition=json.dumps(server_definition)
            )
            
            db.session.add(template)
            db.session.commit()
            
            return True, "Template created successfully", template.id
        except Exception as e:
            logger.error(f"Error creating template: {str(e)}")
            db.session.rollback()
            return False, f"Error creating template: {str(e)}", None
    
    def update_template(self, template_id: int, user_id: int, name: Optional[str] = None, 
                      description: Optional[str] = None, 
                      server_definition: Optional[Dict[str, Any]] = None) -> Tuple[bool, str]:
        """Update a user template.
        
        Args:
            template_id: Template ID
            user_id: User ID
            name: New template name (optional)
            description: New template description (optional)
            server_definition: New server definition (optional)
            
        Returns:
            Tuple of (success, message)
        """
        try:
            # Get template from database
            template = MCPServerTemplate.query.filter_by(id=template_id, user_id=user_id).first()
            
            if not template:
                return False, "Template not found"
            
            # Update fields
            if name is not None:
                template.name = name
            
            if description is not None:
                template.description = description
            
            if server_definition is not None:
                template.server_definition = json.dumps(server_definition)
            
            db.session.commit()
            
            return True, "Template updated successfully"
        except Exception as e:
            logger.error(f"Error updating template: {str(e)}")
            db.session.rollback()
            return False, f"Error updating template: {str(e)}"
    
    def delete_template(self, template_id: int, user_id: int) -> Tuple[bool, str]:
        """Delete a user template.
        
        Args:
            template_id: Template ID
            user_id: User ID
            
        Returns:
            Tuple of (success, message)
        """
        try:
            # Get template from database
            template = MCPServerTemplate.query.filter_by(id=template_id, user_id=user_id).first()
            
            if not template:
                return False, "Template not found"
            
            # Delete template
            db.session.delete(template)
            db.session.commit()
            
            return True, "Template deleted successfully"
        except Exception as e:
            logger.error(f"Error deleting template: {str(e)}")
            db.session.rollback()
            return False, f"Error deleting template: {str(e)}"
