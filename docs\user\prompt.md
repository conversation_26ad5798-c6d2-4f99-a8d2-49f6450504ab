# Athena Update Guidelines

## Roadmap Update Template

When adding new features or enhancements to the Athena roadmap in `update.md`, follow this format to maintain consistency.

```markdown
### [Feature Category Name]
- 🚀 [Main Feature]
  - 🚀 [Subfeature 1]
    - [Detail 1]
    - [Detail 2]
    - [Detail 3]
    - [Detail 4]
  - 🚀 [Subfeature 2]
    - [Detail 1]
    - [Detail 2]
    - [Detail 3]
    - [Detail 4]
  - 🚀 [Subfeature 3]
    - [Detail 1]
    - [Detail 2]
    - [Detail 3]
```

## Status Indicators

Use these emoji indicators to show the current status of each feature:

- 🚀 = Planned feature (not started)
- 🔄 = In progress
- ✅ = Completed feature
- ❌ = Canceled or postponed feature

## Hierarchical Structure

Always maintain the hierarchical structure:
1. Top level: Feature category with ### heading
2. Second level: Main features with bullet points
3. Third level: Subfeatures with indented bullets
4. Fourth level: Implementation details with double-indented bullets

## Guidelines for Adding New Features

1. **Placement**: Add new feature categories after existing ones, before the "Other Enhancements" section. If enhancing an existing category, maintain alphabetical order where possible.

2. **Detail Level**: Include sufficient technical detail so implementation requirements are clear:
   - API endpoints with descriptions
   - Authentication requirements
   - Data structures
   - Integration points
   - UI/UX considerations

3. **Consistency**: Match the writing style and technical depth of existing entries.

4. **Formatting**: Preserve the markdown formatting exactly as shown in existing entries.

## Example Addition Format

When adding a new feature like the Cross-Device API System, use this format:

```markdown
### [New Feature Category]
- 🚀 [Main Component 1]
  - 🚀 [Subcomponent 1.1]
    - [Technical specification 1.1.1]
    - [Technical specification 1.1.2]
  - 🚀 [Subcomponent 1.2]
    - [Technical specification 1.2.1]
    - [Technical specification 1.2.2]

- 🚀 [Main Component 2]
  - 🚀 [Subcomponent 2.1]
    - [Technical specification 2.1.1]
    - [Technical specification 2.1.2]
  - 🚀 [Subcomponent 2.2]
    - [Technical specification 2.2.1]
    - [Technical specification 2.2.2]
```

## Updating Feature Status

When updating the status of a feature:
1. Change the emoji indicator (🚀 → 🔄 → ✅)
2. Maintain all existing detail points
3. Add any new insights or implementation details discovered during development
4. Do not remove detailed descriptions once a feature is complete

## Release Notes Format

When creating release notes for completed features:

```markdown
## Release X.Y.Z - [Date]

### New Features
- **[Feature Name]**: [Brief description of the feature]
  - [Key capability 1]
  - [Key capability 2]

### Improvements
- **[Area]**: [Description of improvement]

### Bug Fixes
- **[Component]**: [Description of bug fix]

### Known Issues
- [Description of known issue]
```
