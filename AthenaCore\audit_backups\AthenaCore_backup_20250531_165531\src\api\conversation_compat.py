"""
Compatibility API for conversation endpoints (singular form).
This provides compatibility for any frontend code still using /api/conversation/ instead of /api/conversations/
"""

import logging
from flask import Blueprint, redirect, request, url_for

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger("conversation_compat_api")

# Create blueprint for singular form (for compatibility)
conversation_compat_bp = Blueprint("conversation_compat", __name__, url_prefix="/api/conversation")

@conversation_compat_bp.route("/<path:path>", methods=["GET", "POST", "PUT", "PATCH", "DELETE"])
def redirect_to_plural(path):
    """
    Redirect any requests to /api/conversation/* to /api/conversations/*
    This ensures backward compatibility with any frontend code still using the singular form.
    """
    logger.info(f"Redirecting from singular /api/conversation/{path} to plural form")
    # Construct the target URL with the same path and query parameters
    target_url = f"/api/conversations/{path}"
    if request.query_string:
        target_url = f"{target_url}?{request.query_string.decode('utf-8')}"
    
    # Redirect with the same method
    return redirect(target_url)

@conversation_compat_bp.route("", methods=["GET", "POST"])
def redirect_root():
    """
    Redirect requests to /api/conversation to /api/conversations
    """
    logger.info("Redirecting from singular /api/conversation to plural form")
    target_url = "/api/conversations"
    if request.query_string:
        target_url = f"{target_url}?{request.query_string.decode('utf-8')}"
    
    return redirect(target_url)
