#!/usr/bin/env python
"""
Prepare Files for Removal <PERSON>t

This script prepares obsolete files for eventual removal by:
1. Adding clear deprecation notices at the top of the file
2. Preserving minimum functionality through compatibility layers
3. Updating imports to use the new locations
4. Adding logging to track usage of deprecated components

Usage:
    python scripts/prepare_for_removal.py

This script helps with the gradual removal of obsolete files while maintaining
backward compatibility during the transition period.
"""

import os
import sys
import re
from pathlib import Path
import datetime

# Define files to prepare for removal
FILES_TO_PREPARE = [
    {
        "path": "src/login/models.py",
        "replacement": "src/models",
        "warning_level": "high",
        "retain_functions": False
    },
    {
        "path": "src/login/views.py",
        "replacement": "src/controllers/auth_controller.py",
        "warning_level": "medium",
        "retain_functions": True
    }
]

# Templates for different warning levels
WARNING_TEMPLATES = {
    "high": """
# ===========================================================================
# CRITICAL DEPRECATION NOTICE
# ===========================================================================
# This file is DEPRECATED and will be REMOVED in the next release.
# All functionality has been migrated to: {replacement}
#
# Please update your imports immediately to use the new location.
# Example:
#   OLD: from {old_path} import {examples}
#   NEW: from {new_path} import {examples}
#
# This file currently maintains compatibility by forwarding imports,
# but this compatibility layer will be removed in a future version.
# ===========================================================================
""",
    "medium": """
# ===========================================================================
# DEPRECATION NOTICE
# ===========================================================================
# This file is deprecated and scheduled for removal in a future release.
# Functionality has been migrated to: {replacement}
#
# Please update your imports to use the new location.
# Example:
#   OLD: from {old_path} import {examples}
#   NEW: from {new_path} import {examples}
# ===========================================================================
"""
}

def get_imports(file_path):
    """Get the import section from a file."""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Look for import statements at the beginning of the file
    imports = []
    for line in content.split('\n'):
        if line.startswith('import ') or line.startswith('from '):
            imports.append(line)
        elif imports and line.strip() == '':
            continue
        elif imports:
            break
    
    return '\n'.join(imports)

def get_examples(file_path):
    """Get example imports for the deprecation notice."""
    # Extract class names from the file for example purposes
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Look for class definitions
    class_pattern = r'class\s+([A-Za-z0-9_]+)'
    classes = re.findall(class_pattern, content)
    
    # Return the first few class names as examples
    return ', '.join(classes[:3]) if classes else "ExampleClass"

def prepare_file(file_info):
    """Prepare a file for eventual removal."""
    file_path = file_info["path"]
    replacement = file_info["replacement"]
    warning_level = file_info["warning_level"]
    retain_functions = file_info["retain_functions"]
    
    # Absolute path for the file
    abs_path = os.path.abspath(file_path)
    
    if not os.path.exists(abs_path):
        print(f"File not found: {abs_path}")
        return False
    
    # Read the current content
    with open(abs_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Get the existing imports
    imports = get_imports(abs_path)
    
    # Get example class names
    examples = get_examples(abs_path)
    
    # Create the deprecation notice
    old_path = '.'.join(file_path.replace('/', '.').split('.')[:-1])
    new_path = replacement.replace('/', '.')
    
    warning = WARNING_TEMPLATES[warning_level].format(
        replacement=replacement,
        old_path=old_path,
        new_path=new_path,
        examples=examples
    )
    
    # Prepare the compatibility layer
    if retain_functions:
        # Keep the original file but add the deprecation notice
        new_content = warning + "\n" + content
    else:
        # Replace with a stub that only provides import forwarding
        compatibility_imports = f"""
import warnings
import logging
from {new_path} import *

# Log usage of deprecated module
logging.getLogger('athena.deprecation').warning(
    f"Deprecated module {old_path} used by {{__name__}}"
)

# Issue a deprecation warning
warnings.warn(
    f"The module {old_path} is deprecated and will be removed in a future version. "
    f"Please update your imports to use {new_path} instead.",
    category=DeprecationWarning,
    stacklevel=2
)
"""
        new_content = warning + compatibility_imports
    
    # Create a backup of the original file
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    backup_path = f"{abs_path}.{timestamp}.bak"
    
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    # Write the new content with deprecation notice
    with open(abs_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"Prepared file for removal: {file_path}")
    print(f"  - Backup created at: {backup_path}")
    print(f"  - Added {warning_level} level deprecation notice")
    print(f"  - {'Retained' if retain_functions else 'Replaced with'} compatibility layer")
    
    return True

def main():
    """Main entry point for the script."""
    print("Prepare Files for Removal Script")
    print("===============================")
    
    # Get the project root directory
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    print(f"Project root: {project_root}")
    
    # Confirm with the user
    print("\nThis script will prepare the following files for eventual removal:")
    for file_info in FILES_TO_PREPARE:
        print(f"  - {file_info['path']} (replace with {file_info['replacement']})")
    
    confirmation = input("\nDo you want to continue? (y/n): ")
    if confirmation.lower() != 'y':
        print("Operation cancelled.")
        return
    
    # Process each file
    success_count = 0
    for file_info in FILES_TO_PREPARE:
        if prepare_file(file_info):
            success_count += 1
    
    print(f"\nPrepared {success_count} of {len(FILES_TO_PREPARE)} files for removal.")
    print("Please review the changes and test the application to ensure compatibility.")

if __name__ == "__main__":
    main()
