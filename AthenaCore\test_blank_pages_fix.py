#!/usr/bin/env python3
"""
Test to verify that the blank pages issue has been fixed.
This test checks that the settings pages display content correctly.
"""

import sys
import os
import requests
import time
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

def test_page_content(url, expected_content_indicators):
    """Test that a page contains expected content indicators."""
    try:
        response = requests.get(url, timeout=10)
        if response.status_code != 200:
            return False, f"HTTP {response.status_code}"
            
        content = response.text.lower()
        
        missing_indicators = []
        for indicator in expected_content_indicators:
            if indicator.lower() not in content:
                missing_indicators.append(indicator)
                
        if missing_indicators:
            return False, f"Missing content: {missing_indicators}"
            
        return True, "Content found"
        
    except Exception as e:
        return False, str(e)

def test_settings_pages():
    """Test that all settings pages display content correctly."""
    print("🔍 Testing Settings Pages Content Display")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test cases: URL and expected content indicators
    test_cases = [
        {
            "name": "AI Connections Page",
            "url": f"{base_url}/settings/connections",
            "expected_content": [
                "AI Provider Connections",
                "Quick Setup",
                "OpenAI",
                "Anthropic", 
                "Google",
                "Azure",
                "Smithery",
                "connectionsList",
                "providerModal"
            ]
        },
        {
            "name": "MCP Servers Page", 
            "url": f"{base_url}/settings/mcp",
            "expected_content": [
                "MCP (Model Context Protocol)",
                "MCP Status",
                "MCP Servers",
                "Smithery Registry",
                "Available Tools",
                "mcp-status",
                "mcp-servers-list"
            ]
        },
        {
            "name": "Admin Panel Page",
            "url": f"{base_url}/settings/admin", 
            "expected_content": [
                "Admin Panel: User Management",
                "Create New User",
                "Existing Users",
                "Username",
                "Email",
                "Role"
            ]
        },
        {
            "name": "API Settings Page",
            "url": f"{base_url}/settings/api",
            "expected_content": [
                "API Configuration",
                "API Keys",
                "Test API"
            ]
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n📄 Testing {test_case['name']}...")
        print(f"   URL: {test_case['url']}")
        
        success, message = test_page_content(test_case['url'], test_case['expected_content'])
        
        if success:
            print(f"   ✅ PASS: {message}")
        else:
            print(f"   ❌ FAIL: {message}")
            
        results.append((test_case['name'], success, message))
        
        # Small delay between requests
        time.sleep(0.5)
    
    return results

def test_javascript_errors():
    """Test for common JavaScript errors that cause blank pages."""
    print("\n🔧 Testing JavaScript Error Prevention")
    print("=" * 60)
    
    # Check that templates use IIFE to prevent variable conflicts
    template_files = [
        "templates/settings/connections.html",
        "templates/settings/mcp.html", 
        "templates/settings/admin.html"
    ]
    
    results = []
    
    for template_file in template_files:
        template_path = project_root / template_file
        if template_path.exists():
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Check for IIFE pattern to prevent variable conflicts
            has_iife = "(function()" in content or "(function (" in content
            has_proper_section_id = 'id="' in content and '-section"' in content
            no_display_none = 'style="display: none;"' not in content
            
            if has_iife and has_proper_section_id and no_display_none:
                print(f"   ✅ {template_file}: Properly structured")
                results.append((template_file, True, "Properly structured"))
            else:
                issues = []
                if not has_iife:
                    issues.append("Missing IIFE")
                if not has_proper_section_id:
                    issues.append("Missing section ID")
                if not no_display_none:
                    issues.append("Has display:none")
                    
                print(f"   ❌ {template_file}: {', '.join(issues)}")
                results.append((template_file, False, ', '.join(issues)))
        else:
            print(f"   ❌ {template_file}: File not found")
            results.append((template_file, False, "File not found"))
    
    return results

def test_api_endpoints():
    """Test that API endpoints are working."""
    print("\n🔌 Testing API Endpoints")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:5000"
    
    endpoints = [
        {
            "name": "Connections API",
            "url": f"{base_url}/api/v1/connections",
            "expected_status": [200, 401, 403]  # May require auth
        },
        {
            "name": "API Keys",
            "url": f"{base_url}/api/keys", 
            "expected_status": [200, 401, 403]
        },
        {
            "name": "API Test",
            "url": f"{base_url}/api/test",
            "expected_status": [200]
        }
    ]
    
    results = []
    
    for endpoint in endpoints:
        try:
            response = requests.get(endpoint['url'], timeout=10)
            
            if response.status_code in endpoint['expected_status']:
                print(f"   ✅ {endpoint['name']}: HTTP {response.status_code}")
                results.append((endpoint['name'], True, f"HTTP {response.status_code}"))
            else:
                print(f"   ❌ {endpoint['name']}: HTTP {response.status_code} (expected {endpoint['expected_status']})")
                results.append((endpoint['name'], False, f"HTTP {response.status_code}"))
                
        except Exception as e:
            print(f"   ❌ {endpoint['name']}: {e}")
            results.append((endpoint['name'], False, str(e)))
            
        time.sleep(0.5)
    
    return results

def run_all_tests():
    """Run all tests and provide a summary."""
    print("🔍 Blank Pages Fix Verification Test")
    print("=" * 60)
    
    # Wait a moment for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(3)
    
    all_results = []
    
    # Test settings pages content
    page_results = test_settings_pages()
    all_results.extend(page_results)
    
    # Test JavaScript error prevention
    js_results = test_javascript_errors()
    all_results.extend(js_results)
    
    # Test API endpoints
    api_results = test_api_endpoints()
    all_results.extend(api_results)
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print("=" * 60)
    
    passed = 0
    total = len(all_results)
    
    for test_name, result, message in all_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if not result:
            print(f"    └─ {message}")
        if result:
            passed += 1
    
    print(f"\n📈 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The blank pages issue has been fixed.")
        print("   ✅ Settings pages display content correctly")
        print("   ✅ JavaScript errors prevented with IIFE")
        print("   ✅ API endpoints responding properly")
        print("   ✅ Provider setup wizards should be functional")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Some issues may remain.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
