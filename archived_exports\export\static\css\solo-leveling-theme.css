/* Solo Leveling Theme for Athena System
   Global theme file to apply the Solo Leveling aesthetic across all pages
*/

:root {
  --sl-dark: #0d1117;
  --sl-darker: #060a10;
  --sl-accent: #4361ee;
  --sl-accent-glow: #4361ee80;
  --sl-accent-light: #0e57d0;
  --sl-text: #e0e0e0;
  --sl-text-secondary: #9ca3af;
  --sl-shadow: #000000;
  --sl-highlight: #3f37c9;
  --sl-danger: #d90429;
}

/* Global Styles */
body {
  background-color: var(--sl-dark);
  color: var(--sl-text);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
  line-height: 1.6;
  height: 100vh;
  overflow: hidden;
  background-image:
    radial-gradient(circle at 20% 35%, #4361ee10 0%, transparent 50%),
    radial-gradient(circle at 75% 44%, #0e57d010 0%, transparent 50%);
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    repeating-linear-gradient(rgba(0, 0, 0, 0.05) 0px, rgba(0, 0, 0, 0.05) 1px, transparent 1px, transparent 50px);
  pointer-events: none;
  z-index: 0;
}

/* Logo Styles */
.logo img {
  display: inline-block !important;
  width: 50px !important;
  height: 50px !important;
  border-radius: 50% !important;
  object-fit: cover !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Floating System Status Button */
.floating-status-button {
  position: fixed;
  right: 20px;
  top: 60px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--sl-darker);
  border: 2px solid var(--sl-accent);
  box-shadow: 0 0 10px var(--sl-accent-glow);
  color: var(--sl-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
  transition: all 0.2s ease;
}

.floating-status-button:hover {
  transform: scale(1.1);
  box-shadow: 0 0 15px var(--sl-accent);
}

.floating-status-button i {
  font-size: 18px;
}

/* System Details Panel */
.system-details-panel {
  position: fixed;
  right: 20px;
  top: 110px;
  width: 300px;
  background-color: var(--sl-darker);
  border: 1px solid var(--sl-accent);
  box-shadow: 0 0 15px var(--sl-accent-glow);
  border-radius: 8px;
  z-index: 1000;
  display: none;
  overflow: hidden;
}

.system-details-panel.active {
  display: block;
  animation: fadeIn 0.3s ease;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid var(--sl-accent);
  background-color: rgba(67, 97, 238, 0.1);
}

.panel-header h3 {
  margin: 0;
  color: var(--sl-accent);
  font-size: 16px;
  font-weight: 600;
}

.close-panel {
  color: var(--sl-text);
  font-size: 20px;
  cursor: pointer;
  transition: color 0.2s ease;
}

.close-panel:hover {
  color: var(--sl-accent);
}

.panel-content {
  padding: 15px;
}

.stat-group {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.stat-label {
  color: var(--sl-text-secondary);
  font-size: 14px;
}

.stat-value {
  color: var(--sl-text);
  font-weight: 500;
  font-size: 14px;
}

.system-status-value {
  color: var(--sl-danger);
  font-weight: bold;
}

.system-status-value.online {
  color: var(--sl-accent);
}

/* Sidebar Styles */
aside,
.sidebar {
  background-color: var(--sl-darker);
  border-right: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

aside::before,
.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, transparent 0%, rgba(67, 97, 238, 0.05) 100%);
  z-index: -1;
}

.logo-container {
  position: relative;
}

.logo-container img {
  filter: drop-shadow(0 0 10px var(--sl-accent-glow));
}

/* Navigation Styles */
nav a,
.nav-link,
.menu-item {
  color: var(--sl-text);
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

nav a:hover,
.nav-link:hover,
.menu-item:hover {
  color: var(--sl-accent);
}

nav a.active,
.nav-link.active,
.menu-item.active {
  color: var(--sl-accent);
  position: relative;
}

nav a.active::before,
.nav-link.active::before,
.menu-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: var(--sl-accent);
  box-shadow: 0 0 10px var(--sl-accent-glow);
  animation: pulseBar 2s infinite;
}

@keyframes pulseBar {
  0% {
    opacity: 0.7;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.7;
  }
}

/* Buttons and Inputs */
button,
.btn,
input[type="submit"] {
  background-color: var(--sl-accent);
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

button:hover,
.btn:hover,
input[type="submit"]:hover {
  background-color: var(--sl-highlight);
  box-shadow: 0 0 15px var(--sl-accent-glow);
}

button::before,
.btn::before,
input[type="submit"]::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(transparent,
      transparent,
      transparent,
      var(--sl-accent-light));
  transform: rotate(45deg);
  opacity: 0.2;
  transition: all 0.5s;
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-150%) rotate(45deg);
  }

  100% {
    transform: translateX(150%) rotate(45deg);
  }
}

/* Form Elements */
input,
textarea,
select {
  background-color: rgba(6, 10, 16, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  padding: 10px 15px;
  color: var(--sl-text);
  transition: all 0.3s ease;
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--sl-accent);
  box-shadow: 0 0 10px var(--sl-accent-glow);
}

/* Cards and Containers */
.card,
.container-card {
  background-color: rgba(13, 17, 23, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.card::before,
.container-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: var(--sl-accent);
}

/* Headers and Titles */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--sl-text);
  position: relative;
}

h1::after,
h2::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 50px;
  height: 2px;
  background: linear-gradient(90deg, var(--sl-accent), transparent);
}

/* Status Elements */
.system-status {
  position: fixed;
  top: 10px;
  right: 10px;
  padding: 5px 10px;
  background-color: var(--sl-darker);
  color: var(--sl-accent);
  border: 1px solid var(--sl-accent);
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
  z-index: 1000;
  box-shadow: 0 0 10px var(--sl-accent-glow);
  display: flex;
  align-items: center;
}

.system-status::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--sl-accent);
  margin-right: 5px;
  animation: pulse 2s infinite;
}

.system-status.offline {
  color: var(--sl-danger);
  border-color: var(--sl-danger);
  box-shadow: 0 0 10px rgba(217, 4, 41, 0.5);
}

.system-status.offline::before {
  background-color: var(--sl-danger);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--sl-danger);
  animation: pulse 2s infinite;
}

.status-indicator.online {
  background-color: var(--sl-accent);
  box-shadow: 0 0 8px var(--sl-accent-glow);
  animation: health-pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.5;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.5;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px var(--sl-accent-glow);
  }

  50% {
    box-shadow: 0 0 15px var(--sl-accent-glow), 0 0 30px var(--sl-accent-glow);
  }

  100% {
    box-shadow: 0 0 5px var(--sl-accent-glow);
  }
}

@keyframes rise-and-glow {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  20% {
    opacity: 1;
  }

  80% {
    opacity: 1;
    transform: translateY(0);
  }

  100% {
    opacity: 0;
    transform: translateY(-20px);
  }
}

@keyframes rise-and-fade {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
  }

  20% {
    opacity: 0.8;
  }

  80% {
    opacity: 0.8;
    transform: translateY(-20px) scale(1.2);
  }

  100% {
    opacity: 0;
    transform: translateY(-40px) scale(1);
  }
}

@keyframes health-pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }

  50% {
    transform: scale(1.2);
    opacity: 1;
  }

  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

/* Table Styles */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
  background-color: rgba(13, 17, 23, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 5px;
  overflow: hidden;
}

thead {
  background-color: rgba(6, 10, 16, 0.8);
}

th {
  padding: 12px 15px;
  text-align: left;
  font-weight: 500;
  font-size: 14px;
  color: var(--sl-accent);
  text-transform: uppercase;
}

td {
  padding: 10px 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

tr:last-child td {
  border-bottom: none;
}

tr:hover {
  background-color: rgba(67, 97, 238, 0.05);
}

/* Chat Elements */
.chat-message {
  margin-bottom: 15px;
  padding: 15px;
  border-radius: 8px;
  position: relative;
  background-color: rgba(13, 17, 23, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.05);
  animation: fadeIn 0.3s ease-in-out;
}

.user-message {
  background-color: rgba(67, 97, 238, 0.1);
  border-left: 3px solid var(--sl-accent);
}

.assistant-message {
  background-color: rgba(114, 9, 183, 0.1);
  border-left: 3px solid var(--sl-accent-light);
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--sl-darker);
}

::-webkit-scrollbar-thumb {
  background: var(--sl-accent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--sl-highlight);
}

/* Solo Leveling Special Elements */
.shadow-soldier {
  position: fixed;
  width: 100px;
  height: 150px;
  background-image: url('/static/img/shadow-soldier.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  z-index: 1000;
  pointer-events: none;
  opacity: 0;
  filter: drop-shadow(0 0 10px var(--sl-accent-glow));
  animation: rise-and-fade 2s ease-out forwards;
}

.glowing-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: transparent;
  border: 2px solid var(--sl-accent);
  box-shadow: 0 0 10px var(--sl-accent-glow);
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 9999;
  transition: width 0.2s, height 0.2s;
}

/* Sidebar Enhancements */
.sidebar {
  background-color: var(--sl-darker);
  border-right: 1px solid var(--sl-accent-glow);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.sidebar-header {
  padding: 15px;
  border-bottom: 1px solid var(--sl-accent-glow);
}

.logo {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.logo img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  box-shadow: 0 0 10px var(--sl-accent-glow);
}

.logo span {
  font-weight: bold;
  letter-spacing: 1px;
  color: var(--sl-accent-light);
}

/* Model selector styling */
#chat-model-select {
  background-color: var(--sl-dark);
  color: var(--sl-text);
  border: 1px solid var(--sl-accent-glow);
  border-radius: 4px;
  padding: 4px 8px;
}

/* New chat button */
.new-chat {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: var(--sl-accent);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 0 10px var(--sl-accent-glow);
  margin-top: 12px;
}

.new-chat:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 15px var(--sl-accent-glow);
}

/* Conversation list */
.conversation-list {
  flex: 0 0 auto;
  padding: 10px 15px;
  overflow-y: auto;
  max-height: 30vh;
  border-bottom: 1px solid var(--sl-accent-glow);
}

.conversation-group h3 {
  font-size: 12px;
  color: var(--sl-text-secondary);
  margin: 5px 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.conversation-group {
  margin-bottom: 10px;
}

.conversation-item {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--sl-text);
  text-decoration: none;
  font-size: 0.9rem;
  margin-bottom: 2px;
}

.conversation-item:hover,
.conversation-item.active {
  background-color: var(--sl-dark);
  color: var(--sl-accent-light);
  box-shadow: 0 0 8px var(--sl-accent-glow);
}

/* Sidebar Navigation */
.sidebar-nav {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
}

.nav-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-radius: 8px;
  color: var(--sl-text);
  text-decoration: none;
  transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
  background-color: var(--sl-dark);
  color: var(--sl-accent-light);
  box-shadow: 0 0 8px var(--sl-accent-glow);
}

.nav-link svg {
  width: 20px;
  height: 20px;
}

/* Solo Leveling Status Container */
.solo-leveling-status {
  display: flex;
  flex-direction: column;
  background-color: rgba(15, 23, 42, 0.6);
  border: 1px solid var(--sl-accent-glow);
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 15px;
  box-shadow: 0 0 10px rgba(67, 97, 238, 0.2);
  transition: all 0.3s ease;
}

.solo-leveling-status:hover {
  box-shadow: 0 0 15px rgba(67, 97, 238, 0.4);
  border-color: var(--sl-accent);
}

.system-rank {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 8px;
}

.rank-label {
  font-size: 12px;
  text-transform: uppercase;
  color: var(--sl-text-secondary);
  margin-bottom: 5px;
  letter-spacing: 1px;
}

.rank {
  font-size: 28px;
  font-weight: 700;
  color: var(--sl-accent);
  text-shadow: 0 0 5px var(--sl-accent-glow);
  margin-bottom: 5px;
}

.progress-container {
  width: 100%;
}

.progress-bar {
  height: 6px;
  background-color: rgba(226, 232, 240, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 5px;
}

.progress {
  height: 100%;
  background: linear-gradient(90deg, var(--sl-accent) 0%, var(--sl-accent-light) 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: var(--sl-text);
  text-align: center;
}

/* Improve User Profile Display */
.user-profile {
  display: flex;
  align-items: center;
  padding: 10px;
  border-top: 1px solid rgba(226, 232, 240, 0.1);
  margin-top: 10px;
}

.user-profile img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--sl-accent);
  box-shadow: 0 0 5px var(--sl-accent-glow);
  margin-right: 10px;
}

.user-info {
  flex: 1;
}

.username {
  font-size: 14px;
  font-weight: 600;
  color: var(--sl-text);
  margin-bottom: 2px;
}

.user-role {
  font-size: 12px;
  color: var(--sl-accent);
}

/* System Status in Sidebar */
.system-status-sidebar {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
  background-color: var(--sl-darker);
  border-radius: 4px;
  padding: 5px 10px;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--sl-danger);
  margin-right: 8px;
  transition: all 0.3s ease;
}

.status-indicator.online {
  background-color: var(--sl-accent);
  box-shadow: 0 0 8px var(--sl-accent-glow);
  animation: health-pulse 2s infinite;
}

.status-indicator.offline {
  background-color: var(--sl-danger);
  box-shadow: 0 0 8px rgba(217, 4, 41, 0.5);
}

/* Original menu item styling with Solo Leveling enhancements */
.menu-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  color: var(--sl-text);
  text-decoration: none;
  border-radius: 8px;
  margin-bottom: 5px;
  transition: all 0.3s ease;
}

.menu-item:hover,
.menu-item.active {
  background-color: var(--sl-dark);
  color: var(--sl-accent-light);
  box-shadow: 0 0 8px var(--sl-accent-glow);
}

.menu-item svg {
  margin-right: 10px;
}

/* Status header in main content */
.system-status-header {
  position: absolute;
  top: 15px;
  right: 15px;
  display: flex;
  align-items: center;
  padding: 5px 10px;
  background-color: var(--sl-darker);
  border: 1px solid var(--sl-accent-glow);
  border-radius: 20px;
  box-shadow: 0 0 10px rgba(67, 97, 238, 0.2);
  pointer-events: none;
}

.system-status-header * {
  pointer-events: auto;
}

.system-status-header .status-text {
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
  color: var(--sl-accent-light);
  margin-left: 5px;
}

/* Enhance theme toggle and logout button */
.theme-toggle-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 7rem;
  padding: 5px;
  background-color: var(--sl-dark);
  border-radius: 20px;
  margin: 5px 0;
}

.theme-icon {
  width: 18px;
  height: 18px;
  color: var(--sl-text-secondary);
}

.theme-toggle {
  width: 50px;
  height: 20px;
  background-color: var(--sl-accent);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 8px var(--sl-accent-glow);
}

.logout-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 8px;
  background-color: var(--sl-dark);
  color: var(--sl-text);
  border: 1px solid var(--sl-accent-glow);
  cursor: pointer;
  transition: all 0.3s ease;
}

.logout-button:hover {
  background-color: var(--sl-accent);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 0 8px var(--sl-accent-glow);
}

/* Shadow Soldier Animation */
.shadow-soldier {
  position: fixed;
  width: 100px;
  height: 150px;
  background-image: url('/static/img/shadow-soldier.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  z-index: 1000;
  pointer-events: none;
  opacity: 0;
  filter: drop-shadow(0 0 10px var(--sl-accent-glow));
  animation: rise-and-fade 2s ease-out forwards;
}

@keyframes rise-and-fade {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
  }

  20% {
    opacity: 0.8;
  }

  80% {
    opacity: 0.8;
    transform: translateY(-20px) scale(1.2);
  }

  100% {
    opacity: 0;
    transform: translateY(-40px) scale(1);
  }
}

/* Ensure sidebar footer elements don't overlap */
.sidebar-footer {
  display: flex;
  flex-direction: column;
  padding: 15px;
  gap: 15px;
  border-top: 1px solid var(--sl-accent-glow);
  margin-top: auto;
}

/* User section styling */
.user-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* User info styling */
.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px;
  border-radius: 8px;
  background-color: var(--sl-dark);
}

.user-info svg {
  width: 20px;
  height: 20px;
  color: var(--sl-accent-light);
}

.user-info span {
  font-size: 14px;
  color: var(--sl-text);
}