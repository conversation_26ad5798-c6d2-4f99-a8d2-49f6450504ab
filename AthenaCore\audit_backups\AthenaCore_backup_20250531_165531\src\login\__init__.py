"""
Login package initialization

This module provides compatibility imports for the login package.
It re-exports symbols from the new module locations to maintain backward compatibility.
"""

# Import from our compatibility modules to maintain backward compatibility
from flask_bcrypt import Bcrypt
bcrypt = Bcrypt()

# Import database instance
from src.models import db

# Import models directly from the main models package
from src.models import User, APIKey, CommandToggle, LLMProviderSetting, LogEntry, UserLog
from src.models.user import Role, UserRole

# Import authentication views and extensions
from src.utils.auth_extensions import login_manager
from src.controllers.auth_controller import auth_bp

# Log a deprecation warning
import warnings
warnings.warn(
    "The module src.login is deprecated. Please update imports to use the new module locations.",
    category=FutureWarning,
    stacklevel=2
)
