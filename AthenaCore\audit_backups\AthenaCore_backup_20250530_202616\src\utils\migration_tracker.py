"""
Migration tracker module for the refactoring process.

This module provides tools to track file migrations during the refactoring process,
ensuring all changes are documented and can be traced. It helps maintain backward
compatibility and enables systematic refactoring.
"""

import json
import os
import datetime
from typing import Dict, List, Optional, Tuple, Any

class MigrationTracker:
    """Class for tracking file migrations during refactoring."""
    
    def __init__(self, migration_file: str = "refactoring_migrations.json"):
        """
        Initialize the migration tracker.
        
        Args:
            migration_file: Path to the migration tracking file
        """
        self.migration_file = migration_file
        self.migrations = self._load_migrations()
    
    def _load_migrations(self) -> Dict[str, Any]:
        """
        Load existing migrations from the migration file.
        
        Returns:
            Dictionary of migration data
        """
        if os.path.exists(self.migration_file):
            try:
                with open(self.migration_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                print(f"Error loading migration file: {e}")
                return self._create_empty_migration_data()
        else:
            return self._create_empty_migration_data()
    
    def _create_empty_migration_data(self) -> Dict[str, Any]:
        """
        Create an empty migration data structure.
        
        Returns:
            Empty migration data dictionary
        """
        return {
            "metadata": {
                "created_at": datetime.datetime.now().isoformat(),
                "updated_at": datetime.datetime.now().isoformat(),
                "version": "1.0.0"
            },
            "file_migrations": {},
            "module_migrations": {},
            "compatibility_layers": [],
            "completed_steps": []
        }
    
    def _save_migrations(self) -> None:
        """Save the migrations to the migration file."""
        # Update the updated_at timestamp
        self.migrations["metadata"]["updated_at"] = datetime.datetime.now().isoformat()
        
        try:
            with open(self.migration_file, 'w', encoding='utf-8') as f:
                json.dump(self.migrations, f, indent=2)
        except IOError as e:
            print(f"Error saving migration file: {e}")
    
    def track_file_migration(self, 
                            original_path: str, 
                            new_path: str, 
                            status: str = "completed",
                            description: Optional[str] = None) -> None:
        """
        Track a file migration from an original path to a new path.
        
        Args:
            original_path: Original file path
            new_path: New file path
            status: Migration status (planned, in-progress, completed)
            description: Optional description of the migration
        """
        # Normalize paths
        original_path = os.path.normpath(original_path)
        new_path = os.path.normpath(new_path)
        
        # Create or update the migration record
        self.migrations["file_migrations"][original_path] = {
            "new_path": new_path,
            "status": status,
            "migrated_at": datetime.datetime.now().isoformat(),
            "description": description or f"Migrated from {original_path} to {new_path}"
        }
        
        # Save the updated migrations
        self._save_migrations()
    
    def track_module_migration(self, 
                              original_module: str, 
                              new_module: str,
                              status: str = "completed",
                              affected_files: Optional[List[str]] = None,
                              description: Optional[str] = None) -> None:
        """
        Track a module migration from an original module to a new module.
        
        Args:
            original_module: Original module name
            new_module: New module name
            status: Migration status (planned, in-progress, completed)
            affected_files: List of files affected by this module migration
            description: Optional description of the migration
        """
        # Create or update the migration record
        self.migrations["module_migrations"][original_module] = {
            "new_module": new_module,
            "status": status,
            "migrated_at": datetime.datetime.now().isoformat(),
            "affected_files": affected_files or [],
            "description": description or f"Migrated from {original_module} to {new_module}"
        }
        
        # Save the updated migrations
        self._save_migrations()
    
    def track_compatibility_layer(self, 
                                 module_path: str, 
                                 original_module: str,
                                 new_module: str,
                                 status: str = "active",
                                 description: Optional[str] = None) -> None:
        """
        Track a compatibility layer created for backward compatibility.
        
        Args:
            module_path: Path to the compatibility layer module
            original_module: Original module name that the layer is mimicking
            new_module: New module name that the layer is redirecting to
            status: Compatibility layer status (active, deprecated, removed)
            description: Optional description of the compatibility layer
        """
        # Create the compatibility layer record
        compat_layer = {
            "module_path": os.path.normpath(module_path),
            "original_module": original_module,
            "new_module": new_module,
            "status": status,
            "created_at": datetime.datetime.now().isoformat(),
            "description": description or f"Compatibility layer for {original_module} -> {new_module}"
        }
        
        # Add the compatibility layer if it doesn't exist, or update it if it does
        for i, layer in enumerate(self.migrations["compatibility_layers"]):
            if layer["module_path"] == os.path.normpath(module_path):
                self.migrations["compatibility_layers"][i] = compat_layer
                break
        else:
            self.migrations["compatibility_layers"].append(compat_layer)
        
        # Save the updated migrations
        self._save_migrations()
    
    def mark_step_completed(self, 
                           step_name: str, 
                           description: Optional[str] = None,
                           artifacts: Optional[List[str]] = None) -> None:
        """
        Mark a refactoring step as completed.
        
        Args:
            step_name: Name of the completed step
            description: Optional description of the step
            artifacts: Optional list of artifacts (files, modules) created or modified
        """
        # Create the completed step record
        step = {
            "step_name": step_name,
            "completed_at": datetime.datetime.now().isoformat(),
            "description": description or f"Completed step: {step_name}",
            "artifacts": artifacts or []
        }
        
        # Add the step to the completed steps list
        self.migrations["completed_steps"].append(step)
        
        # Save the updated migrations
        self._save_migrations()
    
    def get_migration_status(self, original_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Get the migration status for a specific path or all migrations.
        
        Args:
            original_path: Original file path to check, or None for all migrations
            
        Returns:
            Migration status dictionary
        """
        if original_path:
            original_path = os.path.normpath(original_path)
            return self.migrations["file_migrations"].get(original_path, {"status": "not_migrated"})
        else:
            return {
                "total_files": len(self.migrations["file_migrations"]),
                "total_modules": len(self.migrations["module_migrations"]),
                "total_compatibility_layers": len(self.migrations["compatibility_layers"]),
                "total_steps_completed": len(self.migrations["completed_steps"]),
                "migrations": self.migrations
            }
    
    def generate_migration_report(self, output_file: Optional[str] = None) -> str:
        """
        Generate a report of all migrations.
        
        Args:
            output_file: Optional file to write the report to
            
        Returns:
            Migration report as a string
        """
        report = []
        report.append("# Athena Refactoring Migration Report")
        report.append(f"Generated: {datetime.datetime.now().isoformat()}")
        report.append("")
        
        # Add completed steps
        report.append("## Completed Refactoring Steps")
        if self.migrations["completed_steps"]:
            for step in self.migrations["completed_steps"]:
                report.append(f"- **{step['step_name']}** ({step['completed_at']})")
                if step['description']:
                    report.append(f"  - {step['description']}")
                if step['artifacts']:
                    report.append("  - Artifacts:")
                    for artifact in step['artifacts']:
                        report.append(f"    - {artifact}")
        else:
            report.append("*No steps completed yet.*")
        report.append("")
        
        # Add file migrations
        report.append("## File Migrations")
        if self.migrations["file_migrations"]:
            for orig_path, migration in self.migrations["file_migrations"].items():
                report.append(f"- **{orig_path}** → **{migration['new_path']}**")
                report.append(f"  - Status: {migration['status']}")
                report.append(f"  - Migrated: {migration['migrated_at']}")
                if migration['description']:
                    report.append(f"  - {migration['description']}")
        else:
            report.append("*No file migrations recorded yet.*")
        report.append("")
        
        # Add module migrations
        report.append("## Module Migrations")
        if self.migrations["module_migrations"]:
            for orig_module, migration in self.migrations["module_migrations"].items():
                report.append(f"- **{orig_module}** → **{migration['new_module']}**")
                report.append(f"  - Status: {migration['status']}")
                report.append(f"  - Migrated: {migration['migrated_at']}")
                if migration['description']:
                    report.append(f"  - {migration['description']}")
                if migration['affected_files']:
                    report.append("  - Affected files:")
                    for file in migration['affected_files']:
                        report.append(f"    - {file}")
        else:
            report.append("*No module migrations recorded yet.*")
        report.append("")
        
        # Add compatibility layers
        report.append("## Compatibility Layers")
        if self.migrations["compatibility_layers"]:
            for layer in self.migrations["compatibility_layers"]:
                report.append(f"- **{layer['module_path']}**")
                report.append(f"  - Redirects: {layer['original_module']} → {layer['new_module']}")
                report.append(f"  - Status: {layer['status']}")
                report.append(f"  - Created: {layer['created_at']}")
                if layer['description']:
                    report.append(f"  - {layer['description']}")
        else:
            report.append("*No compatibility layers created yet.*")
        
        # Join the report lines
        report_text = "\n".join(report)
        
        # Write the report to a file if requested
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(report_text)
            except IOError as e:
                print(f"Error writing migration report: {e}")
        
        return report_text

# Create a singleton instance
migration_tracker = MigrationTracker()
