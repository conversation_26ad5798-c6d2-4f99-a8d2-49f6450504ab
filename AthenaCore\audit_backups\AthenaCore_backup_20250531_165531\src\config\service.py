"""
Configuration Service for Athena Core.

This module provides a service for accessing application configuration stored in the database,
replacing the need for environment variables and .env files.
"""

import os
import json
import logging
from typing import Any, Dict, Optional, Union, List, cast
from functools import lru_cache

# We'll initially use os.getenv as a fallback during the transition
# Eventually all config will be moved to the database
from flask import current_app
from sqlalchemy.exc import SQLAlchemyError

logger = logging.getLogger(__name__)

class ConfigService:
    """
    Service for managing application configuration from the database.
    
    This class provides methods for retrieving configuration values from the database,
    with fallback to environment variables during the transition period.
    
    It includes caching to reduce database queries and type conversion based on the
    configuration value type.
    """
    
    def __init__(self, app=None):
        """
        Initialize the ConfigService.
        
        Args:
            app: Optional Flask application instance
        """
        self.app = app
        self._cache = {}
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """
        Initialize with a Flask application.
        
        Args:
            app: Flask application instance
        """
        self.app = app
        # Clear cache when initializing with a new app
        self._cache = {}
    
    @lru_cache(maxsize=128)
    def get(self, key: str, default: Any = None, value_type: Optional[str] = None) -> Any:
        """
        Get a configuration value from the database with fallback to environment variables.
        
        Args:
            key: The configuration key
            default: Default value if not found
            value_type: Optional type hint for conversion (string, int, float, bool, json)
            
        Returns:
            The configuration value, converted to the appropriate type
        """
        # Implementation Note: During refactoring, this will first check the database
        # and fall back to environment variables. Eventually, all config will be in the database.
        
        # First, try to get from cache
        if key in self._cache:
            return self._cache[key]
        
        # During transition: First try database, then fall back to environment variables
        try:
            if self.app:
                with self.app.app_context():
                    from src.models import db, Configuration
                    
                    config = Configuration.query.filter_by(key=key).first()
                    if config:
                        value = self._convert_value(config.value, config.value_type)
                        self._cache[key] = value
                        return value
        except (SQLAlchemyError, ImportError) as e:
            logger.warning(f"Error retrieving configuration from database: {str(e)}")
            # Continue to fallback
        
        # Fallback to environment variables during transition
        env_value = os.getenv(key)
        if env_value is not None:
            # Try to convert based on provided value_type or guess from default
            if value_type:
                converted_value = self._convert_value(env_value, value_type)
            else:
                converted_value = self._guess_and_convert(env_value, default)
            
            self._cache[key] = converted_value
            return converted_value
        
        return default
    
    def set(self, key: str, value: Any, value_type: Optional[str] = None, 
            description: str = "", is_sensitive: bool = False) -> bool:
        """
        Set a configuration value in the database.
        
        Args:
            key: The configuration key
            value: The value to store
            value_type: The type of value (string, int, float, bool, json)
            description: Optional description of this configuration
            is_sensitive: Whether this contains sensitive information
            
        Returns:
            True if successful, False otherwise
        """
        if value_type is None:
            value_type = self._guess_type(value)
        
        # Convert to string for storage
        str_value = self._value_to_string(value, value_type)
        
        try:
            if self.app:
                with self.app.app_context():
                    from src.models import db, Configuration
                    
                    # Check if key exists
                    config = Configuration.query.filter_by(key=key).first()
                    if config:
                        config.value = str_value
                        config.value_type = value_type
                        if description:
                            config.description = description
                        config.is_sensitive = is_sensitive
                    else:
                        config = Configuration(
                            key=key,
                            value=str_value,
                            value_type=value_type,
                            description=description,
                            is_sensitive=is_sensitive
                        )
                        db.session.add(config)
                    
                    db.session.commit()
                    
                    # Update cache
                    self._cache[key] = value
                    return True
        except SQLAlchemyError as e:
            logger.error(f"Error setting configuration in database: {str(e)}")
        
        return False
    
    def delete(self, key: str) -> bool:
        """
        Delete a configuration value from the database.
        
        Args:
            key: The configuration key to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if self.app:
                with self.app.app_context():
                    from src.models import db, Configuration
                    
                    config = Configuration.query.filter_by(key=key).first()
                    if config:
                        db.session.delete(config)
                        db.session.commit()
                        
                        # Remove from cache
                        if key in self._cache:
                            del self._cache[key]
                        return True
        except SQLAlchemyError as e:
            logger.error(f"Error deleting configuration from database: {str(e)}")
        
        return False
    
    def clear_cache(self):
        """Clear the configuration cache."""
        self._cache = {}
    
    def _convert_value(self, value: str, value_type: str) -> Any:
        """
        Convert a string value to the specified type.
        
        Args:
            value: The string value to convert
            value_type: The type to convert to (string, int, float, bool, json)
            
        Returns:
            The converted value
        """
        if not value:
            return None
            
        if value_type == 'string' or value_type == 'str':
            return value
        elif value_type == 'int':
            return int(value)
        elif value_type == 'float':
            return float(value)
        elif value_type == 'bool':
            return value.lower() in ('true', 'yes', '1', 'y', 't')
        elif value_type == 'json':
            return json.loads(value)
        else:
            return value
    
    def _guess_and_convert(self, value: str, default: Any) -> Any:
        """
        Guess the type from the default value and convert accordingly.
        
        Args:
            value: The string value to convert
            default: The default value, used to guess the type
            
        Returns:
            The converted value
        """
        if default is None:
            # Try to guess from the value itself
            return self._guess_type_and_convert(value)
        
        # Use the type of the default value
        if isinstance(default, bool):
            return value.lower() in ('true', 'yes', '1', 'y', 't')
        elif isinstance(default, int):
            return int(value)
        elif isinstance(default, float):
            return float(value)
        elif isinstance(default, dict) or isinstance(default, list):
            return json.loads(value)
        else:
            return value
    
    def _guess_type_and_convert(self, value: str) -> Any:
        """
        Guess the type from the value and convert accordingly.
        
        Args:
            value: The string value to convert
            
        Returns:
            The converted value
        """
        # Check if it's a boolean
        if value.lower() in ('true', 'false', 'yes', 'no', 'y', 'n', 't', 'f', '1', '0'):
            return value.lower() in ('true', 'yes', '1', 'y', 't')
        
        # Check if it's a number
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            pass
        
        # Check if it's JSON
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            pass
        
        # Default to string
        return value
    
    def _guess_type(self, value: Any) -> str:
        """
        Guess the type name from a value.
        
        Args:
            value: The value to guess the type of
            
        Returns:
            The type name as a string
        """
        if isinstance(value, bool):
            return 'bool'
        elif isinstance(value, int):
            return 'int'
        elif isinstance(value, float):
            return 'float'
        elif isinstance(value, (dict, list)):
            return 'json'
        else:
            return 'string'
    
    def _value_to_string(self, value: Any, value_type: str) -> str:
        """
        Convert a value to a string based on its type.
        
        Args:
            value: The value to convert
            value_type: The type of the value
            
        Returns:
            The string representation of the value
        """
        if value is None:
            return ''
            
        if value_type == 'json':
            return json.dumps(value)
        else:
            return str(value)


# Create a singleton instance
config_service = ConfigService()
