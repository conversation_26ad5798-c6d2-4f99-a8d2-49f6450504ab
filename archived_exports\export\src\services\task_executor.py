# src/services/task_executor.py

"""
Background task execution service for the Athena Cross-Device API System.

This file implements:
- Background task queue management
- Task execution and monitoring
- Progress tracking and reporting
- Resource management and limiting
"""

import json
import logging
import time
import threading
import queue
import traceback
import heapq
from datetime import datetime, timed<PERSON>ta

from flask import current_app
from sqlalchemy import and_, or_

from src.login.models import db
from src.login.device_models import (
    Device, Command, CommandLog, 
    add_command_log
)

# Import socket service for real-time updates
from src.services.socket import emit_task_update, emit_task_progress, emit_notification

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger("task_executor")

# Task priority queue
task_queue = queue.PriorityQueue()

# Active tasks dictionary {command_uuid: TaskExecutor}
active_tasks = {}

# Task executor lock
executor_lock = threading.RLock()

# Store a reference to the Flask application
flask_app = None

class TaskExecutor:
    """
    Executor for a single background task.
    """
    
    def __init__(self, command):
        """
        Initialize a task executor.
        
        Args:
            command (Command): The command to execute
        """
        self.command = command
        self.command_uuid = command.command_uuid
        self.user_id = command.user_id
        self.thread = None
        self.stop_event = threading.Event()
        self.start_time = None
        self.timeout_timer = None
        
    def start(self):
        """
        Start executing the task in a separate thread.
        """
        if self.thread and self.thread.is_alive():
            logger.warning(f"Task {self.command_uuid} already running")
            return False
            
        # Create a new thread for this task
        self.thread = threading.Thread(target=self._execute_task)
        self.thread.daemon = True  # Allow the thread to be terminated when the main program exits
        
        # Start the thread
        self.thread.start()
        self.start_time = datetime.utcnow()
        
        # Set up timeout if max_runtime is defined
        if self.command.max_runtime > 0:
            self.timeout_timer = threading.Timer(
                self.command.max_runtime, 
                self.cancel, 
                args=["Task exceeded maximum runtime"]
            )
            self.timeout_timer.daemon = True
            self.timeout_timer.start()
            
        logger.info(f"Started task {self.command_uuid} with priority {self.command.priority_level}")
        return True
        
    def cancel(self, reason="Task cancelled"):
        """
        Cancel the task.
        
        Args:
            reason (str): Reason for cancellation
        """
        if self.stop_event.is_set():
            return False
            
        self.stop_event.set()
        
        # Cancel timeout timer if it exists
        if self.timeout_timer:
            self.timeout_timer.cancel()
            
        # Update the command status in the database
        try:
            with current_app.app_context():
                command = Command.query.filter_by(command_uuid=self.command_uuid).first()
                
                if command:
                    command.status = "cancelled"
                    command.completed_at = datetime.utcnow()
                    
                    # Add log entry
                    add_command_log(
                        command=command,
                        status="cancelled",
                        message=reason,
                        log_data={"cancelled_at": datetime.utcnow().isoformat()}
                    )
                    
                    db.session.commit()
                    
                    logger.info(f"Cancelled task {self.command_uuid}: {reason}")
                    
        except Exception as e:
            logger.error(f"Error cancelling task {self.command_uuid}: {str(e)}")
            
        return True
        
    def _update_task_status(self, status, message=None, log_data=None):
        """Update task status and add log entry."""
        # Use the stored Flask app reference
        with flask_app.app_context():
            # Update command status
            self.command = Command.query.get(self.command.id)
            
            if not self.command:
                logger.error(f"Command {self.command_uuid} not found when updating status")
                return
                
            # Add log entry
            if message:
                log_entry = CommandLog(
                    command_id=self.command.id,
                    status=status,
                    message=message,
                    log_data=json.dumps(log_data) if log_data else None
                )
                db.session.add(log_entry)
            
            # Update command
            self.command.status = status
            
            # Set timestamp if applicable
            if status == "delivered":
                self.command.delivered_at = datetime.utcnow()
            elif status in ["completed", "failed", "expired"]:
                self.command.completed_at = datetime.utcnow()
                
            db.session.commit()
            
            # Emit task update event via socket
            emit_task_update(self.command)
            
            # Send a notification for completed or failed tasks
            if status == "completed":
                emit_notification(self.user_id, "Task Completed", 
                                 f"Task '{self.command.capability_name}' has completed.", "success")
            elif status == "failed":
                emit_notification(self.user_id, "Task Failed", 
                                 f"Task '{self.command.capability_name}' has failed.", "error")
            
    def _execute_task(self):
        """
        Execute the task and handle its lifecycle.
        """
        try:
            # Update status to running
            self._update_task_status("running", "Task execution started", {"started_at": datetime.utcnow().isoformat()})
            
            # Simulate task processing
            # In a real implementation, this would handle the actual task execution
            max_steps = 10
            for step in range(1, max_steps + 1):
                # Check if we should stop
                if self.stop_event.is_set():
                    logger.info(f"Task {self.command_uuid} stopped")
                    break
                    
                # Calculate progress
                progress = int((step / max_steps) * 100)
                
                # Update progress
                with flask_app.app_context():
                    command = Command.query.filter_by(command_uuid=self.command_uuid).first()
                    
                    if command:
                        command.progress = progress
                        
                        # Add log entry (only for milestone steps)
                        if step == 1 or step == max_steps or step % 5 == 0:
                            add_command_log(
                                command=command,
                                status="running",
                                message=f"Task progress: {progress}%",
                                log_data={"progress": progress, "step": step}
                            )
                            
                        db.session.commit()
                        
                        logger.debug(f"Task {self.command_uuid} progress: {progress}%")
                
                # Sleep to simulate work (this would be real task processing in production)
                time.sleep(1)
            
            # Complete the task
            with flask_app.app_context():
                command = Command.query.filter_by(command_uuid=self.command_uuid).first()
                
                if command:
                    if not self.stop_event.is_set():
                        # Task completed successfully
                        command.status = "completed"
                        command.progress = 100
                        command.completed_at = datetime.utcnow()
                        
                        # Add log entry
                        add_command_log(
                            command=command,
                            status="completed",
                            message="Task execution completed",
                            log_data={"completed_at": datetime.utcnow().isoformat()}
                        )
                        
                        logger.info(f"Task {self.command_uuid} completed successfully")
                    
                    db.session.commit()
            
        except Exception as e:
            logger.error(f"Error executing task {self.command_uuid}: {str(e)}")
            logger.error(traceback.format_exc())
            
            # Update task status to failed
            try:
                with current_app.app_context():
                    command = Command.query.filter_by(command_uuid=self.command_uuid).first()
                    
                    if command:
                        command.status = "failed"
                        command.completed_at = datetime.utcnow()
                        
                        # Add log entry
                        add_command_log(
                            command=command,
                            status="failed",
                            message=f"Task execution failed: {str(e)}",
                            log_data={
                                "error": str(e),
                                "traceback": traceback.format_exc(),
                                "failed_at": datetime.utcnow().isoformat()
                            }
                        )
                        
                        db.session.commit()
                        
                        logger.info(f"Task {self.command_uuid} failed: {str(e)}")
                        
            except Exception as inner_e:
                logger.error(f"Error updating failed task status: {str(inner_e)}")
                
        finally:
            # Clean up resources
            if self.timeout_timer:
                self.timeout_timer.cancel()
                
            # Remove from active tasks
            with executor_lock:
                if self.command_uuid in active_tasks:
                    del active_tasks[self.command_uuid]
                    logger.debug(f"Removed task {self.command_uuid} from active tasks")


class TaskExecutorService:
    """
    Service for managing and executing background tasks.
    """
    
    def __init__(self, app=None, max_concurrent_tasks=5):
        """
        Initialize the task executor service.
        
        Args:
            app (Flask): Flask application
            max_concurrent_tasks (int): Maximum number of concurrent tasks
        """
        self.app = app
        self.max_concurrent_tasks = max_concurrent_tasks
        self.executor_thread = None
        self.stop_event = threading.Event()
        
        if app is not None:
            self.init_app(app)
            
    def init_app(self, app):
        """
        Initialize the service with a Flask app.
        
        Args:
            app (Flask): Flask application
        """
        self.app = app
        
        # Get configuration from app
        self.max_concurrent_tasks = app.config.get('MAX_CONCURRENT_TASKS', 5)
        
        # Register for signals
        app.teardown_appcontext(self._teardown)
        
        # Create a function to start the service when the first request is received
        @app.route('/api/start-services', methods=['GET'])
        def _start_services():
            self.start()
            return "Services started", 200
        
    def _teardown(self, exception):
        """
        Teardown function called when request context is popped.
        """
        pass
        
    def start(self):
        """
        Start the task executor service.
        """
        global flask_app
        flask_app = self.app
        
        if self.executor_thread and self.executor_thread.is_alive():
            logger.warning("Task executor service already running")
            return
            
        # Reset the stop event
        self.stop_event.clear()
        
        # Create and start the executor thread
        self.executor_thread = threading.Thread(target=self._executor_loop)
        self.executor_thread.daemon = True
        self.executor_thread.start()
        
        logger.info("Task executor service started")
        
    def stop(self):
        """
        Stop the task executor service.
        """
        if not self.executor_thread or not self.executor_thread.is_alive():
            logger.warning("Task executor service not running")
            return
            
        # Set the stop event
        self.stop_event.set()
        
        # Wait for the executor thread to finish (with timeout)
        self.executor_thread.join(timeout=5.0)
        
        # Cancel all active tasks
        with executor_lock:
            for task_uuid, executor in list(active_tasks.items()):
                executor.cancel("Service shutdown")
                
        logger.info("Task executor service stopped")
        
    def _executor_loop(self):
        """
        Main executor loop that processes the task queue.
        """
        logger.info("Task executor loop started")
        
        while not self.stop_event.is_set():
            try:
                # Check if we can start a new task
                with executor_lock:
                    active_task_count = len(active_tasks)
                    
                if active_task_count >= self.max_concurrent_tasks:
                    # Wait and try again
                    time.sleep(1)
                    continue
                
                # Get new tasks from the database
                with self.app.app_context():
                    # Find pending background tasks ordered by priority
                    pending_tasks = Command.query.filter(
                        Command.is_background == True,
                        Command.status.in_(["pending", "delivered"]),
                        or_(
                            Command.expires_at.is_(None),
                            Command.expires_at > datetime.utcnow()
                        )
                    ).order_by(
                        Command.priority_level.desc(),
                        Command.created_at.asc()
                    ).limit(10).all()
                    
                    for task in pending_tasks:
                        # Add task to queue if not already in queue or active
                        if task.command_uuid not in active_tasks and not self._task_in_queue(task.command_uuid):
                            # Add to queue with priority (negative so higher value = higher priority)
                            task_queue.put((-task.priority_level, task.created_at.timestamp(), task))
                            logger.debug(f"Added task {task.command_uuid} to queue")
                
                # Try to get a task from the queue
                try:
                    # Non-blocking get
                    _, _, task = task_queue.get(block=False)
                    
                    # Execute the task
                    with executor_lock:
                        # Check again to make sure we can start a new task
                        if len(active_tasks) < self.max_concurrent_tasks:
                            executor = TaskExecutor(task)
                            active_tasks[task.command_uuid] = executor
                            executor.start()
                        else:
                            # Put the task back in the queue
                            task_queue.put((-task.priority_level, task.created_at.timestamp(), task))
                    
                    # Mark the task as done in the queue
                    task_queue.task_done()
                    
                except queue.Empty:
                    # No tasks in queue, wait for a bit
                    time.sleep(1)
                    
            except Exception as e:
                logger.error(f"Error in task executor loop: {str(e)}")
                logger.error(traceback.format_exc())
                time.sleep(5)  # Wait a bit before retrying after an error
        
        logger.info("Task executor loop stopped")
        
    def _task_in_queue(self, command_uuid):
        """
        Check if a task is already in the queue.
        
        Args:
            command_uuid (str): UUID of the command to check
            
        Returns:
            bool: Whether the task is in the queue
        """
        # This is inefficient but PriorityQueue doesn't provide a way to check contents
        # In production, you would use a more efficient data structure
        with task_queue.mutex:
            for _, _, task in task_queue.queue:
                if task.command_uuid == command_uuid:
                    return True
        return False
        
    def add_task(self, command):
        """
        Add a task to the queue.
        
        Args:
            command (Command): The command to execute
            
        Returns:
            bool: Whether the task was added
        """
        if not command.is_background:
            logger.warning(f"Cannot add non-background task {command.command_uuid} to queue")
            return False
            
        if command.command_uuid in active_tasks or self._task_in_queue(command.command_uuid):
            logger.warning(f"Task {command.command_uuid} already in queue or active")
            return False
            
        # Add to queue with priority
        task_queue.put((-command.priority_level, command.created_at.timestamp(), command))
        logger.info(f"Added task {command.command_uuid} to queue with priority {command.priority_level}")
        return True
        
    def cancel_task(self, command_uuid):
        """
        Cancel a task.
        
        Args:
            command_uuid (str): UUID of the command to cancel
            
        Returns:
            bool: Whether the task was cancelled
        """
        # Check if the task is active
        with executor_lock:
            if command_uuid in active_tasks:
                # Cancel the active task
                return active_tasks[command_uuid].cancel("Cancelled by request")
                
        # Try to remove from queue
        cancelled = False
        with task_queue.mutex:
            for i, (priority, timestamp, task) in enumerate(task_queue.queue):
                if task.command_uuid == command_uuid:
                    # Remove the task
                    task_queue.queue.pop(i)
                    heapq.heapify(task_queue.queue)
                    cancelled = True
                    break
                    
        if cancelled:
            logger.info(f"Cancelled task {command_uuid} in queue")
            
            # Update status in database
            try:
                with self.app.app_context():
                    command = Command.query.filter_by(command_uuid=command_uuid).first()
                    
                    if command:
                        command.status = "cancelled"
                        command.completed_at = datetime.utcnow()
                        
                        # Add log entry
                        add_command_log(
                            command=command,
                            status="cancelled",
                            message="Task cancelled while in queue",
                            log_data={"cancelled_at": datetime.utcnow().isoformat()}
                        )
                        
                        db.session.commit()
            except Exception as e:
                logger.error(f"Error updating cancelled task status: {str(e)}")
        
        return cancelled


# Create a default executor
task_executor_service = TaskExecutorService()

def init_app(app):
    """
    Initialize the task executor service with a Flask app.
    
    Args:
        app (Flask): Flask application
    """
    global task_executor_service
    task_executor_service.init_app(app)
