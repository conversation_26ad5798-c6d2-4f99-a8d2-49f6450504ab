#!/usr/bin/env python
"""
Model Import Migration Script

This script updates imports throughout the codebase to use the new model structure.
It replaces imports from src.login.models with imports from src.models, helping
transition the codebase to the new organization.

Usage:
    python scripts/migrate_imports.py

This script will:
1. Scan the codebase for imports from src.login.models
2. Replace them with equivalent imports from src.models
3. Create a backup of each modified file
4. Report on all changes made

The script can be run multiple times safely, as it won't modify files that
don't contain the old import pattern.
"""

import os
import re
import sys
import shutil
from pathlib import Path
from datetime import datetime

# Define the mapping of old imports to new imports
IMPORT_REPLACEMENTS = {
    # Database import
    r'from src\.login\.models import db': 'from src.models import db',
    
    # User models
    r'from src\.login\.models import User': 'from src.models import User',
    r'from src\.login\.models import UserLog': 'from src.models import UserLog',
    
    # API models
    r'from src\.login\.models import APIKey': 'from src.models import APIKey',
    
    # Config models
    r'from src\.login\.models import ConfigEntry': 'from src.models import Configuration',
    
    # LLM models
    r'from src\.login\.models import LLMProviderSetting': 'from src.models import LLMProviderSetting',
    
    # Command models
    r'from src\.login\.models import CommandToggle': 'from src.models import CommandToggle',
    
    # Connection models
    r'from src\.login\.models import DirectConnection': 'from src.models import DirectConnection',
    r'from src\.login\.models import MCPApiKey': 'from src.models import MCPApiKey',
    r'from src\.login\.models import MCPServerTemplate': 'from src.models import MCPServerTemplate',
    
    # Conversation models
    r'from src\.login\.models import Conversation': 'from src.models import Conversation',
    r'from src\.login\.models import Message': 'from src.models import Message',
    
    # Logging models
    r'from src\.login\.models import LogEntry': 'from src.models import LogEntry',
    
    # Common multiple imports (order matters - more specific patterns first)
    r'from src\.login\.models import db, User, APIKey': 'from src.models import db, User, APIKey',
    r'from src\.login\.models import db, User': 'from src.models import db, User',
    r'from src\.login\.models import db, Conversation, Message': 'from src.models import db, Conversation, Message',
    r'from src\.login\.models import db, Configuration': 'from src.models import db, Configuration',
    r'from src\.login\.models import db, MCPApiKey': 'from src.models import db, MCPApiKey',
    r'from src\.login\.models import APIKey, User, UserLog': 'from src.models import APIKey, User, UserLog',
    r'from src\.login\.models import ConfigEntry, db': 'from src.models import Configuration, db',
    r'from src\.login\.models import ConfigEntry, db, LLMProviderSetting': 'from src.models import Configuration, db, LLMProviderSetting',
}

# Directories to exclude from processing
EXCLUDE_DIRS = [
    '.git',
    '.vscode',
    'venv',
    'env',
    '__pycache__',
    'node_modules',
    'build',
    'dist',
    'migrations',  # Skip migrations as they reference historical models
]

# File extensions to process
INCLUDE_EXTENSIONS = ['.py']

def backup_file(file_path):
    """
    Create a backup of the file before modifying it.
    
    Args:
        file_path: Path to the file to back up
        
    Returns:
        Path to the backup file
    """
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    backup_path = f"{file_path}.{timestamp}.bak"
    shutil.copy2(file_path, backup_path)
    return backup_path

def update_imports(content):
    """
    Update imports in the given content.
    
    Args:
        content: The file content to update
        
    Returns:
        Tuple of (updated_content, count_of_replacements)
    """
    replacement_count = 0
    updated_content = content
    
    for old_pattern, new_import in IMPORT_REPLACEMENTS.items():
        # Use regex to match and replace imports
        matches = re.findall(old_pattern, updated_content)
        replacement_count += len(matches)
        updated_content = re.sub(old_pattern, new_import, updated_content)
    
    return updated_content, replacement_count

def process_file(file_path):
    """
    Process a single file to update imports.
    
    Args:
        file_path: Path to the file to process
        
    Returns:
        Tuple of (file_path, replacement_count, is_modified)
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    updated_content, replacement_count = update_imports(content)
    
    if replacement_count > 0:
        # Only backup and update if changes were made
        backup_path = backup_file(file_path)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        return file_path, replacement_count, True
    
    return file_path, 0, False

def scan_directory(directory):
    """
    Recursively scan a directory for Python files and update imports.
    
    Args:
        directory: Root directory to scan
        
    Returns:
        List of tuples containing (file_path, replacement_count, is_modified)
    """
    results = []
    
    for root, dirs, files in os.walk(directory):
        # Skip excluded directories
        dirs[:] = [d for d in dirs if d not in EXCLUDE_DIRS]
        
        for file in files:
            file_path = os.path.join(root, file)
            _, ext = os.path.splitext(file_path)
            
            # Only process Python files
            if ext.lower() in INCLUDE_EXTENSIONS:
                result = process_file(file_path)
                results.append(result)
    
    return results

def main():
    """Main entry point for the script."""
    print("Model Import Migration Script")
    print("============================")
    print("This script will update imports from src.login.models to src.models")
    print("throughout the codebase.")
    
    # Get the root directory of the project
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    
    # Confirm with the user
    print(f"\nProject root: {project_root}")
    confirmation = input("Continue with import migration? (y/n): ")
    
    if confirmation.lower() != 'y':
        print("Migration cancelled.")
        return
    
    # Scan and update files
    print("\nScanning for files to update...")
    results = scan_directory(project_root)
    
    # Report on the results
    modified_files = [r for r in results if r[2]]
    total_replacements = sum(r[1] for r in results)
    
    print("\nMigration Summary")
    print("----------------")
    print(f"Files scanned: {len(results)}")
    print(f"Files modified: {len(modified_files)}")
    print(f"Total replacements: {total_replacements}")
    
    if modified_files:
        print("\nModified Files:")
        for file_path, count, _ in modified_files:
            rel_path = os.path.relpath(file_path, project_root)
            print(f" - {rel_path} ({count} replacements)")
    
    print("\nMigration complete.")

if __name__ == "__main__":
    main()
