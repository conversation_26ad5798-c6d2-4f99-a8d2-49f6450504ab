"""
Token counting utilities for Athena supporting multiple LLM providers.
Auto-detects provider from model name.
"""
import re

def detect_provider(model_name: str) -> str:
    model_name = model_name.lower()
    if model_name.startswith("gpt-") or model_name.startswith("text-davinci") or model_name.startswith("gpt4"):
        return "openai"
    if model_name.startswith("claude-"):
        return "anthropic"
    if model_name.startswith("gemini-") or model_name.startswith("chat-bison"):
        return "google"
    # Add more heuristics as needed
    return "unknown"


def count_tokens(model: str, text: str) -> int:
    provider = detect_provider(model)
    try:
        if provider == "openai":
            try:
                import tiktoken
                enc = tiktoken.encoding_for_model(model) if hasattr(tiktoken, "encoding_for_model") else tiktoken.get_encoding("cl100k_base")
                return len(enc.encode(text))
            except Exception:
                # Fallback: 1 token per 4 chars (rough estimate)
                return max(1, len(text) // 4)
        elif provider == "anthropic":
            # Anthropic Claude: 1 token ≈ 4 chars (rough estimate)
            return max(1, len(text) // 4)
        elif provider == "google":
            # Gemini/PaLM: 1 token ≈ 4 chars (rough estimate)
            return max(1, len(text) // 4)
        else:
            # Unknown model/provider: fallback
            return max(1, len(text) // 4)
    except Exception:
        return max(1, len(text) // 4)
