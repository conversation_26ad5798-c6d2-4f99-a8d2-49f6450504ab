# AthenaNew Test Suite

This directory contains comprehensive tests for the AthenaNew project ecosystem, organized by functionality and test type.

## 📁 Test Structure

### 🔗 MCP (Model Context Protocol) Tests
- **[mcp/](mcp/)** - MCP integration and functionality tests
  - [simple_mcp_test.py](mcp/simple_mcp_test.py) - Basic MCP connection and functionality test

### 📊 API Tests
- **[api/](api/)** - API endpoint and integration tests
  - [test_athena_mcp_api.py](api/test_athena_mcp_api.py) - Athena MCP API integration tests

### 📚 Knowledge Base Tests
- **[kb/](kb/)** - Knowledge base functionality tests
  - [test_kb_upload.py](kb/test_kb_upload.py) - Knowledge base document upload and embedding tests

### 🔗 Integration Tests
- **[integration/](integration/)** - Cross-component integration tests
  - (Ready for future integration tests)

## 🚀 Running Tests

### Individual Test Categories

#### MCP Tests
```bash
# Run MCP connection test
python tests/mcp/simple_mcp_test.py
```

#### API Tests
```bash
# Test Athena MCP API integration
python tests/api/test_athena_mcp_api.py
```

#### Knowledge Base Tests
```bash
# Test knowledge base upload functionality
python tests/kb/test_kb_upload.py [API_KEY]
```

### Prerequisites

#### For MCP Tests:
- Smithery API key configured in `AthenaCore/config.json`
- Required packages: `smithery`, `mcp`

#### For API Tests:
- AthenaCore server running on localhost:5000
- Valid API endpoints configured

#### For Knowledge Base Tests:
- OpenAI API key (via command line, config.json, or environment variable)
- ChromaDB properly configured
- AthenaCore knowledge base system accessible

## 📋 Test Descriptions

### MCP Tests

#### `simple_mcp_test.py`
- **Purpose**: Test basic MCP connection and functionality
- **Features**:
  - Automatic package installation if missing
  - Smithery API key validation
  - WebSocket connection testing
  - Tool listing and chat functionality
- **Usage**: `python tests/mcp/simple_mcp_test.py`

### API Tests

#### `test_athena_mcp_api.py`
- **Purpose**: Test Athena's MCP API integration
- **Features**:
  - Chat endpoint testing with MCP models
  - Response validation
  - Error handling
- **Usage**: `python tests/api/test_athena_mcp_api.py`

### Knowledge Base Tests

#### `test_kb_upload.py`
- **Purpose**: Test knowledge base document upload and embedding
- **Features**:
  - OpenAI API key detection from multiple sources
  - Direct embedding generation testing
  - ChromaDB connection and collection management
  - Document upload with metadata
  - Multiple upload method fallbacks
- **Usage**: `python tests/kb/test_kb_upload.py [API_KEY]`

## 🔧 Configuration

### API Keys
Tests require various API keys depending on functionality:

1. **Smithery API Key** (for MCP tests):
   - Add to `AthenaCore/config.json`: `"smithery_api_key": "your_key"`

2. **OpenAI API Key** (for KB tests):
   - Command line: `python test_kb_upload.py sk-your-key`
   - Config file: Add to `AthenaCore/config.json`: `"openai_api_key": "your_key"`
   - Environment: Set `OPENAI_API_KEY` environment variable

### Server Requirements
- **AthenaCore server**: Must be running for API tests
- **Database**: SQLite database must be accessible for KB tests
- **ChromaDB**: Persistence directory must be writable

## 📊 Test Results

Tests provide detailed output including:
- ✅ Success indicators
- ❌ Failure indicators with error details
- 📊 Performance metrics where applicable
- 🔍 Debug information for troubleshooting

## 🔍 Troubleshooting

### Common Issues

1. **Missing API Keys**:
   - Ensure API keys are properly configured
   - Check file permissions for config.json

2. **Connection Failures**:
   - Verify server is running for API tests
   - Check network connectivity for MCP tests

3. **Database Issues**:
   - Ensure ChromaDB directory is writable
   - Check SQLite database accessibility

4. **Package Dependencies**:
   - MCP tests auto-install required packages
   - Ensure pip has proper permissions

## 📋 Adding New Tests

When adding new tests:

1. **Choose the right category**: Place tests in appropriate subdirectories
2. **Follow naming conventions**: Use descriptive names with `test_` prefix
3. **Include documentation**: Add clear docstrings and comments
4. **Update this README**: Add new tests to the relevant sections
5. **Provide usage examples**: Include command-line usage instructions

### Test Categories

- **mcp/**: Model Context Protocol functionality
- **api/**: API endpoint testing
- **kb/**: Knowledge base operations
- **integration/**: Cross-component integration tests

## 🔗 Related Documentation

- **AthenaCore Tests**: `AthenaCore/tests/` - Core system tests
- **AthenaAgent Tests**: `AthenaAgent/src/tests/` - Agent-specific tests
- **Project Documentation**: `docs/` - Comprehensive project documentation
- **Scripts**: `scripts/` - Utility scripts for development and maintenance
