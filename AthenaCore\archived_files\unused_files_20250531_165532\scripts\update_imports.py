#!/usr/bin/env python3
"""
Update imports script for Athena refactoring.

This script scans Python files in the codebase and updates imports
from old locations to new locations according to the refactoring plan.
It creates backup files and logs all changes for safety.
"""

import os
import re
import sys
import logging
import argparse
from datetime import datetime
from typing import Dict, List, Tuple, Set

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('import_updates.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('update_imports')

# Mapping of old imports to new imports
IMPORT_MAPPINGS = {
    # User and related models
    'from src.models import User': 'from src.models import User',
    'from src.models import UserLog': 'from src.models import UserLog',
    'from src.models import APIKey': 'from src.models import APIKey',
    
    # Configuration
    'from src.models import Configuration': 'from src.models import Configuration',
    'import os.getenv': 'from src.config.service import config_service',
    'from os import getenv': 'from src.config.service import config_service',
}

# Patterns for replacing os.getenv with config_service.get
GETENV_PATTERNS = [
    (r'os\.getenv\([\'"]([A-Za-z0-9_]+)[\'"]\)', r'config_service.get("\1")'),
    (r'os\.getenv\([\'"]([A-Za-z0-9_]+)[\'"], [\'"]([^\'"]+)[\'"]\)', r'config_service.get("\1", "\2")'),
    (r'os\.getenv\([\'"]([A-Za-z0-9_]+)[\'"], ([0-9]+)\)', r'config_service.get("\1", \2, "int")'),
    (r'os\.getenv\([\'"]([A-Za-z0-9_]+)[\'"], (True|False)\)', r'config_service.get("\1", \2, "bool")'),
]

def scan_directory(directory: str) -> List[str]:
    """
    Scan a directory for Python files.
    
    Args:
        directory: Directory to scan
        
    Returns:
        List of Python file paths
    """
    python_files = []
    
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    return python_files

def backup_file(file_path: str) -> str:
    """
    Create a backup of a file.
    
    Args:
        file_path: Path to the file to backup
        
    Returns:
        Path to the backup file
    """
    backup_path = f"{file_path}.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
    with open(file_path, 'r', encoding='utf-8') as src:
        with open(backup_path, 'w', encoding='utf-8') as dst:
            dst.write(src.read())
    
    return backup_path

def update_imports(file_path: str, dry_run: bool = False) -> Tuple[bool, List[str]]:
    """
    Update imports in a Python file.
    
    Args:
        file_path: Path to the Python file
        dry_run: If True, don't actually change the file
        
    Returns:
        Tuple of (changed, list of changes made)
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes = []
        
        # Update import statements
        for old_import, new_import in IMPORT_MAPPINGS.items():
            if old_import in content:
                content = content.replace(old_import, new_import)
                changes.append(f"Updated: '{old_import}' -> '{new_import}'")
        
        # Update os.getenv calls
        for pattern, replacement in GETENV_PATTERNS:
            matches = re.findall(pattern, content)
            if matches:
                new_content = re.sub(pattern, replacement, content)
                if new_content != content:
                    content = new_content
                    changes.append(f"Updated getenv pattern: '{pattern}' -> '{replacement}'")
        
        # Only write back if changes were made and not in dry run mode
        if original_content != content:
            if not dry_run:
                # Create backup
                backup_path = backup_file(file_path)
                logger.info(f"Created backup: {backup_path}")
                
                # Write updated content
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.info(f"Updated imports in {file_path}")
            
            return True, changes
        
        return False, []
    
    except Exception as e:
        logger.error(f"Error updating {file_path}: {str(e)}")
        return False, [f"ERROR: {str(e)}"]

def main():
    """Main function to run the import updater."""
    parser = argparse.ArgumentParser(description='Update imports in Python files')
    parser.add_argument('--directory', '-d', default='.', help='Directory to scan for Python files')
    parser.add_argument('--dry-run', action='store_true', help='Dry run mode (no changes made)')
    args = parser.parse_args()
    
    logger.info(f"Starting import update {'(DRY RUN)' if args.dry_run else ''}")
    
    python_files = scan_directory(args.directory)
    logger.info(f"Found {len(python_files)} Python files to check")
    
    updated_files = 0
    changes_made = 0
    
    for file_path in python_files:
        changed, changes = update_imports(file_path, args.dry_run)
        if changed:
            updated_files += 1
            changes_made += len(changes)
            
            # Log changes
            logger.info(f"Changes in {file_path}:")
            for change in changes:
                logger.info(f"  - {change}")
    
    logger.info(f"Updated {updated_files} files with {changes_made} changes")
    logger.info(f"Import update {'simulation' if args.dry_run else 'process'} completed")

if __name__ == "__main__":
    main()
