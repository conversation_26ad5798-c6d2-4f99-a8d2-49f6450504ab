"""
Knowledge Base Initializer
Ensures proper initialization of the knowledge base and adds example content if needed
"""

import os
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

# Import the shared instance directly
from src.core.knowledge_db import kb

logger = logging.getLogger("athena.kb_initializer")

class KnowledgeBaseInitializer:
    """Handles initialization and verification of the knowledge base"""
    
    def __init__(self, db_instance=None):
        """
        Initialize with an optional database instance.
        If db_instance is not provided here, it MUST be provided to initialize().
        
        Args:
            db_instance: Optional instance of KnowledgeDatabase
        """
        self.db = db_instance # Store the instance if provided
        
    def initialize(self, db_instance=None):
        """
        Initialize the knowledge base and verify it's working correctly.
        Requires a KnowledgeDatabase instance either from __init__ or passed here.
        
        Args:
            db_instance: Optional KnowledgeDatabase instance to use if not set during __init__.
            
        Returns:
            bool: True if successful
        """
        # Use the passed instance if available, otherwise use the one from __init__
        current_db = db_instance if db_instance else self.db
            
        if not current_db:
            logger.critical("KnowledgeDatabase instance not provided to initializer. Cannot proceed.")
            return False # Cannot proceed without a db instance
        
        # Use the determined db instance for the rest of the method
        self.db = current_db 
            
        # Check if we need to add sample documents
        try:
            docs = self.db.search_documents({})
            if not docs or len(docs) == 0:
                logger.info("No documents found in knowledge base, adding sample document")
                self._add_sample_document()
            else:
                logger.info(f"Found {len(docs)} existing documents in knowledge base")
                return True
        except Exception as e:
            logger.error(f"Error initializing knowledge base: {e}")
            # Try to add a sample document anyway using the confirmed self.db
            self._add_sample_document()
            
        return True
        
    def _add_sample_document(self):
        """Add a sample document to the knowledge base"""
        try:
            # Define a simple document to add
            content = (
                "# Welcome to Athena Knowledge Base\n\n"
                "This is a sample document that demonstrates how the knowledge base works.\n\n"
                "You can add your own documents by:\n"
                "1. Using the 'Add Document' button in the Knowledge section\n"
                "2. Uploading text files or PDFs\n"
                "3. Providing website URLs\n\n"
                "Once documents are added, you can search them using the `/search` command in chat."
            )
            
            metadata = {
                "title": "Athena Knowledge Base Introduction",
                "type": "text",
                "source": "system",
                "tags": ["introduction", "help"],
                "timestamp": datetime.now().isoformat()
            }
            
            # Add the document
            success = self.db.add_document(content, metadata)
            
            if success:
                logger.info("Successfully added sample document to knowledge base")
            else:
                logger.error("Failed to add sample document to knowledge base")
                
            return success
        except Exception as e:
            logger.error(f"Error adding sample document: {e}")
            return False
            
    def verify_persistence(self):
        """
        Verify that documents are being properly persisted
        
        Returns:
            Dict: Status information
        """
        status = {
            "success": False,
            "message": "",
            "documents_found": 0
        }
        
        try:
            # Check if documents exist
            docs = self.db.search_documents({})
            status["documents_found"] = len(docs)
            
            if not docs or len(docs) == 0:
                # Try to add a verification document
                test_content = f"Knowledge Base Verification Document - {datetime.now().isoformat()}"
                test_metadata = {
                    "title": "Verification Document",
                    "type": "text",
                    "source": "system_verification",
                    "timestamp": datetime.now().isoformat()
                }
                
                success = self.db.add_document(test_content, test_metadata)
                
                if success:
                    # Verify it was added
                    new_docs = self.db.search_documents({})
                    status["documents_found"] = len(new_docs)
                    status["success"] = len(new_docs) > 0
                    status["message"] = "Successfully added verification document"
                else:
                    status["success"] = False
                    status["message"] = "Failed to add verification document"
            else:
                status["success"] = True
                status["message"] = f"Found {len(docs)} existing documents"
                
            return status
        except Exception as e:
            logger.error(f"Error verifying persistence: {e}")
            status["success"] = False
            status["message"] = f"Error: {str(e)}"
            return status

# Pre-instantiate a global instance with the shared kb instance
initializer = KnowledgeBaseInitializer(kb)
# Note: This instance is imported by start_athena.py for knowledge base initialization
