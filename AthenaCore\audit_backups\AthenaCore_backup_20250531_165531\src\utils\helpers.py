# This file contains utility functions and classes for various system operations.
#
# It includes:
# - File and directory management functions (`ensure_directory`, `sanitize_filename`).
# - Configuration loading (`load_config`).
# - Error response generation (`create_error_response`).
# - Timestamp formatting (`format_timestamp`).
# - A simple caching system (`CacheManager`) for temporary data storage.
# - Conversation ID generation for unique tracking.
# - Code formatting for HTML display.
#
# These utilities are designed to support Athena’s core functions by handling
# common tasks related to file management, configuration, and caching.

import os
import json
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

def format_timestamp(timestamp: str, format: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    Format an ISO timestamp into a human-readable string.
    
    Args:
        timestamp (str): ISO format timestamp
        format (str): Desired output format
        
    Returns:
        str: Formatted timestamp string
    """
    try:
        dt = datetime.fromisoformat(timestamp)
        return dt.strftime(format)
    except ValueError as e:
        print(f"Error formatting timestamp: {e}")
        return timestamp

def ensure_directory(path: str) -> Path:
    """
    Ensure a directory exists, create it if it doesn't.
    
    Args:
        path (str): Directory path
        
    Returns:
        Path: Path object pointing to the directory
    """
    directory = Path(path)
    directory.mkdir(parents=True, exist_ok=True)
    return directory

def load_config(config_path: str = "config.json") -> Dict[str, Any]:
    """
    Load configuration from a JSON file.
    
    Args:
        config_path (str): Path to the config file
        
    Returns:
        Dict[str, Any]: Configuration dictionary
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Config file not found at {config_path}, using defaults")
        return {}
    except json.JSONDecodeError as e:
        print(f"Error parsing config file: {e}")
        return {}

def sanitize_filename(filename: str) -> str:
    """
    Sanitize a filename to be safe for all operating systems.
    
    Args:
        filename (str): Original filename
        
    Returns:
        str: Sanitized filename
    """
    # Remove or replace illegal characters
    illegal_chars = '<>:"/\\|?*'
    for char in illegal_chars:
        filename = filename.replace(char, '_')
    
    # Ensure filename isn't too long
    max_length = 255
    name, ext = os.path.splitext(filename)
    if len(filename) > max_length:
        return name[:max_length-len(ext)] + ext
    
    return filename

def create_error_response(message: str, code: int = 500) -> Dict[str, Any]:
    """
    Create a standardized error response.
    
    Args:
        message (str): Error message
        code (int): HTTP status code
        
    Returns:
        Dict[str, Any]: Error response dictionary
    """
    return {
        'error': {
            'message': message,
            'code': code,
            'timestamp': datetime.now().isoformat()
        }
    }

class CacheManager:
    """Simple cache manager for storing temporary data."""
    
    def __init__(self, cache_dir: str = ".cache"):
        """
        Initialize cache manager.
        
        Args:
            cache_dir (str): Directory to store cache files
        """
        self.cache_dir = ensure_directory(cache_dir)
        self.cache: Dict[str, Any] = {}

    def get(self, key: str, default: Any = None) -> Any:
        """
        Get value from cache.
        
        Args:
            key (str): Cache key
            default (Any): Default value if key doesn't exist
            
        Returns:
            Any: Cached value or default
        """
        return self.cache.get(key, default)

    def set(self, key: str, value: Any, persist: bool = False) -> None:
        """
        Set cache value.
        
        Args:
            key (str): Cache key
            value (Any): Value to cache
            persist (bool): Whether to persist to disk
        """
        self.cache[key] = value
        if persist:
            cache_file = self.cache_dir / f"{sanitize_filename(key)}.json"
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(value, f)

    def clear(self, key: Optional[str] = None) -> None:
        """
        Clear cache entries.
        
        Args:
            key (Optional[str]): Specific key to clear, or None for all
        """
        if key is None:
            self.cache.clear()
        else:
            self.cache.pop(key, None)

def generate_conversation_id() -> str:
    """
    Generate a unique conversation ID.
    
    Returns:
        str: Unique conversation ID
    """
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    random_suffix = os.urandom(3).hex()
    return f"{timestamp}-{random_suffix}"

def format_code_block(code: str, language: str = "python") -> str:
    """
    Format code for HTML display.
    
    Args:
        code (str): Code to format
        language (str): Programming language
        
    Returns:
        str: Formatted HTML code block
    """
    return f'<pre><code class="language-{language}">{code}</code></pre>'