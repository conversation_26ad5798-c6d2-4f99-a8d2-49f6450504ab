# src/core/commands/__init__.py

from .base_command import BaseCommand
import logging
import os
import traceback

# Set up logger
logger = logging.getLogger('athena.commands')
if not logger.handlers:
    logger.setLevel(logging.INFO)
    # Create console handler with a higher log level
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # Add file handler for detailed logs
    try:
        log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', '..', 'logs')
        os.makedirs(log_dir, exist_ok=True)
        file_handler = logging.FileHandler(os.path.join(log_dir, 'commands.log'))
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    except Exception as e:
        logger.warning(f"Could not set up file logging: {e}")


# Define a dummy command to use if loading fails
class DummyCommand(BaseCommand):
    def __init__(self, name: str, description: str):
        self._name = name
        self._description = description

    @property
    def name(self) -> str:
        return self._name

    @property
    def description(self) -> str:
        return self._description

    @property
    def supported_actions(self) -> list:
        return []

    def execute(self, action: str, params: dict = None) -> str:
        return f"Command '{self._name}' is unavailable."


# Pre-declare known command classes and any pre-instantiated ones
_command_classes = {
    "spotify": "Control Spotify music playback",
    "obsidian": "Manage Obsidian notes and vaults",
    "system": "Control system operations like opening files and URLs",
    "kb": "Access and search the Knowledge Base"
}

_command_instances = {}


def get_command(name: str):
    """
    Get a command module by name, instantiating if necessary.
    """
    name = name.lower()
    print(f"[DEBUG] get_command called with name: '{name}'")  # Debug log
    if name in _command_instances and _command_instances[name] is not None:
        print(f"[DEBUG] Returning cached command instance for: '{name}'")  # Debug log
        return _command_instances[name]

    if name in _command_classes:
        try:
            print(f"[DEBUG] Attempting to load command class for: '{name}'")  # Debug log
            if name == "spotify":
                from src.core.commands.spotify.spotify_control import SpotifyControl
                _command_instances[name] = SpotifyControl()
            elif name == "obsidian":
                from src.core.commands.obsidian.obsidian_control import ObsidianControl
                _command_instances[name] = ObsidianControl()
            elif name == "system":
                from src.core.commands.system.system_control import SystemControl
                _command_instances[name] = SystemControl()
            elif name == "kb" or name == "search" or name == "knowledge":
                from src.core.commands.kb.kb_control import KnowledgeBaseControl
                _command_instances[name] = KnowledgeBaseControl()
            print(f"[DEBUG] Successfully loaded command: '{name}'")  # Debug log
            return _command_instances[name]
        except Exception as e:
            logger.error(f"Error loading {name} command: {str(e)}")
            print(f"[DEBUG] Error loading command '{name}': {str(e)}")  # Debug log
            # Instead of returning None, return a dummy command
            _command_instances[name] = DummyCommand(
                name, f"{name.capitalize()} command unavailable"
            )
            return _command_instances[name]
    print(f"[DEBUG] Command '{name}' not found in _command_classes.")  # Debug log
    return None


def discover_commands():
    """
    Discover available command modules by looking at the files in src/core/commands.
    Returns a dictionary of command names and their descriptions.
    """
    commands = {}
    commands_dir = os.path.dirname(__file__)
    logger.debug(f"Discovering commands in directory: {commands_dir}")

    # First try to find subdirectories as command modules
    for item in os.listdir(commands_dir):
        item_path = os.path.join(commands_dir, item)
        # Skip non-directories, __pycache__, and files/dirs starting with __
        if not os.path.isdir(item_path) or item == '__pycache__' or item.startswith('__'):
            continue

        logger.debug(f"Found potential command directory: {item}")
        # Use the directory name as the command
        cmd_name = item.lower()
        commands[cmd_name] = f"{cmd_name.capitalize()} control"

    # If no commands found, return hardcoded defaults
    if not commands:
        logger.info("No command directories found, using defaults")
        commands = {
            "spotify": "Control Spotify music playback",
            "obsidian": "Manage Obsidian notes and vaults",
            "system": "Control system operations like opening files and URLs",
            "kb": "Access and search the Knowledge Base"
        }

    logger.debug(f"Discovered commands: {list(commands.keys())}")
    return commands


def list_commands():
    """
    Return a dictionary of available commands and their descriptions.
    Format: {"command_name": "description", ...}
    """
    logger.debug("list_commands() called")
    try:
        # Try to dynamically discover commands
        return discover_commands()
    except Exception as e:
        logger.error(f"Error in list_commands(): {str(e)}")
        logger.debug(traceback.format_exc())
        # Fallback to hardcoded commands if discovery fails
        return {
            "spotify": "Control Spotify music playback",
            "obsidian": "Manage Obsidian notes and vaults",
            "system": "Control system operations like opening files and URLs",
            "kb": "Access and search the Knowledge Base"
        }
