"""
Document Processor for Athena Knowledge Base

This module handles the processing of different document types for the knowledge base.
It extracts text content from various file formats and prepares them for embedding.
"""

import os
import logging
import mimetypes
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple, Union

# Configure logging
logger = logging.getLogger("athena.document_processor")

# Try to import optional dependencies for document processing
try:
    import PyPDF2
    HAS_PDF = True
except ImportError:
    logger.warning("PyPDF2 not installed. PDF processing will be limited.")
    HAS_PDF = False

try:
    import docx
    HAS_DOCX = True
except ImportError:
    logger.warning("python-docx not installed. DOCX processing will be limited.")
    HAS_DOCX = False

try:
    from bs4 import BeautifulSoup
    HAS_BS4 = True
except ImportError:
    logger.warning("BeautifulSoup not installed. HTML processing will be limited.")
    HAS_BS4 = False

try:
    import markdown
    HAS_MARKDOWN = True
except ImportError:
    logger.warning("Markdown not installed. Markdown processing will be limited.")
    HAS_MARKDOWN = False

# Supported file types and their processors
SUPPORTED_EXTENSIONS = {
    '.txt': 'process_text',
    '.md': 'process_markdown',
    '.pdf': 'process_pdf',
    '.docx': 'process_docx',
    '.doc': 'process_docx',
    '.html': 'process_html',
    '.htm': 'process_html',
    '.json': 'process_json',
    '.csv': 'process_csv'
}

class DocumentProcessor:
    """Handles processing of different document types for the knowledge base."""
    
    def __init__(self):
        """Initialize the document processor with supported file types."""
        self.processors = {
            'process_text': self.process_text,
            'process_markdown': self.process_markdown,
            'process_pdf': self.process_pdf,
            'process_docx': self.process_docx,
            'process_html': self.process_html,
            'process_json': self.process_json,
            'process_csv': self.process_csv
        }
    
    def process_document(self, file_path: Union[str, Path]) -> Tuple[str, Dict[str, Any]]:
        """
        Process a document and extract its content and metadata.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Tuple of (content, metadata)
        """
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # Get file extension and determine processor
        file_ext = file_path.suffix.lower()
        if file_ext not in SUPPORTED_EXTENSIONS:
            raise ValueError(f"Unsupported file type: {file_ext}")
        
        processor_name = SUPPORTED_EXTENSIONS[file_ext]
        processor = self.processors.get(processor_name)
        
        if not processor:
            raise NotImplementedError(f"Processor {processor_name} not implemented")
        
        # Process the document
        content, metadata = processor(file_path)
        
        # Add basic file metadata
        metadata.update({
            'filename': file_path.name,
            'file_size': file_path.stat().st_size,
            'file_type': file_ext.lstrip('.'),
            'path': str(file_path)
        })
        
        return content, metadata
    
    def process_text(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """Process a plain text file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            metadata = {
                'title': file_path.stem,
                'type': 'text',
                'line_count': content.count('\n') + 1
            }
            
            return content, metadata
        except UnicodeDecodeError:
            # Try different encoding if UTF-8 fails
            with open(file_path, 'r', encoding='latin-1') as f:
                content = f.read()
            
            metadata = {
                'title': file_path.stem,
                'type': 'text',
                'line_count': content.count('\n') + 1,
                'encoding': 'latin-1'
            }
            
            return content, metadata
    
    def process_markdown(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """Process a markdown file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract title from first heading if available
        title = file_path.stem
        lines = content.split('\n')
        for line in lines:
            if line.startswith('# '):
                title = line.lstrip('# ').strip()
                break
        
        metadata = {
            'title': title,
            'type': 'markdown',
            'line_count': content.count('\n') + 1
        }
        
        # Convert markdown to plain text if the library is available
        if HAS_MARKDOWN:
            try:
                html = markdown.markdown(content)
                if HAS_BS4:
                    soup = BeautifulSoup(html, 'html.parser')
                    content = soup.get_text()
            except Exception as e:
                logger.warning(f"Error converting markdown to text: {e}")
        
        return content, metadata
    
    def process_pdf(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """Process a PDF file."""
        if not HAS_PDF:
            raise ImportError("PyPDF2 is required for PDF processing")
        
        try:
            with open(file_path, 'rb') as f:
                pdf_reader = PyPDF2.PdfReader(f)
                
                # Extract metadata
                info = pdf_reader.metadata
                title = info.title if info and info.title else file_path.stem
                
                # Extract text content with improved error handling
                content = ""
                page_count = len(pdf_reader.pages)
                
                for page_num in range(page_count):
                    try:
                        page = pdf_reader.pages[page_num]
                        page_text = page.extract_text()
                        if page_text:
                            content += page_text + "\n\n"
                        else:
                            content += f"[Page {page_num+1} - No extractable text]\n\n"
                    except Exception as page_error:
                        logger.warning(f"Error extracting text from page {page_num+1}: {page_error}")
                        content += f"[Page {page_num+1} - Text extraction error]\n\n"
                        continue
                
                # If no content was extracted at all, use a fallback approach
                if not content.strip():
                    logger.warning(f"No text could be extracted from {file_path}. Using fallback extraction.")
                    try:
                        # Fallback: try simpler extraction approach
                        content = f"PDF document: {file_path.name}\n\nThis document appears to contain non-extractable text or images.\n"
                    except Exception as fallback_error:
                        logger.error(f"Fallback extraction also failed: {fallback_error}")
                        content = f"[Document {file_path.name} - Content could not be extracted]"
                
                metadata = {
                    'title': title,
                    'type': 'pdf',
                    'page_count': page_count,
                    'author': info.author if info and info.author else None,
                    'creator': info.creator if info and info.creator else None,
                    'producer': info.producer if info and info.producer else None
                }
                
                return content, metadata
        except Exception as e:
            logger.error(f"Error processing PDF {file_path}: {e}")
            raise
    
    def process_docx(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """Process a DOCX file."""
        if not HAS_DOCX:
            raise ImportError("python-docx is required for DOCX processing")
        
        try:
            doc = docx.Document(file_path)
            content = "\n".join([paragraph.text for paragraph in doc.paragraphs])
            
            # Get document properties
            core_properties = doc.core_properties
            title = core_properties.title if core_properties.title else file_path.stem
            
            metadata = {
                'title': title,
                'type': 'docx',
                'author': core_properties.author,
                'paragraph_count': len(doc.paragraphs)
            }
            
            return content, metadata
        except Exception as e:
            logger.error(f"Error processing DOCX {file_path}: {e}")
            raise
    
    def process_html(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """Process an HTML file."""
        if not HAS_BS4:
            raise ImportError("BeautifulSoup is required for HTML processing")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Extract title
            title_tag = soup.find('title')
            title = title_tag.text if title_tag else file_path.stem
            
            # Extract text content
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.extract()
            
            # Get text
            content = soup.get_text(separator='\n')
            
            # Clean up whitespace
            lines = (line.strip() for line in content.splitlines())
            content = '\n'.join(line for line in lines if line)
            
            metadata = {
                'title': title,
                'type': 'html'
            }
            
            return content, metadata
        except Exception as e:
            logger.error(f"Error processing HTML {file_path}: {e}")
            raise
    
    def process_json(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """Process a JSON file."""
        import json
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            # Convert JSON to string for knowledge base
            if isinstance(json_data, dict):
                content = "\n".join([f"{key}: {value}" for key, value in json_data.items()])
            elif isinstance(json_data, list):
                content = "\n".join([str(item) for item in json_data])
            else:
                content = str(json_data)
            
            metadata = {
                'title': file_path.stem,
                'type': 'json'
            }
            
            return content, metadata
        except Exception as e:
            logger.error(f"Error processing JSON {file_path}: {e}")
            raise
    
    def process_csv(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """Process a CSV file."""
        import csv
        
        try:
            content = ""
            row_count = 0
            
            with open(file_path, 'r', encoding='utf-8', newline='') as f:
                csv_reader = csv.reader(f)
                headers = next(csv_reader, None)
                row_count += 1
                
                if headers:
                    content += " | ".join(headers) + "\n"
                    content += "-" * 80 + "\n"
                
                for row in csv_reader:
                    content += " | ".join(row) + "\n"
                    row_count += 1
            
            metadata = {
                'title': file_path.stem,
                'type': 'csv',
                'row_count': row_count
            }
            
            return content, metadata
        except Exception as e:
            logger.error(f"Error processing CSV {file_path}: {e}")
            raise

# Create a singleton instance
_processor = None

def get_processor() -> DocumentProcessor:
    """Get or create a singleton instance of the DocumentProcessor."""
    global _processor
    if _processor is None:
        _processor = DocumentProcessor()
    return _processor


def process_document(file_path, metadata=None):
    """Process a document and return its ID and extracted text.
    
    Args:
        file_path (str or Path): Path to the document file
        metadata (dict, optional): Metadata for the document
        
    Returns:
        tuple: (document_id, document_text)
    """
    import uuid
    from datetime import datetime
    import hashlib
    from pathlib import Path
    
    # Generate a unique ID for the document
    if metadata is None:
        metadata = {}
    
    processor = get_processor()
    file_path = Path(file_path)
    
    try:
        # Extract content and additional metadata from the document
        content, doc_metadata = processor.process_document(file_path)
        
        # Ensure doc_metadata is a dictionary
        if not isinstance(doc_metadata, dict):
            doc_metadata = {"content": doc_metadata}
            
        # Merge the extracted metadata with provided metadata
        if metadata and isinstance(metadata, dict):
            # Convert any non-serializable values in input metadata before merging
            serialized_metadata = {}
            for key, value in metadata.items():
                if isinstance(value, (dict, list, set, tuple)):
                    import json
                    serialized_metadata[key] = json.dumps(value)
                elif isinstance(value, (str, int, float, bool)) or value is None:
                    serialized_metadata[key] = value
                else:
                    serialized_metadata[key] = str(value)
            
            # Update with serialized metadata
            doc_metadata.update(serialized_metadata)
        
        # Generate a unique ID based on content hash and timestamp
        content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()
        timestamp = datetime.now().isoformat()
        doc_id = f"{file_path.stem}-{content_hash[:8]}-{uuid.uuid4().hex[:8]}"
        
        # Add timestamp to metadata if not present
        if 'timestamp' not in doc_metadata:
            doc_metadata['timestamp'] = timestamp
        
        return doc_id, content, doc_metadata
    except Exception as e:
        logger.error(f"Error processing document {file_path}: {e}")
        return None, None, None
