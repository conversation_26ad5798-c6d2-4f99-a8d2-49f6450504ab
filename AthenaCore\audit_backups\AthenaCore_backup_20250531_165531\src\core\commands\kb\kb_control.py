# knowledge_control.py
"""
Command module for handling Knowledge Base operations.
Enables /kb and /search commands.
"""

import logging
from src.core.kb_search import handle_kb_search_command

class KnowledgeBaseControl:
    """
    Command module for Knowledge Base operations.
    This module handles /kb and /search commands.
    """
    
    def __init__(self):
        """Initialize the Knowledge Base command module."""
        self.logger = logging.getLogger('athena.commands.kb')
        self.description = "Access and search the Knowledge Base"
        self.aliases = ["kb", "search", "knowledge"]
        
    def execute(self, action, params=None):
        """
        Execute Knowledge Base commands.
        
        Args:
            action (str): Action to perform (search, list, etc.)
            params (dict): Parameters for the action
        
        Returns:
            str: Result of the command
        """
        if params is None:
            params = {}
            
        # Get conversation ID from params if available
        conversation_id = params.get("conversation_id", "")
        
        # Get the actual command text from params
        command_text = params.get("original_command", "")
        if not command_text:
            if "args" in params and params["args"]:
                command_text = " ".join(params["args"])
                
        self.logger.info(f"KB command execution: action={action}, command_text={command_text}")
        
        if not command_text:
            # If no specific command, we'll list all documents
            if action.lower() in ["list", "show", "all"]:
                command_text = "/kb"
            else:
                command_text = f"/kb {action}"
        else:
            # Ensure command has the proper prefix
            if not command_text.startswith('/'):
                command_text = f"/kb {command_text}"
                
        # Call the KB search handler with the appropriate parameters
        try:
            from src.main import athena  # Import here to avoid circular imports
            result = handle_kb_search_command(athena, conversation_id, command_text)
            if result:
                return result
            else:
                return "No results found in the knowledge base."
        except Exception as e:
            self.logger.error(f"Error executing KB command: {e}")
            return f"Error accessing knowledge base: {str(e)}"

    def get_description(self):
        """
        Return a description of this command module.
        """
        return self.description
