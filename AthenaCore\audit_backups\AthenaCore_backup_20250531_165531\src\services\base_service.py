"""
Base service module.

This module provides the foundation for all services in the application, 
implementing common functionality and standardizing the service interface.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Type
import logging

# Configure logging
logger = logging.getLogger(__name__)

class BaseService(ABC):
    """Base class for all services in the application."""
    
    def __init__(self):
        """Initialize the service."""
        self._initialized = False
        self._dependencies = {}
        self.initialize()
        self._initialized = True
    
    def initialize(self):
        """
        Initialize the service. This method should be overridden by subclasses
        to perform any necessary setup.
        """
        pass
    
    def register_dependency(self, name: str, service: Any) -> None:
        """
        Register a dependency service.
        
        Args:
            name: Name of the dependency
            service: Service instance to register
        """
        self._dependencies[name] = service
    
    def get_dependency(self, name: str) -> Any:
        """
        Get a registered dependency service.
        
        Args:
            name: Name of the dependency to retrieve
            
        Returns:
            The requested dependency service
            
        Raises:
            KeyError: If the dependency is not registered
        """
        if name not in self._dependencies:
            raise KeyError(f"Dependency '{name}' not registered with {self.__class__.__name__}")
        
        return self._dependencies[name]
    
    @property
    def is_initialized(self) -> bool:
        """
        Check if the service is initialized.
        
        Returns:
            True if the service is initialized, False otherwise
        """
        return self._initialized


class ServiceRegistry:
    """Registry for managing service instances."""
    
    _instance = None
    _services = {}
    
    def __new__(cls):
        """Create a singleton instance of the service registry."""
        if cls._instance is None:
            cls._instance = super(ServiceRegistry, cls).__new__(cls)
        return cls._instance
    
    def register(self, service_class: Type[BaseService], instance: Optional[BaseService] = None) -> BaseService:
        """
        Register a service with the registry.
        
        Args:
            service_class: Class of the service to register
            instance: Optional instance of the service to register
            
        Returns:
            The registered service instance
        """
        service_name = service_class.__name__
        
        # If the service is already registered, return the existing instance
        if service_name in self._services:
            return self._services[service_name]
        
        # If an instance was provided, register it
        if instance is not None:
            self._services[service_name] = instance
            return instance
        
        # Otherwise, create a new instance and register it
        instance = service_class()
        self._services[service_name] = instance
        return instance
    
    def get(self, service_class: Type[BaseService]) -> BaseService:
        """
        Get a service from the registry.
        
        Args:
            service_class: Class of the service to retrieve
            
        Returns:
            The requested service instance
            
        Raises:
            KeyError: If the service is not registered
        """
        service_name = service_class.__name__
        
        if service_name not in self._services:
            # Auto-register the service if it's not registered
            return self.register(service_class)
        
        return self._services[service_name]
    
    def clear(self) -> None:
        """Clear all registered services."""
        self._services = {}


# Create a singleton instance of the service registry
service_registry = ServiceRegistry()

def get_service(service_class: Type[BaseService]) -> BaseService:
    """
    Get a service instance from the registry.
    
    This is a convenience function for accessing the service registry.
    
    Args:
        service_class: Class of the service to retrieve
        
    Returns:
        The requested service instance
    """
    return service_registry.get(service_class)
