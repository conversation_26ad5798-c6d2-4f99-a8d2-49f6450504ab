"""
Configuration controller module.

This module provides the controller logic for integrating the configuration
management system with the Flask application. It handles route registration
and request processing for configuration-related operations.
"""

from flask import Blueprint, render_template
from flask_login import login_required, current_user
from src.api.config import config_api

# Create a blueprint for configuration routes
config_bp = Blueprint("config", __name__, url_prefix="/config")

@config_bp.route("/", methods=["GET"])
@login_required
def config_dashboard():
    """
    Render the configuration dashboard.
    
    Returns:
        Rendered configuration manager template
    """
    # Only admins can access the configuration dashboard
    if current_user.role != "admin":
        return render_template("errors/403.html"), 403
    
    return render_template("admin/config_manager.html")

def register_blueprints(app):
    """
    Register all configuration-related blueprints with the app.
    
    Args:
        app: Flask application instance
    """
    # Register the configuration dashboard
    app.register_blueprint(config_bp)
    
    # Register the configuration API
    app.register_blueprint(config_api)
