import json
import logging
import async<PERSON>
from typing import Dict, Any, Optional, List, Callable, Union

from src.mcp.smithery_client import Smithery<PERSON>lient
from src.mcp.connection import MCPConnectionManager

logger = logging.getLogger(__name__)

class AthenaMCP:
    """Integration of MCP capabilities with Athena."""
    
    def __init__(self, smithery_client: SmitheryClient):
        """Initialize the Athena MCP integration.
        
        Args:
            smithery_client: Initialized SmitheryClient instance.
        """
        self.smithery_client = smithery_client
        self.connection_manager = MCPConnectionManager(self.smithery_client)
        self.is_initialized = True
        print("✓ AthenaMCP initialized with connection manager")
        
    def initialize(self):
        """Initialize the connection manager synchronously."""
        # Already initialized in __init__
        return True
        
    def get_initialization_future(self):
        """Get a future for asynchronous initialization.
        
        Note: This implementation returns a pre-resolved 'future' to 
        maintain compatibility with async workflows while actually
        performing synchronous initialization.
        """
        # Create a simple Future object that's already resolved
        import concurrent.futures
        future = concurrent.futures.Future()
        future.set_result(True)
        return future
    
    def search_mcp_servers(self, query: str = "", page: int = 1, page_size: int = 10) -> Dict[str, Any]:
        """Search for MCP servers in the Smithery Registry.
        
        Args:
            query: Search query for servers.
            page: Page number for pagination.
            page_size: Number of results per page.
            
        Returns:
            Dictionary with search results.
        """
        return self.smithery_client.list_servers(query, page, page_size)
    
    def get_mcp_server_info(self, qualified_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific MCP server.
        
        Args:
            qualified_name: Qualified name of the server (e.g., 'owner/repo').
            
        Returns:
            Server information or None if not found.
        """
        return self.smithery_client.get_server(qualified_name)
    
    async def connect_to_mcp_server(self, qualified_name: str, config: Dict[str, Any]) -> bool:
        """Connect to an MCP server.
        
        Args:
            qualified_name: Qualified name of the server.
            config: Configuration for the connection.
            
        Returns:
            True if connected successfully, False otherwise.
        """
        if self.connection_manager is None:
            await self.initialize()
            
        connection = await self.connection_manager.create_connection(qualified_name, config)
        return connection is not None
    
    async def disconnect_from_mcp_server(self, qualified_name: str) -> bool:
        """Disconnect from an MCP server.
        
        Args:
            qualified_name: Qualified name of the server to disconnect from.
            
        Returns:
            True if disconnected successfully, False otherwise.
        """
        if self.connection_manager is None:
            return False
            
        return await self.connection_manager.disconnect(qualified_name)
    
    async def send_message_to_mcp(self, qualified_name: str, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Send a message to an MCP server and wait for response.
        
        Args:
            qualified_name: Qualified name of the server.
            message: Message to send to the server.
            
        Returns:
            Response from the server or None if failed.
        """
        if self.connection_manager is None:
            return None
            
        connection = self.connection_manager.get_connection(qualified_name)
        if not connection or not connection.is_connected:
            return None
            
        # Create a future to get the response
        response_future = asyncio.Future()
        
        async def handle_response(response):
            response_future.set_result(response)
            
        # Send message with callback
        try:
            await connection.send_message(message, handle_response)
            
            # Wait for response with timeout
            try:
                return await asyncio.wait_for(response_future, timeout=30.0)
            except asyncio.TimeoutError:
                logger.error(f"Timeout waiting for response from {qualified_name}")
                return None
                
        except Exception as e:
            logger.error(f"Error sending message to {qualified_name}: {e}")
            return None
            
    async def process_chat_with_mcp(self, qualified_name: str, user_input: str) -> str:
        """Process a chat message through an MCP server.
        
        Args:
            qualified_name: Qualified name of the server.
            user_input: User's chat message.
            
        Returns:
            Response from the MCP server as a string.
        """
        if self.connection_manager is None:
            raise ValueError("MCP connection manager not initialized")
            
        connection = self.connection_manager.get_connection(qualified_name)
        if not connection:
            # Try to establish connection if not already connected
            config = {}
            connection = await self.connection_manager.create_connection(qualified_name, config)
            
        if not connection:
            raise ConnectionError(f"Could not connect to MCP server: {qualified_name}")
        
        # Format the message according to MCP protocol
        message = {
            "type": "chat",
            "content": user_input,
            "role": "user"
        }
        
        # Send the message and get the response
        try:
            response = await connection.send_message(message)
            
            # Extract the content from the response
            if response and "content" in response:
                return response["content"]
            elif isinstance(response, str):
                return response
            else:
                return f"Error: Unexpected response format from MCP server {qualified_name}"
                
        except Exception as e:
            logger.error(f"Error communicating with MCP server {qualified_name}: {str(e)}")
            raise ConnectionError(f"Failed to communicate with MCP server: {str(e)}")
