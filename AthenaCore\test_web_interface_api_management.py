#!/usr/bin/env python3
"""
Comprehensive test to verify the web interface provides complete API key management capabilities.
This test verifies that users can manage all their API keys through the web interface without
needing environment variables or config files.
"""

import sys
import os
import sqlite3
import requests
import json
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

def test_web_interface_availability():
    """Test that the web interface is accessible."""
    print("=== Testing Web Interface Availability ===")
    
    try:
        # Test main settings page
        response = requests.get("http://127.0.0.1:5000/settings", timeout=10)
        if response.status_code == 200 or response.status_code == 302:  # 302 for redirect to login
            print("✅ Settings page accessible")
        else:
            print(f"❌ Settings page returned status {response.status_code}")
            return False
            
        # Test connections page
        response = requests.get("http://127.0.0.1:5000/settings/connections", timeout=10)
        if response.status_code == 200 or response.status_code == 302:  # 302 for redirect to login
            print("✅ Connections page accessible")
        else:
            print(f"❌ Connections page returned status {response.status_code}")
            return False
            
        # Test API page
        response = requests.get("http://127.0.0.1:5000/settings/api", timeout=10)
        if response.status_code == 200 or response.status_code == 302:  # 302 for redirect to login
            print("✅ API settings page accessible")
        else:
            print(f"❌ API settings page returned status {response.status_code}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Web interface test failed: {e}")
        return False

def test_api_endpoints():
    """Test that the API endpoints for connection management are available."""
    print("\n=== Testing API Endpoints ===")
    
    try:
        # Test connections API endpoint
        response = requests.get("http://127.0.0.1:5000/api/v1/connections", timeout=10)
        if response.status_code in [200, 401, 403]:  # 401/403 expected without auth
            print("✅ Connections API endpoint accessible")
        else:
            print(f"❌ Connections API returned unexpected status {response.status_code}")
            return False
            
        # Test models API endpoint
        response = requests.get("http://127.0.0.1:5000/api/v1/models", timeout=10)
        if response.status_code in [200, 401, 403]:  # 401/403 expected without auth
            print("✅ Models API endpoint accessible")
        else:
            print(f"❌ Models API returned unexpected status {response.status_code}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ API endpoints test failed: {e}")
        return False

def test_database_structure():
    """Test that the database has the required tables for API key management."""
    print("\n=== Testing Database Structure ===")
    
    try:
        db_path = project_root / "instance" / "athena.db"
        if not db_path.exists():
            print("❌ Database not found")
            return False
            
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Check DirectConnections table
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='direct_connections'
        """)
        if cursor.fetchone():
            print("✅ DirectConnections table exists")
        else:
            print("❌ DirectConnections table missing")
            return False
            
        # Check DirectConnections table structure
        cursor.execute("PRAGMA table_info(direct_connections)")
        columns = [row[1] for row in cursor.fetchall()]
        required_columns = ['id', 'name', 'url', 'api_key', 'enabled', 'user_id']
        
        missing_columns = [col for col in required_columns if col not in columns]
        if missing_columns:
            print(f"❌ DirectConnections table missing columns: {missing_columns}")
            return False
        else:
            print("✅ DirectConnections table has required columns")
            
        # Check Configuration table
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='configurations'
        """)
        if cursor.fetchone():
            print("✅ Configuration table exists")
        else:
            print("❌ Configuration table missing")
            return False
            
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database structure test failed: {e}")
        return False

def test_template_files():
    """Test that the required template files exist."""
    print("\n=== Testing Template Files ===")
    
    try:
        templates_dir = project_root / "templates" / "settings"
        
        required_templates = [
            "connections.html",
            "api.html",
            "llm.html"
        ]
        
        for template in required_templates:
            template_path = templates_dir / template
            if template_path.exists():
                print(f"✅ Template {template} exists")
            else:
                print(f"❌ Template {template} missing")
                return False
                
        return True
        
    except Exception as e:
        print(f"❌ Template files test failed: {e}")
        return False

def test_javascript_files():
    """Test that the required JavaScript files exist."""
    print("\n=== Testing JavaScript Files ===")
    
    try:
        js_dir = project_root / "static" / "js"
        
        required_js_files = [
            "connections.js",
            "llm.js",
            "settings.js"
        ]
        
        for js_file in required_js_files:
            js_path = js_dir / js_file
            if js_path.exists():
                print(f"✅ JavaScript file {js_file} exists")
                
                # Check for key functions in connections.js
                if js_file == "connections.js":
                    with open(js_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    required_functions = [
                        "openProviderModal",
                        "saveProviderConnection", 
                        "loadConnections",
                        "renderConnections",
                        "PROVIDER_CONFIGS"
                    ]
                    
                    for func in required_functions:
                        if func in content:
                            print(f"  ✅ Function {func} found")
                        else:
                            print(f"  ❌ Function {func} missing")
                            return False
            else:
                print(f"❌ JavaScript file {js_file} missing")
                return False
                
        return True
        
    except Exception as e:
        print(f"❌ JavaScript files test failed: {e}")
        return False

def test_provider_configurations():
    """Test that provider configurations are comprehensive."""
    print("\n=== Testing Provider Configurations ===")
    
    try:
        js_path = project_root / "static" / "js" / "connections.js"
        with open(js_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check for all required providers
        required_providers = ["openai", "anthropic", "google", "azure", "smithery"]

        for provider in required_providers:
            if f'{provider}:' in content:
                print(f"✅ Provider {provider} configuration found")
            else:
                print(f"❌ Provider {provider} configuration missing")
                return False
                
        # Check for required configuration fields
        required_fields = ["defaultUrl", "defaultModels", "keyHelp", "urlHelp"]
        
        for field in required_fields:
            if field in content:
                print(f"✅ Configuration field {field} found")
            else:
                print(f"❌ Configuration field {field} missing")
                return False
                
        return True
        
    except Exception as e:
        print(f"❌ Provider configurations test failed: {e}")
        return False

def test_no_environment_dependencies():
    """Test that the system doesn't depend on environment variables."""
    print("\n=== Testing Environment Variable Independence ===")
    
    try:
        # Check that config files don't contain environment variable references
        config_path = project_root / "src" / "utils" / "config.py"
        with open(config_path, 'r', encoding='utf-8') as f:
            config_content = f.read()
            
        # Should not contain os.getenv calls for API keys
        problematic_patterns = [
            'os.getenv("OPENAI_API_KEY"',
            'os.getenv("ANTHROPIC_API_KEY"',
            'os.getenv("GOOGLE_API_KEY"',
            'os.getenv("AZURE_OPENAI_API_KEY"',
            'os.getenv("SMITHERY_API_KEY"'
        ]
        
        for pattern in problematic_patterns:
            if pattern in config_content:
                print(f"❌ Found environment variable dependency: {pattern}")
                return False
                
        print("✅ No environment variable dependencies found in config")
        
        # Check .env.sample file has deprecation notice
        env_sample_path = project_root.parent / ".env.sample"
        if env_sample_path.exists():
            with open(env_sample_path, 'r', encoding='utf-8') as f:
                env_content = f.read()
                
            if "DEPRECATED" in env_content or "no longer used" in env_content:
                print("✅ .env.sample file has deprecation notice")
            else:
                print("❌ .env.sample file missing deprecation notice")
                return False
        else:
            print("✅ No .env.sample file found (good)")
            
        return True
        
    except Exception as e:
        print(f"❌ Environment independence test failed: {e}")
        return False

def run_all_tests():
    """Run all tests and provide a summary."""
    print("🔍 Comprehensive Web Interface API Management Test")
    print("=" * 60)
    
    tests = [
        ("Web Interface Availability", test_web_interface_availability),
        ("API Endpoints", test_api_endpoints),
        ("Database Structure", test_database_structure),
        ("Template Files", test_template_files),
        ("JavaScript Files", test_javascript_files),
        ("Provider Configurations", test_provider_configurations),
        ("Environment Independence", test_no_environment_dependencies)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The web interface provides complete API key management.")
        print("   ✅ Users can configure all API providers through the web interface")
        print("   ✅ No environment variable or config file dependencies")
        print("   ✅ Database-driven configuration system working correctly")
        print("   ✅ Provider-specific setup wizards available")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Review the output above.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
