"""
Authentication controller module.

This module provides controller functions for handling authentication-related
routes, including login, logout, registration, and password management.
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_user, current_user, login_required, logout_user
import os
import time
from werkzeug.utils import secure_filename

from src.services.user_service import UserService
from src.services.auth_service import AuthenticationService
from src.services.base_service import get_service
from src.config.service import config_service
from src.models import User

# Create a blueprint for authentication routes
auth_bp = Blueprint("auth", __name__, url_prefix="/auth")

@auth_bp.route("/login", methods=["GET", "POST"])
def login():
    """Handle user login."""
    if current_user.is_authenticated:
        try:
            return redirect(url_for("main.dashboard"))
        except:
            return redirect(url_for("main.index"))

    if request.method == "POST":
        username_or_email = request.form.get("username")
        password = request.form.get("password")
        remember = bool(request.form.get("remember"))

        if not username_or_email or not password:
            flash("Please enter both username/email and password", "error")
            return render_template("auth/login.html")

        # Use the authentication service to handle login
        auth_service = get_service(AuthenticationService)
        success, user, error = auth_service.login(username_or_email, password, remember)

        if success:
            flash("Login successful", "success")

            # Redirect to the requested page or dashboard
            next_page = request.args.get("next")
            if not next_page or not next_page.startswith("/"):
                try:
                    next_page = url_for("main.dashboard")
                except:
                    # Fallback to index if dashboard isn't available
                    next_page = url_for("main.index")

            return redirect(next_page)

        flash(error or "Login failed", "error")
        return render_template("auth/login.html")

    return render_template("auth/login.html")

@auth_bp.route("/logout")
@login_required
def logout():
    """Handle user logout."""
    auth_service = get_service(AuthenticationService)
    auth_service.logout()
    flash("You have been logged out", "info")
    return redirect(url_for("auth.login"))

@auth_bp.route("/register", methods=["GET", "POST"])
def register():
    """
    Handle user registration.
    
    GET: Render the registration form
    POST: Process the registration form submission
    """
    # If user is already authenticated, redirect to dashboard
    if current_user.is_authenticated:
        try:
            return redirect(url_for("main.dashboard"))
        except:
            return redirect(url_for("main.index"))
    
    # Get the user service
    user_service = get_service(UserService)
    
    # Check if any users exist in the database
    users_exist = User.query.count() > 0
    
    # Allow registration if no users exist, otherwise check config
    registration_enabled = not users_exist or config_service.get("REGISTRATION_ENABLED", False, "bool")
    if not registration_enabled:
        flash("Registration is currently disabled", "warning")
        return redirect(url_for("auth.login"))
    
    # Handle form submission
    if request.method == "POST":
        username = request.form.get("username")
        email = request.form.get("email")
        password = request.form.get("password")
        confirm_password = request.form.get("confirm_password")
        
        # Validate passwords match
        if password != confirm_password:
            flash("Passwords do not match", "danger")
            return render_template("auth/register.html")
        
        # Get the user service
        user_service = get_service(UserService)
        
        try:
            # Determine if this is the first user (admin)
            is_first_user = User.query.count() == 0
            role = "admin" if is_first_user else "user"
            
            # Create the user with appropriate role
            user = user_service.create_user(
                username=username,
                email=email,
                password=password,
                role=role
            )
            
            # Log the user in
            login_user(user)
            
            flash("Registration successful", "success")
            return redirect(url_for("main.dashboard"))
        except ValueError as e:
            flash(str(e), "danger")
    
    # Render the registration form
    return render_template("auth/register.html")

@auth_bp.route("/profile", methods=["GET", "POST"])
@login_required
def profile():
    """
    Handle user profile management.
    
    GET: Render the profile form
    POST: Process the profile form submission
    """
    # Get the user service
    user_service = get_service(UserService)
    
    # Handle form submission
    if request.method == "POST":
        email = request.form.get("email")
        current_password = request.form.get("current_password")
        new_password = request.form.get("new_password")
        confirm_password = request.form.get("confirm_password")
        
        # Validate current password if provided
        if current_password:
            if not current_user.check_password(current_password):
                flash("Current password is incorrect", "danger")
                return redirect(url_for("main.settings") + "?section=account")
            
            # Validate new password
            if new_password != confirm_password:
                flash("New passwords do not match", "danger")
                return redirect(url_for("main.settings") + "?section=account")
            
            # Update the password
            user_service.update_user(
                current_user.id,
                password=new_password
            )
            
            flash("Password updated successfully", "success")
        
        # Update other profile information
        updates = {}
        if email and email != current_user.email:
            updates["email"] = email
        
        # Additional profile fields
        for field in ["display_name", "bio", "location", "phone"]:
            if field in request.form:
                updates[field] = request.form.get(field)
        
        # Update theme preference
        if "theme_preference" in request.form:
            updates["theme_preference"] = request.form.get("theme_preference")
        
        # Apply updates if any
        if updates:
            user_service.update_user(current_user.id, **updates)
            flash("Profile updated successfully", "success")
        
        return redirect(url_for("main.settings") + "?section=account")
    
    # Redirect to the settings page with the account section selected
    return redirect(url_for("main.settings") + "?section=account")

@auth_bp.route("/api-keys")
@login_required
def api_keys():
    """Handle API key management."""
    auth_service = get_service(AuthenticationService)
    api_keys = auth_service.get_api_keys_for_user(current_user.id)
    return render_template("auth/api_keys.html", api_keys=api_keys)

@auth_bp.route("/api-keys/create", methods=["POST"])
@login_required
def create_api_key():
    """Handle API key creation."""
    # Get the authentication service
    auth_service = get_service(AuthenticationService)
    
    # Create a new API key
    api_key_name = request.form.get("name", "")
    api_key = auth_service.generate_api_key(current_user.id, api_key_name)
    
    if api_key:
        flash(f"API key created: {api_key.id}", "success")
    else:
        flash("Failed to create API key", "error")
        
    return redirect(url_for("auth.api_keys"))

@auth_bp.route("/api-keys/<api_key_id>/delete", methods=["POST"])
@login_required
def delete_api_key(api_key_id):
    """
    Handle API key deletion.
    
    Args:
        api_key_id: ID of the API key to delete
    """
    # Get the authentication service
    auth_service = get_service(AuthenticationService)
    
    # Delete the API key
    success, error = auth_service.delete_api_key(api_key_id, current_user.id)
    
    if success:
        flash("API key deleted successfully", "success")
    else:
        flash(error or "Failed to delete API key", "danger")
    
    return redirect(url_for("auth.api_keys"))

@auth_bp.route("/token", methods=["POST"])
def get_token():
    """
    Generate a JWT token for API authentication.
    
    This endpoint accepts username/email and password or an API key
    and returns a JWT token for API authentication.
    """
    # Get the authentication service
    auth_service = get_service(AuthenticationService)
    
    # Get request data
    data = request.get_json()
    if not data:
        data = request.form
    
    # Check for API key
    api_key = data.get("api_key")
    if api_key:
        # Verify the API key
        user = auth_service.verify_api_key(api_key)
        if not user:
            return jsonify({"error": "Invalid API key"}), 401
    else:
        # Check for username/email and password
        username_or_email = data.get("username")
        password = data.get("password")
        
        if not username_or_email or not password:
            return jsonify({"error": "Missing username/email or password"}), 400
        
        # Authenticate the user
        success, user, error = auth_service.login(username_or_email, password, remember=False)
        if not success or not user:
            return jsonify({"error": error or "Invalid credentials"}), 401
    
    # Generate JWT token
    token = auth_service.generate_jwt_token(user)
    
    # Get token expiration from config
    expires_in = int(config_service.get("JWT_EXPIRES_IN", "3600"))
    
    return jsonify({
        "token": token,
        "expires_in": expires_in,
        "user": {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "role": user.role
        }
    })

# Handle account settings updates.
@auth_bp.route("/account_settings", methods=["GET", "POST"])
@login_required
def account_settings():
    """Handle user account settings updates."""
    if request.method == "POST":
        try:
            # Handle profile picture upload
            profile_picture = request.files.get("profile_picture")
            updates = {}
            
            if profile_picture and profile_picture.filename:
                # Generate a unique filename
                filename = secure_filename(profile_picture.filename)
                unique_filename = f"{current_user.id}_{int(time.time())}_{filename}"
                
                # Ensure the upload directory exists
                upload_dir = os.path.join(current_app.root_path, "static", "uploads", "profile_pics")
                os.makedirs(upload_dir, exist_ok=True)
                
                # Save the file
                file_path = os.path.join(upload_dir, unique_filename)
                profile_picture.save(file_path)
                
                # Update the user's profile picture path (relative to static)
                updates["profile_picture"] = f"uploads/profile_pics/{unique_filename}"
            
            # Process other profile fields
            for field in ["display_name", "bio", "location", "phone"]:
                if field in request.form:
                    updates[field] = request.form.get(field)
            
            # Apply updates if any
            if updates:
                user_service = get_service(UserService)
                user_service.update_user(current_user.id, **updates)
                flash("Profile updated successfully", "success")
            
            return redirect(url_for("main.settings") + "?section=account")
        except Exception as e:
            flash(f"Error updating profile: {str(e)}", "error")
            return redirect(url_for("main.settings") + "?section=account")
    
    # GET requests should be handled by the profile route
    return redirect(url_for("main.settings") + "?section=account")

# Handle username updates.
@auth_bp.route("/update-username", methods=["POST"])
@login_required
def update_username():
    """Handle username update."""
    if request.method == "POST":
        new_username = request.form.get("new_username")
        
        if not new_username:
            flash("Please enter a new username", "error")
            return redirect(url_for("main.settings") + "?section=account")
        
        # Get the user service
        user_service = get_service(UserService)
        
        # Check if username is already taken
        if user_service.get_user_by_username(new_username) and new_username != current_user.username:
            flash("Username is already taken", "error")
            return redirect(url_for("main.settings") + "?section=account")
        
        # Update the username
        user_service.update_user(current_user.id, username=new_username)
        flash("Username updated successfully", "success")
        
    return redirect(url_for("main.settings") + "?section=account")

# Handle password reset.
@auth_bp.route("/reset-password", methods=["POST"])
@login_required
def reset_password():
    """Handle password reset."""
    if request.method == "POST":
        current_password = request.form.get("current_password")
        new_password = request.form.get("new_password")
        confirm_password = request.form.get("confirm_password")
        
        if not current_password or not new_password or not confirm_password:
            flash("Please fill in all password fields", "error")
            return redirect(url_for("main.settings") + "?section=account")
        
        if new_password != confirm_password:
            flash("New passwords do not match", "error")
            return redirect(url_for("main.settings") + "?section=account")
        
        # Get the user service
        user_service = get_service(UserService)
        
        # Verify current password
        if not user_service.verify_password(current_user, current_password):
            flash("Current password is incorrect", "error")
            return redirect(url_for("main.settings") + "?section=account")
        
        # Update the password using update_user with password parameter
        user_service.update_user(current_user.id, password=new_password)
        flash("Password updated successfully", "success")
        
    return redirect(url_for("main.settings") + "?section=account")

def register_blueprints(app):
    """
    Register all authentication-related blueprints with the app.
    
    Args:
        app: Flask application instance
    """
    # Register the authentication blueprint
    app.register_blueprint(auth_bp)
