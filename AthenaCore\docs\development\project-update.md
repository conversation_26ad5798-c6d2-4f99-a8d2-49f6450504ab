# Athena Background Task Tracking System

## Overview

This document outlines the implementation plan for adding a background task tracking system to <PERSON>. The system will allow <PERSON> to execute tasks in the background while remaining responsive to user commands. It will include a notification system to inform users of task status and completion.

## Current System Analysis

Athena already has a foundation for background task execution:

- **TaskExecutor** (`src/services/task_executor.py`): A service that manages the execution of background tasks in separate threads
- **Command and CommandLog models** (`src/login/device_models.py`): Database models for storing task information and execution logs
- **Tasks API** (`src/api/tasks.py`): API endpoints for creating, tracking, and managing background tasks

## Implementation Plan

### 1. Real-time Notification System

Add real-time communication capabilities using WebSockets to deliver task status updates to the client:

```python
# Add to app initialization
from flask_socketio import SocketIO
socketio = SocketIO()

def create_app():
    app = Flask(__name__)
    # ...existing initialization...
    socketio.init_app(app)
    return app
```

Emit task status updates whenever a task changes state:

```python
# In task_executor.py
def _update_task_status(self, status, message=None, progress=None):
    # Update the command in the database
    # ...existing code...
    
    # Emit an event to connected clients
    task_data = self.command.to_dict()
    socketio.emit('task_update', task_data, room=f'user_{self.user_id}')
```

### 2. UI Components

#### 2.1 Notification Bell

Add a notification bell to the main interface that shows the number of active tasks:

```html
<div id="notification-bell" class="notification-bell">
    <i class="fa fa-bell"></i>
    <span class="notification-badge" id="notification-count">0</span>
</div>
```

```javascript
// Update notification count when tasks change
socket.on('task_update', function(data) {
    updateNotificationCount();
});

function updateNotificationCount() {
    fetch('/api/tasks/background?status=running,pending')
        .then(response => response.json())
        .then(data => {
            const count = data.tasks.length;
            document.getElementById('notification-count').textContent = count;
            document.getElementById('notification-count').style.display = count > 0 ? 'block' : 'none';
        });
}
```

#### 2.2 Task Panel

Create a panel to display active and completed tasks:

```html
<div id="task-panel" class="task-panel">
    <div class="task-panel-header">
        <h3>Tasks</h3>
        <div class="task-filter">
            <button class="active" data-filter="active">Active</button>
            <button data-filter="completed">Completed</button>
        </div>
        <button class="close-panel">×</button>
    </div>
    <div class="task-list" id="task-list">
        <!-- Task items will be populated here -->
    </div>
</div>
```

#### 2.3 Task Item

Create expandable task items that show details and progress:

```html
<div class="task-item" data-id="${task.command_uuid}">
    <div class="task-header">
        <div class="task-name">${task.capability_name}</div>
        <div class="task-status ${task.status}">${task.status}</div>
        <div class="task-progress">
            <div class="progress-bar" style="width: ${task.progress}%"></div>
        </div>
        <button class="expand-task">↓</button>
    </div>
    <div class="task-details">
        <div class="task-logs">
            <!-- Task logs will be populated here -->
        </div>
    </div>
</div>
```

### 3. Task Executor Enhancements

Extend the TaskExecutor to provide more detailed progress updates:

```python
class TaskExecutor:
    # ...existing code...
    
    def log_progress(self, message, progress=None):
        """Log progress message and optionally update progress percentage."""
        with self.app.app_context():
            # Add log entry
            log_entry = CommandLog(
                command_id=self.command.id,
                status=self.command.status,
                message=message
            )
            db.session.add(log_entry)
            
            # Update progress if provided
            if progress is not None:
                self.command.progress = progress
                db.session.commit()
            
            # Emit update
            socketio.emit(
                'task_progress', 
                {
                    'command_uuid': self.command.command_uuid,
                    'message': message,
                    'progress': self.command.progress,
                    'timestamp': int(datetime.utcnow().timestamp())
                },
                room=f'user_{self.user_id}'
            )
```

### 4. Client-side JavaScript

Add client-side JavaScript to handle WebSocket events and update the UI:

```javascript
// Initialize Socket.IO connection
const socket = io();

// Join user-specific room for receiving updates
socket.on('connect', function() {
    socket.emit('join', { user_id: USER_ID });
});

// Listen for task updates
socket.on('task_update', function(data) {
    updateTaskInList(data);
    updateNotificationCount();
    
    // Show notification for completed tasks
    if (data.status === 'completed') {
        showNotification(`Task ${data.capability_name} completed`);
    } else if (data.status === 'failed') {
        showNotification(`Task ${data.capability_name} failed`, 'error');
    }
});

// Listen for task progress updates
socket.on('task_progress', function(data) {
    updateTaskProgress(data);
});
```

### 5. API Enhancements

Add API endpoints for retrieving task logs and managing notifications:

```python
@tasks_bp.route("/background/<command_uuid>/logs", methods=["GET"])
@login_required
def get_task_logs(command_uuid):
    """Get logs for a specific background task."""
    command = Command.query.filter_by(
        command_uuid=command_uuid, user_id=current_user.id
    ).first_or_404()
    
    logs = CommandLog.query.filter_by(command_id=command.id).order_by(CommandLog.timestamp).all()
    
    return jsonify({
        "logs": [log.to_dict() for log in logs]
    })
```

## Implementation Sequence

1. **Backend Infrastructure**
   - Add WebSocket support with Flask-SocketIO
   - Extend TaskExecutor to emit events on status changes
   - Create task log streaming endpoints

2. **Frontend Components**
   - Build notification bell component
   - Implement expandable task panel
   - Create task item components with progress visualization

3. **Integration**
   - Connect WebSocket events to UI components
   - Implement automatic task status updates
   - Add notification system for task completion

4. **Testing**
   - Test with long-running tasks
   - Verify real-time updates
   - Test notification delivery

## Benefits

- **Improved User Experience**: Users will know when background tasks complete without having to check manually
- **Increased Productivity**: Users can start tasks and continue working with Athena while tasks execute
- **Better Visibility**: Clear indication of what tasks are running and their current status
- **Enhanced Communication**: Real-time feedback on task progress

## Technical Requirements

- Flask-SocketIO
- JavaScript Socket.IO client
- CSS for notification and panel styling
- Extended TaskExecutor with progress reporting
