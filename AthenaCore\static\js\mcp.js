/**
 * MCP Server Management JavaScript
 * Handles interactions with MCP servers in the Athena Core frontend
 */

// Global initialization state
window.mcpInitialized = false;

/**
 * Initialize the MCP page
 */
window.initMcpPage = function() {
    // If already initialized, don't initialize again
    if (window.mcpInitialized) {
        console.log('MCP page already initialized, skipping initialization');
        return;
    }

    // Create alerts container if it doesn't exist
    if (!document.getElementById('alerts-container')) {
        console.log('Creating alerts container');
        const wrapper = document.getElementById('mcp-content-wrapper');
        if (wrapper) {
            const alertsContainer = document.createElement('div');
            alertsContainer.id = 'alerts-container';
            alertsContainer.className = 'mb-4';
            wrapper.prepend(alertsContainer);
        }
    }

    // Check if we have the required elements before initializing anything
    let mcpServersList = document.getElementById('mcpServersList');
    let activeConnectionsList = document.getElementById('activeConnectionsList');

    // If key elements aren't found, try to create them
    if (!mcpServersList || !activeConnectionsList) {
        console.warn('MCP elements not found, attempting to create them');

        // Try to find the MCP content wrapper
        const mcpContainer = document.getElementById('mcp-content-wrapper') ||
                            document.querySelector('.mcp-content') ||
                            document.querySelector('#mcp-section');

        if (mcpContainer) {
            // Create a warning message
            const warningDiv = document.createElement('div');
            warningDiv.className = 'alert alert-warning';
            warningDiv.innerHTML = 'Some MCP elements were missing and have been recreated. The page may not function correctly.';
            mcpContainer.prepend(warningDiv);

            // Create the server list table if it doesn't exist
            if (!mcpServersList) {
                console.log('Creating server list table');
                const serversSection = document.createElement('div');
                serversSection.className = 'card mb-4';
                serversSection.innerHTML = `
                    <div class="card-header">
                        <h5 class="mb-0">Available MCP Servers</h5>
                    </div>
                    <div class="card-body">
                        <div id="serverListProgress" class="progress mb-3" style="display: none;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                        </div>
                        <div class="table-responsive">
                            <table id="mcpServersTable" class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Version</th>
                                        <th>Author</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="mcpServersList">
                                    <tr>
                                        <td colspan="5" class="text-center">Loading servers...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;
                mcpContainer.appendChild(serversSection);
                mcpServersList = document.getElementById('mcpServersList');
            }

            // Create the connections list table if it doesn't exist
            if (!activeConnectionsList) {
                console.log('Creating connections list table');
                const connectionsSection = document.createElement('div');
                connectionsSection.className = 'card mb-4';
                connectionsSection.innerHTML = `
                    <div class="card-header">
                        <h5 class="mb-0">Active MCP Connections</h5>
                    </div>
                    <div class="card-body">
                        <div id="activeConnectionsProgress" class="progress mb-3" style="display: none;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                        </div>
                        <div class="table-responsive">
                            <table id="activeConnectionsTable" class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Server Name</th>
                                        <th>Connection ID</th>
                                        <th>Connected At</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="activeConnectionsList">
                                    <tr>
                                        <td colspan="5" class="text-center">Loading connections...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;
                mcpContainer.appendChild(connectionsSection);
                activeConnectionsList = document.getElementById('activeConnectionsList');
            }

            // Create the connection modal if it doesn't exist
            if (!document.getElementById('connectionModal')) {
                console.log('Creating connection modal');
                const modalDiv = document.createElement('div');
                modalDiv.className = 'mcp-connection-modal';
                modalDiv.id = 'connectionModal';
                modalDiv.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0,0,0,0.5);
                    display: none;
                    justify-content: center;
                    align-items: center;
                    z-index: 10000;
                `;
                modalDiv.innerHTML = `
                    <div class="modal-content" style="
                        background-color: white;
                        padding: 20px;
                        border-radius: 8px;
                        width: 90%;
                        max-width: 500px;
                        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                    ">
                        <div class="modal-header" style="
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 20px;
                            border-bottom: 1px solid #dee2e6;
                            padding-bottom: 15px;
                        ">
                            <h5 class="modal-title" id="connectionModalLabel">Connect to MCP Server</h5>
                            <button type="button" class="btn-close" onclick="closeMCPConnectionModal()" style="
                                background: none;
                                border: none;
                                font-size: 24px;
                                cursor: pointer;
                                color: #aaa;
                                padding: 0;
                                width: 30px;
                                height: 30px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                            ">&times;</button>
                        </div>
                        <div class="modal-body">
                            <p id="connectionMessage">Connecting to server...</p>
                            <div class="progress" style="
                                width: 100%;
                                height: 20px;
                                background-color: #e9ecef;
                                border-radius: 10px;
                                overflow: hidden;
                                margin-top: 15px;
                            ">
                                <div class="progress-bar" style="
                                    height: 100%;
                                    background-color: #007bff;
                                    width: 100%;
                                    animation: progress-animation 1.5s ease-in-out infinite;
                                "></div>
                            </div>
                        </div>
                        <div class="modal-footer" style="
                            display: flex;
                            justify-content: flex-end;
                            margin-top: 20px;
                            border-top: 1px solid #dee2e6;
                            padding-top: 15px;
                        ">
                            <button type="button" class="btn btn-secondary" onclick="closeMCPConnectionModal()" style="
                                padding: 10px 20px;
                                border: none;
                                border-radius: 4px;
                                cursor: pointer;
                                background-color: #6c757d;
                                color: white;
                                font-weight: 500;
                            ">Cancel</button>
                        </div>
                    </div>
                `;

                // Add CSS animation for progress bar
                if (!document.getElementById('mcp-modal-styles')) {
                    const style = document.createElement('style');
                    style.id = 'mcp-modal-styles';
                    style.textContent = `
                        @keyframes progress-animation {
                            0% { transform: translateX(-100%); }
                            50% { transform: translateX(0%); }
                            100% { transform: translateX(100%); }
                        }
                        .btn-close:hover {
                            color: #000 !important;
                        }
                        .btn:hover {
                            opacity: 0.8;
                        }
                    `;
                    document.head.appendChild(style);
                }

                document.body.appendChild(modalDiv);

                // Ensure modal is hidden initially
                modalDiv.style.display = 'none';

                // Add click outside to close
                modalDiv.addEventListener('click', function(e) {
                    if (e.target === modalDiv) {
                        closeMCPConnectionModal();
                    }
                });

                // Add escape key handler
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && modalDiv.style.display === 'flex') {
                        closeMCPConnectionModal();
                    }
                });
            }
        }
    }

    console.log('MCP elements found, continuing initialization');

    try {
        // Set up event handlers if the connect form exists
        // Define setupEventHandlers function if it doesn't exist
        function setupEventHandlers() {
            console.log('Setting up MCP event handlers');

            // Set up search form
            const searchForm = document.getElementById('searchForm');
            if (searchForm) {
                searchForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const searchQuery = document.getElementById('searchQuery');
                    if (searchQuery) {
                        searchMcpServers(searchQuery.value);
                    }
                });
            }

            // Set up search input
            const searchInput = document.getElementById('serverSearchInput');
            const searchBtn = document.getElementById('searchBtn');
            if (searchInput && searchBtn) {
                searchBtn.addEventListener('click', function() {
                    searchMcpServers(searchInput.value);
                });

                // Also search when Enter is pressed in the input
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        searchMcpServers(searchInput.value);
                    }
                });
            }

            // Set up refresh buttons
            const refreshServersBtn = document.getElementById('refreshServersBtn');
            if (refreshServersBtn) {
                refreshServersBtn.addEventListener('click', function() {
                    loadAvailableServers();
                });
            }

            const refreshConnectionsBtn = document.getElementById('refreshConnectionsBtn');
            if (refreshConnectionsBtn) {
                refreshConnectionsBtn.addEventListener('click', function() {
                    loadActiveConnections();
                });
            }

            // Set up event delegation for dynamic elements
            document.addEventListener('click', function(e) {
                // Handle connect buttons
                if (e.target && (e.target.classList.contains('connect-btn') ||
                                (e.target.parentElement && e.target.parentElement.classList.contains('connect-btn')))) {
                    const button = e.target.classList.contains('connect-btn') ? e.target : e.target.parentElement;
                    const serverName = button.getAttribute('data-server');
                    const displayName = button.getAttribute('data-display');
                    if (serverName && displayName) {
                        connectToServer(serverName, displayName);
                    }
                }

                // Handle disconnect buttons
                if (e.target && (e.target.classList.contains('disconnect-btn') ||
                                (e.target.parentElement && e.target.parentElement.classList.contains('disconnect-btn')))) {
                    const button = e.target.classList.contains('disconnect-btn') ? e.target : e.target.parentElement;
                    const connectionId = button.getAttribute('data-connection');
                    const displayName = button.getAttribute('data-display');
                    if (connectionId && displayName) {
                        disconnectFromServer(connectionId, displayName);
                    }
                }
            });
        }

        // Call the function to set up event handlers
        setupEventHandlers();

        // Load available servers and active connections with error handling
        if (window.loadAvailableServers) {
            setTimeout(() => window.loadAvailableServers(), 100);
        } else {
            console.error('loadAvailableServers function not found');
        }

        if (window.loadActiveConnections) {
            setTimeout(() => window.loadActiveConnections(), 300);
        } else {
            console.error('loadActiveConnections function not found');
        }

        // Mark as initialized
        window.mcpInitialized = true;
        console.log('MCP page initialization complete');
    } catch (error) {
        console.error('Error during MCP page initialization:', error);

        // Try to find an alerts container to show the error
        let alertsContainer = document.getElementById('alerts-container');
        if (!alertsContainer) {
            alertsContainer = document.querySelector('.alerts-container');
        }
        if (!alertsContainer) {
            alertsContainer = document.querySelector('.mcp-alerts');
        }

        // If we still don't have an alerts container, try to create one
        if (!alertsContainer) {
            const mcpContainer = document.getElementById('mcp-content-wrapper');
            if (mcpContainer) {
                alertsContainer = document.createElement('div');
                alertsContainer.id = 'alerts-container';
                alertsContainer.className = 'mb-4';
                mcpContainer.prepend(alertsContainer);
            }
        }

        if (alertsContainer) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'alert alert-danger alert-dismissible fade show';
            errorDiv.innerHTML = `
                Failed to initialize MCP page: ${error.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;
            alertsContainer.appendChild(errorDiv);

            // Auto-dismiss after 10 seconds
            setTimeout(() => {
                errorDiv.classList.remove('show');
                setTimeout(() => errorDiv.remove(), 500);
            }, 10000);
        }
    }
};

/**
 * Function to create MCP elements if they don't exist
 */
function tryCreateMcpElements() {
    const contentContainer = document.getElementById('content-container');
    if (!contentContainer) {
        console.error('Content container not found, cannot create MCP elements');
        return;
    }

    // Check if MCP content already exists
    if (contentContainer.querySelector('#mcp-content')) {
        console.log('MCP content already exists, skipping creation');
        return;
    }

    console.log('Creating MCP elements...');

    // Create the MCP content container
    const mcpContent = document.createElement('div');
    mcpContent.id = 'mcp-content';
    mcpContent.innerHTML = `
        <h2>MCP Servers</h2>
        <div class="mcp-container">
            <div class="mcp-section">
                <h3>Available Servers</h3>
                <div id="available-servers-container" class="server-list"></div>
            </div>
            <div class="mcp-section">
                <h3>Active Connections</h3>
                <div id="active-connections-container" class="connection-list"></div>
            </div>
        </div>
    `;

    // Clear the content container and append the MCP content
    contentContainer.innerHTML = '';
    contentContainer.appendChild(mcpContent);

    // Try initializing again after a short delay
    setTimeout(() => {
        window.mcpInitialized = false; // Reset initialization flag
        window.initMcpPage(); // Try initializing again
    }, 500);
}

// Initialize when all scripts are loaded
document.addEventListener('athenaScriptsLoaded', function() {
    console.log('All scripts loaded, checking if MCP section is active...');
    // Check if we're on the settings page with the MCP section active
    const contentContainer = document.getElementById('content-container');
    const mcpNavItem = document.querySelector('.nav-item[data-section="mcp"]');

    if (contentContainer && mcpNavItem && mcpNavItem.classList.contains('active')) {
        console.log('MCP section is active, initializing...');
        window.initMcpPage();
    } else {
        console.log('MCP section is not active, skipping initialization');
    }
});

// Initialize when the DOM is loaded (fallback)
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on the settings page with the MCP section active
    const contentContainer = document.getElementById('content-container');
    const mcpNavItem = document.querySelector('.nav-item[data-section="mcp"]');

    if (contentContainer && mcpNavItem && mcpNavItem.classList.contains('active')) {
        console.log('MCP section is active, initializing...');
        window.initMcpPage();
    } else {
        console.log('MCP section is not active, skipping initialization');
    }
});

// Also initialize when the MCP section is loaded
document.addEventListener('sectionLoaded', function(event) {
    if (event.detail && event.detail.section === 'mcp') {
        console.log('MCP section loaded event detected, initializing...');
        // Reset initialization flag to ensure proper initialization
        window.mcpInitialized = false;
        // Give a small delay to ensure DOM is updated
        setTimeout(() => {
            window.initMcpPage();
        }, 100);
    }
});

// Listen for the custom mcpContentLoaded event
document.addEventListener('mcpContentLoaded', function() {
    console.log('MCP content loaded event received');
    if (typeof window.initMcpPage === 'function') {
        window.initMcpPage();
    }
});

// For backward compatibility and direct script inclusion
document.addEventListener('DOMContentLoaded', function() {
    console.log('MCP.js loaded via direct script inclusion');

    // Check if we're on the MCP page
    if (document.getElementById('mcp-content-wrapper')) {
        console.log('MCP page detected, initializing');
        if (typeof window.initMcpPage === 'function') {
            window.initMcpPage();
        }
    }
});

// Also initialize when content is dynamically loaded
document.addEventListener('contentLoaded', function(e) {
    console.log('Content loaded event received, checking for MCP content');
    // Reset initialization state when content changes
    window.mcpInitialized = false;

    // Check if the MCP content is now present
    if (document.getElementById('mcp-content-wrapper')) {
        console.log('MCP content detected after content load');
        if (typeof window.initMcpPage === 'function') {
            window.initMcpPage();
        }
    }
});

/**
 * Load available MCP servers
 */
window.loadAvailableServers = function() {
    showProgress('serverListProgress');

    // Call the API to get available servers
    fetch('/mcp/servers')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            hideProgress('serverListProgress');
            if (data.success) {
                displayServerList(data.servers || []);
            } else {
                showError('Failed to load MCP servers: ' + (data.error || 'Unknown error'));
                // Display empty list as fallback
                displayServerList([]);
            }
        })
        .catch(error => {
            hideProgress('serverListProgress');
            console.error('Error loading MCP servers:', error);
            showError('Error loading MCP servers. Please check the console for details.');
            // Display empty list as fallback
            displayServerList([]);
        });
}

/**
 * Search for MCP servers
 * @param {string} query - Search query
 */
window.searchMcpServers = function(query) {
    if (!query) {
        loadAvailableServers();
        return;
    }

    showProgress('serverListProgress');

    // Call the API to search for servers
    fetch(`/mcp/servers?query=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            hideProgress('serverListProgress');
            if (data.success) {
                displayServerList(data.servers || []);
            } else {
                showError('Search failed: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            hideProgress('serverListProgress');
            showError('Search error: ' + error);
        });
}

/**
 * Load active MCP connections
 */
window.loadActiveConnections = function() {
    showProgress('activeConnectionsProgress');

    // Call the API to get active connections
    fetch('/mcp/connections')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            hideProgress('activeConnectionsProgress');
            if (data.success) {
                displayConnectionsList(data.connections || []);
            } else {
                showError('Failed to load active connections: ' + (data.error || 'Unknown error'));
                // Display empty list as fallback
                displayConnectionsList([]);
            }
        })
        .catch(error => {
            hideProgress('activeConnectionsProgress');
            console.error('Error loading active connections:', error);
            showError('Error loading active connections. Please check the console for details.');
            // Display empty list as fallback
            displayConnectionsList([]);
        });
}

/**
 * Connect to an MCP server
 * @param {string} serverName - Qualified name of the server
 * @param {string} displayName - Display name of the server
 */
function connectToServer(serverName, displayName) {
    console.log('connectToServer called with:', serverName, displayName);
    console.trace('connectToServer call stack');

    // Get the modal element
    const modalElement = document.getElementById('connectionModal');
    if (!modalElement) {
        console.error('Connection modal not found');
        showError(`Cannot connect to ${displayName}: UI elements not found`);
        return;
    }

    // Show connection modal using custom modal system
    showMCPConnectionModal();

    // Update modal content
    const modalLabel = document.getElementById('connectionModalLabel');
    const connectionMessage = document.getElementById('connectionMessage');

    if (modalLabel) {
        modalLabel.textContent = `Connect to ${displayName}`;
    }

    if (connectionMessage) {
        connectionMessage.textContent = `Connecting to ${displayName}...`;
    }

    // Call the API to connect to the server
    fetch('/mcp/connect', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            mcp_name: serverName,
            config: {}
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (connectionMessage) {
                connectionMessage.textContent = `Successfully connected to ${displayName}`;
            }

            // Reload active connections after a short delay
            setTimeout(() => {
                loadActiveConnections();
                closeMCPConnectionModal();
            }, 1500);
        } else {
            if (connectionMessage) {
                connectionMessage.textContent = `Failed to connect: ${data.error || 'Unknown error'}`;
            }
            console.error('Failed to connect:', data.error || 'Unknown error');
        }
    })
    .catch(error => {
        if (connectionMessage) {
            connectionMessage.textContent = `Connection error: ${error}`;
        }
        console.error('Connection error:', error);
    });
}

/**
 * Disconnect from an MCP server
 * @param {string} connectionId - Connection ID
 * @param {string} displayName - Display name of the server
 */
function disconnectFromServer(connectionId, displayName) {
    try {
        if (!confirm(`Are you sure you want to disconnect from ${displayName}?`)) {
            return;
        }

        // Call the API to disconnect
        fetch(`/mcp/disconnect/${connectionId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(`Disconnected from ${displayName}`);
                loadActiveConnections();
            } else {
                showError(`Failed to disconnect: ${data.error || 'Unknown error'}`);
            }
        })
        .catch(error => {
            console.error('Disconnect error:', error);
            showError(`Disconnect error: ${error}`);
        });
    } catch (error) {
        console.error('Error in disconnectFromServer:', error);
        showError(`Error processing disconnect request: ${error.message}`);
    }
}

/**
 * Display the list of available MCP servers
 * @param {Array} servers - List of server objects
 */
function displayServerList(servers) {
    const tableBody = document.getElementById('mcpServersList');
    if (!tableBody) {
        console.error('Cannot display server list: mcpServersList element not found');
        return;
    }

    tableBody.innerHTML = '';

    if (!servers || servers.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center">No servers found.</td>
            </tr>
        `;
        return;
    }

    servers.forEach(server => {
        const row = document.createElement('tr');

        // Extract display name and author from qualified name if available
        const displayName = server.displayName || server.name || 'Unknown';
        const description = server.description || 'No description available';
        const version = server.version || 'Unknown';
        const author = server.owner || (server.qualifiedName ? server.qualifiedName.split('/')[0] : 'Unknown');

        row.innerHTML = `
            <td>${displayName}</td>
            <td>${description}</td>
            <td>${version}</td>
            <td>${author}</td>
            <td>
                <button class="btn btn-sm btn-primary connect-btn"
                        data-server="${server.qualifiedName}"
                        data-display="${displayName}">
                    <i class="fas fa-plug"></i> Connect
                </button>
            </td>
        `;

        tableBody.appendChild(row);
    });

    // Add event listeners to connect buttons
    document.querySelectorAll('.connect-btn').forEach(button => {
        button.addEventListener('click', function() {
            const serverName = this.getAttribute('data-server');
            const displayName = this.getAttribute('data-display');
            connectToServer(serverName, displayName);
        });
    });
}

/**
 * Show progress indicator
 * @param {string} id - ID of the progress element
 */
function showProgress(id) {
    const progressElement = document.getElementById(id);
    if (progressElement) {
        progressElement.style.display = 'block';
    } else {
        console.warn(`Progress element with ID '${id}' not found`);
    }
}

/**
 * Hide progress indicator
 * @param {string} id - ID of the progress element
 */
function hideProgress(id) {
    const progressElement = document.getElementById(id);
    if (progressElement) {
        progressElement.style.display = 'none';
    } else {
        console.warn(`Progress element with ID '${id}' not found`);
    }
}

/**
 * Show error message
 * @param {string} message - Error message to display
 */
function showError(message) {
    console.error('MCP Error:', message);

    // Try to find the alerts container in different locations
    let alertsContainer = document.getElementById('alerts-container');
    if (!alertsContainer) {
        alertsContainer = document.querySelector('.alerts-container');
    }
    if (!alertsContainer) {
        alertsContainer = document.querySelector('.mcp-alerts');
    }

    if (alertsContainer) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        alertsContainer.appendChild(alertDiv);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            alertDiv.classList.remove('show');
            setTimeout(() => alertDiv.remove(), 500);
        }, 5000);
    }
}

/**
 * Show success message
 * @param {string} message - Success message to display
 */
function showSuccess(message) {
    console.log('MCP Success:', message);

    // Try to find the alerts container in different locations
    let alertsContainer = document.getElementById('alerts-container');
    if (!alertsContainer) {
        alertsContainer = document.querySelector('.alerts-container');
    }
    if (!alertsContainer) {
        alertsContainer = document.querySelector('.mcp-alerts');
    }

    if (alertsContainer) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        alertsContainer.appendChild(alertDiv);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            alertDiv.classList.remove('show');
            setTimeout(() => alertDiv.remove(), 500);
        }, 5000);
    }
}

/**
 * Display the list of active MCP connections
 * @param {Array} connections - Array of connection objects
 */
function displayConnectionsList(connections) {
    const tableBody = document.getElementById('activeConnectionsList');
    if (!tableBody) {
        console.error('Cannot display connections list: activeConnectionsList element not found');
        return;
    }

    tableBody.innerHTML = '';

    if (!connections || connections.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center">No active connections.</td>
            </tr>
        `;
        return;
    }

    connections.forEach(conn => {
        const row = document.createElement('tr');

        // Extract connection details with fallbacks
        const id = conn.id || 'unknown';
        const displayName = conn.name || conn.qualified_name || id;

        // Format the connection time (handling both created and created_at)
        const createdDate = conn.created || conn.created_at || new Date().toISOString();
        let connectedAt = '';
        try {
            connectedAt = new Date(createdDate).toLocaleString();
        } catch (e) {
            connectedAt = createdDate;
            console.warn('Could not format date:', e);
        }

        row.innerHTML = `
            <td>${displayName}</td>
            <td>${conn.qualified_name || id}</td>
            <td>${connectedAt}</td>
            <td><span class="badge bg-success">Connected</span></td>
            <td>
                <button class="btn btn-sm btn-danger disconnect-btn"
                        data-connection="${id}"
                        data-display="${displayName}">
                    <i class="fas fa-unlink"></i> Disconnect
                </button>
            </td>
        `;

        tableBody.appendChild(row);
    });

    // Add event listeners to disconnect buttons
    document.querySelectorAll('.disconnect-btn').forEach(button => {
        button.addEventListener('click', function() {
            const connectionId = this.getAttribute('data-connection');
            const displayName = this.getAttribute('data-display');
            disconnectFromServer(connectionId, displayName);
        });
    });
}

/**
 * Show progress indicator
 * @param {string} elementId - ID of the progress element
 */
function showProgress(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.style.display = 'flex';
    }
}

/**
 * Hide progress indicator
 * @param {string} elementId - ID of the progress element
 */
function hideProgress(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.style.display = 'none';
    }
}

/**
 * Show error message
 * @param {string} message - Error message
 */
function showError(message) {
    console.error(message);

    // Create alert element
    const alertElement = document.createElement('div');
    alertElement.className = 'alert alert-danger alert-dismissible fade show';
    alertElement.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // Add to alerts container
    const alertsContainer = document.getElementById('alerts-container');
    if (alertsContainer) {
        alertsContainer.appendChild(alertElement);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            alertElement.classList.remove('show');
            setTimeout(() => alertElement.remove(), 150);
        }, 5000);
    } else {
        alert(message);
    }
}

/**
 * Show success message
 * @param {string} message - Success message
 */
function showSuccess(message) {
    console.log(message);

    // Create alert element
    const alertElement = document.createElement('div');
    alertElement.className = 'alert alert-success alert-dismissible fade show';
    alertElement.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // Add to alerts container
    const alertsContainer = document.getElementById('alerts-container');
    if (alertsContainer) {
        alertsContainer.appendChild(alertElement);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            alertElement.classList.remove('show');
            setTimeout(() => alertElement.remove(), 150);
        }, 5000);
    } else {
        alert(message);
    }
}

/**
 * Show MCP connection modal
 */
function showMCPConnectionModal() {
    console.log('showMCPConnectionModal called');
    const modal = document.getElementById('connectionModal');
    if (modal) {
        console.log('Showing connection modal');
        modal.style.display = 'flex';
    } else {
        console.error('Connection modal not found');
    }
}

/**
 * Close MCP connection modal
 */
function closeMCPConnectionModal() {
    console.log('closeMCPConnectionModal called');
    const modal = document.getElementById('connectionModal');
    if (modal) {
        console.log('Hiding connection modal');
        modal.style.display = 'none';
    } else {
        console.error('Connection modal not found for closing');
    }
}

// Make modal functions globally available
window.showMCPConnectionModal = showMCPConnectionModal;
window.closeMCPConnectionModal = closeMCPConnectionModal;
