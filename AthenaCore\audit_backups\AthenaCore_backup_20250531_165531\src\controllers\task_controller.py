"""
Task management controller module.

This module provides controller functions for handling task-related routes and APIs,
including task listing, creation, and monitoring.
"""

from flask import Blueprint, render_template, request, g
from flask_login import login_required, current_user
from flask_socketio import emit
from typing import Dict, Any, Optional, List

from src.services.task_service import TaskService
from src.services.base_service import get_service
from src.utils.api_response import success_response, error_response
from src.utils.middleware import require_auth, require_role, api_route
from src.utils.exceptions import ValidationError, ResourceNotFoundError, PermissionDeniedError

# Create a blueprint for task routes
task_bp = Blueprint("task", __name__, url_prefix="/tasks")

# Create a blueprint for task API routes
task_api = Blueprint("task_api", __name__, url_prefix="/api/tasks")

@task_bp.route("/")
@login_required
def task_dashboard():
    """Render the task dashboard."""
    # Get the task service
    task_service = get_service(TaskService)
    
    # Get all tasks
    active_tasks = task_service.get_active_tasks()
    completed_tasks = task_service.get_completed_tasks()
    
    # Render the task dashboard
    return render_template(
        "tasks/dashboard.html",
        active_tasks=active_tasks,
        completed_tasks=completed_tasks
    )

@task_bp.route("/demo")
@login_required
def create_demo_tasks():
    """Create demo tasks for testing."""
    # Get the task service
    task_service = get_service(TaskService)
    
    # Create demo tasks
    task_service.demo_tasks()
    
    # Redirect to the task dashboard
    return jsonify({"message": "Demo tasks created successfully"}), 200

@task_api.route("/", methods=["GET"])
@require_auth
@api_route
def get_all_tasks():
    """
    Get all tasks.
    
    Returns:
        API response with all tasks
    """
    try:
        # Get the task service
        task_service = get_service(TaskService)
        
        # Get query parameters
        status = request.args.get("status")
        limit = request.args.get("limit", default=20, type=int)
        user_id = request.args.get("user_id", type=int)
        
        # Check permission for viewing other user's tasks
        if user_id is not None and user_id != g.user.id and g.user.role != "admin":
            raise PermissionDeniedError("You do not have permission to view tasks for other users")
            
        # If no specific user_id is provided, use the current user's ID
        if user_id is None:
            user_id = g.user.id
        
        # Get tasks based on status
        if status == "active":
            tasks = task_service.get_active_tasks()
        elif status == "completed":
            tasks = task_service.get_completed_tasks()
        elif status == "pending":
            # Filter for pending tasks
            tasks = [t for t in task_service.get_all_tasks() if getattr(t, "status", "") == "pending"]
        else:
            tasks = task_service.get_all_tasks()
        
        # Filter by user_id if needed and if tasks have a user_id attribute
        if user_id:
            tasks = [t for t in tasks if hasattr(t, "user_id") and t.user_id == user_id]
        
        # Limit the number of tasks returned
        tasks = tasks[:limit]
        
        # Convert tasks to dictionaries for JSON response
        task_dicts = [task.to_dict() for task in tasks]
        
        return success_response(
            data={"tasks": task_dicts},
            message=f"Retrieved {len(task_dicts)} tasks"
        )
    except PermissionDeniedError as e:
        return error_response(str(e), status_code=403)
    except Exception as e:
        return error_response(f"Error retrieving tasks: {str(e)}", status_code=500)

@task_api.route("/<task_id>", methods=["GET"])
@require_auth
@api_route
def get_task(task_id):
    """
    Get a specific task.
    
    Args:
        task_id: ID of the task to get
        
    Returns:
        API response with the task
    """
    try:
        # Get the task service
        task_service = get_service(TaskService)
        
        # Get the task
        task = task_service.get_task(task_id)
        
        # Return 404 if task not found
        if not task:
            return error_response(f"Task {task_id} not found", status_code=404)
        
        # Check permission if the task has a user_id attribute
        if hasattr(task, 'user_id') and task.user_id and task.user_id != g.user.id and g.user.role != "admin":
            return error_response("You do not have permission to view this task", status_code=403)
        
        # Convert task to dictionary
        task_dict = task.to_dict()
        
        return success_response(
            data={"task": task_dict},
            message=f"Task {task_id} retrieved successfully"
        )
    except Exception as e:
        return error_response(f"Error retrieving task: {str(e)}", status_code=500)

@task_api.route("/<task_id>/cancel", methods=["POST"])
@require_auth
@api_route
def cancel_task(task_id):
    """
    Cancel a running task.
    
    Args:
        task_id: ID of the task to cancel
        
    Returns:
        API response indicating success or failure
    """
    try:
        # Get the task service
        task_service = get_service(TaskService)
        
        # Get the task
        task = task_service.get_task(task_id)
        if not task:
            return error_response(f"Task {task_id} not found", status_code=404)
        
        # Check permission if the task has a user_id attribute
        if hasattr(task, 'user_id') and task.user_id and task.user_id != g.user.id and g.user.role != "admin":
            return error_response("You do not have permission to cancel this task", status_code=403)
        
        # Get the reason for cancellation
        data = request.get_json() or {}
        reason = data.get("reason", "Cancelled by user")
        
        # Cancel the task using our new cancel_task method
        canceled_task = task_service.cancel_task(task_id, reason)
        
        if not canceled_task:
            return error_response(f"Failed to cancel task {task_id}", status_code=500)
        
        return success_response(
            data={"task": canceled_task.to_dict()},
            message=f"Task {task_id} cancelled successfully"
        )
    except ValidationError as e:
        return error_response(str(e), status_code=400)
    except ResourceNotFoundError as e:
        return error_response(str(e), status_code=404)
    except PermissionDeniedError as e:
        return error_response(str(e), status_code=403)
    except Exception as e:
        return error_response(f"Error cancelling task: {str(e)}", status_code=500)

@task_api.route("/cleanup", methods=["POST"])
@require_auth
@require_role(["admin"])
@api_route
def cleanup_tasks():
    """
    Clean up old completed or failed tasks.
    
    Returns:
        API response indicating success and number of tasks removed
    """
    try:
        # Get the task service
        task_service = get_service(TaskService)
        
        # Get the maximum age in days from request
        data = request.get_json() or {}
        max_age_days = data.get("max_age_days", 7)
        
        # Validate max_age_days
        if not isinstance(max_age_days, int) or max_age_days < 1:
            return error_response("max_age_days must be a positive integer", status_code=400)
        
        # Clean up old tasks
        removed_count = task_service.cleanup_old_tasks(max_age_days)
        
        return success_response(
            data={"removed_count": removed_count},
            message=f"Successfully removed {removed_count} old tasks"
        )
    except Exception as e:
        return error_response(f"Error cleaning up tasks: {str(e)}", status_code=500)

@task_api.route("/stats", methods=["GET"])
@require_auth
@api_route
def get_task_stats():
    """
    Get statistics about tasks in the system.
    
    Returns:
        API response with task statistics
    """
    try:
        # Get the task service
        task_service = get_service(TaskService)
        
        # Get task statistics
        stats = task_service.get_task_stats()
        
        return success_response(
            data={"stats": stats},
            message="Task statistics retrieved successfully"
        )
    except Exception as e:
        return error_response(f"Error retrieving task statistics: {str(e)}", status_code=500)

@task_api.route("/create", methods=["POST"])
@require_auth
@api_route
def create_task():
    """
    Create a new task with optional prioritization.
    
    Request Body:
        name: Name of the task
        description: Description of the task
        total_steps: Total number of steps in the task
        priority: Task priority (0-10, higher is more important)
        metadata: Optional metadata to attach to the task
    
    Returns:
        API response with the created task
    """
    try:
        # Get the task service
        task_service = get_service(TaskService)
        
        # Get request data
        data = request.get_json()
        if not data:
            return error_response("Invalid request data", status_code=400)
        
        # Validate required fields
        if "name" not in data:
            return error_response("Task name is required", status_code=400)
        
        # Extract task parameters
        name = data.get("name")
        description = data.get("description", "")
        total_steps = data.get("total_steps", 100)
        priority = data.get("priority", task_service.PRIORITY_NORMAL)
        metadata = data.get("metadata", {})
        
        # Create the task
        task = task_service.create_task(
            name=name,
            description=description,
            total_steps=total_steps,
            user_id=g.user.id,
            priority=priority,
            metadata=metadata
        )
        
        return success_response(
            data={"task": task.to_dict()},
            message="Task created successfully",
            status_code=201
        )
    except ValidationError as e:
        return error_response(str(e), status_code=400)
    except Exception as e:
        return error_response(f"Error creating task: {str(e)}", status_code=500)

@task_api.route("/schedule", methods=["POST"])
@require_auth
@api_route
def schedule_task():
    """
    Schedule a task to run in the background with priority.
    
    Request Body:
        name: Name of the task
        description: Description of the task
        function_name: Name of the function to run
        function_args: Arguments for the function
        function_kwargs: Keyword arguments for the function
        priority: Task priority (0-10, higher is more important)
        metadata: Optional metadata to attach to the task
    
    Returns:
        API response with the scheduled task
    """
    try:
        # Get the task service
        task_service = get_service(TaskService)
        
        # Get request data
        data = request.get_json()
        if not data:
            return error_response("Invalid request data", status_code=400)
        
        # Validate required fields
        required_fields = ["name", "function_name"]
        for field in required_fields:
            if field not in data:
                return error_response(f"Field '{field}' is required", status_code=400)
        
        # Extract task parameters
        name = data.get("name")
        description = data.get("description", "")
        function_name = data.get("function_name")
        function_args = data.get("function_args", [])
        function_kwargs = data.get("function_kwargs", {})
        priority = data.get("priority", task_service.PRIORITY_NORMAL)
        metadata = data.get("metadata", {})
        
        # This is a placeholder for dynamic function execution
        # In a real implementation, you would use a registry of allowed functions
        # or a more secure approach to execute the function
        def execute_function():
            # Here you would dynamically execute the function
            # For now, we'll just return a success message
            return f"Executed function {function_name}"
        
        # Create the task
        task = task_service.create_task(
            name=name,
            description=description,
            user_id=g.user.id,
            priority=priority,
            metadata=metadata
        )
        
        # Schedule the task
        task_service.run_task(
            task_or_id=task,
            func=execute_function,
            schedule=True,
            priority=priority
        )
        
        return success_response(
            data={"task": task.to_dict()},
            message="Task scheduled successfully",
            status_code=201
        )
    except ValidationError as e:
        return error_response(str(e), status_code=400)
    except Exception as e:
        return error_response(f"Error scheduling task: {str(e)}", status_code=500)

@task_api.route("/configure", methods=["POST"])
@require_auth
@require_role(["admin"])
@api_route
def configure_task_service():
    """
    Configure the task service settings.
    
    Request Body:
        max_concurrent_tasks: Maximum number of concurrent tasks
    
    Returns:
        API response indicating success
    """
    try:
        # Get the task service
        task_service = get_service(TaskService)
        
        # Get request data
        data = request.get_json()
        if not data:
            return error_response("Invalid request data", status_code=400)
        
        # Configure max concurrent tasks if provided
        if "max_concurrent_tasks" in data:
            max_concurrent_tasks = data.get("max_concurrent_tasks")
            task_service.set_max_concurrent_tasks(max_concurrent_tasks)
        
        # Get updated task service stats
        stats = task_service.get_task_stats()
        
        return success_response(
            data={"stats": stats},
            message="Task service configured successfully"
        )
    except ValidationError as e:
        return error_response(str(e), status_code=400)
    except Exception as e:
        return error_response(f"Error configuring task service: {str(e)}", status_code=500)

# Socket.IO event handlers
def register_socket_events(socketio):
    """
    Register Socket.IO event handlers for real-time task updates.
    
    Args:
        socketio: SocketIO instance
    """
    # Get the task service
    task_service = get_service(TaskService)
    
    # Set the socketio instance on the task service
    task_service.set_socketio(socketio)
    
    @socketio.on('connect')
    def handle_connect():
        print(f"Client connected: {request.sid}")
    
    @socketio.on('disconnect')
    def handle_disconnect():
        # Log disconnection
        print(f"Client disconnected: {request.sid}")
    
    @socketio.on('get_active_tasks')
    def handle_get_active_tasks():
        # Get active tasks
        tasks = task_service.get_active_tasks()
        
        # Convert tasks to dictionaries
        tasks_dict = [task.to_dict() for task in tasks]
        
        # Send tasks to the client
        emit('active_tasks', {'tasks': tasks_dict})
    
    @socketio.on("task_status")
    def handle_task_status(data):
        # Get task ID from data
        task_id = data.get("task_id")
        
        # Get task status
        task = task_service.get_task(task_id)
        
        # Emit task status to client
        emit("task_update", task.to_dict())

def register_blueprints(app):
    """
    Register all task-related blueprints with the app.
    
    Args:
        app: Flask application instance
    """
    # Register the task blueprint
    app.register_blueprint(task_bp)
    
    # Register the task API blueprint
    app.register_blueprint(task_api)
    
    # Register Socket.IO event handlers if SocketIO is available
    if hasattr(app, 'socketio'):
        register_socket_events(app.socketio)
        
        # Set SocketIO instance in the task service
        task_service = get_service(TaskService)
        task_service.set_socketio(app.socketio)
