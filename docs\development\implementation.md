# Cross-Device API System Implementation Plan

## Overview

The Cross-Device API System enables Athena to operate seamlessly across multiple devices. The system allows commands initiated on one device to be routed and executed on another device, creating a unified experience regardless of which device the user is currently using.

## Key Components

### 1. Device Registration and Management

- **Device Registration**: Allow devices to register with Athena core
- **Device Heartbeat**: Track device online status
- **Capability Discovery**: Discover and register device capabilities
- **Device Groups**: Group devices for targeting commands

### 2. Command Routing Infrastructure

- **Command Submission**: Submit commands from one device to another
- **Command Validation**: Validate commands against device capabilities
- **Command Execution**: Execute commands on target devices
- **Command Status Tracking**: Track the status of submitted commands

### 3. Authentication and Security

- **JWT Authentication**: Authenticate devices using JWT tokens
- **Permission Model**: Control what commands devices can execute
- **Encryption**: Secure communication between devices

### 4. Background Task Processing

- **Task Queue System**: Manage background vs. foreground tasks
- **Progress Tracking**: Monitor and report progress of long-running tasks
- **Task Hierarchy**: Support for parent-child task relationships
- **Priority Management**: Handle task execution based on priority levels

### 5. Attachment Handling

- **File Storage**: Secure storage for file attachments
- **Transfer Protocol**: Chunked file transfer for reliability
- **Attachment Association**: Link files to commands and devices
- **Access Control**: Manage permission for attachment access

### 6. Semi-Autonomous Operation

- **Task Scheduling**: Schedule tasks to run at specific times
- **Event-Triggered Execution**: Run tasks in response to events
- **Device Selection**: Auto-select appropriate devices for tasks
- **Automatic Retry**: Handle failures with retry mechanisms

## Implementation Status

### Completed Features 

#### 1. Database Models (Phase 1)
- Device and Device Capability models
- Command and CommandLog models
- Attachment model for file metadata
- ScheduledTask model for autonomous operations

#### 2. Background Task Processing (Phase 4)
- Enhanced Command model with background task flags
- Priority-based task scheduling (1-10)
- Progress tracking (0-100%)
- Parent-child task relationships
- Resource limiting with max_runtime
- Task status monitoring with detailed logging

#### 3. Attachment Handling (Phase 5)
- Secure file storage with access control
- File metadata tracking and integrity verification
- Progress tracking for file transfers
- Command and device association for attachments
- Automatic cleanup of expired attachments

#### 4. Semi-Autonomous Operation (Phase 6)
- Scheduled task system with multiple scheduling methods
- Support for interval and cron-based scheduling
- Flexible device targeting (specific device, type, capability)
- Manual and automatic task triggering
- Recurring and one-time task support

### API Endpoints Implemented 

#### Background Tasks API
- `POST /api/tasks/background` - Create a new background task
- `GET /api/tasks/background` - List background tasks
- `GET /api/tasks/background/<uuid>` - Get task details
- `POST /api/tasks/background/<uuid>/cancel` - Cancel a task
- `POST /api/tasks/background/<uuid>/progress` - Update task progress

#### Scheduled Tasks API
- `POST /api/tasks/scheduled` - Create a new scheduled task
- `GET /api/tasks/scheduled` - List scheduled tasks
- `GET /api/tasks/scheduled/<uuid>` - Get task details
- `PUT /api/tasks/scheduled/<uuid>` - Update a scheduled task
- `DELETE /api/tasks/scheduled/<uuid>` - Delete a scheduled task
- `POST /api/tasks/scheduled/<uuid>/trigger` - Manually trigger a task

#### Attachments API
- `POST /api/attachments` - Upload a new attachment
- `GET /api/attachments` - List attachments
- `GET /api/attachments/<uuid>` - Download an attachment
- `GET /api/attachments/<uuid>/info` - Get attachment metadata
- `PUT /api/attachments/<uuid>` - Update attachment metadata
- `DELETE /api/attachments/<uuid>` - Delete an attachment
- `GET /api/attachments/commands/<id>` - Get attachments for a command
- `GET /api/attachments/devices/<uuid>` - Get attachments for a device

#### Search API
- `GET /api/search` - Unified search across devices, commands, attachments, and tasks
- `POST /api/search/advanced` - Advanced search with complex filtering
- `GET /api/search/stats` - Get statistics about all entities in the system

### Services Implemented 

- **TaskExecutor Service**: Manages concurrent execution of background tasks with priority queuing and resource management
- **TaskScheduler Service**: Handles scheduled task triggers based on configured schedules
- **Attachment Storage Service**: Manages secure file storage and access control

## Implementation Phases

### Phase 1: Database Models and API Structure 

1. Create database models for:
   - Devices 
   - Device Capabilities 
   - Commands 
   - Command Status 

2. Set up API endpoints for:
   - Device registration and management 
   - Command submission and retrieval 
   - Capability registration 
   - Authentication 

### Phase 2: Core Logic and Security 

1. Implement command routing logic 
2. Implement device status tracking 
3. Set up JWT authentication system 
4. Implement command validation 

### Phase 3: Client Libraries and UI 

1. Create client libraries for different platforms 
2. Implement UI for device management 
3. Create UI for command monitoring 

### Phase 4: Background Task Processing 

1. Enhance Command model with background task flags 
2. Implement task priority management 
3. Add progress tracking for long-running tasks 
4. Create parent-child task relationships 

### Phase 5: Attachment Handling 

1. Create Attachment model for file metadata 
2. Implement secure file storage 
3. Add file transfer capabilities 
4. Integrate attachments with commands 

### Phase 6: Semi-Autonomous Operation 

1. Create ScheduledTask model for autonomous execution 
2. Implement task scheduler service 
3. Add trigger system for event-based tasks 
4. Create automatic device selection logic 

## Database Schema

### Devices Table
```
devices
├── id (PK)
├── user_id (FK to users)
├── device_uuid (unique)
├── name
├── device_type
├── os_info
├── last_active
├── created_at
├── is_active
```

### Device Capabilities Table
```
device_capabilities
├── id (PK)
├── device_id (FK to devices)
├── capability_name
├── capability_description
├── parameters (JSON)
```

### Commands Table
```
commands
├── id (PK)
├── command_uuid (unique)
├── user_id (FK to users)
├── source_device_id (FK to devices)
├── target_device_id (FK to devices)
├── capability_name
├── parameters (JSON)
├── priority
├── status
├── created_at
├── delivered_at
├── completed_at
├── expires_at
├── is_background
├── parent_command_id (FK to commands)
├── priority_level (1-10)
├── max_runtime
├── progress (0-100)
```

### Attachments Table
```
attachments
├── id (PK)
├── attachment_uuid (unique)
├── user_id (FK to users)
├── command_id (FK to commands, nullable)
├── device_id (FK to devices, nullable)
├── filename
├── file_type
├── file_size
├── file_hash
├── storage_path
├── is_public
├── status
├── transfer_progress
├── created_at
├── updated_at
├── expires_at
```

### Scheduled Tasks Table
```
scheduled_tasks
├── id (PK)
├── task_uuid (unique)
├── user_id (FK to users)
├── name
├── capability_name
├── parameters (JSON)
├── schedule_type
├── schedule_data (JSON)
├── trigger_condition (JSON)
├── target_device_id (FK to devices, nullable)
├── device_type_filter
├── capability_filter
├── is_active
├── last_run
├── next_run
├── created_at
├── updated_at
├── priority_level (1-10)
├── is_recurring
├── execution_count
├── error_count
├── last_error
```

## Using the Background Task API

### Creating a Background Task

```python
# Example: Creating a background task
response = requests.post('/api/tasks/background', json={
    'target_device_id': device_id,
    'capability_name': 'process_files',
    'parameters': {
        'file_path': '/path/to/file.txt',
        'options': {'convert': True}
    },
    'priority_level': 7,  # Higher priority (1-10 scale)
    'max_runtime': 1800  # 30 minutes maximum
})

task = response.json()['task']
task_uuid = task['command_uuid']
```

### Monitoring Task Progress

```python
# Get task details
response = requests.get(f'/api/tasks/background/{task_uuid}')
task = response.json()['task']

# Check progress
progress = task['progress']  # 0-100%
status = task['status']      # pending, running, completed, failed, cancelled

# Get task logs
logs = response.json()['logs']
```

### Updating Task Progress

```python
# Update progress from the executing device
requests.post(f'/api/tasks/background/{task_uuid}/progress', json={
    'progress': 50,
    'status': 'running',
    'message': 'Processing halfway complete'
})
```

### Cancelling a Task

```python
# Cancel a running task
requests.post(f'/api/tasks/background/{task_uuid}/cancel')
```

## Using the Attachment API

### Uploading an Attachment

```python
# Upload a file attachment
with open('document.pdf', 'rb') as f:
    files = {'file': f}
    data = {
        'command_id': command_id,  # Optional association with a command
        'device_id': device_id,    # Optional association with a device
        'is_public': 'false',
        'expires_in_hours': '24'
    }
    response = requests.post('/api/attachments', files=files, data=data)

attachment = response.json()['attachment']
attachment_uuid = attachment['attachment_uuid']
```

### Downloading an Attachment

```python
# Download a file attachment
response = requests.get(f'/api/attachments/{attachment_uuid}')
with open('downloaded_file.pdf', 'wb') as f:
    f.write(response.content)
```

### Managing Attachments

```python
# List attachments
response = requests.get('/api/attachments')
attachments = response.json()['attachments']

# Filter by command
response = requests.get(f'/api/attachments?command_id={command_id}')

# Filter by device
response = requests.get(f'/api/attachments?device_id={device_id}')

# Filter by filename
response = requests.get('/api/attachments?filename=document')

# Update attachment metadata
requests.put(f'/api/attachments/{attachment_uuid}', json={
    'filename': 'new_filename.pdf',
    'is_public': True,
    'expires_in_hours': 48
})

# Delete an attachment
requests.delete(f'/api/attachments/{attachment_uuid}')
```

## Using the Scheduled Task API

### Creating a Scheduled Task

```python
# Example: Create an interval-based scheduled task
response = requests.post('/api/tasks/scheduled', json={
    'name': 'Daily Backup',
    'capability_name': 'backup_files',
    'parameters': {
        'target_dir': '/documents',
        'backup_location': '/backups'
    },
    'schedule_type': 'interval',
    'schedule_data': {
        'hours': 24
    },
    'device_type_filter': 'desktop',  # Run on any desktop device
    'priority_level': 5,
    'is_recurring': True
})

task = response.json()['task']
task_uuid = task['task_uuid']

# Example: Create a cron-based scheduled task
response = requests.post('/api/tasks/scheduled', json={
    'name': 'Weekly Report',
    'capability_name': 'generate_report',
    'parameters': {
        'report_type': 'weekly',
        'output_format': 'pdf'
    },
    'schedule_type': 'cron',
    'schedule_data': {
        'expression': '0 9 * * 1'  # 9:00 AM every Monday
    },
    'target_device_id': specific_device_id,  # Run on a specific device
    'is_recurring': True
})
```

### Managing Scheduled Tasks

```python
# List scheduled tasks
response = requests.get('/api/tasks/scheduled')
tasks = response.json()['tasks']

# List only active tasks
response = requests.get('/api/tasks/scheduled?active_only=true')

# Filter by schedule type
response = requests.get('/api/tasks/scheduled?schedule_type=interval')

# Get task details
response = requests.get(f'/api/tasks/scheduled/{task_uuid}')
task = response.json()

# Update a scheduled task
requests.put(f'/api/tasks/scheduled/{task_uuid}', json={
    'name': 'Updated Task Name',
    'parameters': {'updated': 'parameters'},
    'is_active': False  # Temporarily disable the task
})

# Manually trigger a task
response = requests.post(f'/api/tasks/scheduled/{task_uuid}/trigger')
command = response.json()['command']  # The command created by triggering

# Delete a task
requests.delete(f'/api/tasks/scheduled/{task_uuid}')
```

## Using the Search API

### Unified Search

```python
# Search across all entity types
response = requests.get('/api/search?query=backup')
results = response.json()

# Search specific entity types
response = requests.get('/api/search?query=backup&entities=commands,devices')
results = response.json()

# Sort and paginate results
response = requests.get('/api/search?query=backup&sort_by=created_at&sort_dir=desc&limit=20&offset=0')
results = response.json()

# Check total results count
total_results = results['total_results']
```

### Advanced Search

```python
# Search commands with complex filtering
response = requests.post('/api/search/advanced', json={
    "entity_type": "command",
    "filters": [
        {"field": "is_background", "operator": "equals", "value": True},
        {"field": "priority_level", "operator": "greater_than", "value": 5}
    ],
    "sort": {"field": "created_at", "direction": "desc"},
    "pagination": {"limit": 10, "offset": 0}
})
results = response.json()

# Search attachments by file type
response = requests.post('/api/search/advanced', json={
    "entity_type": "attachment",
    "filters": [
        {"field": "file_type", "operator": "contains", "value": "image"}
    ]
})
results = response.json()

# Search scheduled tasks
response = requests.post('/api/search/advanced', json={
    "entity_type": "task",
    "filters": [
        {"field": "is_active", "operator": "equals", "value": True},
        {"field": "schedule_type", "operator": "equals", "value": "cron"}
    ]
})
results = response.json()
```

### System Statistics

```python
# Get statistics about all entities
response = requests.get('/api/search/stats')
stats = response.json()

# Access specific statistics
device_count = stats['devices']['total']
active_devices = stats['devices']['active']
device_types = stats['devices']['type_distribution']

total_attachments = stats['attachments']['total']
storage_usage = stats['attachments']['total_size_bytes']

active_tasks = stats['scheduled_tasks']['active']
```

## Security Considerations

1. JWT tokens include device ID and capability scopes 
2. Commands are only executable by devices with matching capabilities 
3. Sensitive commands require additional verification 
4. Communication between devices uses TLS/SSL 
5. Command parameters are validated before execution 
6. File attachments have size and type validation 
7. Background tasks have configurable resource limits 
8. Autonomous tasks have strict permission controls 
9. File attachments have proper access control and expiration 

## Next Steps

1. **Client SDK Development**
   - Create client libraries for different platforms
   - Implement UI components for task monitoring
   - Add support for attachment management in clients

2. **Performance Optimization**
   - Optimize task executor for high concurrency
   - Implement chunked file uploads for large attachments
   - Add caching for frequently accessed files

3. **Enhanced Security Features**
   - Add virus scanning for file attachments
   - Implement end-to-end encryption for sensitive tasks
   - Add more granular permission controls

4. **Advanced Scheduling Features**
   - Add event-based triggers from external systems
   - Implement conditional task execution
   - Create task templates for common operations

## Getting Started

### Starting the Services

To start using the Cross-Device API System, you need to ensure the services are running:

```python
# Make a request to start the services
requests.get('/api/start-services')
```

### Initializing in Development

When developing or testing, you can use the provided script to initialize services:

```bash
# Run the service initialization script
python tests/start_services.py
```

### Starting the Services

To start using the Cross-Device API System, you need to ensure the services are running:

```python
# Make a request to start the services
requests.get('/api/start-services')
```

### Initializing in Development

When developing or testing, you can use the provided script to initialize services:

```bash
# Run the service initialization script
python tests/start_services.py
```

Follow these instructions to make the following change to my code document.

Instruction: Fix the markdown formatting by adding the closing code fence

Code Edit:
```
{{ ... }}
# Run the service initialization script
python tests/start_services.py
```
```python
# Run the service initialization script
python tests/start_services.py
