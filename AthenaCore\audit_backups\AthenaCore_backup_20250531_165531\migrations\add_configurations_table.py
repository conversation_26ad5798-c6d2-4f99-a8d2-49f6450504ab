"""
Migration script to add the configurations table for database-driven configuration.

This migration creates the new 'configurations' table and migrates any existing
configuration from the 'config_entries' table.
"""

import os
import sys
import datetime
import json

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import database instance from models package
from src.models import db

# Import ConfigEntry with backward compatibility handling
try:
    from src.models.configuration import ConfigEntry
except ImportError:
    # Fallback to old import location for backward compatibility
    from src.login.models import ConfigEntry
from src.models.configuration import Configuration
from flask import Flask

def run_migration(app=None):
    """
    Run the migration to create the configurations table and migrate existing data.
    
    Args:
        app: Flask application instance (optional)
    """
    if app is None:
        # Create a minimal Flask application if one wasn't provided
        app = Flask(__name__)
        app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URI', 'sqlite:///instance/athena.sqlite3')
        app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        db.init_app(app)
    
    with app.app_context():
        # Check if the table already exists
        table_exists = False
        try:
            Configuration.query.first()
            table_exists = True
        except:
            pass
            
        if not table_exists:
            print("Creating configurations table...")
            
            # Create the table
            db.create_all(tables=[Configuration.__table__])
            
            # Migrate data from the old config_entries table if it exists
            try:
                config_entries = ConfigEntry.query.all()
                print(f"Found {len(config_entries)} existing config entries to migrate")
                
                for entry in config_entries:
                    # Try to determine value type
                    value = entry.value
                    value_type = "string"
                    
                    # Attempt to detect JSON
                    try:
                        json.loads(value)
                        value_type = "json"
                    except:
                        # Check for boolean
                        if value.lower() in ('true', 'false', 'yes', 'no'):
                            value_type = "bool"
                        # Check for integer
                        elif value.isdigit():
                            value_type = "int"
                        # Check for float
                        elif value.replace('.', '', 1).isdigit() and value.count('.') == 1:
                            value_type = "float"
                    
                    # Create new configuration entry
                    config = Configuration(
                        key=entry.key,
                        value=entry.value,
                        value_type=value_type,
                        description=f"Migrated from config_entries on {datetime.datetime.utcnow().isoformat()}",
                        is_sensitive='key' in entry.key.lower() or 'secret' in entry.key.lower() or 'password' in entry.key.lower()
                    )
                    db.session.add(config)
                
                db.session.commit()
                print("Migration of config entries completed successfully")
            except Exception as e:
                print(f"Error migrating config entries: {str(e)}")
                # Continue - the config_entries table might not exist yet
            
            # Migrate environment variables to the database
            try:
                # List of common environment variables to migrate
                env_vars_to_migrate = [
                    # API keys and credentials (marked as sensitive)
                    {"key": "OPENAI_API_KEY", "sensitive": True, "description": "OpenAI API key"},
                    {"key": "ANTHROPIC_API_KEY", "sensitive": True, "description": "Anthropic API key"},
                    {"key": "GOOGLE_API_KEY", "sensitive": True, "description": "Google API key"},
                    {"key": "AZURE_OPENAI_API_KEY", "sensitive": True, "description": "Azure OpenAI API key"},
                    {"key": "AZURE_OPENAI_ENDPOINT", "sensitive": False, "description": "Azure OpenAI endpoint URL"},
                    
                    # Database configuration
                    {"key": "DATABASE_URI", "sensitive": True, "description": "Database connection URI"},
                    {"key": "SQLALCHEMY_DATABASE_URI", "sensitive": True, "description": "SQLAlchemy database connection URI"},
                    
                    # Flask configuration
                    {"key": "SECRET_KEY", "sensitive": True, "description": "Flask secret key for session encryption"},
                    {"key": "FLASK_ENV", "sensitive": False, "description": "Flask environment (development, production)"},
                    {"key": "FLASK_DEBUG", "sensitive": False, "description": "Flask debug mode"},
                    
                    # App configuration
                    {"key": "APP_PORT", "sensitive": False, "description": "Application port number"},
                    {"key": "LOG_LEVEL", "sensitive": False, "description": "Logging level"},
                ]
                
                print("Migrating environment variables to database...")
                migrated_count = 0
                
                for var_info in env_vars_to_migrate:
                    key = var_info["key"]
                    value = os.getenv(key)
                    
                    if value is not None:
                        # Check if this configuration already exists
                        existing = Configuration.query.filter_by(key=key).first()
                        if not existing:
                            # Determine value type
                            value_type = "string"
                            if key.endswith('_PORT') or key == 'APP_PORT':
                                value_type = "int"
                            elif key == 'FLASK_DEBUG':
                                value_type = "bool"
                                
                            # Create new configuration entry
                            config = Configuration(
                                key=key,
                                value=value,
                                value_type=value_type,
                                description=var_info.get("description", ""),
                                is_sensitive=var_info.get("sensitive", False)
                            )
                            db.session.add(config)
                            migrated_count += 1
                
                if migrated_count > 0:
                    db.session.commit()
                    print(f"Successfully migrated {migrated_count} environment variables to database")
                else:
                    print("No environment variables to migrate")
                
            except Exception as e:
                db.session.rollback()
                print(f"Error migrating environment variables: {str(e)}")
            
            print("Configurations table creation and migration completed")
        else:
            print("Configurations table already exists")
            
if __name__ == "__main__":
    run_migration()
