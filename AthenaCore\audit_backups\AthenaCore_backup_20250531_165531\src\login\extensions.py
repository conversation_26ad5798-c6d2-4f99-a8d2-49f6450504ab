"""
Authentication extensions.

===========================================================================
DEPRECATED: This file is deprecated and will be removed in a future release.
Please use src.utils.auth_extensions instead.
===========================================================================

This module contains authentication-related extensions like Flask-Login.
"""

import warnings
import sys
import logging

# Set up logging
logger = logging.getLogger('athena.compat')

# Show deprecation warning
warnings.warn(
    "The module src.login.extensions is deprecated. Please update imports to use src.utils.auth_extensions",
    category=FutureWarning,
    stacklevel=2
)

# Log usage of this deprecated module
frame = sys._getframe(1)
caller_module = frame.f_globals.get('__name__', 'unknown')
caller_function = frame.f_code.co_name
logger.info(f"Deprecated module src.login.extensions used by {caller_module}.{caller_function}")

# Import from the new location to maintain backward compatibility
from src.utils.auth_extensions import *
