"""
Error handling utilities for Athena Core.

This module provides standardized error handling for the application,
ensuring consistent error responses across different routes and APIs.
"""

from flask import jsonify, render_template, request, current_app
import traceback
import logging
from werkzeug.exceptions import HTTPException

from src.utils.exceptions import AthenaException, ValidationError
from src.utils.api_response import error_response, validation_error_response

# Configure logging
logger = logging.getLogger(__name__)

def is_api_request():
    """
    Determine if the current request is an API request.
    
    Returns:
        bool: True if the request is an API request, False otherwise
    """
    return (
        request.path.startswith('/api/') or
        request.headers.get('Accept') == 'application/json' or
        request.headers.get('Content-Type') == 'application/json'
    )

def handle_400_error(error):
    """
    Handle 400 Bad Request errors.
    
    Args:
        error: The error that occurred
        
    Returns:
        HTTP response with appropriate format based on the request type
    """
    logger.warning(f"400 Bad Request: {error}")
    
    if is_api_request():
        return jsonify({
            "error": "Bad Request",
            "message": str(error),
            "status_code": 400
        }), 400
    
    return render_template("errors/400.html", error=error), 400

def handle_401_error(error):
    """
    Handle 401 Unauthorized errors.
    
    Args:
        error: The error that occurred
        
    Returns:
        HTTP response with appropriate format based on the request type
    """
    logger.warning(f"401 Unauthorized: {error}")
    
    if is_api_request():
        return jsonify({
            "error": "Unauthorized",
            "message": str(error),
            "status_code": 401
        }), 401
    
    return render_template("errors/401.html", error=error), 401

def handle_403_error(error):
    """
    Handle 403 Forbidden errors.
    
    Args:
        error: The error that occurred
        
    Returns:
        HTTP response with appropriate format based on the request type
    """
    logger.warning(f"403 Forbidden: {error}")
    
    if is_api_request():
        return jsonify({
            "error": "Forbidden",
            "message": str(error),
            "status_code": 403
        }), 403
    
    return render_template("errors/403.html", error=error), 403

def handle_404_error(error):
    """
    Handle 404 Not Found errors.
    
    Args:
        error: The error that occurred
        
    Returns:
        HTTP response with appropriate format based on the request type
    """
    logger.warning(f"404 Not Found: {request.path}")
    
    if is_api_request():
        return jsonify({
            "error": "Not Found",
            "message": f"The requested URL {request.path} was not found on the server.",
            "status_code": 404
        }), 404
    
    return render_template("errors/404.html", error=error), 404

def handle_405_error(error):
    """
    Handle 405 Method Not Allowed errors.
    
    Args:
        error: The error that occurred
        
    Returns:
        HTTP response with appropriate format based on the request type
    """
    logger.warning(f"405 Method Not Allowed: {request.method} {request.path}")
    
    if is_api_request():
        return jsonify({
            "error": "Method Not Allowed",
            "message": f"The method {request.method} is not allowed for the requested URL.",
            "status_code": 405
        }), 405
    
    return render_template("errors/405.html", error=error), 405

def handle_500_error(error):
    """
    Handle 500 Internal Server Error errors.
    
    Args:
        error: The error that occurred
        
    Returns:
        HTTP response with appropriate format based on the request type
    """
    # Log the full traceback for debugging
    logger.error(f"500 Internal Server Error: {error}\n{traceback.format_exc()}")
    
    if is_api_request():
        return jsonify({
            "error": "Internal Server Error",
            "message": "The server encountered an internal error and was unable to complete your request.",
            "status_code": 500
        }), 500
    
    return render_template("errors/500.html", error=error), 500

def handle_athena_exception(error):
    """
    Handle Athena-specific exceptions.
    
    Args:
        error: The AthenaException that occurred
        
    Returns:
        HTTP response with appropriate format based on the request type
    """
    logger.error(f"Athena Exception: {error.error_code} - {error.message}")
    
    if isinstance(error, ValidationError):
        if is_api_request():
            return validation_error_response(error.errors, error.message, error.status_code)
        # Render validation error template for web requests
        return render_template(
            "errors/422.html", 
            error=error,
            validation_errors=error.errors
        ), 422
    
    if is_api_request():
        return error_response(
            message=error.message,
            error_code=error.error_code,
            status_code=error.status_code,
            errors=[error.details] if error.details else None
        )
    
    # Choose appropriate error template based on status code
    template = f"errors/{error.status_code}.html"
    fallback_template = "errors/generic.html"
    
    try:
        return render_template(template, error=error), error.status_code
    except:
        return render_template(fallback_template, error=error), error.status_code

def handle_http_exception(error):
    """
    Handle all other HTTP exceptions.
    
    Args:
        error: The HTTP exception that occurred
            
    Returns:
        HTTP response with appropriate format based on the request type
    """
    logger.warning(f"HTTP Exception {error.code}: {error}")
    
    if is_api_request():
        return jsonify({
            "error": error.name,
            "message": error.description,
            "status_code": error.code
        }), error.code
    
    # Try to use a specific error template if available
    try:
        return render_template(f"errors/{error.code}.html", error=error), error.code
    except:
        # Fall back to a generic error template
        return render_template("errors/generic.html", error=error), error.code

def register_error_handlers(app):
    """
    Register error handlers with the Flask application.
    
    Args:
        app: Flask application instance
    """
    app.register_error_handler(400, handle_400_error)
    app.register_error_handler(401, handle_401_error)
    app.register_error_handler(403, handle_403_error)
    app.register_error_handler(404, handle_404_error)
    app.register_error_handler(405, handle_405_error)
    app.register_error_handler(500, handle_500_error)
    
    # Register handler for all HTTPExceptions
    app.register_error_handler(HTTPException, handle_http_exception)
    
    # Register handler for Athena-specific exceptions
    app.register_error_handler(AthenaException, handle_athena_exception)
    
    # Register handler for unhandled exceptions
    @app.errorhandler(Exception)
    def handle_exception(error):
        """
        Handle all unhandled exceptions.
        
        Args:
            error: The exception that occurred
            
        Returns:
            HTTP response with appropriate format based on the request type
        """
        logger.error(f"Unhandled Exception: {error}\n{traceback.format_exc()}")
        
        if is_api_request():
            return error_response(
                message="An unexpected error occurred",
                error_code="internal_server_error",
                status_code=500,
                errors=[{"type": error.__class__.__name__, "detail": str(error)}] if not current_app.config.get("DEBUG", False) else None
            )
        
        return render_template("errors/500.html", error=error), 500
