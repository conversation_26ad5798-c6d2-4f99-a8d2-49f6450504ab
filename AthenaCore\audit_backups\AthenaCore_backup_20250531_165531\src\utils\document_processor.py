"""
Document Processing Utilities for Knowledge Base
Provides functions to extract text and metadata from different document types
"""

import os
import logging
from typing import Di<PERSON>, <PERSON>ple, BinaryIO, Union, List
import mimetypes
import io
import uuid
import re

import tiktoken
from PIL import Image
from unstructured.partition.pdf import partition_pdf
from tika import parser  # Apache Tika for robust PDF parsing/OCR
import fitz  # PyMuPDF

# Setup logger
logger = logging.getLogger("athena.document_processor")


def setup_document_processor():
    """Initialize the document processor and required libraries"""
    # Initialize mimetypes
    if not mimetypes.inited:
        mimetypes.init()

    # Add additional MIME types that might be missing
    mimetypes.add_type("application/pdf", ".pdf")
    mimetypes.add_type("text/markdown", ".md")
    mimetypes.add_type("text/markdown", ".markdown")

    logger.info("Document processor initialized")
    return True


def get_document_type(filename: str) -> str:
    """
    Determine document type from filename or content

    Args:
        filename: Name of the file

    Returns:
        str: Document type (pdf, text, markdown, etc.)
    """
    if not filename:
        return "text"

    ext = os.path.splitext(filename)[1].lower()
    if ext == ".pdf":
        return "pdf"
    if ext in (".md", ".markdown"):
        return "markdown"
    if ext in (".txt", ".text"):
        return "text"
    if ext in (".html", ".htm"):
        return "html"
    if ext in (".py", ".js", ".java", ".c", ".cpp", ".cs", ".go", ".rb", ".php"):
        return "code"
    if ext in (".docx", ".doc"):
        return "docx"
    if ext in (".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"):
        return "image"

    mimetype, _ = mimetypes.guess_type(filename)
    if mimetype:
        if mimetype.startswith("text/"):
            return "text"
        if mimetype.startswith("image/"):
            return "image"
        if mimetype == "application/pdf":
            return "pdf"

    return "binary"



def extract_with_unstructured(pdf_data: Union[bytes, str, BinaryIO]) -> str:
    """
    Extract text from PDF using unstructured + HuggingFace Layout LM.
    Best quality, but slower than other methods.
    """
    try:
        from unstructured.partition.pdf import partition_pdf
    except ImportError:
        return "[PDF text extraction error: unstructured library not installed]"
    
    try:
        # Handle different input types
        if isinstance(pdf_data, str) and os.path.isfile(pdf_data):
            filename = pdf_data
            elements = partition_pdf(filename=filename, infer_table_structure=True)
        elif isinstance(pdf_data, (bytes, bytearray)):
            file_like = io.BytesIO(pdf_data)
            elements = partition_pdf(file=file_like, infer_table_structure=True)
        else:
            # For file objects, we need to make a copy to avoid disturbing the original position
            pos = pdf_data.tell()
            file_content = pdf_data.read()
            pdf_data.seek(pos)  # Restore position for other extractors
            file_like = io.BytesIO(file_content)
            elements = partition_pdf(file=file_like, infer_table_structure=True)
            
        # Check if elements is empty
        if not elements:
            return "[PDF text extraction warning: unstructured returned empty elements]"
            
        # Convert elements to text
        text = "\n\n".join([str(el) for el in elements])
        return text.strip() or "[PDF document without extractable text content]"
    except TypeError as e:
        logger.warning(f"Unstructured extractor received unsupported input type: {e}")
        return "[PDF text extraction warning: unstructured received unsupported input type]"
    except Exception as e:
        logger.warning(f"Unstructured extraction error: {e}")
        return f"[PDF text extraction error: {str(e)}]"



def extract_text_from_pdf(pdf_data: Union[bytes, str, BinaryIO]) -> str:
    """
    Extract text from PDF data, trying multiple methods in order:
      1) pdfplumber (+ pytesseract OCR)
      2) PyPDF2
      3) pdfminer.six
      4) Apache Tika
    """
    # Check if this is a large PDF (> 10MB) and adjust extraction strategy
    file_size_mb = 0
    if isinstance(pdf_data, str) and os.path.isfile(pdf_data):
        file_size_mb = os.path.getsize(pdf_data) / (1024 * 1024)
    elif hasattr(pdf_data, "seek") and hasattr(pdf_data, "tell"):
        current_pos = pdf_data.tell()
        pdf_data.seek(0, 2)  # Seek to end
        file_size_mb = pdf_data.tell() / (1024 * 1024)
        pdf_data.seek(current_pos)  # Reset position
    

    extractors = [
        extract_with_pdfplumber,
        extract_with_pypdf2,
        extract_with_pdfminer,
        extract_with_tika,
        extract_with_unstructured,
    ]
    
    errors: List[str] = []

    for extractor in extractors:
        logger.debug(f"Trying PDF extractor: {extractor.__name__}")
        try:
            result = extractor(pdf_data)
            # Check if extraction was successful and not an error message
            if result and not result.startswith("[PDF") and not result.startswith("[Document"):
                snippet_before = result[:200].replace("\n", " ")
                logger.info(f"PDF successfully extracted by {extractor.__name__}: \"{snippet_before}...\"")
                # --- Add Cleaning Step START ---
                cleaned_result = _clean_extracted_text(result)
                logger.info(f"Cleaned PDF text snippet: \"{cleaned_result[:200]}...\"") # Log cleaned snippet
                # --- Add Cleaning Step END ---
                return cleaned_result # Return cleaned result
            elif result:
                # Log specific known error messages from extractors if returned
                logger.warning(f"{extractor.__name__} returned: {result}")
                errors.append(f"{extractor.__name__}: {result}")
            else:
                 logger.warning(f"{extractor.__name__} returned empty result.")
                 errors.append(f"{extractor.__name__}: Empty result")

        except Exception as e:
            logger.warning(f"{extractor.__name__} failed: {e}")
            errors.append(f"{extractor.__name__}: {e}")

    err_msg = "; ".join(errors) or "unknown error"
    logger.error(f"All PDF extraction methods failed: {err_msg}")
    return "[PDF text extraction error: Unable to extract text. Tried multiple methods.]"


def _clean_extracted_text(text: str) -> str:
    """Basic cleaning for text extracted from documents."""
    if not text:
        return ""

    # Split into lines first for line-based cleaning
    lines = text.split('\n')
    cleaned_lines = []
    potential_title_lines = [line.strip() for line in lines[:5] if len(line.strip()) > 5] # Look at first few lines
    
    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue

        # Skip very short lines
        if len(line) < 10:
            continue
            
        # Skip lines that look like simple page numbers
        if re.fullmatch(r'(?i)-? ?\d+ ?-?', line):
             continue
        if re.search(r'(?i)page \d+ (of \d+)?', line):
             continue

        # Skip lines with a high ratio of non-alphanumeric chars (excluding spaces)
        non_alnum_count = sum(1 for char in line if not char.isalnum() and not char.isspace())
        alnum_count = sum(1 for char in line if char.isalnum())
        if alnum_count > 0 and non_alnum_count / alnum_count > 0.5: # If more than 50% non-alnum (heuristic)
             logger.debug(f"Skipping line due to high non-alphanumeric ratio: '{line[:50]}...'" )
             continue

        # Attempt to skip repeated title lines (simple check based on first few lines)
        if i < 10 and len(potential_title_lines) > 1 and line in potential_title_lines[1:] and line == potential_title_lines[0]:
             logger.debug(f"Skipping likely repeated title line: '{line[:50]}...'" )
             continue

        cleaned_lines.append(line)

    # Join cleaned lines and normalize spaces
    text = ' '.join(cleaned_lines)
    text = re.sub(r'\s+', ' ', text).strip()

    logger.debug("Cleaned extracted text snippet: %s...", text[:200])
    return text


def extract_with_pypdf2(pdf_data: Union[bytes, str, BinaryIO]) -> str:
    """Extract text from PDF using PyPDF2."""
    from PyPDF2 import PdfReader

    if isinstance(pdf_data, str) and os.path.isfile(pdf_data):
        reader = PdfReader(pdf_data)
    elif isinstance(pdf_data, (bytes, bytearray)):
        reader = PdfReader(io.BytesIO(pdf_data))
    else:
        reader = PdfReader(pdf_data)

    logger.info(f"PDF has {len(reader.pages)} pages")
    
    # For very large PDFs, just extract a subset of pages
    # This gives a good representative sample without processing the entire document
    text = ""
    
    for i, page in enumerate(reader.pages, 1):
        try:
            pt = page.extract_text() or ""
            text += f"\n--- Page {i} ---\n{pt}"
        except Exception as e:
            logger.warning(f"PyPDF2 page {i} extraction error: {e}")
    
    return text.strip() or "[PDF document without extractable text content]"


def extract_with_pdfminer(pdf_data: Union[bytes, str, BinaryIO]) -> str:
    """Extract text from PDF using pdfminer.six."""
    from pdfminer.high_level import extract_text

    if isinstance(pdf_data, str) and os.path.isfile(pdf_data):
        return extract_text(pdf_data).strip() or "[PDF document without extractable text content]"
    if isinstance(pdf_data, (bytes, bytearray)):
        return extract_text(io.BytesIO(pdf_data)).strip() or "[PDF document without extractable text content]"
    pos = pdf_data.tell()
    text = extract_text(pdf_data).strip()
    pdf_data.seek(pos)
    return text or "[PDF document without extractable text content]"


def extract_with_tika(pdf_data: Union[bytes, str, BinaryIO]) -> str:
    """
    Extract text from PDF using Apache Tika.
    For OCR on images, Tika server must be started with --enable-ocr.
    """
    try:
        if isinstance(pdf_data, str) and os.path.isfile(pdf_data):
            parsed = parser.from_file(pdf_data)
        elif isinstance(pdf_data, (bytes, bytearray)):
            parsed = parser.from_buffer(pdf_data)
        else:
            pos = pdf_data.tell()
            buf = pdf_data.read()
            parsed = parser.from_buffer(buf)
            pdf_data.seek(pos)
        content = parsed.get("content", "")
        return content.strip() or "[PDF document without extractable text content]"
    except Exception as e:
        logger.warning(f"Tika extraction error: {e}")
        return "[PDF text extraction error: Tika failed]"


def extract_with_pdfplumber(pdf_data: Union[bytes, str, BinaryIO]) -> str:
    """
    Extract text (+ OCR) using pdfplumber + pytesseract.
    """
    try:
        import pdfplumber
        import pytesseract
    except ImportError:
        return "[PDF text extraction error: pdfplumber or pytesseract not installed]"

    if isinstance(pdf_data, str) and os.path.isfile(pdf_data):
        doc = pdfplumber.open(pdf_data)
    else:
        pos = None
        if hasattr(pdf_data, "tell"):
            pos = pdf_data.tell()
        stream = io.BytesIO(pdf_data if isinstance(pdf_data, (bytes, bytearray)) else pdf_data.read())
        doc = pdfplumber.open(stream)
        if pos is not None:
            pdf_data.seek(pos)

    full_text: List[str] = []
    for page in doc.pages:
        txt = page.extract_text() or ""
        full_text.append(txt)
        try:
            img = page.to_image(resolution=200).original
            ocr_txt = pytesseract.image_to_string(img)
            full_text.append(ocr_txt)
        except Exception:
            pass
    doc.close()
    content = "\n".join(full_text).strip()
    return content or "[PDF document without extractable text content]"


def process_document(
    file_data: Union[bytes, BinaryIO], filename: str
) -> Tuple[str, Dict, str]:
    """
    Process a document and extract text & metadata.
    """
    doc_type = get_document_type(filename)
    metadata = {"filename": os.path.basename(filename), "type": doc_type}
    content = ""

    try:
        if doc_type == "pdf":
            content = extract_text_from_pdf(file_data)
            metadata["title"] = os.path.splitext(os.path.basename(filename))[0]
        elif doc_type in ("text", "markdown", "code"):
            if isinstance(file_data, (bytes, bytearray)):
                for enc in ("utf-8", "latin-1", "cp1252"):
                    try:
                        content = file_data.decode(enc)
                        break
                    except Exception:
                        continue
                if not content:
                    content = file_data.decode("latin-1", errors="replace")
            else:
                content = file_data.read()
                if isinstance(content, (bytes, bytearray)):
                    content = content.decode("utf-8", errors="replace")
            metadata["title"] = os.path.splitext(os.path.basename(filename))[0]
        elif doc_type in ("binary", "image"):
            content = f"[Binary document: {os.path.basename(filename)}]"
            metadata["title"] = os.path.basename(filename)
        else:
            content = f"[Unsupported document type: {doc_type}]"
            metadata["title"] = os.path.basename(filename)
    except Exception as e:
        logger.error(f"Error processing {filename}: {e}")
        content = f"[Document processing error: {e}]"
        metadata["error"] = str(e)

    return content, metadata, doc_type


def extract_text_from_document(document_path: str) -> str:
    """
    Convenience wrapper to extract text from a file path.
    """
    if not os.path.exists(document_path):
        return f"[Document not found: {document_path}]"
    with open(document_path, "rb") as f:
        content, _, _ = process_document(f, os.path.basename(document_path))
    return content


def chunk_document(
    text: str, model: str = "gpt-3.5-turbo", max_tokens: int = 800, overlap: int = 100
) -> List[str]:
    """Chunks text based on token count."""
    try:
        encoding = tiktoken.encoding_for_model(model)
    except KeyError:
        logger.warning(f"Model {model} not found. Using cl100k_base encoding.")
        encoding = tiktoken.get_encoding("cl100k_base")

    tokens = encoding.encode(text)
    total_tokens = len(tokens)
    chunks = []

    start_index = 0
    while start_index < total_tokens:
        end_index = min(start_index + max_tokens, total_tokens)
        chunk_tokens = tokens[start_index:end_index]
        chunk_text = encoding.decode(chunk_tokens)
        chunks.append(chunk_text)

        if end_index == total_tokens:
            break

        # Move start index back for overlap, ensuring it doesn't go below 0
        start_index = max(0, end_index - overlap)
        # Prevent infinite loops if overlap is too large or chunk size too small
        if start_index <= (end_index - max_tokens):
             start_index = end_index - overlap + 1 # Force progression


    logger.info(f"Chunked document into {len(chunks)} chunks (max_tokens={max_tokens}, overlap={overlap})")
    return chunks


def partition_pdf_multimodal(
    pdf_input: Union[bytes, str, BinaryIO]
) -> Tuple[List[Dict], List[Dict]]:
    """
    Partitions a PDF document using PyMuPDF (fitz) to extract text blocks and images.

    Attempts to identify simple captions for images by looking for text blocks
    immediately below the image bounding box.

    Args:
        pdf_input: PDF file path, bytes, or a file-like object.

    Returns:
        A tuple containing two lists:
        1.  text_chunks: List of dictionaries, each like
            {'text': <chunk_text>, 'metadata': {'page': <page_number>}}
        2.  image_data: List of dictionaries, each like
            {'image': <PIL.Image.Image>, 'metadata': {'page': <page_number>, 'caption': <potential_caption>, 'bbox': <fitz.Rect>}}
    """
    text_chunks = []
    image_data = []
    doc = None # Initialize doc to None

    try:
        if isinstance(pdf_input, str) and os.path.isfile(pdf_input):
            doc = fitz.open(pdf_input)
            logger.info(f"Opened PDF from path: {pdf_input}")
        elif isinstance(pdf_input, bytes):
            doc = fitz.open(stream=pdf_input, filetype="pdf")
            logger.info("Opened PDF from bytes.")
        elif hasattr(pdf_input, "read"): # File-like object
            # Read into memory, as fitz stream requires bytes
            pdf_input.seek(0)
            stream_bytes = pdf_input.read()
            doc = fitz.open(stream=stream_bytes, filetype="pdf")
            logger.info("Opened PDF from file-like object.")
        else:
            raise ValueError("Invalid pdf_input type. Must be path, bytes, or file-like object.")

        logger.info(f"Processing PDF with {len(doc)} pages using PyMuPDF.")

        for page_num, page in enumerate(doc):
            page_number = page_num + 1
            logger.debug(f"Processing Page {page_number}")

            # 1. Extract Text Blocks
            # Using 'dict' format provides bounding boxes which can be useful later
            # but for now, we'll primarily use 'blocks' for simpler text structure.
            blocks = page.get_text("blocks", sort=True) # Sort blocks by vertical position
            for b_idx, block in enumerate(blocks):
                # block format: (x0, y0, x1, y1, "text lines...", block_no, block_type)
                # block_type 0 is text, 1 is image? (verify this - image block text is complex)
                if block[6] == 0: # It's a text block
                    block_text = block[4].strip()
                    block_text = re.sub(r'\s+', ' ', block_text) # Normalize whitespace
                    if block_text and len(block_text) > 10: # Basic filter for meaningful text
                        text_chunks.append({
                            "text": block_text,
                            "metadata": {"page": page_number, "block_index": b_idx}
                        })
            logger.debug(f"Page {page_number}: Extracted {len(blocks)} text blocks (approx)")

            # 2. Extract Images and Attempt Caption Detection
            extracted_image_count = 0
            images_on_page = page.get_images(full=True)
            logger.debug(f"Page {page_number}: Found {len(images_on_page)} raw image objects.")

            if not images_on_page:
                 continue # Skip image processing if none found

            page_text_blocks_for_captions = page.get_text("dict", sort=True)["blocks"]

            for img_index, img_info in enumerate(images_on_page):
                xref = img_info[0]
                if xref == 0: # Skip invalid xrefs if any
                     continue
                try:
                    base_image = doc.extract_image(xref)
                    image_bytes = base_image["image"]
                    image_ext = base_image["ext"]
                    
                    # Convert to PIL Image
                    img = Image.open(io.BytesIO(image_bytes))
                    img.load() # Ensure image data is loaded

                    # Get image bounding box on the page
                    img_bboxes = page.get_image_bboxes(img_info, transform=False) # Use direct bbox
                    
                    if not img_bboxes: # If bbox extraction fails, skip captioning
                         logger.warning(f"Page {page_number}, Img xref {xref}: Could not get bounding box.")
                         potential_caption = None
                         img_rect = None
                    else:
                         img_rect = img_bboxes[0] # Usually one bbox per image instance
                         extracted_image_count += 1
                         logger.debug(f"Page {page_number}, Img xref {xref}: Found bbox {img_rect}")
                         # Basic Caption Heuristic: Find text block immediately below the image
                         potential_caption = None
                         min_v_distance = float('inf')
                         caption_search_v_threshold = 25 # How far below the image to look (points)
                         caption_search_h_overlap = 0.5 # Required horizontal overlap fraction

                         for block in page_text_blocks_for_captions:
                              if block['type'] == 0: # Text block
                                   block_rect = fitz.Rect(block['bbox'])
                                   # Check vertical distance (block below image?)
                                   v_distance = block_rect.y0 - img_rect.y1
                                   # Check horizontal overlap
                                   overlap_width = max(0, min(img_rect.x1, block_rect.x1) - max(img_rect.x0, block_rect.x0))
                                   min_rect_width = min(img_rect.width, block_rect.width)
                                   h_overlap_ratio = overlap_width / min_rect_width if min_rect_width > 0 else 0

                                   if 0 < v_distance < caption_search_v_threshold and h_overlap_ratio >= caption_search_h_overlap:
                                        if v_distance < min_v_distance: # Find the closest block below
                                             # Extract text from spans within the block
                                             block_text_lines = []
                                             for line in block.get('lines', []):
                                                  line_text = "".join(span['text'] for span in line.get('spans', []))
                                                  block_text_lines.append(line_text.strip())
                                             candidate_text = " ".join(block_text_lines).strip()
                                             candidate_text = re.sub(r'\s+', ' ', candidate_text)
                                             # Basic check: is it a reasonable caption length? Avoid long paragraphs.
                                             if candidate_text and len(candidate_text) < 250:
                                                  potential_caption = candidate_text
                                                  min_v_distance = v_distance
                                                  logger.debug(f"Page {page_number}, Img xref {xref}: Found potential caption: '{potential_caption[:50]}...'")

                    image_data.append({
                        "image": img,
                        "metadata": {
                            "page": page_number,
                            "caption": potential_caption,
                            "bbox": [img_rect.x0, img_rect.y0, img_rect.x1, img_rect.y1] if img_rect else None, # Store as list
                            "xref": xref,
                            "format": image_ext
                        }
                    })

                except Exception as e:
                    logger.warning(f"Page {page_number}, Img xref {xref}: Error extracting/processing image: {e}")
            
            logger.debug(f"Page {page_number}: Successfully extracted {extracted_image_count} images.")


        logger.info(f"Finished processing PDF. Extracted {len(text_chunks)} text chunks and {len(image_data)} images.")
        return text_chunks, image_data

    except Exception as e:
        logger.error(f"Failed to process PDF with PyMuPDF: {e}", exc_info=True)
        # Return empty lists or re-raise, depending on desired error handling
        return [], []
    finally:
        if doc:
             doc.close()
             logger.debug("Closed PDF document.")

# --- Potentially remove or comment out old BLIP related functions if they exist here ---
# Example:
# def _process_image_with_blip(...):
#    pass # Keep commented or remove if no longer needed anywhere


# Ensure other functions like process_document might need updates
# if they directly called the old partition_pdf_multimodal and expected a different format.
# For now, assuming the caller (knowledge_db.py) will be adapted.

