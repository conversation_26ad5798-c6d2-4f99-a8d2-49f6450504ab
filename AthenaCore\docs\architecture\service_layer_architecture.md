# Service Layer Architecture

## Overview

The Service Layer Architecture is a design pattern implemented in Athena to separate business logic from presentation and data access concerns. This architecture improves maintainability, testability, and promotes cleaner separation of concerns throughout the application.

## Architecture Components

The Service Layer Architecture consists of the following components:

### 1. Models Layer

**Location**: `src/models/`

Models represent the data structures and database schema of the application. They define:
- Database tables and relationships
- Data validation rules
- Basic data manipulation methods

Models should be focused on data representation and should not contain complex business logic.

### 2. Service Layer

**Location**: `src/services/`

The Service Layer contains the core business logic of the application. Services:
- Implement business rules and workflows
- Coordinate operations across multiple models
- Handle complex data transformations
- Provide a consistent API for controllers to use
- Manage transactions and data integrity

Services are designed to be stateless and reusable across different parts of the application.

### 3. Controller Layer

**Location**: `src/controllers/`

Controllers handle the HTTP requests and responses. They:
- Define API endpoints and routes
- Parse and validate request data
- Call appropriate service methods to execute business logic
- Format responses according to standardized formats
- Handle authentication and authorization
- Implement error handling at the API level

### 4. Utility Layer

**Location**: `src/utils/`

Utilities provide common functionality used across the application:
- Helper functions
- Middleware
- Custom exceptions
- Standardized responses
- Configuration management

## Service Layer Implementation

### Base Service

All services inherit from a common base service that provides:
- Access to the database
- Configuration
- Logging
- Common utility methods

```python
# src/services/base_service.py
class BaseService:
    """Base class for all service implementations."""
    
    def __init__(self, db=None):
        """Initialize the service with a database instance."""
        self.db = db or current_app.extensions['sqlalchemy'].db
        self.logger = logging.getLogger(f'athena.services.{self.__class__.__name__}')
```

### Service Registry

Services are registered and accessed through a central registry that manages their lifecycle:

```python
# src/services/base_service.py
# Service registry to store service instances
_service_registry = {}

def get_service(service_class, *args, **kwargs):
    """
    Get or create a service instance from the registry.
    
    Args:
        service_class: The service class to instantiate
        *args, **kwargs: Arguments to pass to the service constructor
        
    Returns:
        An instance of the requested service
    """
    service_name = service_class.__name__
    
    # Create service instance if it doesn't exist
    if service_name not in _service_registry:
        _service_registry[service_name] = service_class(*args, **kwargs)
        
    return _service_registry[service_name]
```

### Service Layer Pattern Example

Below is an example of how the service layer pattern is implemented for task management:

#### 1. Model Definition

```python
# src/models/task.py
class Task(db.Model):
    """Model representing a background task."""
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text, nullable=True)
    status = db.Column(db.String(50), default="pending")
    progress = db.Column(db.Integer, default=0)
    priority = db.Column(db.Integer, default=5)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    started_at = db.Column(db.DateTime, nullable=True)
    completed_at = db.Column(db.DateTime, nullable=True)
    
    # Relationships
    user = db.relationship('User', backref=db.backref('tasks', lazy=True))
    
    def to_dict(self):
        """Convert the task to a dictionary."""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'status': self.status,
            'progress': self.progress,
            'priority': self.priority,
            'user_id': self.user_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }
```

#### 2. Service Implementation

```python
# src/services/task_service.py
class TaskService(BaseService):
    """Service for managing background tasks."""
    
    # Priority constants
    PRIORITY_LOW = 3
    PRIORITY_NORMAL = 5
    PRIORITY_HIGH = 7
    PRIORITY_CRITICAL = 9
    
    def __init__(self, db=None):
        """Initialize the task service."""
        super().__init__(db)
        self.tasks = {}
        self.max_concurrent_tasks = 5
        self.socketio = None
    
    def create_task(self, name, description="", total_steps=100, user_id=None, priority=None):
        """
        Create a new task.
        
        Args:
            name: Name of the task
            description: Description of the task
            total_steps: Total number of steps in the task
            user_id: ID of the user who created the task
            priority: Task priority (0-10, higher is more important)
            
        Returns:
            The created task
        """
        if priority is None:
            priority = self.PRIORITY_NORMAL
            
        task = Task(
            name=name,
            description=description,
            user_id=user_id,
            priority=priority,
            status="pending",
            progress=0
        )
        
        self.db.session.add(task)
        self.db.session.commit()
        
        self.logger.info(f"Created task: {task.id} - {name}")
        return task
    
    # Additional methods for task management
    # ...
```

#### 3. Controller Implementation

```python
# src/controllers/task_controller.py
@task_api.route("/create", methods=["POST"])
@require_auth
@api_route
def create_task():
    """
    Create a new task with optional prioritization.
    
    Request Body:
        name: Name of the task
        description: Description of the task
        total_steps: Total number of steps in the task
        priority: Task priority (0-10, higher is more important)
        metadata: Optional metadata to attach to the task
    
    Returns:
        API response with the created task
    """
    try:
        # Get the task service
        task_service = get_service(TaskService)
        
        # Get request data
        data = request.get_json()
        if not data:
            return error_response("Invalid request data", status_code=400)
        
        # Validate required fields
        if "name" not in data:
            return error_response("Task name is required", status_code=400)
        
        # Extract task parameters
        name = data.get("name")
        description = data.get("description", "")
        total_steps = data.get("total_steps", 100)
        priority = data.get("priority", task_service.PRIORITY_NORMAL)
        metadata = data.get("metadata", {})
        
        # Create the task
        task = task_service.create_task(
            name=name,
            description=description,
            total_steps=total_steps,
            user_id=g.user.id,
            priority=priority,
            metadata=metadata
        )
        
        return success_response(
            data={"task": task.to_dict()},
            message="Task created successfully",
            status_code=201
        )
    except ValidationError as e:
        return error_response(str(e), status_code=400)
    except Exception as e:
        return error_response(f"Error creating task: {str(e)}", status_code=500)
```

## Benefits of Service Layer Architecture

1. **Separation of Concerns**
   - Business logic is isolated from presentation and data access
   - Each layer has a specific responsibility

2. **Testability**
   - Services can be tested independently of controllers and models
   - Mocking dependencies is easier with clear interfaces

3. **Maintainability**
   - Changes to business logic don't affect controllers or models
   - Consistent patterns make the codebase easier to understand

4. **Reusability**
   - Services can be used by multiple controllers or other services
   - Common functionality is centralized

5. **Scalability**
   - Services can be extracted into microservices if needed
   - Performance optimizations can be applied at the service level

## Best Practices

### Service Design

1. **Keep Services Focused**
   - Each service should have a specific domain responsibility
   - Avoid creating "god" services that do too much

2. **Use Dependency Injection**
   - Services should receive their dependencies rather than creating them
   - This improves testability and flexibility

3. **Handle Transactions**
   - Services should manage database transactions
   - Ensure data integrity across multiple operations

4. **Error Handling**
   - Use custom exceptions for different error scenarios
   - Provide meaningful error messages

### Controller Design

1. **Thin Controllers**
   - Controllers should delegate business logic to services
   - Focus on request/response handling, not business rules

2. **Standardized Responses**
   - Use consistent response formats across all endpoints
   - Include success/error indicators and messages

3. **Input Validation**
   - Validate request data before passing to services
   - Use middleware for common validations

### Model Design

1. **Rich Domain Models**
   - Models should include data validation and simple operations
   - Complex business logic belongs in services

2. **Clear Relationships**
   - Define relationships between models explicitly
   - Use foreign keys and constraints for data integrity

## Transitioning to Service Layer Architecture

### Migration Steps

1. **Identify Business Logic**
   - Find business logic in controllers and models
   - Group related functionality

2. **Create Service Classes**
   - Start with core services first
   - Move business logic into appropriate services

3. **Update Controllers**
   - Refactor controllers to use services
   - Standardize request/response handling

4. **Test Thoroughly**
   - Write unit tests for services
   - Ensure backward compatibility

### Backward Compatibility

During the transition period, maintain backward compatibility by:

1. **Creating Compatibility Layers**
   - Forward old imports to new locations
   - Provide deprecated methods that call new implementations

2. **Documentation**
   - Clearly document deprecated features
   - Provide migration guides for developers

## Real-World Examples

The following services demonstrate the service layer pattern in Athena:

1. **TaskService** - Manages background task execution with prioritization
2. **ConfigurationService** - Handles application configuration storage and retrieval
3. **AuthenticationService** - Manages user authentication and authorization
4. **UserService** - Handles user management operations

## References

- [Patterns of Enterprise Application Architecture](https://martinfowler.com/books/eaa.html) by Martin Fowler
- [Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html) by Robert C. Martin
- [Domain-Driven Design](https://domainlanguage.com/ddd/) by Eric Evans
