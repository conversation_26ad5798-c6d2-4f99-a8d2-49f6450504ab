import json
import logging
import functools
from typing import Dict, List, Optional, Any, Union

from flask import Blueprint, request, jsonify, current_app
from werkzeug.exceptions import BadRequest, NotFound, InternalServerError, Unauthorized

from src.mcp.smithery_client import SmitheryClient
from src.utils.config import AthenaConfig

logger = logging.getLogger(__name__)

# Create Blueprint for MCP API routes
mcp_bp = Blueprint('mcp', __name__, url_prefix='/api/v1/mcp')

# Simple authentication decorator
def require_api_key(f):
    """A decorator to check for a valid API key in the request."""
    @functools.wraps(f)
    def decorated(*args, **kwargs):
        # In a real implementation, you would check against a stored API key
        # For now, we'll just check if any API key is provided
        api_key = request.headers.get('X-API-Key')
        if not api_key:
            return jsonify({"error": "API key is required"}), 401
        # In a full implementation, validate the API key here
        return f(*args, **kwargs)
    return decorated

# Initialize the SmitheryClient
_smithery_client = None

def get_smithery_client():
    """Get or create the SmitheryClient instance."""
    global _smithery_client
    if _smithery_client is None:
        config = AthenaConfig.load()
        api_key = config.smithery_api_key
        _smithery_client = SmitheryClient(api_key=api_key)
        logger.info("Initialized Smithery MCP client")
    return _smithery_client

@mcp_bp.route('/servers', methods=['GET'])
@require_api_key
def list_servers():
    """List available MCP servers from Smithery Registry."""
    query = request.args.get('q', '')
    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('pageSize', 10))
    
    try:
        client = get_smithery_client()
        result = client.list_servers(query, page, page_size)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Error listing MCP servers: {e}")
        return jsonify({"error": str(e)}), 500

@mcp_bp.route('/servers/<qualified_name>', methods=['GET'])
@require_api_key
def get_server(qualified_name):
    """Get details for a specific MCP server."""
    try:
        client = get_smithery_client()
        server = client.get_server(qualified_name)
        if not server:
            return jsonify({"error": f"Server {qualified_name} not found"}), 404
        return jsonify(server)
    except Exception as e:
        logger.error(f"Error getting MCP server {qualified_name}: {e}")
        return jsonify({"error": str(e)}), 500

@mcp_bp.route('/connection/websocket-url', methods=['POST'])
@require_api_key
def create_websocket_url():
    """Create a WebSocket URL for connecting to an MCP server."""
    try:
        data = request.get_json()
        if not data:
            raise BadRequest("Missing JSON body")
            
        if 'qualifiedName' not in data:
            raise BadRequest("Missing 'qualifiedName' in request")
            
        if 'config' not in data:
            raise BadRequest("Missing 'config' in request")
        
        client = get_smithery_client()    
        # Get server details
        server = client.get_server(data['qualifiedName'])
        if not server:
            raise NotFound(f"Server {data['qualifiedName']} not found")
            
        # Create WebSocket URL
        url = client.create_websocket_url(
            server['deploymentUrl'], data['config'])
            
        return jsonify({
            "url": url,
            "serverInfo": server
        })
        
    except BadRequest as e:
        return jsonify({"error": str(e)}), 400
    except NotFound as e:
        return jsonify({"error": str(e)}), 404
    except Exception as e:
        logger.error(f"Error creating WebSocket URL: {e}")
        return jsonify({"error": str(e)}), 500

@mcp_bp.route('/status', methods=['GET'])
@require_api_key
def check_connection_status():
    """Check if the connection to Smithery Registry is working."""
    try:
        client = get_smithery_client()
        if client.check_connection():
            return jsonify({"status": "connected"})
        else:
            return jsonify({"status": "disconnected"}), 503
    except Exception as e:
        logger.error(f"Error checking MCP connection: {e}")
        return jsonify({"error": str(e)}), 500

@mcp_bp.route('/api-keys', methods=['GET'])
@require_api_key
def list_mcp_api_keys():
    """List all MCP service-specific API keys for the current user."""
    from flask_login import current_user
    from src.models import MCPApiKey
    
    if not current_user.is_authenticated:
        return jsonify({"error": "Authentication required"}), 401
    
    try:
        keys = MCPApiKey.query.filter_by(user_id=current_user.id).all()
        return jsonify({
            "keys": [key.to_dict() for key in keys]
        })
    except Exception as e:
        logger.error(f"Error listing MCP API keys: {e}")
        return jsonify({"error": str(e)}), 500


@mcp_bp.route('/api-keys', methods=['POST'])
@require_api_key
def add_mcp_api_key():
    """Add a new MCP service-specific API key for the current user."""
    from flask_login import current_user
    from src.models import MCPApiKey, db
    
    if not current_user.is_authenticated:
        return jsonify({"error": "Authentication required"}), 401
    
    try:
        data = request.get_json()
        if not data:
            raise BadRequest("Missing JSON body")
            
        required_fields = ['service_name', 'api_key']
        for field in required_fields:
            if field not in data:
                raise BadRequest(f"Missing '{field}' in request")
        
        # Check if key already exists for this service and user
        existing_key = MCPApiKey.query.filter_by(
            user_id=current_user.id,
            service_name=data['service_name']
        ).first()
        
        if existing_key:
            # Update existing key
            existing_key.api_key = data['api_key']
            db.session.commit()
            return jsonify({
                "message": f"Updated API key for {data['service_name']}",
                "key": existing_key.to_dict()
            })
        
        # Create new key
        new_key = MCPApiKey(
            user_id=current_user.id,
            service_name=data['service_name'],
            api_key=data['api_key']
        )
        db.session.add(new_key)
        db.session.commit()
        
        return jsonify({
            "message": f"Added API key for {data['service_name']}",
            "key": new_key.to_dict()
        }), 201
    except BadRequest as e:
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        logger.error(f"Error adding MCP API key: {e}")
        return jsonify({"error": str(e)}), 500


@mcp_bp.route('/api-keys/<int:key_id>', methods=['DELETE'])
@require_api_key
def delete_mcp_api_key(key_id):
    """Delete an MCP service-specific API key."""
    from flask_login import current_user
    from src.models import MCPApiKey, db
    
    if not current_user.is_authenticated:
        return jsonify({"error": "Authentication required"}), 401
    
    try:
        key = MCPApiKey.query.filter_by(id=key_id, user_id=current_user.id).first()
        if not key:
            return jsonify({"error": "API key not found"}), 404
        
        service_name = key.service_name
        db.session.delete(key)
        db.session.commit()
        
        return jsonify({
            "message": f"Deleted API key for {service_name}"
        })
    except Exception as e:
        logger.error(f"Error deleting MCP API key: {e}")
        return jsonify({"error": str(e)}), 500
