"""
Simple test script to verify compatibility layer functionality.
"""

import os
import sys
import logging

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("CompatibilityTest")

def test_models_import():
    """Test importing from src.login.models"""
    try:
        from src.login.models import db, User
        
        logger.info("Successfully imported db from src.login.models")
        logger.info("Successfully imported User from src.login.models")
        
        # Show actual source location
        logger.info(f"User model's source: {User.__module__}")
        return True
    except Exception as e:
        logger.error(f"Error importing from models: {str(e)}")
        return False

def test_views_import():
    """Test importing from src.login.views"""
    try:
        from src.login.views import login, logout
        
        logger.info("Successfully imported login from src.login.views")
        logger.info("Successfully imported logout from src.login.views")
        
        # Show actual source location
        logger.info(f"login function's source: {login.__module__}")
        return True
    except Exception as e:
        logger.error(f"Error importing from views: {str(e)}")
        return False

def run_all_tests():
    """Run all compatibility tests"""
    models_success = test_models_import()
    views_success = test_views_import()
    
    logger.info(f"Models import test: {'PASSED' if models_success else 'FAILED'}")
    logger.info(f"Views import test: {'PASSED' if views_success else 'FAILED'}")
    
    return models_success and views_success

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
