"""
Knowledge Base Context Manager
This module maintains knowledge base context within a conversation
so that documents can be referenced in follow-up questions.
"""

import logging
from typing import Dict, List, Optional
import re
from datetime import datetime, timedelta

from src.core.knowledge_db import KnowledgeDatabase
from src.utils.document_processor import chunk_document
import tiktoken

# Document context holder - maps conversation_id to document references
# Format: {conversation_id: {"documents": [doc_refs], "last_updated": timestamp}}
_document_contexts = {}

# Document reference timeout (10 minutes)
CONTEXT_TIMEOUT = timedelta(minutes=10)


def setup_logger():
    """Set up a logger for the KB context module"""
    logger = logging.getLogger("athena.kb_context")
    logger.setLevel(logging.INFO)
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    return logger


logger = setup_logger()


def store_document_context(conversation_id: str, documents: List[Dict]) -> None:
    """
    Store document references for a conversation

    Args:
        conversation_id: The ID of the current conversation
        documents: List of document references from a knowledge search
    """
    if not conversation_id or not documents:
        return

    # Store document references with timestamp
    _document_contexts[conversation_id] = {
        "documents": documents,
        "last_updated": datetime.now(),
    }

    logger.info(
        f"Stored {len(documents)} document references for conversation {conversation_id}"
    )


def get_document_by_reference(conversation_id: str, reference: str) -> Optional[Dict]:
    """
    Get document by a reference (ID, number, or partial title)

    Args:
        conversation_id: The ID of the current conversation
        reference: Document reference (ID, number in results, or title)

    Returns:
        Document dict if found, None otherwise
    """
    if not conversation_id or not reference:
        return None

    # Get document context for this conversation
    context = _document_contexts.get(conversation_id)
    if not context:
        logger.info(f"No document context found for conversation {conversation_id}")
        return None

    # Check if context is too old
    if datetime.now() - context["last_updated"] > CONTEXT_TIMEOUT:
        logger.info(f"Document context for conversation {conversation_id} has expired")
        _document_contexts.pop(conversation_id, None)
        return None

    documents = context["documents"]

    # Try to match by document number (e.g., "1" or "document 2")
    if reference.isdigit() or re.match(r"document\s+(\d+)", reference):
        doc_num = (
            int(reference)
            if reference.isdigit()
            else int(re.match(r"document\s+(\d+)", reference).group(1))
        )
        if 1 <= doc_num <= len(documents):
            return documents[doc_num - 1]

    # Try to match by document ID
    for doc in documents:
        if doc.get("metadata", {}).get("id") == reference:
            return doc

    # Try more flexible matching by partial title or content
    reference_lower = reference.lower()
    best_match = None
    best_score = 0

    for doc in documents:
        # Get document metadata
        meta = doc.get("metadata", {})
        title = meta.get("title", doc.get("title", "")).lower()
        content = doc.get("content", "").lower() if doc.get("content") else ""

        # Check title match (highest priority)
        if title and reference_lower in title:
            title_score = 10  # High score for title match
            # Exact match scores higher than partial match
            if title == reference_lower:
                title_score = 20
            # If this is better than our current best match, update it
            if title_score > best_score:
                best_match = doc
                best_score = title_score

        # Check content match (lower priority than title)
        elif content and reference_lower in content:
            content_score = 5  # Medium score for content match
            if content_score > best_score:
                best_match = doc
                best_score = content_score

        # Try matching individual words
        elif title:
            ref_words = reference_lower.split()
            for word in ref_words:
                if len(word) > 3 and word in title:  # Only match significant words
                    word_match_score = 2
                    if word_match_score > best_score:
                        best_match = doc
                        best_score = word_match_score

    if best_match:
        return best_match

    # If still no match, check for semantic similarity with key terms
    key_terms = ["magnus", "world", "realm", "dimension", "universe", "magic", "system"]
    for term in key_terms:
        if term in reference_lower:
            # Find any document that might relate to this term
            for doc in documents:
                meta = doc.get("metadata", {})
                title = meta.get("title", doc.get("title", "")).lower()
                content = doc.get("content", "").lower() if doc.get("content") else ""

                if term in title or term in content:
                    logger.info(
                        f"Found document via key term '{term}' matching reference '{reference}'"
                    )
                    return doc

    return None


def extract_document_reference(query: str) -> Optional[str]:
    """
    Extract document reference from a query

    Examples:
    - "tell me about document 1"
    - "what is in vibecode"
    - "summarize the quantum AI document"
    - "tell me about the worlds in Magnus"

    Args:
        query: User query

    Returns:
        Document reference if found, None otherwise
    """
    query_lower = query.lower()

    # Check for document number pattern
    num_match = re.search(r"document\s+(\d+)", query_lower)
    if num_match:
        return num_match.group(1)

    # Check for specific topic within document
    topic_in_doc_match = re.search(
        r"(?:about|tell me about|what are|explain) (?:the )?(.+?) (?:in| from| of) (.+?)(?:\?|$)",
        query_lower,
    )
    if topic_in_doc_match:
        doc_name = topic_in_doc_match.group(2).strip()
        return doc_name

    # Check for common document reference patterns
    patterns = [
        r"what(?:'s| is) in (.+?)(?:\?|$)",
        r"tell me about (.+?)(?:\?|$)",
        r"summarize (?:the )?(.+?)(?:\?|$)",
        r"what does (.+?) (?:contain|say|talk about)(?:\?|$)",
        r"contents? of (.+?)(?:\?|$)",
        r"read (.+?)(?:\?|$)",
        r"information (?:about|on|in) (.+?)(?:\?|$)",
        r"details (?:about|on|in) (.+?)(?:\?|$)",
        r"describe (.+?)(?:\?|$)",
        r"explain (.+?)(?:\?|$)",
    ]

    for pattern in patterns:
        match = re.search(pattern, query_lower)
        if match:
            return match.group(1).strip()

    # Fallback: extract key nouns that might be document names
    words = query_lower.split()
    for word in words:
        if len(word) > 3 and word not in [
            "what",
            "where",
            "when",
            "tell",
            "about",
            "information",
            "data",
            "please",
            "could",
            "would",
            "document",
            "documents",
        ]:
            return word

    return None


def get_document_content(conversation_id: str, query: str) -> Optional[Dict]:
    """
    Get document content based on a user query

    Args:
        conversation_id: The ID of the current conversation
        query: User query about a document

    Returns:
        Dict with document content and metadata if found, None otherwise
    """
    # Try to extract document reference from query first
    reference = extract_document_reference(query)
    if not reference:
        logger.info(f"No document reference extracted from query: '{query}'")
        # Fallback: try using common keywords from the query
        words = query.lower().split()
        for word in words:
            if len(word) > 3 and word not in [
                "what",
                "where",
                "when",
                "how",
                "tell",
                "about",
                "please",
            ]:
                reference = word
                logger.info(f"Using fallback keyword as reference: '{reference}'")
                break

    if not reference:
        return None

    # Get document by reference
    doc = get_document_by_reference(conversation_id, reference)
    if not doc:
        logger.info(
            f"No document found for reference '{reference}' in conversation {conversation_id}"
        )

        # Try to find any document that might contain relevant information
        # by checking all documents for content matches to the query
        context = _document_contexts.get(conversation_id)
        if context and "documents" in context:
            documents = context["documents"]
            query_lower = query.lower()

            # Extract potential topic of interest
            topic_match = re.search(
                r"(?:about|tell me about|what are|explain) (?:the )?(.+?)(?: in| from| of|\?|$)",
                query_lower,
            )
            topic = topic_match.group(1).strip() if topic_match else ""

            for doc in documents:
                content = doc.get("content", "").lower() if doc.get("content") else ""
                title = (
                    doc.get("metadata", {}).get("title", doc.get("title", "")).lower()
                )

                # Check if the content contains information about the topic
                if (topic and topic in content) or any(
                    word in content for word in query_lower.split() if len(word) > 3
                ):
                    logger.info(
                        f"Found relevant document '{title}' for query: '{query}'"
                    )
                    return {
                        "content": doc.get("content", ""),
                        "metadata": doc.get("metadata", {}),
                        "from_content_search": True,
                    }

        return None

    logger.info(
        f"Found document for reference '{reference}' in conversation {conversation_id}"
    )

    # Attempt to fetch full document content from the database
    doc_id = doc.get("metadata", {}).get("id") or doc.get("id")
    if doc_id:
        try:
            full_doc = get_document_by_id(doc_id)
            if full_doc and full_doc.get("content"):
                logger.info(
                    f"Fetched full document content for id '{doc_id}' from database"
                )
                return {
                    "content": full_doc.get("content", ""),
                    "metadata": full_doc.get("metadata", {}),
                    "from_db": True,
                }
        except Exception as e:
            logger.error(f"Error fetching full document content: {e}")
    # Fallback to context content
    return {
        "content": doc.get("content", ""),
        "metadata": doc.get("metadata", {}),
        "from_context": True,
    }


def get_document_by_id(doc_id: str) -> Optional[Dict]:
    """
    Get document directly from the knowledge database by ID

    Args:
        doc_id: Document ID

    Returns:
        Document dict if found, None otherwise
    """
    try:
        kb = KnowledgeDatabase()
        doc = kb.get_document(doc_id)
        if doc:
            return {
                "content": doc.get("content", ""),
                "metadata": {
                    "id": doc.get("id", ""),
                    "title": doc.get("title", ""),
                    "type": doc.get("type", ""),
                    "source": doc.get("source", ""),
                    "tags": doc.get("tags", []),
                },
                "from_db": True,
            }
        return None
    except Exception as e:
        logger.error(f"Error getting document by ID: {e}")
        return None


def get_document_context_for_conversation(conversation_id: str) -> List[Dict]:
    """
    Get document context for a conversation

    Args:
        conversation_id: The ID of the current conversation

    Returns:
        List of document references if found, empty list otherwise
    """
    if not conversation_id:
        return []

    # Get document context for this conversation
    context = _document_contexts.get(conversation_id)
    if not context:
        logger.info(f"No document context found for conversation {conversation_id}")
        return []

    # Check if context is too old
    if datetime.now() - context["last_updated"] > CONTEXT_TIMEOUT:
        logger.info(f"Document context for conversation {conversation_id} has expired")
        _document_contexts.pop(conversation_id, None)
        return []

    return context["documents"]


def enhance_user_query_with_kb_context(
    conversation_id: str, query: str, system_prompt: str, history_tokens: int = 0
) -> str:
    """
    Enhance the system prompt with relevant knowledge base CHUNKS.

    Args:
        conversation_id: The ID of the current conversation.
        query: User query (used primarily to check if KB interaction is likely needed).
        system_prompt: Current system prompt.
        history_tokens: Number of tokens from conversation history.

    Returns:
        Enhanced system prompt with KB chunk context if relevant, or original prompt.
    """
    try:
        # Check if there are relevant chunks stored in the context for this conversation
        context_chunks = get_document_context_for_conversation(conversation_id)
        if not context_chunks:
            logger.debug("No KB context chunks found for this conversation.")
            return system_prompt

        # Sort chunks by distance (ascending) if available, otherwise keep order
        context_chunks.sort(key=lambda x: x.get("distance") if x.get("distance") is not None else float('inf'))

        # Limit the number of chunks to include (e.g., top 3)
        max_chunks_to_include = 3
        top_chunks = context_chunks[:max_chunks_to_include]

        if not top_chunks:
            logger.debug("No KB top chunks to add to context.")
            return system_prompt

        # Dynamically calculate available token space
        enc = tiktoken.encoding_for_model("gpt-3.5-turbo") # Use a common model for estimation
        sys_tokens = len(enc.encode(system_prompt))
        user_tokens = len(enc.encode(query))
        reserved_for_completion = 500  # Tokens reserved for the LLM's response
        # Estimate max model tokens - adjust if using models with larger windows
        # Using 8k as a reasonable default guess, could be 4k, 16k, 32k, 128k depending on model
        max_model_tokens = 8192
        available_tokens = max_model_tokens - reserved_for_completion - sys_tokens - user_tokens - history_tokens

        if available_tokens < 150: # Need at least some space for context
            logger.warning(f"Not enough token space ({available_tokens}) to add KB context.")
            return system_prompt

        # Build the context string from the top chunks
        kb_context_str = "\n\nRelevant excerpts from the Knowledge Base:\n"
        added_tokens = 0
        added_chunk_count = 0

        for chunk in top_chunks:
            content = chunk.get("content")
            metadata = chunk.get("metadata")
            if not content or not metadata:
                continue

            title = metadata.get('title', 'Untitled Document')
            chunk_id = chunk.get('id')
            chunk_index = metadata.get('chunk_index', '?')
            total_chunks = metadata.get('total_chunks', '?')
            distance = chunk.get("distance")
            dist_str = f"(distance: {distance:.4f})" if distance is not None else ""

            header = f"--- Excerpt from: \"{title}\" (Chunk {chunk_index}/{total_chunks}) {dist_str} ---\
"
            footer = "\n---"
            chunk_text = header + content + footer

            chunk_tokens = len(enc.encode(chunk_text))

            if added_tokens + chunk_tokens <= available_tokens:
                kb_context_str += chunk_text + "\n"
                added_tokens += chunk_tokens
                added_chunk_count += 1
            else:
                # Try adding a truncated version if space allows
                remaining_space = available_tokens - added_tokens
                if remaining_space > 100: # Only add if we have reasonable space
                    header_tokens = len(enc.encode(header))
                    footer_tokens = len(enc.encode(footer))
                    trunc_content_tokens = remaining_space - header_tokens - footer_tokens - 20 # -20 for safety margin
                    if trunc_content_tokens > 50:
                        truncated_content = enc.decode(enc.encode(content)[:trunc_content_tokens]) + "... [truncated]"
                        truncated_chunk_text = header + truncated_content + footer
                        kb_context_str += truncated_chunk_text + "\n"
                        added_tokens += remaining_space # Approximate
                        added_chunk_count += 1
                break # Stop adding chunks if we run out of space

        if added_chunk_count == 0:
             logger.info("Insufficient token space to add any KB context chunks.")
             return system_prompt

        # Final instruction for the LLM
        kb_context_str += "\nUse the relevant excerpts above to answer the user's query accurately."

        # Append KB context to system prompt, ensuring old excerpts are removed
        system_prompt_cleaned = re.sub(
            r"Relevant excerpts from the Knowledge Base:[\s\S]*?query accurately\.",
            "",
            system_prompt,
        ).strip()

        enhanced_prompt = f"{system_prompt_cleaned}\n{kb_context_str}"
        logger.info(f"Enhanced system prompt with {added_chunk_count} KB context chunks ({added_tokens} tokens)." )

        return enhanced_prompt

    except Exception as e:
        logger.error(f"Error enhancing system prompt with KB context: {e}")
        logger.error(traceback.format_exc())
        return system_prompt # Return original prompt on error
