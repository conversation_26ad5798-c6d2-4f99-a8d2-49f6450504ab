import json
import logging
import asyncio
from typing import Dict, Any, Optional, List, Callable, Union

import websockets
from websockets.exceptions import ConnectionClosed

from src.mcp.smithery_client import SmitheryClient

logger = logging.getLogger(__name__)

class MCPConnection:
    """Manages a WebSocket connection to an MCP server."""
    
    def __init__(self, qualified_name: str, config: Dict[str, Any], smithery_client: SmitheryClient):
        """Initialize an MCP connection.
        
        Args:
            qualified_name: Qualified name of the MCP server (e.g., 'owner/repo' or '@owner/repo').
            config: Configuration settings for the MCP server connection.
            smithery_client: Initialized SmitheryClient instance.
        """
        # Store both original and normalized names
        self.display_name = smithery_client.format_display_name(qualified_name)
        self.qualified_name = smithery_client.normalize_server_name(qualified_name)
        self.config = config
        self.smithery_client = smithery_client
        self.connection_url = None
        self.websocket = None
        self.server_info = None
        self.is_connected = False
        self.message_queue = asyncio.Queue()
        self.response_callbacks = {}
        self.active_task = None
    
    async def connect(self) -> bool:
        """Establish connection to the MCP server.
        
        Returns:
            True if connection successful, False otherwise.
        """
        try:
            # Get server details from Smithery Registry
            self.server_info = self.smithery_client.get_server(self.qualified_name)
            if not self.server_info:
                logger.error(f"Could not retrieve server info for {self.display_name}")
                return False
            
            # Create WebSocket URL
            deployment_url = self.server_info.get('deploymentUrl')
            if not deployment_url:
                logger.error(f"No deployment URL found for {self.display_name}")
                return False
            
            self.connection_url = self.smithery_client.create_websocket_url(
                deployment_url, self.config)
            
            # Connect to WebSocket
            self.websocket = await websockets.connect(self.connection_url)
            self.is_connected = True
            
            # Start background tasks for message handling
            self.active_task = asyncio.create_task(self._message_handler())
            
            logger.info(f"Successfully connected to MCP server {self.display_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to MCP server {self.display_name}: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from the MCP server and clean up resources."""
        if self.active_task and not self.active_task.done():
            self.active_task.cancel()
        
        if self.websocket:
            await self.websocket.close()
            self.websocket = None
        
        self.is_connected = False
        logger.info(f"Disconnected from MCP server {self.display_name}")
    
    async def send_message(self, message: Dict[str, Any], callback: Optional[Callable] = None) -> str:
        """Send a message to the MCP server.
        
        Args:
            message: The message to send, following MCP protocol format.
            callback: Optional callback function to handle the response.
            
        Returns:
            Message ID for tracking the response.
        """
        if not self.is_connected or not self.websocket:
            raise ConnectionError("Not connected to MCP server")
        
        # Ensure message has required fields
        if 'id' not in message:
            message['id'] = str(hash(json.dumps(message)))
        
        if callback and 'id' in message:
            self.response_callbacks[message['id']] = callback
        
        await self.websocket.send(json.dumps(message))
        logger.debug(f"Sent message to {self.display_name}: {message}")
        
        return message['id']
    
    async def _message_handler(self):
        """Background task to handle incoming messages."""
        try:
            while self.is_connected and self.websocket:
                try:
                    message_text = await self.websocket.recv()
                    message = json.loads(message_text)
                    
                    # Handle response callbacks
                    msg_id = message.get('id')
                    if msg_id and msg_id in self.response_callbacks:
                        callback = self.response_callbacks[msg_id]
                        try:
                            await callback(message)
                        except Exception as callback_error:
                            logger.error(f"Error in callback for message {msg_id}: {callback_error}")
                        finally:
                            # One-time callback, remove after use
                            del self.response_callbacks[msg_id]
                    
                    # Add to queue for other consumers
                    await self.message_queue.put(message)
                    
                except ConnectionClosed:
                    logger.warning(f"Connection closed to {self.display_name}")
                    self.is_connected = False
                    break
                    
        except asyncio.CancelledError:
            logger.info(f"Message handler for {self.display_name} cancelled")
            raise
        except Exception as e:
            logger.error(f"Error in message handler for {self.display_name}: {e}")
            self.is_connected = False

class MCPConnectionManager:
    """Manages multiple MCP connections."""
    
    def __init__(self, smithery_client: SmitheryClient):
        """Initialize the connection manager.
        
        Args:
            smithery_client: SmitheryClient instance for server discovery.
        """
        self.smithery_client = smithery_client
        self.connections: Dict[str, MCPConnection] = {}
        print("u2713 MCPConnectionManager initialized")
    
    def create_connection(self, qualified_name: str, config: Dict[str, Any]) -> Optional[MCPConnection]:
        """Create and establish a new MCP connection.
        
        Args:
            qualified_name: Qualified name of the MCP server (with or without @ prefix).
            config: Configuration for the connection.
            
        Returns:
            MCPConnection instance if successful, None otherwise.
        """
        # Normalize qualified name for consistency
        normalized_name = self.smithery_client.normalize_server_name(qualified_name)
        display_name = self.smithery_client.format_display_name(qualified_name)
        
        # Check if connection already exists
        if normalized_name in self.connections:
            print(f"Connection to {display_name} already exists")
            return self.connections[normalized_name]
        
        try:
            # Create new connection object
            connection = MCPConnection(normalized_name, config, self.smithery_client)
            
            # Store connection
            self.connections[normalized_name] = connection
            print(f"Created connection to {display_name}")
            return connection
        except Exception as e:
            logger.error(f"Failed to create connection to {display_name}: {e}")
            return None
    
    def disconnect(self, qualified_name: str) -> bool:
        """Disconnect a specific MCP connection.
        
        Args:
            qualified_name: The qualified name of the connection to disconnect.
            
        Returns:
            True if successfully disconnected, False if not found.
        """
        normalized_name = self.smithery_client.normalize_server_name(qualified_name)
        
        if normalized_name in self.connections:
            try:
                # No async needed - use synchronous approach
                # Placeholder for any cleanup needed
                del self.connections[normalized_name]
                return True
            except Exception as e:
                logger.error(f"Error disconnecting from {qualified_name}: {e}")
                return False
        return False
    
    def disconnect_all(self):
        """Disconnect all active MCP connections."""
        for name in list(self.connections.keys()):
            self.disconnect(name)
    
    def get_connection(self, qualified_name: str) -> Optional[MCPConnection]:
        """Get an existing MCP connection by qualified name.
        
        Args:
            qualified_name: The qualified name of the connection.
            
        Returns:
            MCPConnection instance if found, None otherwise.
        """
        normalized_name = self.smithery_client.normalize_server_name(qualified_name)
        return self.connections.get(normalized_name)
    
    def list_connections(self) -> List[Dict[str, Any]]:
        """List all active MCP connections.
        
        Returns:
            List of connection details.
        """
        result = []
        for name, connection in self.connections.items():
            result.append({
                "qualified_name": connection.qualified_name,
                "display_name": connection.display_name,
                "is_connected": connection.is_connected
            })
        return result
