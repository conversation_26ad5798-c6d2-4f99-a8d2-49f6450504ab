# API Standardization Guide

## Overview

This document describes the standardized API approach implemented in Athena. These standards ensure consistency, improve developer experience, and enable easier integration with frontend applications and third-party services.

## API Response Format

All API endpoints return responses in a consistent JSON format:

```json
{
  "success": true,          // Boolean indicating success or failure
  "message": "string",      // Human-readable message about the operation
  "data": {},               // Response payload (optional)
  "error_code": "string",   // Error code in case of failure (optional)
  "meta": {}                // Additional metadata (optional)
}
```

### Success Response Example

```json
{
  "success": true,
  "message": "User created successfully",
  "data": {
    "user": {
      "id": 123,
      "username": "johndo<PERSON>",
      "email": "<EMAIL>",
      "created_at": "2025-05-24T14:30:00Z"
    }
  },
  "meta": {
    "request_id": "req-123456"
  }
}
```

### Error Response Example

```json
{
  "success": false,
  "message": "Invalid user data provided",
  "error_code": "validation_error",
  "data": {
    "validation_errors": {
      "email": "Invalid email format",
      "password": "Password must be at least 8 characters"
    }
  }
}
```

## Status Codes

API endpoints use standard HTTP status codes:

| Status Code | Description | Example Scenario |
|-------------|-------------|------------------|
| 200 | OK | Successful GET request |
| 201 | Created | Resource successfully created |
| 204 | No Content | Successful operation with no response body |
| 400 | Bad Request | Invalid request parameters |
| 401 | Unauthorized | Missing or invalid authentication |
| 403 | Forbidden | Authentication valid but insufficient permissions |
| 404 | Not Found | Resource not found |
| 409 | Conflict | Request conflicts with current state |
| 422 | Unprocessable Entity | Semantic errors in request |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Server-side error |

## Authentication

API endpoints that require authentication accept the following methods:

1. **Bearer Token**: `Authorization: Bearer <token>`
2. **API Key**: `X-API-Key: <api_key>`
3. **Session Cookie**: For browser-based applications

Authentication is handled by the middleware layer before the request reaches the controller.

## Request Validation

Request validation follows these principles:

1. **Early Validation**: Validate inputs before processing
2. **Descriptive Errors**: Return specific validation errors
3. **Security Focus**: Never expose sensitive information in error messages

## API Versioning

API versioning is implemented via URL prefixes:

```
/api/v1/resource
/api/v2/resource
```

The current default version is v1 (implicit when no version is specified).

## Implementation Details

### Middleware

API standardization is implemented through middleware components:

1. **Authentication Middleware** (`src/utils/middleware.py`)
   - Verifies authentication credentials
   - Populates request context with user information

2. **Authorization Middleware** (`src/utils/middleware.py`) 
   - Checks user permissions against required roles
   - Handles role-based access control

3. **API Route Decorator** (`src/utils/middleware.py`)
   - Standardizes response formatting
   - Handles exception conversion to appropriate responses
   - Adds request metadata

```python
@api_route
def endpoint_function():
    # Your endpoint logic here
    return success_response(data={"key": "value"})
```

### Response Helpers

Standard helper functions for API responses:

```python
from src.utils.api_response import success_response, error_response

# Success response
success_response(
    data={"user": user.to_dict()},
    message="User created successfully",
    status_code=201
)

# Error response
error_response(
    message="Resource not found",
    error_code="not_found",
    status_code=404
)
```

### Custom Exceptions

Custom exceptions that automatically translate to standardized API responses:

```python
from src.utils.exceptions import ValidationError, ResourceNotFoundError

# These exceptions are caught by the api_route decorator
# and converted to appropriate error responses

raise ValidationError("Invalid email format")
raise ResourceNotFoundError("User not found")
```

## Example Implementation

### Controller

```python
from flask import Blueprint, request
from src.utils.middleware import require_auth, require_role, api_route
from src.utils.api_response import success_response, error_response
from src.utils.exceptions import ValidationError, ResourceNotFoundError
from src.services.user_service import UserService
from src.services.base_service import get_service

user_api = Blueprint("user_api", __name__, url_prefix="/api/users")

@user_api.route("/", methods=["GET"])
@require_auth
@require_role(["admin"])
@api_route
def get_users():
    """Get a list of users."""
    try:
        user_service = get_service(UserService)
        users = user_service.get_all_users()
        
        return success_response(
            data={"users": [user.to_dict() for user in users]},
            message=f"Retrieved {len(users)} users"
        )
    except Exception as e:
        return error_response(f"Error retrieving users: {str(e)}", status_code=500)

@user_api.route("/<user_id>", methods=["GET"])
@require_auth
@api_route
def get_user(user_id):
    """Get a specific user."""
    try:
        user_service = get_service(UserService)
        user = user_service.get_user_by_id(user_id)
        
        if not user:
            return error_response(f"User {user_id} not found", status_code=404)
        
        return success_response(
            data={"user": user.to_dict()},
            message=f"User {user_id} retrieved successfully"
        )
    except ResourceNotFoundError as e:
        return error_response(str(e), status_code=404)
    except Exception as e:
        return error_response(f"Error retrieving user: {str(e)}", status_code=500)
```

## Client Integration

### JavaScript Example

```javascript
// Function to call standardized API
async function callAPI(endpoint, method = 'GET', data = null) {
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${getAuthToken()}`
    }
  };
  
  if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
    options.body = JSON.stringify(data);
  }
  
  const response = await fetch(`/api/${endpoint}`, options);
  const result = await response.json();
  
  if (!result.success) {
    // Handle error
    console.error(`API Error: ${result.message}`);
    throw new Error(result.message);
  }
  
  return result.data;
}

// Example usage
async function getUserProfile(userId) {
  try {
    const data = await callAPI(`users/${userId}`);
    return data.user;
  } catch (error) {
    console.error('Failed to fetch user profile:', error);
    throw error;
  }
}
```

### Python Example

```python
import requests

def call_api(endpoint, method='GET', data=None, token=None):
    """
    Call the standardized API.
    
    Args:
        endpoint: API endpoint path (without /api/ prefix)
        method: HTTP method to use
        data: Request data for POST/PUT/PATCH requests
        token: Authentication token
        
    Returns:
        The data portion of the API response
        
    Raises:
        Exception: If the API returns an error
    """
    headers = {'Content-Type': 'application/json'}
    if token:
        headers['Authorization'] = f'Bearer {token}'
    
    url = f'https://api.example.com/api/{endpoint}'
    
    response = requests.request(
        method=method,
        url=url,
        json=data,
        headers=headers
    )
    
    result = response.json()
    
    if not result.get('success', False):
        raise Exception(f"API Error: {result.get('message', 'Unknown error')}")
    
    return result.get('data')
```

## Best Practices

1. **Consistent Naming**: Use consistent resource names and URL patterns
   - Collection endpoints: `/api/resources`
   - Individual resource: `/api/resources/{id}`
   - Actions on resources: `/api/resources/{id}/action`

2. **Request Parameters**:
   - Use query parameters for filtering, sorting, and pagination
   - Use path parameters for resource identification
   - Use request body for complex data

3. **Response Filtering**:
   - Support filtering of response fields where appropriate
   - Allow clients to specify which fields they need

4. **Error Handling**:
   - Provide specific, actionable error messages
   - Include error codes for programmatic handling
   - Never expose sensitive information in errors

5. **Documentation**:
   - Document all endpoints with examples
   - Include parameter validation rules
   - Document error scenarios and codes

## Migration Guide

### Migrating Existing Endpoints

1. Add the `@api_route` decorator to the endpoint function
2. Replace direct Flask response construction with `success_response` or `error_response`
3. Add appropriate authentication and authorization decorators
4. Update error handling to use the standardized exceptions

### Before

```python
@app.route("/api/users", methods=["GET"])
@login_required
def get_users():
    if current_user.role != "admin":
        return jsonify({"error": "Unauthorized"}), 403
    
    users = User.query.all()
    return jsonify({"users": [user.to_dict() for user in users]})
```

### After

```python
@user_api.route("/", methods=["GET"])
@require_auth
@require_role(["admin"])
@api_route
def get_users():
    try:
        user_service = get_service(UserService)
        users = user_service.get_all_users()
        
        return success_response(
            data={"users": [user.to_dict() for user in users]},
            message=f"Retrieved {len(users)} users"
        )
    except Exception as e:
        return error_response(f"Error retrieving users: {str(e)}", status_code=500)
```

## References

- [JSON:API Specification](https://jsonapi.org/)
- [Google API Design Guide](https://cloud.google.com/apis/design)
- [Microsoft REST API Guidelines](https://github.com/microsoft/api-guidelines/blob/vNext/Guidelines.md)
- [RFC 7807: Problem Details for HTTP APIs](https://tools.ietf.org/html/rfc7807)
