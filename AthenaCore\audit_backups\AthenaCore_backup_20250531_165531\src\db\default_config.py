"""
Default configuration for the Athena database.
This module provides utility functions to create default configurations.
"""

import logging
from datetime import datetime
from flask import current_app
from flask_login import current_user
from src.models import db, DirectConnection, LLMProviderSetting

logger = logging.getLogger(__name__)

def create_default_openai_connection():
    """
    Creates a default OpenAI connection in the database if one doesn't exist.
    This ensures that the chat functionality has at least one connection to work with.
    """
    try:
        # Use app context
        with current_app.app_context():
            # First check if the user already has connections
            from src.models import User, DirectConnection
            
            # Get all users
            users = User.query.all()
            
            for user in users:
                # Check if user already has a connection
                connection = DirectConnection.query.filter_by(user_id=user.id).first()
                if not connection:
                    # Create a default OpenAI connection
                    connection = DirectConnection(
                        name="OpenAI (Default)",
                        url="https://api.openai.com/v1/chat/completions",
                        prefix="openai",
                        model_ids="gpt-4o,gpt-4-turbo,gpt-4,gpt-3.5-turbo",
                        embedding_model="text-embedding-ada-002",
                        # api_type parameter removed as it's no longer in the DirectConnection model
                        enabled=True,
                        user_id=user.id
                    )
                    db.session.add(connection)
                    
                    # Also create default LLM Provider Settings
                    provider_setting = LLMProviderSetting.query.filter_by(user_id=user.id).first()
                    if not provider_setting:
                        # Get OpenAI API key from AthenaConfig
                        from src.utils.config import AthenaConfig
                        config = AthenaConfig.load()
                        api_key_value = config.openai_api_key
                        
                        # If not in config, try to get directly from database
                        if not api_key_value:
                            try:
                                # Direct database access as fallback
                                import sqlite3
                                import os
                                from pathlib import Path
                                db_path = Path(current_app.config.get('DATABASE_PATH', 'athena.db'))
                                conn = sqlite3.connect(db_path)
                                cursor = conn.cursor()
                                cursor.execute("SELECT value FROM config_entries WHERE key=? AND user_id IS NULL", ("openai_api_key",))
                                result = cursor.fetchone()
                                if result:
                                    api_key_value = result[0]
                                conn.close()
                            except Exception as e:
                                logger.error(f"Error retrieving API key from database: {e}")
                                api_key_value = ''
                        
                        provider_setting = LLMProviderSetting(
                            provider="openai",
                            secret=api_key_value,  # Add the API key as the secret
                            model="gpt-3.5-turbo",
                            temperature=0.7,
                            max_tokens=2000,
                            user_id=user.id
                        )
                        db.session.add(provider_setting)
                
                db.session.commit()
                logger.info(f"Created default OpenAI connection for user {user.id}")
                
    except Exception as e:
        logger.error(f"Error creating default OpenAI connection: {str(e)}")
        db.session.rollback()
