<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Athena Settings</title>
  
  <!-- Embed critical CSS directly to ensure the UI is always visible -->
  <style>
    /* Essential settings.css styles embedded directly */
    :root {
      --primary-color: #7666f1;
      --background-color: #ffffff;
      --sidebar-color: #f9fafb;
      --text-primary: #111827;
      --text-secondary: #6b7280;
      --border-color: #e5e7eb;
      --hover-color: #f3f4f6;
      --card-bg: #ffffff;
      --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
      --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
      --success-color: #10b981;
      --warning-color: #f59e0b;
      --danger-color: #ef4444;
      --primary-color-light: #8b7ff9;
      --active-nav-bg: #ebeafc;
      --accent-color: #7666f1;
    }
    [data-theme="dark"] {
      --primary-color: #8b7ff9;
      --background-color: #111827;
      --sidebar-color: #1f2937;
      --text-primary: #f9fafb;
      --text-secondary: #9ca3af;
      --border-color: #374151;
      --hover-color: #374151;
      --card-bg: #1f2937;
      --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.5);
      --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.5);
    }
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: var(--background-color);
      color: var(--text-primary);
      opacity: 1; /* Show body immediately */
    }
    .settings-container {
      display: flex;
      width: 100%;
      min-height: 100vh;
    }
    .settings-sidebar {
      width: 250px;
      min-height: 100vh;
      background-color: var(--sidebar-color);
      border-right: 1px solid var(--border-color);
      overflow-y: auto;
      padding: 1rem 0;
    }
    .main-content {
      flex: 1;
      padding: 20px;
      background-color: var(--background-color);
    }
    
    /* Navigation styles */
    .nav-section {
      margin-bottom: 1rem;
      padding: 0 1rem;
    }
    .nav-section-title {
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
      color: var(--text-secondary);
      margin-bottom: 0.5rem;
      padding: 0 0.5rem;
    }
    .nav-item {
      display: flex;
      align-items: center;
      padding: 0.75rem 0.5rem;
      border-radius: 0.25rem;
      margin-bottom: 0.25rem;
      color: var(--text-primary);
      cursor: pointer;
      transition: background-color 0.2s ease;
    }
    .nav-item:hover {
      background-color: var(--hover-color);
    }
    .nav-item.active {
      background-color: var(--active-nav-bg);
      color: var(--primary-color);
    }
    .nav-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 1.5rem;
      height: 1.5rem;
      margin-right: 0.75rem;
      color: var(--text-secondary);
    }
    .nav-item.active .nav-icon {
      color: var(--primary-color);
    }
    
    /* Header */
    .settings-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 1rem;
      border-bottom: 1px solid var(--border-color);
    }
    .settings-logo {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--text-primary);
    }
    
    /* Content area */
    #content-container {
      padding: 1rem;
      transition: opacity 0.3s ease;
    }
    #content-container.loading {
      opacity: 0.5;
    }
    
    /* Loading spinner */
    .loading-spinner {
      display: none;
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 1000;
    }
    .loading-spinner.active {
      display: block;
    }
  </style>
  
  <!-- Multiple CSS loading approaches as fallback -->
  <link rel="stylesheet" href="/static/css/settings.css?v={{ static_version }}&_={{ range(1000, 9999) | random }}" importance="high">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/settings.css') }}?v={{ static_version }}&_={{ range(1000, 9999) | random }}" importance="high">
  <link rel="stylesheet" href="/static/css/devices.css?v={{ static_version }}&_={{ range(1000, 9999) | random }}">
  
  <!-- Add Bootstrap JS for modals and other interactive components -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p" crossorigin="anonymous"></script>
  
  <!-- Critical JavaScript to ensure the page functions even if settings.js fails to load -->
  <script>
    // Define parseSectionId function first since loadSection depends on it
    window.parseSectionId = window.parseSectionId || function(sectionId) {
      console.log('Using inline parseSectionId for:', sectionId);
      let actualSection = sectionId;
      let logsPage = 1;
      
      if (typeof sectionId === 'string' && sectionId.startsWith('logs-page-')) {
        actualSection = 'logs';
        const suffix = sectionId.substring('logs-page-'.length);
        const parsedPage = parseInt(suffix, 10);
        if (!isNaN(parsedPage) && parsedPage > 0) {
          logsPage = parsedPage;
        }
      }
      return { actualSection, logsPage };
    };
    
    // CRITICAL: Define loadSection function directly in HTML to ensure it's always available
    window.loadSection = window.loadSection || async function(sectionId) {
      console.log('Using inline loadSection fallback for section:', sectionId);
      try {
        const contentContainer = document.getElementById('content-container');
        const loadingSpinner = document.querySelector('.loading-spinner');
        
        if (loadingSpinner) loadingSpinner.classList.add('active');
        if (contentContainer) contentContainer.classList.add('loading');
        
        // Use parseSectionId function to parse the section
        const { actualSection, logsPage } = window.parseSectionId(sectionId);
        
        try {
          // Fetch the HTML template for that section
          const response = await fetch(`/settings/${actualSection}`, {
            headers: {
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          });
          
          if (!response.ok) {
            throw new Error(`HTTP error: ${response.status}`);
          }
          
          const html = await response.text();
          
          // Update the content container
          if (contentContainer) {
            contentContainer.innerHTML = html;
            contentContainer.classList.remove('loading');
          }
          
          // Update active state of nav items
          document.querySelectorAll('.nav-item').forEach(item => {
            if (item.dataset.section === actualSection) {
              item.classList.add('active');
            } else {
              item.classList.remove('active');
            }
          });
          
          // Save the last section to localStorage
          localStorage.setItem('lastSection', sectionId);
        } catch (error) {
          console.error('Error fetching section:', error);
          if (contentContainer) {
            contentContainer.innerHTML = `
              <div style="text-align: center; padding: 2rem;">
                <h3>Error loading section</h3>
                <p>${error.message}</p>
                <button onclick="window.loadSection('${sectionId}')">Retry</button>
              </div>
            `;
            contentContainer.classList.remove('loading');
          }
        }
      } catch (error) {
        console.error('Critical error in loadSection:', error);
      } finally {
        const loadingSpinner = document.querySelector('.loading-spinner');
        if (loadingSpinner) loadingSpinner.classList.remove('active');
      }
    };
    
    // Initialize page when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
      console.log('DOM Content Loaded');      
      // Apply theme
      const theme = localStorage.getItem('theme') || 'dark';
      document.documentElement.setAttribute('data-theme', theme);
      
      // Setup nav items click handlers
      document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', function(e) {
          e.preventDefault();
          const section = this.dataset.section;
          window.loadSection(section);
          
          // Update URL
          const url = new URL(window.location);
          url.searchParams.set('section', section);
          window.history.pushState({ section: section }, '', url);
        });
      });
      
      // Setup logout button click handler
      const logoutButton = document.getElementById('logout-button');
      if (logoutButton) {
        logoutButton.addEventListener('click', function(e) {
          e.preventDefault();
          window.location.href = "{{ url_for('auth.logout') }}";
        });
      }
      
      // Load initial section
      const urlParams = new URLSearchParams(window.location.search);
      const section = urlParams.get('section') || localStorage.getItem('lastSection') || 'llm';
      // Force initial section to be 'llm' if not explicitly set to prevent defaulting to broken MCP
      if (!urlParams.get('section') && (!localStorage.getItem('lastSection') || localStorage.getItem('lastSection') === 'mcp')) {
        localStorage.setItem('lastSection', 'llm');
        window.loadSection('llm');
      } else {
        window.loadSection(section);
      }
    });
  </script>
</head>
<body>
  {% include 'partials/emulation_banner.html' %}
  <!-- Settings Sidebar -->
  <div class="settings-container">
    <aside class="settings-sidebar">
      <!-- Athena Logo -->
      <div class="settings-logo-container">
        <img src="/static/img/AthenaLogoHQ.png" alt="Athena Logo" class="settings-logo-img" />
      </div>
      <div class="sidebar-header">
        <button class="back-button" onclick="window.location.href='/'">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M19 12H5M12 19l-7-7 7-7"/>
          </svg>
        </button>
        <h1 class="settings-title">Settings</h1>
      </div>
      <nav class="nav-menu">
        <div class="nav-item" data-section="llm">
          <div class="nav-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
            </svg>
          </div>
          <span data-tooltip="LLM Provider">LLM Provider</span>
        </div>

        <div class="nav-item" data-section="connections">
          <div class="nav-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"></path>
            </svg>
          </div>
          <span data-tooltip="AI Connections">AI Connections</span>
        </div>

        <div class="nav-item" data-section="logs">
          <div class="nav-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <path d="M14 2v6h6M16 13H8M16 17H8M10 9H8"></path>
            </svg>
          </div>
          <span data-tooltip="Inference Logs">Inference Logs</span>
        </div>

        <div class="nav-item" data-section="vector">
          <div class="nav-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
            </svg>
          </div>
          <span data-tooltip="Vector Store">Vector Store</span>
        </div>
        
        <div class="nav-item" data-section="mcp">
          <div class="nav-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
            </svg>
          </div>
          <span data-tooltip="MCP Servers">MCP Servers</span>
        </div>

        <div class="nav-item" data-section="api">
          <div class="nav-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
          </div>
          <span data-tooltip="Developer API">Developer API</span>
        </div>

        <div class="nav-item" data-section="devices">
          <div class="nav-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M16 16v1a2 2 0 01-2 2H4a2 2 0 01-2-2V7a2 2 0 012-2h10a2 2 0 012 2v1"></path>
              <rect x="14" y="8" width="8" height="12" rx="2"></rect>
              <line x1="18" y1="17" x2="18" y2="17"></line>
            </svg>
          </div>
          <span data-tooltip="Device Management">Device Management</span>
        </div>

        <div class="nav-item" data-section="commands">
          <div class="nav-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M9 18v-6h13v6H9zM9 6v4h13V6H9zM9 10h3M9 14h3M3 6h3M3 10h3M3 14h3"></path>
            </svg>
          </div>
          <span data-tooltip="Commands">Commands</span>
        </div>

        <div class="nav-item" data-section="account">
          <div class="nav-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 12c2.21 0 4-1.79 4-4S14.21 4 12 4 8 5.79 8 8s1.79 4 4 4z"></path>
              <path d="M6.32 19a9 9 0 0 1 11.36 0"></path>
            </svg>
          </div>
          <span data-tooltip="Account Settings">Account Settings</span>
        </div>

        {% if current_user.role == 'admin' %}
        <div class="nav-item" data-section="admin">
          <div class="nav-icon">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"/>
              <line x1="12" y1="8" x2="12" y2="12"/>
              <line x1="12" y1="16" x2="12" y2="16"/>
            </svg>
          </div>
          <span data-tooltip="Admin Panel">Admin Panel</span>
        </div>
        
        <div class="nav-item" data-section="debug">
          <div class="nav-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="2" y="4" width="20" height="16" rx="2" />
              <path d="M8 12h8" />
              <path d="M12 8v8" />
              <path d="M2 8h4" />
              <path d="M2 12h2" />
              <path d="M2 16h4" />
              <path d="M18 8h4" />
              <path d="M20 12h2" />
              <path d="M18 16h4" />
            </svg>
          </div>
          <span data-tooltip="Debug Console">Debug Console</span>
        </div>
        {% endif %}
      </nav>

      <!-- Footer with logout button pinned at bottom -->
      <div class="sidebar-footer">
        <button class="logout-button" id="logout-button">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
            <polyline points="16 17 21 12 16 7"/>
            <line x1="21" y1="12" x2="9" y2="12"/>
          </svg>
          <span>Logout</span>
        </button>
      </div>
    </aside>

    <main class="main-content">
      <div class="chat-container">
        <!-- Alerts container for notifications -->
        <div id="alerts-container"></div>
        <div id="content-container"></div>
        <div class="loading-spinner"><div class="spinner"></div></div>
      </div>
    </main>
  </div>
  
  <!-- Include JavaScript files with cache busting -->
  <!-- Add a small script to ensure proper initialization sequence -->
  <script>
    // Global initialization state tracking
    window.athenaInitialized = {
      settings: false,
      connections: false,
      logs: false,
      mcp: false,
      devices: false
    };
    
    // Create a global namespace for Athena
    window.Athena = window.Athena || {};
    
    // Create a function to safely load scripts in sequence
    window.Athena.loadScript = function(src, callback) {
      const script = document.createElement('script');
      script.src = src;
      script.onload = callback;
      script.onerror = function() {
        console.error('Error loading script:', src);
      };
      document.body.appendChild(script);
    };
  </script>
  
  <!-- Load scripts in sequence to ensure proper initialization -->
  <script src="{{ url_for('static', filename='js/settings.js') }}?v={{ static_version }}"></script>
  <script>
    // Then load connections.js
    window.Athena.loadScript("{{ url_for('static', filename='js/connections.js') }}?v={{ static_version }}", function() {
      console.log('Connections.js loaded successfully');
      window.athenaInitialized.connections = true;
      // Then load logs.js
      window.Athena.loadScript("{{ url_for('static', filename='js/logs.js') }}?v={{ static_version }}", function() {
        console.log('Logs.js loaded successfully');
        window.athenaInitialized.logs = true;
        // Then load mcp.js
        window.Athena.loadScript("{{ url_for('static', filename='js/mcp.js') }}?v={{ static_version }}", function() {
          console.log('MCP.js loaded successfully');
          window.athenaInitialized.mcp = true;
        });
      });
    });
  </script>
  <script src="{{ url_for('static', filename='js/settings.js') }}?v={{ static_version }}&_={{ range(1000,9999) | random }}"></script>
</body>
</html>
