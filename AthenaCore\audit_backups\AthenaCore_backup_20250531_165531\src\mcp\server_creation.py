# Athena MCP Server Creation Module

import logging
import os
import json
from typing import Optional, Dict, List, Any, Callable, AsyncIterator, Union
from contextlib import asynccontextmanager
from dataclasses import dataclass

from mcp.server.fastmcp import FastMCP, Context
import mcp.types as mcp_types

from src.models import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.utils.config import AthenaConfig
from src.core.athena import Athena
# Removed because module doesn't exist
# from src.vector_db.vector_db import VectorDB

logger = logging.getLogger(__name__)


class AthenaMCPServerFactory:
    """Factory class for creating and managing MCP servers within Athena."""
    
    def __init__(self, athena_instance: Athena, config: AthenaConfig):
        self.athena = athena_instance
        self.config = config
        self.vector_db = VectorDB(config)
        self.servers: Dict[str, FastMCP] = {}
    
    @asynccontextmanager
    async def athena_lifespan(self, server: FastMCP) -> AsyncIterator[Dict[str, Any]]:
        """Lifecycle manager for Athena MCP servers."""
        # Initialize resources needed by the MCP server
        logger.info(f"Initializing MCP server: {server.name}")
        try:
            # Initialize necessary resources
            yield {
                "athena": self.athena,
                "vector_db": self.vector_db,
                "config": self.config
            }
        finally:
            # Cleanup resources
            logger.info(f"Shutting down MCP server: {server.name}")
    
    def create_server(self, 
                      name: str, 
                      description: str = "", 
                      version: str = "0.1.0",
                      dependencies: Optional[List[str]] = None) -> FastMCP:
        """Create a new MCP server with Athena capabilities."""
        if name in self.servers:
            logger.warning(f"MCP server '{name}' already exists. Returning existing instance.")
            return self.servers[name]
        
        # Create new MCP server
        server = FastMCP(name, 
                         description=description, 
                         version=version,
                         dependencies=dependencies or [],
                         lifespan=self.athena_lifespan)
        
        # Register standard Athena capabilities
        self._register_standard_capabilities(server)
        
        # Store server instance
        self.servers[name] = server
        logger.info(f"Created new MCP server: {name} (v{version})")
        
        return server
    
    def _register_standard_capabilities(self, server: FastMCP) -> None:
        """Register standard Athena capabilities with the MCP server."""
        # Register standard resources
        @server.resource("athena://config")
        def get_athena_config(ctx: Context) -> Dict[str, Any]:
            """Get Athena configuration information."""
            # Return non-sensitive config information
            config_dict = ctx.request_context.lifespan_context["config"].to_dict()
            # Remove sensitive keys
            for key in ["api_key", "secret", "password", "token"]:
                config_dict.pop(key, None)
            return config_dict
        
        # Register standard tools
        @server.tool()
        async def search_web(query: str, ctx: Context) -> str:
            """Search the web for information."""
            athena = ctx.request_context.lifespan_context["athena"]
            # Use Athena's web search capability
            results = await athena.search_web(query)
            return json.dumps(results)
        
        @server.tool()
        async def query_memory(query: str, ctx: Context) -> str:
            """Query Athena's vector database memory."""
            vector_db = ctx.request_context.lifespan_context["vector_db"]
            user_id = 1  # Default to system user
            results = await vector_db.query(query, limit=5, user_id=user_id)
            return json.dumps([{"text": r.text, "metadata": r.metadata} for r in results])
        
        logger.info(f"Registered standard Athena capabilities with MCP server: {server.name}")
        
    def export_server_config(self, server_name: str) -> Dict[str, Any]:
        """Export server configuration for submission to Smithery."""
        if server_name not in self.servers:
            raise ValueError(f"MCP server '{server_name}' not found")
            
        server = self.servers[server_name]
        
        # Build export configuration
        export_config = {
            "name": server.name,
            "description": server.description,
            "version": server.version,
            "dependencies": server.dependencies,
            "tools": [{
                "name": tool.name,
                "description": tool.description,
                "parameters": tool.parameters
            } for tool in server.tools],
            "resources": [{
                "uri_template": resource.uri_template,
                "description": resource.description,
            } for resource in server.resources],
        }
        
        return export_config


def create_default_server(athena_instance: Athena, config: AthenaConfig) -> FastMCP:
    """Create a default Athena MCP server with standard capabilities."""
    factory = AthenaMCPServerFactory(athena_instance, config)
    
    # Create default server
    server = factory.create_server(
        name="Athena Assistant",
        description="An MCP server providing Athena's AI assistant capabilities",
        version="1.0.0",
        dependencies=["requests", "beautifulsoup4", "openai"]
    )
    
    # Add custom capabilities beyond the standard ones
    @server.tool()
    async def execute_command(command: str, ctx: Context) -> str:
        """Execute a system command and return the result."""
        athena = ctx.request_context.lifespan_context["athena"]
        # This should implement proper security checks
        result = await athena.execute_command(command)
        return result
    
    @server.tool()
    async def chat_with_model(prompt: str, ctx: Context, model: str = "default") -> str:
        """Send a chat message to the specified AI model."""
        athena = ctx.request_context.lifespan_context["athena"]
        response = await athena.chat(prompt, model=model)
        return response
    
    return server
