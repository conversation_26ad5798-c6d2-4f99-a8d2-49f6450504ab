# src/utils/config.py
# This file manages Athena's configuration settings.
#
# It includes:
# - Default settings for AI model usage, conversation limits, and logging.
# - Security settings, including system command permissions and API restrictions.
# - Database and session management configurations.
# - Methods to load and save configuration data from a JSON file.
#
# The configuration is designed to be customizable, allowing adjustments
# via a `config.json` file while providing default values for ease of use.

from dataclasses import dataclass
import json
import os
from pathlib import Path
from flask import current_app
from flask_login import current_user
from src.models import Configuration, db, LLMProviderSetting


@dataclass
class AthenaConfig:
    """Configuration settings for Athena."""

    # OpenAI settings
    openai_model: str = "gpt-4o"  # Model to use for API calls
    max_tokens: int = 4096  # Maximum tokens per API response

    # Controls randomness in responses (0.0-1.0)
    temperature: float = 0.7
    # API keys - loaded from database only, no environment variable fallbacks
    openai_api_key: str = ""

    # Anthropic API key
    anthropic_api_key: str = ""

    # Google API key
    google_api_key: str = ""

    # Azure OpenAI settings
    azure_openai_api_key: str = ""
    azure_openai_endpoint: str = ""

    # MCP (Model Context Protocol) settings
    smithery_api_key: str = ""  # API key for Smithery Registry - loaded from database only
    enable_mcp: bool = True  # Whether to enable MCP integration
    mcp_registry_url: str = "https://registry.smithery.ai"  # Smithery Registry API URL

    # Conversation settings
    # Maximum number of messages to keep in history
    max_history: int = 10
    # Maximum time to wait for response (seconds)
    max_response_time: int = 30

    # Logging settings
    log_dir: str = "logs"  # Directory for storing log files
    enable_debug_logging: bool = True  # Enable detailed debug logging

    # Security settings
    # Whether to allow system command execution
    allow_system_commands: bool = True
    # List of allowed Python modules (None = all)
    allowed_python_modules: list = None

    # Database settings (SQL)
    # Use absolute path for database to avoid path resolution issues
    db_uri: str = f"sqlite:///{Path(__file__).resolve().parents[2] / 'instance' / 'athena.db'}"  # Database URI for SQLAlchemy

    # Session and Security
    secret_key: str = "change-this-in-production"  # Secret key for Flask sessions
    allow_registration: bool = True  # Allow new users to register
    min_password_length: int = 8  # Minimum password length for registration
    session_lifetime: int = 86400  # Session lifetime in seconds

    # Vector Database (Chroma) settings
    CHROMA_PERSIST_DIR: str = "chroma_db"  # Directory to persist the vector DB
    CHROMA_SERVER_HOST: str = "localhost"  # Host for REST-based Chroma (if used)
    CHROMA_SERVER_HTTP_PORT: int = 8000  # Port for REST-based Chroma (if used)
    CHROMA_API_IMPL: str = "persistent"  # Either "persistent" or "rest"
    EMBEDDING_MODEL: str = "text-embedding-ada-002"  # Model for computing embeddings
    CLIP_MODEL_NAME: str = "openai/clip-vit-base-patch32" # Model for image embeddings (CLIP)

    # Cross-Device API System settings
    attachment_storage_path: str = "attachments"  # Path to store file attachments
    max_attachment_size: int = 50 * 1024 * 1024  # Maximum attachment size (50MB)
    allowed_attachment_types: list = None  # List of allowed file extensions (None = use default)

    # Background Task settings
    max_concurrent_tasks: int = 5  # Maximum number of concurrent background tasks
    default_task_priority: int = 5  # Default priority for background tasks (1-10)
    default_task_runtime: int = 3600  # Default maximum runtime in seconds (1 hour)

    # Task Scheduler settings
    scheduler_check_interval: int = 60  # How often to check for scheduled tasks (seconds)
    enable_scheduler: bool = True  # Whether to enable the task scheduler
    max_scheduled_tasks_per_user: int = 50  # Maximum number of scheduled tasks per user

    @classmethod
    def load(cls, config_path: str = "config.json") -> "AthenaConfig":
        """
        Load configuration from a JSON file.

        Args:
            config_path (str): Path to the configuration file

        Returns:
            AthenaConfig: Configuration instance
        """
        config = cls()

        # Initialize logging for more detailed diagnostics
        import logging
        logger = logging.getLogger('AthenaConfig')
        
        # Setup for robust database access
        app_context_exists = False
        db_accessed = False
        
        # Approach 1: Load from database using existing Flask app context
        try:
            from flask import current_app
            # Check if we have an application context already
            if current_app:
                app_context_exists = True
                logger.debug("Using existing Flask application context")
                
                # Load global entries first
                for entry in Configuration.query.all():
                    if hasattr(config, entry.key):
                        try:
                            val = json.loads(entry.value)
                        except Exception:
                            val = entry.value
                        setattr(config, entry.key, val)
                        logger.debug(f"Loaded config from DB: {entry.key}")

                # Note: Configuration table doesn't have user_id, so no per-user settings here
                
                db_accessed = True
        except Exception as e:
            logger.debug(f"Could not use existing app context: {e}")
        
        # Approach 2: Create a temporary Flask app to access the database if needed
        if not db_accessed:
            try:
                # Only import Flask and create an app if we need to
                from flask import Flask, current_app
                from src.models import Configuration, db
                
                # Create a bare-minimum Flask app for database access
                import os
                from pathlib import Path
                
                # Determine database path
                base_dir = Path(__file__).resolve().parents[2]
                # Use the instance folder for database
                instance_dir = base_dir / "instance"
                instance_dir.mkdir(exist_ok=True)  # Ensure instance folder exists
                db_path = f"sqlite:///{instance_dir}/athena.db"
                
                logger.debug(f"Creating temporary Flask app with database at {db_path}")
                app = Flask("temp_config_app")
                app.config["SQLALCHEMY_DATABASE_URI"] = db_path
                app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False
                db.init_app(app)
                
                # Use the temporary app context to access the database
                with app.app_context():
                    # Load global entries
                    for entry in Configuration.query.all():
                        if hasattr(config, entry.key):
                            try:
                                val = json.loads(entry.value)
                            except Exception:
                                val = entry.value
                            setattr(config, entry.key, val)
                            logger.debug(f"Loaded config from DB via temp app: {entry.key}")
                            
                db_accessed = True
                logger.debug("Successfully loaded config from database using temporary app")
            except Exception as e:
                logger.debug(f"Failed to create temporary app context: {e}")
                # Continue to fallback methods if this fails

        # Configuration is now fully database-driven - no environment variable fallbacks
        # All API keys must be configured through the web interface and stored in the database
        
        # Override EMBEDDING_MODEL from per-user LLM settings
        try:
            # Check if we're in a Flask app context
            from flask import current_app
            from flask_login import current_user
            
            if getattr(current_user, 'is_authenticated', False):
                # Import here to avoid circular imports
                from src.models import LLMProviderSetting
                
                user_setting = LLMProviderSetting.query.filter_by(user_id=current_user.id).first()
                if user_setting and getattr(user_setting, 'embedding_model', None):
                    config.EMBEDDING_MODEL = user_setting.embedding_model
        except (ImportError, RuntimeError):
            # We're outside Flask application context, skip this override
            pass
        except Exception as e:
            # Only log the error, don't print it
            import logging
            logger = logging.getLogger('AthenaConfig')
            logger.debug(f"Skipping EMBEDDING_MODEL override: {e}")

        return config

    def save(self, config_path: str = "config.json") -> None:
        """
        Save configuration to a JSON file.

        Args:
            config_path (str): Path to save the configuration
        """
        config_data = {key: getattr(self, key) for key in self.__annotations__}

        # File-based config save disabled; settings persisted in DB only

        # Persist global config entries in DB
        try:
            with current_app.app_context():
                for key, value in config_data.items():
                    val_str = json.dumps(value) if not isinstance(value, str) else value
                    entry = Configuration.query.filter_by(key=key).first()
                    if entry:
                        entry.value = val_str
                    else:
                        entry = Configuration(key=key, value=val_str, value_type="string")
                        db.session.add(entry)
                db.session.commit()
        except Exception as e:
            print(f"Error saving config to DB: {e}")
