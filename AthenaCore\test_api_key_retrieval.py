#!/usr/bin/env python3
"""
Test script to verify API key retrieval from DirectConnections.
"""

import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

def test_direct_database_access():
    """Test direct database access to check API keys."""
    print("=== Testing Direct Database Access ===")
    
    try:
        import sqlite3
        
        db_path = project_root / "instance" / "athena.db"
        if not db_path.exists():
            print(f"❌ Database not found at {db_path}")
            return False
            
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Check DirectConnections
        cursor.execute("""
            SELECT id, name, url, api_key, enabled, user_id 
            FROM direct_connections 
            WHERE enabled=1 AND api_key IS NOT NULL AND api_key != ''
        """)
        connections = cursor.fetchall()
        
        print(f"Found {len(connections)} enabled connections with API keys:")
        for conn_data in connections:
            id, name, url, api_key, enabled, user_id = conn_data
            print(f"  ID: {id}, Name: {name}, User: {user_id}, Has API Key: {'Yes' if api_key else 'No'}")
        
        conn.close()
        return len(connections) > 0
        
    except Exception as e:
        print(f"❌ Error accessing database: {e}")
        return False

def test_athena_api_key_retrieval():
    """Test the Athena class API key retrieval."""
    print("\n=== Testing Athena API Key Retrieval ===")
    
    try:
        # Import Flask app to get app context
        from src.app_factory import create_app
        from src.core.athena import Athena
        
        app = create_app()
        
        with app.app_context():
            # Create Athena instance
            athena = Athena()
            
            # Test API key retrieval for user 1 (Admin)
            api_key, connection_url = athena._get_api_key_for_model(model="gpt-4o", user_id=1)
            
            if api_key:
                print(f"✅ Successfully retrieved API key for user 1")
                print(f"   Connection URL: {connection_url}")
                print(f"   API Key: {api_key[:10]}...{api_key[-4:] if len(api_key) > 14 else api_key}")
                return True
            else:
                print("❌ No API key found for user 1")
                return False
                
    except Exception as e:
        print(f"❌ Error testing Athena API key retrieval: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_flask_context_retrieval():
    """Test API key retrieval within Flask context (simulating a real request)."""
    print("\n=== Testing Flask Context API Key Retrieval ===")
    
    try:
        from src.app_factory import create_app
        from src.core.athena import Athena
        from src.models.user import User
        from flask_login import login_user
        
        app = create_app()
        
        with app.app_context():
            # Get user 1 (Admin)
            user = User.query.get(1)
            if not user:
                print("❌ User 1 not found in database")
                return False
            
            # Create test request context
            with app.test_request_context():
                # Simulate user login
                login_user(user)
                
                # Create Athena instance
                athena = Athena()
                
                # Test API key retrieval (should use current_user context)
                api_key, connection_url = athena._get_api_key_for_model(model="gpt-4o")
                
                if api_key:
                    print(f"✅ Successfully retrieved API key in Flask context")
                    print(f"   Connection URL: {connection_url}")
                    print(f"   API Key: {api_key[:10]}...{api_key[-4:] if len(api_key) > 14 else api_key}")
                    return True
                else:
                    print("❌ No API key found in Flask context")
                    return False
                    
    except Exception as e:
        print(f"❌ Error testing Flask context retrieval: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing API Key Retrieval System")
    print("=" * 50)
    
    # Run tests
    test1_passed = test_direct_database_access()
    test2_passed = test_athena_api_key_retrieval()
    test3_passed = test_flask_context_retrieval()
    
    print("\n" + "=" * 50)
    print("Test Results:")
    print(f"  Direct Database Access: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"  Athena API Key Retrieval: {'✅ PASS' if test2_passed else '❌ FAIL'}")
    print(f"  Flask Context Retrieval: {'✅ PASS' if test3_passed else '❌ FAIL'}")
    
    if all([test1_passed, test2_passed, test3_passed]):
        print("\n🎉 All tests passed! API key retrieval system is working correctly.")
        sys.exit(0)
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
        sys.exit(1)
