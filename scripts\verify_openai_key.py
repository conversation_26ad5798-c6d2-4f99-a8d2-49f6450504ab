#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
verify_openai_key.py - A diagnostic script to verify OpenAI API key functionality
"""

import sys
import os
import requests
import json
import traceback
from datetime import datetime

# For colored output
try:
    from colorama import init, Fore, Style
    init()  # Initialize colorama
    COLOR_ENABLED = True
except ImportError:
    # Define dummy color variables if colorama is not available
    class DummyColors:
        def __getattr__(self, name):
            return ""
    Fore = DummyColors()
    Style = DummyColors()
    COLOR_ENABLED = False

def log(message, level="INFO"):
    """Log a message with timestamp and color."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    if level == "INFO":
        color = Fore.GREEN if COLOR_ENABLED else ""
    elif level == "WARNING":
        color = Fore.YELLOW if COLOR_ENABLED else ""
    elif level == "ERROR":
        color = Fore.RED if COLOR_ENABLED else ""
    else:
        color = Fore.WHITE if COLOR_ENABLED else ""
    
    reset = Style.RESET_ALL if COLOR_ENABLED else ""
    print(f"{color}[{timestamp}] {level}: {message}{reset}")

def mask_api_key(key):
    """Mask the API key for secure display."""
    if not key:
        return "None"
    return key[:4] + "***" + key[-4:] if len(key) > 8 else "***"

def validate_api_key_format(key):
    """Validate that the API key has the correct format (starts with 'sk-')."""
    if not key:
        return False
    return key.startswith("sk-")

def test_api_key(api_key):
    """Test the API key by making a simple request to the OpenAI API."""
    if not api_key:
        log("No API key provided", "ERROR")
        return False
    
    log(f"Testing API key {mask_api_key(api_key)}...")
    
    # Validate format
    if not validate_api_key_format(api_key):
        log(f"API key format is invalid. Should start with 'sk-'", "ERROR")
        return False
    
    # Setup API request
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # Use a tiny input for testing
    payload = {
        "input": "Hello, world",
        "model": "text-embedding-ada-002"
    }
    
    # Make API request
    try:
        log("Making test request to OpenAI embeddings API...")
        response = requests.post(
            "https://api.openai.com/v1/embeddings",
            headers=headers,
            data=json.dumps(payload),
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            log(f"API request successful! Received embedding of length {len(data['data'][0]['embedding'])}")
            return True
        else:
            log(f"API request failed with status code {response.status_code}: {response.text}", "ERROR")
            return False
    except Exception as e:
        log(f"Exception during API request: {str(e)}", "ERROR")
        log(traceback.format_exc(), "ERROR")
        return False

def get_api_key_from_environment():
    """Get the OpenAI API key from environment variables."""
    key = os.environ.get("OPENAI_API_KEY")
    if key:
        log(f"Found API key in environment variable: {mask_api_key(key)}")
        return key
    return None

def get_api_key_from_config():
    """Get the API key from the AthenaConfig."""
    try:
        # Try to import from the Athena project
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from src.utils.config import AthenaConfig
        
        cfg = AthenaConfig.load()
        if hasattr(cfg, "openai_api_key") and cfg.openai_api_key:
            log(f"Found API key in AthenaConfig: {mask_api_key(cfg.openai_api_key)}")
            return cfg.openai_api_key
    except Exception as e:
        log(f"Error accessing AthenaConfig: {str(e)}", "WARNING")
    
    return None

def get_api_key_from_db():
    """Get the API key from the DirectConnection database model."""
    try:
        # Try to get from the database
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # We need the Flask app context to access the database
        from src.app import create_app
        app = create_app()
        
        with app.app_context():
            from src.login.models import DirectConnection
            dc = DirectConnection.query.filter_by(enabled=True).first()
            if dc and dc.api_key:
                log(f"Found API key in DirectConnection (name={dc.name}): {mask_api_key(dc.api_key)}")
                log(f"DirectConnection URL: {dc.url}")
                return dc.api_key
            else:
                if dc:
                    log("DirectConnection found but has no API key", "WARNING")
                else:
                    log("No enabled DirectConnection found in database", "WARNING")
                    
    except Exception as e:
        log(f"Error accessing DirectConnection: {str(e)}", "WARNING")
        log(traceback.format_exc(), "WARNING")
    
    return None

def main():
    """Main function to verify API key validity"""
    log("OpenAI API Key Verification Tool", "INFO")
    log("=" * 50)
    
    # Check for command line argument first
    import sys
    manual_key = None
    if len(sys.argv) > 1:
        manual_key = sys.argv[1].strip()
        log(f"Using API key provided as command line argument: {mask_api_key(manual_key)}", "INFO")
    
    # Try from each source in priority order
    key = manual_key or get_api_key_from_config() or get_api_key_from_db() or get_api_key_from_environment()
    
    if not key:
        log("No API key found in any source", "ERROR")
        log("Please set your OpenAI API key in one of the following places:")
        log("1. Environment variable: OPENAI_API_KEY")
        log("2. AthenaConfig in the database")
        log("3. DirectConnection in the database")
        return False
    
    # Test the API key
    success = test_api_key(key)
    
    if success:
        log("✅ API key is valid and working correctly!", "INFO")
        return True
    else:
        log("❌ API key verification failed.", "ERROR")
        log("Please check your API key and try again.", "ERROR")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            sys.exit(0)
        else:
            sys.exit(1)
    except KeyboardInterrupt:
        log("Operation cancelled by user", "WARNING")
        sys.exit(130)
    except Exception as e:
        log(f"Unhandled exception: {str(e)}", "ERROR")
        log(traceback.format_exc(), "ERROR")
        sys.exit(1)
