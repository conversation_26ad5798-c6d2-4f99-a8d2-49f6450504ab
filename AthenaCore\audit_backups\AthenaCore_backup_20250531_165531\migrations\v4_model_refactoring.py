"""
Database migration for model refactoring.

This migration script ensures that all tables required by our refactored models
are properly created in the database. It serves as a consolidation point for
all the model changes made during the refactoring process.

Migration version: 4
Description: Consolidate model schema changes after refactoring
Dependencies: ['v3_add_authentication_tables']
"""

from alembic import op
import sqlalchemy as sa
import datetime
import uuid

# We'll use dynamic imports at runtime to avoid import errors
# during migration discovery

# Define the models we expect to exist after refactoring
REFACTORED_MODELS = [
    'User', 'UserLog', 'APIKey', 'Task', 'Configuration',
    'LLMProviderSetting', 'CommandToggle', 'DirectConnection',
    'MCPApiKey', 'MCPServerTemplate', 'Conversation', 'Message', 'LogEntry'
]

def upgrade(connection=None):
    """Apply the migration to create or modify any missing tables."""
    import sys
    import os
    
    # Add the project root to the path to enable imports
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    # Now we can import our models dynamically
    try:
        from src.models import db
        
        # Import each model to ensure tables exist
        for model_name in REFACTORED_MODELS:
            try:
                exec(f"from src.models import {model_name}")
                print(f"Verified model: {model_name}")
            except ImportError as e:
                print(f"Warning: Could not import model {model_name}: {e}")
        
        # Create any missing tables
        db.create_all()
        
        print("Model refactoring migration applied successfully.")
        print("All refactored models have been properly registered with the database.")
    except ImportError as e:
        print(f"Warning: Could not import models: {e}")
        print("Migration will continue but model verification was skipped.")
        print("Please run the application to ensure all models are properly created.")
        
    # This migration primarily serves as documentation that all models have been refactored
    # The actual table creation is handled by SQLAlchemy when the app runs

def downgrade(connection=None):
    """Revert the migration."""
    # This is a consolidation migration, so downgrading would involve
    # reverting to the original models, which would be complex and risky.
    # It's safer to handle downgrades through database backups.
    
    print("WARNING: Downgrade not supported for model refactoring migration.")
    print("Please restore from a database backup if needed.")
