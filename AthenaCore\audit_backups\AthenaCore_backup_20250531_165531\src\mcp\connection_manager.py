import asyncio
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional

import websockets

from .smithery_client import SmitheryClient


logger = logging.getLogger(__name__)


class MCPConnection:
    """Represents a connection to an external MCP server."""
    
    def __init__(self, qualified_name: str, display_name: str, connection_id: str, config: Dict[str, Any] = None):
        """Initialize a new MCP connection.
        
        Args:
            qualified_name: The qualified name of the MCP server
            display_name: The display name for the MCP server
            connection_id: Unique ID for this connection
            config: Configuration data for the connection
        """
        self.qualified_name = qualified_name
        self.display_name = display_name
        self.connection_id = connection_id
        self.config = config or {}
        self.created_at = datetime.now().isoformat()
        self.connection = None
        self.status = "initialized"
        
    async def connect(self, websocket_url: str):
        """Establish a WebSocket connection to the MCP server.
        
        Args:
            websocket_url: WebSocket URL for the MCP server
            
        Returns:
            True if connection was successful, False otherwise
        """
        try:
            self.connection = await websockets.connect(websocket_url)
            self.status = "connected"
            return True
        except Exception as e:
            logger.error(f"Failed to connect to MCP server {self.qualified_name}: {str(e)}")
            self.status = "failed"
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert connection to dictionary representation."""
        return {
            "id": self.connection_id,
            "qualified_name": self.qualified_name,
            "display_name": self.display_name,
            "status": self.status,
            "created_at": self.created_at
        }


class MCPConnectionManager:
    """Manages connections to external MCP servers."""
    
    def __init__(self, smithery_client: SmitheryClient):
        """Initialize the connection manager.
        
        Args:
            smithery_client: SmitheryClient instance for Smithery Registry API access
        """
        self.smithery_client = smithery_client
        self.connections = {}
    
    async def create_connection(self, qualified_name: str, config: Dict[str, Any] = None) -> Optional[MCPConnection]:
        """Create a new connection to an MCP server.
        
        Args:
            qualified_name: The qualified name of the MCP server
            config: Configuration data for the connection
            
        Returns:
            MCPConnection instance if successful, None otherwise
        """
        try:
            # Get server details from Smithery Registry
            server_info = self.smithery_client.get_server(qualified_name)
            if not server_info:
                logger.error(f"Server not found: {qualified_name}")
                return None
            
            # Create connection ID and format display name
            connection_id = str(uuid.uuid4())
            display_name = self.smithery_client.format_display_name(qualified_name)
            
            # Initialize connection object
            connection = MCPConnection(
                qualified_name=qualified_name,
                display_name=display_name,
                connection_id=connection_id,
                config=config
            )
            
            # Create WebSocket URL
            deployment_url = server_info.get("deploymentUrl")
            if not deployment_url:
                logger.error(f"No deployment URL for server: {qualified_name}")
                return None
                
            websocket_url = self.smithery_client.create_websocket_url(deployment_url, config or {})
            
            # Connect to the server
            success = await connection.connect(websocket_url)
            if not success:
                return None
                
            # Store connection
            self.connections[connection_id] = connection
            return connection
            
        except Exception as e:
            logger.error(f"Failed to create connection to {qualified_name}: {str(e)}")
            return None
    
    def get_connection(self, connection_id: str) -> Optional[MCPConnection]:
        """Get a connection by ID."""
        return self.connections.get(connection_id)
    
    def list_connections(self) -> List[Dict[str, Any]]:
        """Get list of all active connections."""
        return [conn.to_dict() for conn in self.connections.values()]
    
    def close_connection(self, connection_id: str) -> bool:
        """Close and remove a connection."""
        if connection_id not in self.connections:
            return False
            
        connection = self.connections[connection_id]
        if connection.connection:
            asyncio.create_task(connection.connection.close())
            
        del self.connections[connection_id]
        return True
