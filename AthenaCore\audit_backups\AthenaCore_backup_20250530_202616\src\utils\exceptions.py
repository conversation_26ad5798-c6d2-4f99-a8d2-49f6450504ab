"""
Custom exceptions for Athena Core.

This module defines application-specific exceptions that can be raised and
handled consistently throughout the application.
"""

from typing import Dict, List, Optional, Any


class AthenaException(Exception):
    """Base exception for all Athena-specific exceptions."""
    
    def __init__(
        self, 
        message: str = "An error occurred",
        error_code: str = "unknown_error",
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the exception.
        
        Args:
            message: Human-readable error message
            error_code: Machine-readable error code
            status_code: HTTP status code to return
            details: Additional error details
        """
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(AthenaException):
    """Exception raised for validation errors."""
    
    def __init__(
        self, 
        errors: Dict[str, List[str]],
        message: str = "Validation failed",
        error_code: str = "validation_error",
        status_code: int = 422
    ):
        """
        Initialize the validation error.
        
        Args:
            errors: Dictionary mapping field names to lists of error messages
            message: Human-readable error message
            error_code: Machine-readable error code
            status_code: HTTP status code to return
        """
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status_code,
            details={"validation_errors": errors}
        )
        self.errors = errors


class AuthenticationError(AthenaException):
    """Exception raised for authentication errors."""
    
    def __init__(
        self, 
        message: str = "Authentication failed",
        error_code: str = "authentication_error",
        status_code: int = 401,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the authentication error.
        
        Args:
            message: Human-readable error message
            error_code: Machine-readable error code
            status_code: HTTP status code to return
            details: Additional error details
        """
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status_code,
            details=details
        )


class AuthorizationError(AthenaException):
    """Exception raised for authorization errors."""
    
    def __init__(
        self, 
        message: str = "You do not have permission to perform this action",
        error_code: str = "authorization_error",
        status_code: int = 403,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the authorization error.
        
        Args:
            message: Human-readable error message
            error_code: Machine-readable error code
            status_code: HTTP status code to return
            details: Additional error details
        """
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status_code,
            details=details
        )


class ResourceNotFoundError(AthenaException):
    """Exception raised when a requested resource is not found."""
    
    def __init__(
        self, 
        resource_type: str,
        resource_id: Any,
        message: Optional[str] = None,
        error_code: str = "resource_not_found",
        status_code: int = 404
    ):
        """
        Initialize the resource not found error.
        
        Args:
            resource_type: Type of resource that was not found (e.g., "User", "Task")
            resource_id: ID of the resource that was not found
            message: Human-readable error message
            error_code: Machine-readable error code
            status_code: HTTP status code to return
        """
        if message is None:
            message = f"{resource_type} with ID {resource_id} not found"
            
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status_code,
            details={
                "resource_type": resource_type,
                "resource_id": resource_id
            }
        )


class ConfigurationError(AthenaException):
    """Exception raised for configuration errors."""
    
    def __init__(
        self, 
        message: str = "Configuration error occurred",
        error_code: str = "configuration_error",
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the configuration error.
        
        Args:
            message: Human-readable error message
            error_code: Machine-readable error code
            status_code: HTTP status code to return
            details: Additional error details
        """
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status_code,
            details=details
        )


class ServiceError(AthenaException):
    """Exception raised for service-level errors."""
    
    def __init__(
        self, 
        service_name: str,
        operation: str,
        message: Optional[str] = None,
        error_code: str = "service_error",
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the service error.
        
        Args:
            service_name: Name of the service where the error occurred
            operation: Name of the operation that failed
            message: Human-readable error message
            error_code: Machine-readable error code
            status_code: HTTP status code to return
            details: Additional error details
        """
        if message is None:
            message = f"Error in {service_name} service during {operation} operation"
            
        error_details = {
            "service": service_name,
            "operation": operation
        }
        
        if details:
            error_details.update(details)
            
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status_code,
            details=error_details
        )


class DatabaseError(AthenaException):
    """Exception raised for database errors."""
    
    def __init__(
        self, 
        operation: str,
        message: Optional[str] = None,
        error_code: str = "database_error",
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the database error.
        
        Args:
            operation: Database operation that failed
            message: Human-readable error message
            error_code: Machine-readable error code
            status_code: HTTP status code to return
            details: Additional error details
        """
        if message is None:
            message = f"Database error during {operation} operation"
            
        error_details = {"operation": operation}
        
        if details:
            error_details.update(details)
            
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status_code,
            details=error_details
        )


class PermissionDeniedError(AuthorizationError):
    """Exception raised when a user does not have permission to perform an action."""
    
    def __init__(
        self, 
        message: str = "You do not have permission to perform this action",
        error_code: str = "permission_denied",
        status_code: int = 403,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the permission denied error.
        
        Args:
            message: Human-readable error message
            error_code: Machine-readable error code
            status_code: HTTP status code to return
            details: Additional error details
        """
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status_code,
            details=details
        )
