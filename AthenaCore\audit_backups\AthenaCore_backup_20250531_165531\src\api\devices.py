# src/api/devices.py

"""
API endpoints for device management in the Cross-Device API System.

This file implements:
- Device registration and authentication
- Device capability discovery and management 
- Device status/heartbeat updates
- Device listing and querying
"""

import json
import logging
import uuid
from datetime import datetime, timedelta

from flask import Blueprint, jsonify, request, current_app
from flask_login import current_user, login_required
from functools import wraps

from src.models import db, User
# Import device models directly instead of using the deprecated module
from src.models.device import Device, DeviceCapability, create_device

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger("devices_api")

# Create blueprint
devices_bp = Blueprint("devices", __name__, url_prefix="/api/devices")

# Helper Functions

def get_device_or_404(device_uuid, user_id=None):
    """
    Get a device by UUID with optional user ownership check.
    
    Args:
        device_uuid (str): The UUID of the device
        user_id (int, optional): User ID to check ownership against
        
    Returns:
        Device object or 404 error
    """
    query = Device.query.filter_by(device_uuid=device_uuid)
    
    if user_id is not None:
        query = query.filter_by(user_id=user_id)
        
    device = query.first_or_404(description=f"Device with UUID {device_uuid} not found")
    return device


def require_device_auth(f):
    """
    Decorator to require device authentication.
    This checks for a valid device token in the authorization header.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Allow access if user is already authenticated through Flask-Login
        if (
            current_user
            and hasattr(current_user, "is_authenticated")
            and current_user.is_authenticated
        ):
            # If device_uuid is in kwargs, verify ownership
            if "device_uuid" in kwargs:
                device = get_device_or_404(kwargs["device_uuid"], current_user.id)
                # Add device to kwargs for convenience
                kwargs["device"] = device
            
            return f(*args, **kwargs)
            
        # TODO: For API clients, implement JWT token verification
        # For now, just return 401
        return jsonify({"error": "Device authentication required"}), 401
        
    return decorated_function


# API Endpoints

@devices_bp.route("", methods=["GET"])
@login_required
def list_devices():
    """
    Get a list of the user's registered devices.
    
    Query Parameters:
        active_only (bool): If true, only return active devices (default: true)
        type (str): Filter by device type (e.g., "desktop", "mobile", "web")
        
    Returns:
        JSON response with list of devices
    """
    active_only = request.args.get("active_only", "true").lower() == "true"
    device_type = request.args.get("type")
    
    query = Device.query.filter_by(user_id=current_user.id)
    
    if active_only:
        query = query.filter_by(is_active=True)
        
    if device_type:
        query = query.filter_by(device_type=device_type)
    
    # Order by most recently active
    query = query.order_by(Device.last_active.desc())
        
    devices = query.all()
    
    return jsonify({
        "devices": [device.to_dict() for device in devices],
        "count": len(devices)
    })


@devices_bp.route("", methods=["POST"])
@login_required
def register_device():
    """
    Register a new device.
    
    Request Body:
        name (str): Name for the device
        device_type (str): Type of device (desktop, mobile, web)
        os_info (str, optional): Operating system information
        capabilities (list, optional): List of device capabilities
        
    Returns:
        JSON response with the registered device including its UUID
    """
    logger.info(f"Device registration request received from user: {current_user.id}")
    
    try:
        # Get and log the raw request data
        raw_data = request.get_data(as_text=True)
        logger.info(f"Raw request data: {raw_data}")
        
        # Parse JSON data
        data = request.json or {}
        logger.info(f"Parsed request data: {data}")
        
        # Validate required fields
        required_fields = ["name", "device_type"]
        missing_fields = [field for field in required_fields if field not in data]
        
        if missing_fields:
            error_msg = f"Missing required fields: {', '.join(missing_fields)}"
            logger.error(error_msg)
            return jsonify({"error": error_msg}), 400
        
        # Create device
        device = create_device(
            user_id=current_user.id,
            name=data["name"],
            device_type=data["device_type"],
            os_info=data.get("os_info")
        )
        
        # Add capabilities if provided
        if "capabilities" in data and isinstance(data["capabilities"], list):
            for cap_data in data["capabilities"]:
                # Extract capability info
                if not isinstance(cap_data, dict) or "capability_name" not in cap_data:
                    logger.warning(f"Invalid capability data: {cap_data}")
                    continue
                
                capability_name = cap_data["capability_name"]
                
                # Extract parameters
                parameters = cap_data.get("parameters", {})
                if isinstance(parameters, dict):
                    parameters = json.dumps(parameters)
                
                # Create the capability
                capability = DeviceCapability(
                    device_id=device.id,
                    capability_name=capability_name,
                    capability_description=cap_data.get("description", ""),
                    parameters=parameters,
                    version=cap_data.get("version", "1.0")
                )
                
                db.session.add(capability)
                logger.info(f"Added capability {capability_name} to device {device.device_uuid}")
        
        # Commit to database
        db.session.commit()
        
        logger.info(f"Device '{data['name']}' registered successfully with UUID: {device.device_uuid}")
        
        # Return device info
        return jsonify({
            "device": device.to_dict(),
            "message": "Device registered successfully"
        }), 201
        
    except Exception as e:
        # Log the error and rollback any database changes
        logger.error(f"Error registering device: {str(e)}")
        db.session.rollback()
        return jsonify({"error": f"Failed to register device: {str(e)}"}), 500


@devices_bp.route("/<string:device_uuid>", methods=["GET"])
@require_device_auth
def get_device(device_uuid, device=None):
    """
    Get details for a specific device.
    
    Path Parameters:
        device_uuid (str): UUID of the device to retrieve
        
    Returns:
        JSON response with device details
    """
    # The require_device_auth decorator handles getting the device
    # and adds it to kwargs if it exists
    if not device:
        device = get_device_or_404(device_uuid, current_user.id)
    
    # Include capabilities with full details
    return jsonify({
        "device": device.to_dict()
    })


@devices_bp.route("/<string:device_uuid>", methods=["PUT", "PATCH"])
@require_device_auth
def update_device(device_uuid, device=None):
    """
    Update a device's information.
    
    Path Parameters:
        device_uuid (str): UUID of the device to update
        
    Request Body:
        name (str, optional): New name for the device
        is_active (bool, optional): Whether the device is active
        
    Returns:
        JSON response with the updated device
    """
    if not device:
        device = get_device_or_404(device_uuid, current_user.id)
        
    data = request.json or {}
    
    # Update name if provided
    if "name" in data:
        device.name = data["name"]
        
    # Update active status if provided
    if "is_active" in data:
        device.is_active = bool(data["is_active"])
    
    device.last_active = datetime.utcnow()
    db.session.commit()
    
    logger.info(f"Updated device {device_uuid} for user {current_user.id}")
    
    return jsonify({
        "device": device.to_dict(),
        "message": "Device updated successfully"
    })


@devices_bp.route("/<string:device_uuid>/heartbeat", methods=["POST"])
@require_device_auth
def device_heartbeat(device_uuid, device=None):
    """
    Update device's last active timestamp.
    
    Path Parameters:
        device_uuid (str): UUID of the device sending the heartbeat
        
    Request Body:
        capabilities (list, optional): Updated list of capabilities
        status (str, optional): Current device status
        
    Returns:
        JSON response confirming the heartbeat
    """
    if not device:
        device = get_device_or_404(device_uuid, current_user.id)
        
    data = request.json or {}
    
    # Update last active timestamp
    device.last_active = datetime.utcnow()
    
    # Update capabilities if provided
    if "capabilities" in data and isinstance(data["capabilities"], list):
        # First, determine which capabilities to keep, update, or add
        existing_capabilities = {cap.capability_name: cap for cap in device.capabilities}
        seen_capabilities = set()
        
        for cap_data in data["capabilities"]:
            if not isinstance(cap_data, dict) or "name" not in cap_data:
                continue
                
            capability_name = cap_data["name"]
            seen_capabilities.add(capability_name)
            
            if capability_name in existing_capabilities:
                # Update existing capability
                cap = existing_capabilities[capability_name]
                
                # Update description if provided
                if "description" in cap_data:
                    cap.capability_description = cap_data["description"]
                
                # Update parameters if provided
                if "parameters" in cap_data:
                    parameters = cap_data["parameters"]
                    if isinstance(parameters, dict):
                        parameters = json.dumps(parameters)
                    cap.parameters = parameters
                
                # Update version if provided
                if "version" in cap_data:
                    cap.version = cap_data["version"]
            else:
                # Add new capability
                parameters = cap_data.get("parameters", {})
                if isinstance(parameters, dict):
                    parameters = json.dumps(parameters)
                    
                new_cap = DeviceCapability(
                    device_id=device.id,
                    capability_name=capability_name,
                    capability_description=cap_data.get("description", ""),
                    parameters=parameters,
                    version=cap_data.get("version", "1.0")
                )
                db.session.add(new_cap)
        
        # Remove capabilities not in the update
        for cap_name, cap in existing_capabilities.items():
            if cap_name not in seen_capabilities:
                db.session.delete(cap)
    
    db.session.commit()
    
    # Only log every 10 minutes to avoid excessive logging
    if not hasattr(device, "_last_heartbeat_log") or \
       (datetime.utcnow() - device._last_heartbeat_log).total_seconds() > 600:
        logger.info(f"Received heartbeat from device {device_uuid}")
        device._last_heartbeat_log = datetime.utcnow()
    
    return jsonify({
        "message": "Heartbeat received",
        "timestamp": int(device.last_active.timestamp())
    })


@devices_bp.route("/<string:device_uuid>/capabilities", methods=["GET"])
@require_device_auth
def get_capabilities(device_uuid, device=None):
    """
    Get the capabilities of a device.
    
    Path Parameters:
        device_uuid (str): UUID of the device
        
    Returns:
        JSON response with device capabilities
    """
    if not device:
        device = get_device_or_404(device_uuid, current_user.id)
    
    capabilities = [cap.to_dict() for cap in device.capabilities]
    
    return jsonify({
        "device_uuid": device_uuid,
        "device_name": device.name,
        "capabilities": capabilities,
        "count": len(capabilities)
    })


@devices_bp.route("/<string:device_uuid>/capabilities", methods=["POST"])
@require_device_auth
def update_capabilities(device_uuid, device=None):
    """
    Update the capabilities of a device.
    
    Path Parameters:
        device_uuid (str): UUID of the device
        
    Request Body:
        capabilities (list): List of device capabilities
        mode (str, optional): Update mode - "replace" (default) or "merge"
        
    Returns:
        JSON response with updated device details
    """
    if not device:
        device = get_device_or_404(device_uuid, current_user.id)
        
    data = request.json or {}
    
    # Validate request
    if "capabilities" not in data or not isinstance(data["capabilities"], list):
        return jsonify({
            "error": "Missing or invalid capabilities field"
        }), 400
    
    # Determine update mode
    mode = data.get("mode", "replace").lower()
    if mode not in ["replace", "merge"]:
        return jsonify({
            "error": "Invalid mode. Must be 'replace' or 'merge'."
        }), 400
    
    if mode == "replace":
        # Remove existing capabilities
        for cap in device.capabilities:
            db.session.delete(cap)
    
    # Add/update capabilities
    existing_capabilities = {cap.capability_name: cap for cap in device.capabilities} if mode == "merge" else {}
    
    for cap_data in data["capabilities"]:
        if not isinstance(cap_data, dict) or "name" not in cap_data:
            continue
            
        capability_name = cap_data["name"]
        
        if mode == "merge" and capability_name in existing_capabilities:
            # Update existing capability
            cap = existing_capabilities[capability_name]
            
            # Update description if provided
            if "description" in cap_data:
                cap.capability_description = cap_data["description"]
            
            # Update parameters if provided
            if "parameters" in cap_data:
                parameters = cap_data["parameters"]
                if isinstance(parameters, dict):
                    parameters = json.dumps(parameters)
                cap.parameters = parameters
            
            # Update version if provided
            if "version" in cap_data:
                cap.version = cap_data["version"]
        else:
            # Add new capability
            parameters = cap_data.get("parameters", {})
            if isinstance(parameters, dict):
                parameters = json.dumps(parameters)
                
            capability = DeviceCapability(
                device_id=device.id,
                capability_name=capability_name,
                capability_description=cap_data.get("description", ""),
                parameters=parameters,
                version=cap_data.get("version", "1.0")
            )
            
            db.session.add(capability)
    
    # Update last active timestamp
    device.last_active = datetime.utcnow()
    
    db.session.commit()
    
    logger.info(f"Updated capabilities for device {device_uuid} (mode: {mode})")
    
    return jsonify({
        "device": device.to_dict(),
        "message": "Device capabilities updated successfully"
    })


@devices_bp.route("/<string:device_uuid>", methods=["DELETE"])
@require_device_auth
def deregister_device(device_uuid, device=None):
    """
    Deregister a device (soft delete).
    
    Path Parameters:
        device_uuid (str): UUID of the device to deregister
        
    Query Parameters:
        permanent (bool): If true, permanently delete the device (default: false)
        
    Returns:
        JSON response confirming deregistration
    """
    if not device:
        device = get_device_or_404(device_uuid, current_user.id)
    
    permanent = request.args.get("permanent", "false").lower() == "true"
    
    if permanent:
        # Permanently delete the device and its capabilities (cascade)
        db.session.delete(device)
        message = "Device permanently deleted"
    else:
        # Soft delete - just mark as inactive
        device.is_active = False
        message = "Device deactivated"
    
    db.session.commit()
    
    logger.info(f"Deregistered device {device_uuid} for user {current_user.id} (permanent={permanent})")
    
    return jsonify({
        "message": message,
        "device_uuid": device_uuid
    })


@devices_bp.route("/find", methods=["POST"])
@login_required
def find_devices():
    """
    Find devices matching specific criteria.
    
    Request Body:
        capability (str, optional): Find devices with this capability
        device_type (str, optional): Find devices of this type
        status (str, optional): Find devices with this status ("online", "offline", "any")
        limit (int, optional): Maximum number of devices to return (default: 10)
        
    Returns:
        JSON response with matching devices
    """
    data = request.json or {}
    
    # Build query
    query = Device.query.filter_by(user_id=current_user.id, is_active=True)
    
    # Filter by device type if provided
    if "device_type" in data:
        query = query.filter_by(device_type=data["device_type"])
    
    # Filter by online status if provided
    if "status" in data:
        status = data["status"].lower()
        
        if status == "online":
            # Consider devices active in the last 5 minutes as online
            five_min_ago = datetime.utcnow() - timedelta(minutes=5)
            query = query.filter(Device.last_active >= five_min_ago)
        elif status == "offline":
            # Consider devices not active in the last 5 minutes as offline
            five_min_ago = datetime.utcnow() - timedelta(minutes=5)
            query = query.filter(Device.last_active < five_min_ago)
    
    # Get devices
    devices = query.all()
    
    # Filter by capability if provided
    if "capability" in data:
        capability = data["capability"]
        devices = [d for d in devices if any(cap.capability_name == capability for cap in d.capabilities)]
    
    # Apply limit
    limit = int(data.get("limit", 10))
    if limit > 0:
        devices = devices[:limit]
    
    return jsonify({
        "devices": [device.to_dict() for device in devices],
        "count": len(devices)
    })
