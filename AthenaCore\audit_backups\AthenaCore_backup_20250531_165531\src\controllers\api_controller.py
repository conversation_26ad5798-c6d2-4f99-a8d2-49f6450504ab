"""
API controller module.

This module provides controller functions for handling API routes, including
user management, task management, and configuration endpoints.
"""

from flask import Blueprint, request, jsonify, current_app, g
import datetime

from src.services.user_service import UserService
from src.services.auth_service import AuthenticationService
from src.services.task_service import TaskService
from src.services.base_service import get_service
from src.utils.api_response import success_response, error_response
from src.utils.middleware import require_auth, require_api_key, require_role, api_route
from src.utils.exceptions import ValidationError, ResourceNotFoundError

# Create a blueprint for API routes
api_bp = Blueprint("api", __name__, url_prefix="/api/v1")

@api_bp.route("/health", methods=["GET"])
def health_check():
    """
    Health check endpoint.
    
    Returns:
        API response with health status
    """
    return success_response(
        data={
            "status": "healthy",
            "timestamp": datetime.datetime.utcnow().isoformat(),
            "version": current_app.config.get("VERSION", "unknown")
        },
        message="Athena API is healthy"
    )

@api_bp.route("/users/me", methods=["GET"])
@require_auth
def get_current_user():
    """
    Get the current authenticated user.
    
    Returns:
        API response with user data
    """
    return success_response(
        data=g.user.to_dict(),
        message="User profile retrieved successfully"
    )

@api_bp.route("/users/<int:user_id>", methods=["GET"])
@require_auth
@require_role("admin")
def get_user(user_id):
    """
    Get a user by ID.
    
    Args:
        user_id: ID of the user to get
        
    Returns:
        API response with user data
    """
    user_service = get_service(UserService)
    user = user_service.get_user_by_id(user_id)
    
    if not user:
        raise ResourceNotFoundError("User", user_id)
    
    return success_response(
        data=user.to_dict(),
        message="User retrieved successfully"
    )

@api_bp.route("/users", methods=["GET"])
@require_auth
@require_role("admin")
def get_users():
    """
    Get all users.
    
    Returns:
        API response with list of users
    """
    user_service = get_service(UserService)
    users = user_service.get_all_users()
    
    return success_response(
        data=[user.to_dict() for user in users],
        message="Users retrieved successfully",
        meta={"total": len(users)}
    )

@api_bp.route("/users", methods=["POST"])
@require_auth
@require_role("admin")
def create_user():
    """
    Create a new user.
    
    Returns:
        API response with created user data
    """
    data = request.get_json()
    
    if not data:
        raise ValidationError({"general": ["No data provided"]})
    
    # Validate required fields
    errors = {}
    required_fields = ["username", "email", "password"]
    
    for field in required_fields:
        if field not in data or not data[field]:
            errors[field] = [f"{field} is required"]
    
    if errors:
        raise ValidationError(errors)
    
    user_service = get_service(UserService)
    
    # Check if username or email already exists
    if user_service.get_user_by_username(data["username"]):
        errors["username"] = ["Username already exists"]
    
    if user_service.get_user_by_email(data["email"]):
        errors["email"] = ["Email already exists"]
    
    if errors:
        raise ValidationError(errors)
    
    # Create the user
    user = user_service.create_user(
        username=data["username"],
        email=data["email"],
        password=data["password"],
        role=data.get("role", "user")
    )
    
    return success_response(
        data=user.to_dict(),
        message="User created successfully",
        status_code=201
    )

@api_bp.route("/users/<int:user_id>", methods=["PUT"])
@require_auth
def update_user(user_id):
    """
    Update a user.
    
    Args:
        user_id: ID of the user to update
        
    Returns:
        API response with updated user data
    """
    # Only allow users to update their own profile, or admins to update any profile
    if g.user.id != user_id and g.user.role != "admin":
        return error_response(
            message="You are not authorized to update this user",
            error_code="authorization_error",
            status_code=403
        )
    
    data = request.get_json()
    
    if not data:
        raise ValidationError({"general": ["No data provided"]})
    
    user_service = get_service(UserService)
    user = user_service.get_user_by_id(user_id)
    
    if not user:
        raise ResourceNotFoundError("User", user_id)
    
    # Update the user
    updated_user = user_service.update_user(
        user_id=user_id,
        data=data
    )
    
    return success_response(
        data=updated_user.to_dict(),
        message="User updated successfully"
    )

@api_bp.route("/users/<int:user_id>", methods=["DELETE"])
@require_auth
@require_role("admin")
def delete_user(user_id):
    """
    Delete a user.
    
    Args:
        user_id: ID of the user to delete
        
    Returns:
        API response with success message
    """
    user_service = get_service(UserService)
    user = user_service.get_user_by_id(user_id)
    
    if not user:
        raise ResourceNotFoundError("User", user_id)
    
    # Prevent deleting the last admin user
    if user.role == "admin":
        admin_count = len([u for u in user_service.get_all_users() if u.role == "admin"])
        if admin_count <= 1:
            return error_response(
                message="Cannot delete the last admin user",
                error_code="validation_error",
                status_code=422
            )
    
    # Delete the user
    user_service.delete_user(user_id)
    
    return success_response(
        message="User deleted successfully"
    )

@api_bp.route("/tasks", methods=["GET"])
@require_auth
def get_tasks():
    """
    Get all tasks for the current user.
    
    Returns:
        API response with list of tasks
    """
    task_service = get_service(TaskService)
    
    # Admins can view all tasks, regular users can only view their own
    if g.user.role == "admin" and request.args.get("all") == "true":
        tasks = task_service.get_all_tasks()
    else:
        tasks = task_service.get_all_tasks(user_id=g.user.id)
    
    return success_response(
        data=[task.to_dict() for task in tasks],
        message="Tasks retrieved successfully",
        meta={"total": len(tasks)}
    )

@api_bp.route("/tasks/<task_id>", methods=["GET"])
@require_auth
def get_task(task_id):
    """
    Get a task by ID.
    
    Args:
        task_id: ID of the task to get
        
    Returns:
        API response with task data
    """
    task_service = get_service(TaskService)
    task = task_service.get_task(task_id)
    
    if not task:
        raise ResourceNotFoundError("Task", task_id)
    
    # Only allow users to view their own tasks, or admins to view any task
    if task.user_id != g.user.id and g.user.role != "admin":
        return error_response(
            message="You are not authorized to view this task",
            error_code="authorization_error",
            status_code=403
        )
    
    return success_response(
        data=task.to_dict(),
        message="Task retrieved successfully"
    )

@api_bp.route("/tasks", methods=["POST"])
@require_auth
def create_task():
    """
    Create a new task.
    
    Returns:
        API response with created task data
    """
    data = request.get_json()
    
    if not data:
        raise ValidationError({"general": ["No data provided"]})
    
    # Validate required fields
    if "name" not in data or not data["name"]:
        raise ValidationError({"name": ["Task name is required"]})
    
    # Create the task
    task_service = get_service(TaskService)
    task = task_service.create_task(
        name=data["name"],
        description=data.get("description", ""),
        user_id=g.user.id
    )
    
    return success_response(
        data=task.to_dict(),
        message="Task created successfully",
        status_code=201
    )

def register_blueprints(app):
    """
    Register all API-related blueprints with the app.
    
    Args:
        app: Flask application instance
    """
    app.register_blueprint(api_bp)
