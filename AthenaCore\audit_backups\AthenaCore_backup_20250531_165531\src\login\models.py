# ===========================================================================
# DEPRECATED: src/login/models.py
# ===========================================================================
# This file is DEPRECATED and will be REMOVED in a future release.
# All functionality has been migrated to the src/models directory.
#
# Please update your imports to use the new locations:
#   OLD: from src.login.models import User, APIKey
#   NEW: from src.models.user import User
#        from src.models.api import APIKey

import warnings
import logging

# Set up logging
logger = logging.getLogger('athena.compat')

# Show deprecation warning
warnings.warn(
    "The module src.login.models is deprecated. Please update imports to use src.models.*",
    category=FutureWarning,
    stacklevel=2
)

# Import database instance
from src.models import db

# Import user models
from src.models.user import User, Role, UserRole, UserLog

# Import API models
from src.models.api import APIKey

# Import configuration models
from src.models.configuration import ConfigEntry

# Import other models as needed
from src.models.conversation import Conversation, Message
from src.models.llm import LLMProviderSetting
from src.models.command import CommandToggle
from src.models.connection import DirectConnection, MCPApiKey, MCPServerTemplate
from src.models.logging import LogEntry

# For backward compatibility with code that might use Flask-SQLAlchemy directly
from flask_sqlalchemy import SQLAlchemy

# Define __all__ to control what's imported with 'from src.login.models import *'
__all__ = [
    'db', 'SQLAlchemy',
    'User', 'Role', 'UserRole', 'UserLog',
    'APIKey', 
    'ConfigEntry',
    'Conversation', 'Message',
    'LLMProviderSetting',
    'CommandToggle',
    'DirectConnection', 'MCPApiKey', 'MCPServerTemplate',
    'LogEntry'
]

# Note: There are no duplicate imports or warnings after this point
def log_deprecated_usage():
    """Log the module that imported this deprecated module."""
    frame = sys._getframe(1)
    caller_module = frame.f_globals.get('__name__', 'unknown')
    caller_function = frame.f_code.co_name
    
    # Do not log if imported by the compatibility module itself
    if caller_module.startswith('src.login.models_compat'):
        return
        
    # Do not log if imported by compat module
    if caller_module.startswith('src.login.compat'):
        return
    
    # Log the usage of this deprecated file
    logger.warning(f"Deprecated module src.login.models used directly by {caller_module}.{caller_function}")
    
    # Show a warning to the developer
    warnings.warn(
        f"The module src.login.models is deprecated and will be removed in a future version. "
        f"Please update your imports to use src.models instead.",
        category=FutureWarning,
        stacklevel=2
    )

# Log usage of this deprecated module
log_deprecated_usage()

# Import all models from the compatibility module to ensure backwards compatibility
from src.login.models_compat import *

# Only import models from src.models if they're not already in the caller's globals
# This helps prevent duplicate model definition issues
try:
    # Import the database instance directly
    from src.models import db
    
    # Import all model classes from the new location
    from src.models import (
        User, UserLog, APIKey, Configuration,
        LLMProviderSetting, CommandToggle, DirectConnection,
        MCPApiKey, MCPServerTemplate, Conversation, Message, LogEntry
    )
    
    # Expose all imported models to maintain backward compatibility
    __all__ = [
        'db',
        'User', 'UserLog', 'APIKey', 'Configuration',
        'LLMProviderSetting', 'CommandToggle', 'DirectConnection',
        'MCPApiKey', 'MCPServerTemplate', 'Conversation', 'Message', 'LogEntry'
    ]
    
    logger.debug(f"Successfully imported models for {caller_module}")
    
except ImportError as e:
    logger.error(f"Error importing models: {e}")
    # If imports fail, provide a meaningful error message
    warnings.warn(
        f"Failed to import models from src.models: {e}. "
        f"This might cause functionality issues.",
        category=ImportWarning,
        stacklevel=2
    )
