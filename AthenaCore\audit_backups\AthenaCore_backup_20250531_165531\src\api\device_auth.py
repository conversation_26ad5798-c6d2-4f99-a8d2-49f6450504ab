# src/api/device_auth.py

"""
API endpoints for device authentication in the Cross-Device API System.

This file implements:
- JWT token generation for devices
- Token validation and refresh
- Device authentication middleware
"""

import json
import logging
import uuid
from datetime import datetime, timedelta
import jwt

from flask import Blueprint, jsonify, request, current_app
from flask_login import current_user, login_required
from functools import wraps

from src.models import db, User
# Import device models directly instead of using the deprecated module
from src.models.device import Device, get_device_or_404

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger("device_auth_api")

# Create blueprint
device_auth_bp = Blueprint("device_auth", __name__, url_prefix="/api/auth/device")


# Helper Functions

def generate_device_token(device, expires_in_hours=24):
    """
    Generate a JWT token for a device.
    
    Args:
        device (Device): The device to generate a token for
        expires_in_hours (int): Hours until the token expires
        
    Returns:
        str: The generated JWT token
    """
    # Calculate expiration time
    expires = datetime.utcnow() + timedelta(hours=expires_in_hours)
    
    # Get device capabilities
    capabilities = [cap.capability_name for cap in device.capabilities]
    
    # Create payload
    payload = {
        "sub": device.device_uuid,  # subject
        "uid": device.user_id,      # user ID
        "name": device.name,        # device name
        "type": device.device_type, # device type
        "cap": capabilities,        # device capabilities
        "iat": datetime.utcnow(),   # issued at
        "exp": expires,             # expiration time
    }
    
    # Get secret key from app config
    secret_key = current_app.config.get("SECRET_KEY", "default-secret-key")
    
    # Generate token
    token = jwt.encode(
        payload, 
        secret_key, 
        algorithm="HS256"
    )
    
    return token


def validate_device_token(token):
    """
    Validate a device JWT token.
    
    Args:
        token (str): The JWT token to validate
        
    Returns:
        dict: The decoded token payload if valid, None otherwise
    """
    try:
        # Get secret key from app config
        secret_key = current_app.config.get("SECRET_KEY", "default-secret-key")
        
        # Decode and validate token
        payload = jwt.decode(
            token, 
            secret_key, 
            algorithms=["HS256"]
        )
        
        # Check if device exists
        device = Device.query.filter_by(
            device_uuid=payload["sub"]
        ).first()
        
        if not device:
            logger.warning(f"Token validation failed: Device not found: {payload['sub']}")
            return None
        
        # Check if device is active
        if not device.is_active:
            logger.warning(f"Token validation failed: Device is not active: {payload['sub']}")
            return None
        
        # Token is valid
        return payload
    except jwt.ExpiredSignatureError:
        logger.warning("Token validation failed: Token has expired")
        return None
    except jwt.InvalidTokenError as e:
        logger.warning(f"Token validation failed: Invalid token: {str(e)}")
        return None


def get_token_from_request():
    """
    Extract the JWT token from the request.
    
    Returns:
        str: The JWT token if found, None otherwise
    """
    # Check Authorization header
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        return auth_header[7:]  # Remove "Bearer " prefix
    
    # Check query parameters
    token = request.args.get("token")
    if token:
        return token
    
    # Token not found
    return None


def require_device_token(f):
    """
    Decorator to require a valid device token.
    
    This extracts the token from the request, validates it,
    and adds the device to the request context.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Allow access if user is already authenticated through Flask-Login
        if (
            current_user
            and hasattr(current_user, "is_authenticated")
            and current_user.is_authenticated
        ):
            return f(*args, **kwargs)
        
        # Get token from request
        token = get_token_from_request()
        if not token:
            return jsonify({
                "error": "Authentication required",
                "details": "Missing device token"
            }), 401
        
        # Validate token
        payload = validate_device_token(token)
        if not payload:
            return jsonify({
                "error": "Authentication failed",
                "details": "Invalid or expired device token"
            }), 401
        
        # Get device
        device = Device.query.filter_by(
            device_uuid=payload["sub"]
        ).first()
        
        if not device:
            return jsonify({
                "error": "Authentication failed",
                "details": "Device not found"
            }), 401
        
        # Add device to request context
        request.device = device
        request.device_token_payload = payload
        
        # If device_uuid is in kwargs, verify it matches the token
        if "device_uuid" in kwargs and kwargs["device_uuid"] != device.device_uuid:
            return jsonify({
                "error": "Authentication failed",
                "details": "Device UUID mismatch"
            }), 403
        
        # Add device to kwargs for convenience
        kwargs["device"] = device
        
        return f(*args, **kwargs)
    
    return decorated_function


# API Endpoints

@device_auth_bp.route("/token", methods=["POST"])
@login_required
def get_device_token():
    """
    Generate a JWT token for a device.
    
    Request Body:
        device_uuid (str): UUID of the device to generate a token for
        expires_in_hours (int, optional): Hours until the token expires (default: 24)
        
    Returns:
        JSON response with the generated token
    """
    data = request.json or {}
    
    # Validate required fields
    if "device_uuid" not in data:
        return jsonify({
            "error": "Missing required field: device_uuid"
        }), 400
    
    # Get device
    device = Device.query.filter_by(
        device_uuid=data["device_uuid"],
        user_id=current_user.id
    ).first()
    
    if not device:
        return jsonify({
            "error": f"Device not found: {data['device_uuid']}"
        }), 404
    
    # Check if device is active
    if not device.is_active:
        return jsonify({
            "error": f"Device is not active: {data['device_uuid']}"
        }), 400
    
    # Get expiration time
    expires_in_hours = data.get("expires_in_hours", 24)
    try:
        expires_in_hours = int(expires_in_hours)
    except (ValueError, TypeError):
        return jsonify({
            "error": f"Invalid expires_in_hours: {expires_in_hours}. Must be an integer."
        }), 400
    
    # Generate token
    token = generate_device_token(device, expires_in_hours)
    
    # Update device's last active timestamp
    device.last_active = datetime.utcnow()
    db.session.commit()
    
    logger.info(f"Generated token for device {device.device_uuid} (expires in {expires_in_hours} hours)")
    
    return jsonify({
        "token": token,
        "expires_in": expires_in_hours * 3600,  # Convert hours to seconds
        "token_type": "Bearer",
        "device": device.to_dict()
    })


@device_auth_bp.route("/verify", methods=["POST"])
def verify_device_token():
    """
    Verify a device JWT token.
    
    Request Body:
        token (str): The JWT token to verify
        
    Returns:
        JSON response with the token payload if valid
    """
    data = request.json or {}
    
    # Validate required fields
    if "token" not in data:
        return jsonify({
            "error": "Missing required field: token"
        }), 400
    
    # Validate token
    payload = validate_device_token(data["token"])
    if not payload:
        return jsonify({
            "error": "Token validation failed",
            "is_valid": False
        }), 401
    
    # Get device
    device = Device.query.filter_by(
        device_uuid=payload["sub"]
    ).first()
    
    if not device:
        return jsonify({
            "error": "Device not found",
            "is_valid": False
        }), 404
    
    # Token is valid
    return jsonify({
        "is_valid": True,
        "device_uuid": device.device_uuid,
        "device_name": device.name,
        "user_id": device.user_id,
        "expires_at": payload["exp"],
        "capabilities": payload["cap"]
    })


@device_auth_bp.route("/refresh", methods=["POST"])
@require_device_token
def refresh_device_token(device):
    """
    Refresh a device JWT token.
    
    The current token is validated using the require_device_token decorator,
    which adds the device to the request context.
    
    Request Body:
        expires_in_hours (int, optional): Hours until the new token expires (default: 24)
        
    Returns:
        JSON response with the new token
    """
    data = request.json or {}
    
    # Get expiration time
    expires_in_hours = data.get("expires_in_hours", 24)
    try:
        expires_in_hours = int(expires_in_hours)
    except (ValueError, TypeError):
        return jsonify({
            "error": f"Invalid expires_in_hours: {expires_in_hours}. Must be an integer."
        }), 400
    
    # Generate new token
    token = generate_device_token(device, expires_in_hours)
    
    # Update device's last active timestamp
    device.last_active = datetime.utcnow()
    db.session.commit()
    
    logger.info(f"Refreshed token for device {device.device_uuid} (expires in {expires_in_hours} hours)")
    
    return jsonify({
        "token": token,
        "expires_in": expires_in_hours * 3600,  # Convert hours to seconds
        "token_type": "Bearer",
        "device": device.to_dict()
    })


@device_auth_bp.route("/revoke", methods=["POST"])
@login_required
def revoke_device_token():
    """
    Revoke a device's authentication by deactivating the device.
    
    This doesn't actually invalidate existing tokens, but it prevents
    them from being used by setting the device as inactive.
    
    Request Body:
        device_uuid (str): UUID of the device to revoke
        
    Returns:
        JSON response confirming the revocation
    """
    data = request.json or {}
    
    # Validate required fields
    if "device_uuid" not in data:
        return jsonify({
            "error": "Missing required field: device_uuid"
        }), 400
    
    # Get device
    device = Device.query.filter_by(
        device_uuid=data["device_uuid"],
        user_id=current_user.id
    ).first_or_404(
        description=f"Device not found: {data['device_uuid']}"
    )
    
    # Deactivate device
    device.is_active = False
    db.session.commit()
    
    logger.info(f"Revoked authentication for device {device.device_uuid}")
    
    return jsonify({
        "message": f"Device {device.name} authentication revoked",
        "device_uuid": device.device_uuid
    })
