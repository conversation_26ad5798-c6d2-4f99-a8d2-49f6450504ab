# MCP Server Creation and Deployment

This document provides a comprehensive guide for creating, implementing, and deploying Model Context Protocol (MCP) servers with Athena.

## Table of Contents

- [Overview](#overview)
- [MCP Server Implementation](#mcp-server-implementation)
  - [WebSocket Transport Layer](#websocket-transport-layer)
  - [STDIO Implementation](#stdio-implementation)
- [Smithery Deployments](#smithery-deployments)
  - [Benefits of Hosting](#benefits-of-hosting)
  - [Deployment Process](#deployment-process)
  - [Technical Requirements](#technical-requirements)
- [Creating MCPs with <PERSON>](#creating-mcps-with-athena)
  - [Using the MCP Designer UI](#using-the-mcp-designer-ui)
  - [Programmatic Creation](#programmatic-creation)
  - [MCP Server Templates](#mcp-server-templates)
- [Implementation Examples](#implementation-examples)
  - [Web Search MCP](#web-search-mcp)
  - [Database Explorer MCP](#database-explorer-mcp)
  - [System Command MCP](#system-command-mcp)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)
- [Further Reading](#further-reading)

## Overview

Model Context Protocol (MCP) allows AI models to access external tools and data through a standardized API interface. Athena can not only connect to existing MCP servers but also create and deploy new ones.

## MCP Server Implementation

### WebSocket Transport Layer

The WebSocket transport layer is a critical component for MCP server implementation, especially for deployment on Smithery.

```python
@asynccontextmanager
async def websocket_server(scope: Scope, receive: Receive, send: Send):
    """WebSocket server transport for MCP. ASGI application compatible with Starlette and Hypercorn."""
    
    websocket = WebSocket(scope, receive, send)
    await websocket.accept(subprotocol="mcp")
    
    # Setup bidirectional streams
    read_stream_writer, read_stream = anyio.create_memory_object_stream(0)
    write_stream, write_stream_reader = anyio.create_memory_object_stream(0)
    
    # Message handling functions
    async def ws_reader():
        # Reads and validates client messages
        ...
        
    async def ws_writer():
        # Sends responses to the client
        ...
        
    # Run concurrent read/write operations
    async with anyio.create_task_group() as tg:
        tg.start_soon(ws_reader)
        tg.start_soon(ws_writer)
        yield (read_stream, write_stream)
```

This implementation provides:

- ASGI compatibility for standard web frameworks
- Bidirectional communication with clients
- Proper message validation and serialization
- Concurrent read/write capabilities

### STDIO Implementation

For local development and testing, MCP servers typically use STDIO (Standard Input/Output) for communication. Smithery will convert this to WebSocket for hosted deployments.

## Smithery Deployments

Smithery Deployments allow you to host your STDIO MCP server in a serverless environment, making it accessible via WebSocket connections.

### Benefits of Hosting

1. **Interactive Playground**: Smithery provides an MCP playground on your server page for users to try your MCP online
2. **No Client Dependencies**: Users can call your server without installing dependencies
3. **Enhanced Security**: Eliminates client-side security concerns
4. **Improved Discoverability**: Hosted servers rank higher in Smithery search results
5. **Automatic Scaling**: Handles increased traffic without manual intervention

### Deployment Process

Deploying an MCP server on Smithery is straightforward:

1. Add your server to Smithery (or claim it if already listed)
2. Navigate to the Deployments tab on your server page (only visible to authenticated server owners)
3. Click "Deploy"

Your MCP server will be built and deployed according to your configuration.

### Technical Requirements

1. **Ephemeral Design**: Servers timeout after 5 minutes of inactivity and should be designed with ephemeral storage in mind
2. **External Persistence**: Persistent data should be stored in external databases
3. **Tool List Accessibility**: Tool lists must be available without authentication
   - Implement "lazy loading" - only authenticate upon tool call, not initialization
4. **Reconnection Handling**: Clients must implement reconnection logic
5. **WebSocket Support**: Server must support WebSocket transport (Smithery converts STDIO to WebSocket)

## Creating MCPs with Athena

Athena provides several ways to create, test, and deploy MCP servers:

### Using the MCP Designer UI

Athena includes a graphical MCP Server Designer that allows you to create MCP servers without writing code:

1. In the Athena Agent, navigate to Settings → MCP Settings
2. Click on the "Create Server" button in the MCP Settings dialog
3. Use the designer to define tools, resources, and server properties
4. Test locally or deploy directly to Smithery from the UI

Recent enhancements to the MCP Designer UI include:

- Fixed template loading to properly add tools and resources from existing templates
- Improved form field management with proper reset capabilities
- Anonymous access to templates for better usability
- Enhanced error handling to prevent attribute errors during template loading

### Programmatic Creation

For advanced users, Athena provides a Python API for programmatically creating MCP servers:

```python
from src.mcp.server_creation import AthenaMCPServerFactory
from src.core.athena import Athena
from src.utils.config import AthenaConfig

# Initialize dependencies
config = AthenaConfig()
athena = Athena(config)

# Create MCP server factory
factory = AthenaMCPServerFactory(athena, config)

# Create a new server
server = factory.create_server(
    name="My Web Search MCP",
    description="An MCP server for web searching and content extraction",
    version="1.0.0",
    dependencies=["requests", "beautifulsoup4"]
)

# Add custom tools
@server.tool()
async def search_web(query: str, ctx):
    """Search the web for information"""
    # Use Athena's built-in web search
    athena = ctx.request_context.lifespan_context["athena"]
    results = await athena.search_web(query)
    return results

# Start the server
server.run()
```

### MCP Server Templates

Athena includes several templates for common MCP server types:

1. **Web Services MCP**: Provides tools for web search, content extraction, and API access
2. **File System MCP**: Provides tools for file operations and document processing
3. **Database MCP**: Provides tools for database access and querying
4. **System MCP**: Provides tools for system command execution and monitoring

To use a template:

```python
from src.mcp.server_creation import create_from_template

# Create a server from a template
server = create_from_template(
    template="web_services",
    name="My Web Services MCP",
    config=config,
    athena=athena
)

# Customize as needed
@server.tool()
async def translate_text(text: str, target_language: str, ctx):
    """Translate text to the target language"""
    # Implementation...
```

## Implementation Examples

### Web Search MCP

This example demonstrates creating an MCP server focused on web search and content extraction:

```python
from mcp.server.fastmcp import FastMCP, Context
from src.core.athena import Athena
from src.utils.config import AthenaConfig
import requests
from bs4 import BeautifulSoup
import json

# Create server
server = FastMCP(
    "Web Explorer MCP",
    description="A tool for searching the web and extracting content",
    version="1.0.0",
    dependencies=["requests", "beautifulsoup4"]
)

# Context setup for Athena integration
@dataclass
class AppContext:
    athena: Athena
    config: AthenaConfig

@asynccontextmanager
async def app_lifespan(server):
    config = AthenaConfig()
    athena = Athena(config)
    try:
        yield AppContext(athena=athena, config=config)
    finally:
        # Cleanup
        pass

# Configure server with lifespan
server = FastMCP(
    "Web Explorer MCP",
    description="A tool for searching the web and extracting content",
    version="1.0.0",
    dependencies=["requests", "beautifulsoup4"],
    lifespan=app_lifespan
)

# Search tool
@server.tool()
async def search_web(query: str, num_results: int = 5, ctx: Context) -> str:
    """Search the web for information."""
    athena = ctx.request_context.lifespan_context.athena
    results = await athena.search_web(query, limit=num_results)
    return json.dumps(results)

# Extract content tool
@server.tool()
async def extract_content(url: str, ctx: Context) -> str:
    """Extract main content from a webpage."""
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.extract()
        
        # Get text
        text = soup.get_text(separator='\n', strip=True)
        
        # Remove extra whitespace
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = '\n'.join(chunk for chunk in chunks if chunk)
        
        return text[:8000]  # Limit length
    except Exception as e:
        return f"Error extracting content: {str(e)}"

# Resource for recent searches
@server.resource("recent-searches")
async def get_recent_searches(ctx: Context) -> str:
    """Get recent web searches performed by the user."""
    # In a real implementation, this would retrieve from a database
    return json.dumps(["climate change", "python programming", "MCP protocol"])

if __name__ == "__main__":
    server.run()
```

### Database Explorer MCP

This example creates an MCP server for exploring SQLite databases:

```python
from mcp.server.fastmcp import FastMCP, Context
import sqlite3
import json
import os
from typing import List, Dict, Any, Optional

# Create server
server = FastMCP(
    "SQLite Explorer MCP",
    description="A tool for exploring and querying SQLite databases",
    version="1.0.0",
    dependencies=[]
)

# Database connection management
class DatabaseManager:
    def __init__(self):
        self.connections = {}
    
    def get_connection(self, db_path: str) -> sqlite3.Connection:
        if db_path not in self.connections:
            self.connections[db_path] = sqlite3.connect(db_path)
        return self.connections[db_path]
    
    def close_all(self):
        for conn in self.connections.values():
            conn.close()
        self.connections.clear()

# Setup lifespan
@asynccontextmanager
async def app_lifespan(server):
    db_manager = DatabaseManager()
    try:
        yield {"db_manager": db_manager}
    finally:
        db_manager.close_all()

server.lifespan = app_lifespan

# Tool to list tables
@server.tool()
def list_tables(db_path: str, ctx: Context) -> str:
    """List all tables in a SQLite database."""
    try:
        db_manager = ctx.request_context.lifespan_context["db_manager"]
        conn = db_manager.get_connection(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        
        return json.dumps(tables)
    except Exception as e:
        return f"Error listing tables: {str(e)}"

# Tool to run SQL query
@server.tool()
def run_query(db_path: str, query: str, ctx: Context) -> str:
    """Run a SQL query on a SQLite database."""
    try:
        db_manager = ctx.request_context.lifespan_context["db_manager"]
        conn = db_manager.get_connection(db_path)
        cursor = conn.cursor()
        
        # Execute query
        cursor.execute(query)
        
        # Check if it's a SELECT query
        if query.strip().upper().startswith("SELECT"):
            columns = [desc[0] for desc in cursor.description]
            rows = cursor.fetchall()
            
            # Format results as list of dictionaries
            results = [
                {columns[i]: value for i, value in enumerate(row)}
                for row in rows
            ]
            
            return json.dumps(results)
        else:
            # For non-SELECT queries, just return affected row count
            conn.commit()
            return json.dumps({"affected_rows": cursor.rowcount})
    except Exception as e:
        return f"Error executing query: {str(e)}"

# Resource for database schema
@server.resource("database/{db_path}/schema")
def get_database_schema(db_path: str, ctx: Context) -> str:
    """Get the schema of a SQLite database."""
    try:
        db_manager = ctx.request_context.lifespan_context["db_manager"]
        conn = db_manager.get_connection(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        
        schema = {}
        for table in tables:
            cursor.execute(f"PRAGMA table_info({table});")
            columns = [
                {
                    "name": row[1],
                    "type": row[2],
                    "not_null": bool(row[3]),
                    "default": row[4],
                    "primary_key": bool(row[5])
                }
                for row in cursor.fetchall()
            ]
            schema[table] = columns
        
        return json.dumps(schema)
    except Exception as e:
        return f"Error retrieving schema: {str(e)}"

if __name__ == "__main__":
    server.run()
```

### System Command MCP

This example creates an MCP server for executing system commands:

```python
from mcp.server.fastmcp import FastMCP, Context
import subprocess
import os
import platform
import json
import psutil

# Create server
server = FastMCP(
    "System Command MCP",
    description="A tool for executing system commands and monitoring system resources",
    version="1.0.0",
    dependencies=["psutil"]
)

# Security check function
def is_safe_command(command: str) -> bool:
    """Check if a command is safe to execute."""
    # This is a simplified check - in production, use a more robust approach
    dangerous_commands = [
        "rm -rf", "format", "mkfs", "dd", ":(){ :|:& };", "chmod -R 777",
        "mv /* /dev/null", "> /dev/sda", "/dev/null > "
    ]
    
    command = command.lower()
    return not any(cmd in command for cmd in dangerous_commands)

# Command execution tool (with safety checks)
@server.tool()
def execute_command(command: str, ctx: Context) -> str:
    """Execute a system command."""
    if not is_safe_command(command):
        return "Error: Command rejected for security reasons"
    
    try:
        # Execute command with 10-second timeout
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=10
        )
        
        return json.dumps({
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode
        })
    except subprocess.TimeoutExpired:
        return "Error: Command timed out after 10 seconds"
    except Exception as e:
        return f"Error executing command: {str(e)}"

# System information tool
@server.tool()
def get_system_info(ctx: Context) -> str:
    """Get system information."""
    try:
        info = {
            "platform": platform.platform(),
            "processor": platform.processor(),
            "python_version": platform.python_version(),
            "architecture": platform.architecture(),
            "memory": {
                "total": psutil.virtual_memory().total,
                "available": psutil.virtual_memory().available,
                "percent": psutil.virtual_memory().percent
            },
            "disk": {
                "total": psutil.disk_usage('/').total,
                "free": psutil.disk_usage('/').free,
                "percent": psutil.disk_usage('/').percent
            },
            "cpu": {
                "cores_physical": psutil.cpu_count(logical=False),
                "cores_logical": psutil.cpu_count(logical=True),
                "percent": psutil.cpu_percent(interval=1)
            }
        }
        
        return json.dumps(info)
    except Exception as e:
        return f"Error retrieving system info: {str(e)}"

# Resource for running processes
@server.resource("system/processes")
def get_running_processes(ctx: Context) -> str:
    """Get information about running processes."""
    try:
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'username', 'memory_percent', 'cpu_percent']):
            processes.append(proc.info)
        
        # Sort by CPU usage (descending)
        processes.sort(key=lambda x: x['cpu_percent'], reverse=True)
        
        # Return top 20 processes
        return json.dumps(processes[:20])
    except Exception as e:
        return f"Error retrieving processes: {str(e)}"

if __name__ == "__main__":
    server.run()
```

## Best Practices

1. **Stateless Design**: Design your MCP server to be stateless whenever possible
2. **Tool Documentation**: Provide clear documentation for each tool
3. **Error Handling**: Implement robust error handling and logging
4. **Rate Limiting**: Consider implementing rate limiting for resource-intensive operations
5. **Authentication**: Use secure authentication methods for sensitive operations
6. **Versioning**: Implement versioning for your MCP server API
7. **Security Checks**: Always validate inputs and implement security checks for dangerous operations
8. **Resource Cleanup**: Properly clean up resources in the lifespan shutdown handler

## Troubleshooting

### Common Issues

1. **Deployment Failures**
   - Check that all dependencies are correctly specified
   - Ensure your server runs correctly locally before deploying
   - Verify your Smithery API key is valid

2. **Connection Errors**
   - Check network connectivity
   - Verify WebSocket transport is properly configured
   - Ensure the server is running and accessible

3. **Tool Execution Failures**
   - Implement proper error handling in all tools
   - Add logging to track execution flow
   - Test tools individually before integrating

### Debugging Tips

1. Use the local development server for testing before deployment
2. Enable debug logging to trace execution flow
3. Implement progress reporting in long-running tools
4. Use the Athena MCP Designer's Test Locally feature

## Further Reading

- [MCP Specification](https://github.com/modelcontextprotocol/specification)
- [Python SDK Documentation](https://github.com/modelcontextprotocol/python-sdk)
- [Smithery Registry API](https://smithery.dev/docs/registry-api)
- [Athena MCP Integration Guide](./mcp_integration.md)
