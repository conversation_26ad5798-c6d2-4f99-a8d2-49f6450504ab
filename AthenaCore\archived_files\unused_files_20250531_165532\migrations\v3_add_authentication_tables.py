"""
Migration v3: Add Authentication Tables

Create tables for user authentication, including users, user_logs, and api_keys.

Generated: 2025-05-22
"""

# Migration version and metadata
version = 3
description = "Create tables for user authentication, including users, user_logs, and api_keys"
dependencies = []  # List of migration versions this depends on

def upgrade(db):
    """
    Apply the migration.
    
    Args:
        db: SQLAlchemy database instance
    """
    # Create the users table if it doesn't exist
    db.engine.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) NOT NULL UNIQUE,
            email VARCHAR(120) NOT NULL UNIQUE,
            password_hash VARCHAR(128) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN NOT NULL DEFAULT 1,
            role VARCHAR(20) NOT NULL DEFAULT 'user',
            theme_preference VARCHAR(20) DEFAULT 'dark',
            profile_picture VARCHAR(255),
            display_name VARCHAR(100),
            bio TEXT,
            phone VARCHAR(32),
            location VARCHAR(128)
        )
    """)
    
    # Create the user_logs table if it doesn't exist
    db.engine.execute("""
        CREATE TABLE IF NOT EXISTS user_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            log_id VARCHAR(50) NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
    """)
    
    # Create the api_keys table if it doesn't exist
    db.engine.execute("""
        CREATE TABLE IF NOT EXISTS api_keys (
            id VARCHAR(64) PRIMARY KEY,
            name VARCHAR(100),
            user_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_used TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
    """)
    
    # Create indexes for faster lookups
    db.engine.execute("""
        CREATE INDEX IF NOT EXISTS idx_users_username
        ON users (username)
    """)
    
    db.engine.execute("""
        CREATE INDEX IF NOT EXISTS idx_users_email
        ON users (email)
    """)
    
    db.engine.execute("""
        CREATE INDEX IF NOT EXISTS idx_api_keys_user_id
        ON api_keys (user_id)
    """)
    
    db.engine.execute("""
        CREATE INDEX IF NOT EXISTS idx_user_logs_user_id
        ON user_logs (user_id)
    """)
    
    # Check if we need to create a default admin user
    result = db.engine.execute("SELECT COUNT(*) FROM users WHERE role = 'admin'")
    admin_count = result.fetchone()[0]
    
    if admin_count == 0:
        # Create a default admin user with password 'admin'
        # In a real-world scenario, you would want to set this up through environment variables
        # or configuration settings
        from werkzeug.security import generate_password_hash
        admin_password_hash = generate_password_hash("admin")
        
        db.engine.execute(
            """
            INSERT INTO users (username, email, password_hash, role, is_active)
            VALUES (?, ?, ?, ?, ?)
            """,
            ("admin", "<EMAIL>", admin_password_hash, "admin", 1)
        )
        
        print("Created default admin user: username 'admin', password 'admin'")
        print("IMPORTANT: Change this password immediately after first login!")

def downgrade(db):
    """
    Revert the migration.
    
    Args:
        db: SQLAlchemy database instance
    """
    # Drop the tables in reverse order to respect foreign keys
    db.engine.execute("DROP TABLE IF EXISTS api_keys")
    db.engine.execute("DROP TABLE IF EXISTS user_logs")
    db.engine.execute("DROP TABLE IF EXISTS users")
