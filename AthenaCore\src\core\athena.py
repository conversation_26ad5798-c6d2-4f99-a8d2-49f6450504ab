# src/core/athena.py
import os
import sys
import json
import tempfile
import subprocess
import platform
import requests
import logging
import tiktoken

# Import models directly instead of the deprecated login module
from flask_login import current_user
from src.models import DirectConnection
from src.models.llm_provider_setting import LLMProviderSetting
from .commands import get_command
from .state_manager import StateManager
from .command_processor import CommandProcessor
from src.utils.config import AthenaConfig

# Import the MCP components
from src.mcp.smithery_client import SmitheryClient
from src.mcp.athena_mcp import AthenaMCP

# print("Athena: Not using environment OPENAI_API_KEY.")
IS_WINDOWS = platform.system().lower() == "windows"
print(f"Running on {platform.system()} system")


class Athena:
    """
    Main Athena AI assistant class that handles conversation, command execution,
    and memory. This version dynamically updates its system prompt to reflect
    the direct connection model selected by the user, and integrates MCP servers.
    """

    def __init__(self):
        # Initialize optional modules
        self.smithery_available = False
        self.mcp_available = False

        try:
            import smithery

            self.smithery_available = True
        except ImportError:
            pass

        try:
            import mcp

            self.mcp_available = True
        except ImportError:
            pass

        # Initialize logger
        self.logger = logging.getLogger("athena")
        self.logger.setLevel(logging.INFO)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

        self.state_manager = StateManager()
        self.conversation_history = []
        # Track last conversation to reset history on new sessions
        self.last_conversation_id = None
        self.pending_memory = None
        self.config = None  # Will be loaded on first use
        self.command_processor = CommandProcessor()
        # Default system prompt text (will be updated dynamically based on connection)
        self.default_system_prompt = f"""You are Athena, a helpful AI assistant running on {platform.system()}.
You can handle commands, code, and manage conversation memory.
"""
        # Store the last used direct connection model string and its details.
        self.last_direct_model = None
        self.last_direct_info = None
        # User model preferences dictionary keyed by user_id
        self.user_model_preferences = {}

        # MCP integration components
        self.mcp_client = None
        self.mcp_instance = None
        self.mcp_initialized = False
        self.active_mcp_connection = None

        # Load configuration from database
        self._load_config()

        print("Athena initialized with system prompt")

    def _load_config(self):
        """Load configuration from database."""
        try:
            from src.utils.config import AthenaConfig
            self.config = AthenaConfig.load()
            self.logger.info("Configuration loaded successfully from database")
        except Exception as e:
            self.logger.warning(f"Failed to load configuration from database: {e}")
            # Create a minimal config object as fallback
            from src.utils.config import AthenaConfig
            self.config = AthenaConfig()

    def _get_api_key_for_model(self, model=None, user_id=None):
        """
        Get API key for a specific model from DirectConnections or Configuration.

        Args:
            model: The model name to find an API key for
            user_id: The user ID to get connections for

        Returns:
            tuple: (api_key, connection_url) or (None, None) if not found
        """
        api_key = None
        connection_url = None

        # Try to get from DirectConnections first (preferred method)
        try:
            # Import here to avoid circular imports
            from flask import current_app
            from flask_login import current_user

            # Try to get user_id from current_user if not provided
            if user_id is None:
                try:
                    if hasattr(current_user, 'id') and current_user.is_authenticated:
                        user_id = current_user.id
                        self.logger.debug(f"Using current user ID: {user_id}")
                except Exception:
                    pass

            if user_id:
                # Use Flask app context to access database
                with current_app.app_context():
                    from src.models.direct_connection import DirectConnection

                    # Get enabled connections for the user
                    connections = DirectConnection.query.filter_by(
                        user_id=user_id,
                        enabled=True
                    ).all()

                    self.logger.debug(f"Found {len(connections)} enabled connections for user {user_id}")

                    # Look for a connection that supports the requested model
                    for conn in connections:
                        if conn.api_key:
                            # Check if this connection supports the model
                            if model and conn.model_ids:
                                supported_models = [m.strip() for m in conn.model_ids.split(',')]
                                if model in supported_models:
                                    self.logger.info(f"Found API key in DirectConnection '{conn.name}' for model '{model}'")
                                    return conn.api_key, conn.url
                            else:
                                # If no specific model requested, use first connection with API key
                                self.logger.info(f"Using API key from DirectConnection '{conn.name}'")
                                return conn.api_key, conn.url

                    # If no model-specific connection found, try any connection with an API key
                    for conn in connections:
                        if conn.api_key:
                            self.logger.info(f"Using fallback API key from DirectConnection '{conn.name}'")
                            return conn.api_key, conn.url

        except Exception as e:
            self.logger.debug(f"Error accessing DirectConnections: {e}")

        # Fallback to Configuration table
        try:
            from flask import current_app
            with current_app.app_context():
                from src.models.configuration import Configuration

                config_entry = Configuration.query.filter_by(key='openai_api_key').first()
                if config_entry and config_entry.value:
                    self.logger.info("Using API key from Configuration table")
                    return config_entry.value, "https://api.openai.com/v1/chat/completions"

        except Exception as e:
            self.logger.debug(f"Error accessing Configuration table: {e}")

        # Last resort: direct database access
        try:
            import sqlite3
            from pathlib import Path

            db_path = Path(__file__).resolve().parents[2] / "instance" / "athena.db"
            if db_path.exists():
                conn = sqlite3.connect(str(db_path))
                cursor = conn.cursor()

                # Try DirectConnections first
                if user_id:
                    cursor.execute("""
                        SELECT api_key, url FROM direct_connections
                        WHERE user_id=? AND enabled=1 AND api_key IS NOT NULL AND api_key != ''
                        LIMIT 1
                    """, (user_id,))
                    result = cursor.fetchone()
                    if result:
                        api_key, connection_url = result
                        self.logger.info("Retrieved API key from DirectConnections via direct database access")
                        conn.close()
                        return api_key, connection_url

                # Try Configuration table
                cursor.execute("SELECT value FROM configurations WHERE key=?", ("openai_api_key",))
                result = cursor.fetchone()
                if result and result[0]:
                    api_key = result[0]
                    self.logger.info("Retrieved API key from Configuration table via direct database access")
                    conn.close()
                    return api_key, "https://api.openai.com/v1/chat/completions"

                conn.close()
        except Exception as e:
            self.logger.debug(f"Direct database access failed: {e}")

        return None, None

    def update_history(self, role: str, content: str):
        """
        Update conversation history with a new message.
        
        Args:
            role (str): The role of the message sender (e.g., 'user', 'assistant').
            content (str): The content of the message.
        """
        self.logger.info(f"Updating conversation history with {role} message")
        self.conversation_history.append({"role": role, "content": content})
        # Limit history size to prevent context overflow
        if len(self.conversation_history) > 20:
            self.conversation_history = self.conversation_history[-20:]
            
    def get_response(self, user_input, model=None, conversation_id=None, knowledge_context=None, **kwargs):
        """
        Generate a response based on user input and conversation context.

        Args:
            user_input (str): The user's message.
            model (str, optional): Model to use for response generation.
            conversation_id (str, optional): ID of the current conversation for context retrieval.
            knowledge_context (str, optional): Pre-formatted knowledge base context to include in the prompt.
            **kwargs: Additional keyword arguments.
                use_kb (bool): Whether to use knowledge base for context enhancement.

        Returns:
            tuple: (response_text, command, python_code)
        """
        try:
            # Extract use_kb flag from kwargs, default to True if not provided
            use_kb = kwargs.get('use_kb', True)
            self.logger.info(f"Using knowledge base: {use_kb}")
            
            # Prepare the system prompt with knowledge base context if available
            system_prompt = self.prepare_system_prompt(
                model, 
                user_input, 
                conversation_id, 
                knowledge_context=knowledge_context,
                use_kb=use_kb
            )
            self.logger.info(f"Generated system prompt with length {len(system_prompt)}")
            
            # Update conversation history with user message
            self.update_history("user", user_input)
            
            # Construct message array for OpenAI API
            messages = [
                {"role": "system", "content": system_prompt}
            ]
            
            # Add recent conversation history (last 10 messages)
            history = self.conversation_history[-10:] if len(self.conversation_history) > 10 else self.conversation_history
            messages.extend([{"role": msg["role"], "content": msg["content"]} for msg in history])
            
            # Get API key and connection URL for the specified model
            api_key, connection_url = self._get_api_key_for_model(model)

            if not api_key:
                self.logger.error("No API key found for the requested model")
                response_text = "Error: No API key configured. Please set up your API key in the DirectConnections settings."
                self.update_history("assistant", response_text)
                return response_text, None, None
            
            # Set up OpenAI client
            try:
                from openai import OpenAI

                # Create client using the newer API format
                # Use custom base URL if provided by DirectConnection
                client_kwargs = {"api_key": api_key}
                if connection_url and connection_url != "https://api.openai.com/v1/chat/completions":
                    # Extract base URL from the full endpoint URL
                    if "/chat/completions" in connection_url:
                        base_url = connection_url.replace("/chat/completions", "")
                        client_kwargs["base_url"] = base_url
                        self.logger.info(f"Using custom base URL: {base_url}")

                client = OpenAI(**client_kwargs)

                # Make API request
                self.logger.info(f"Making API request with model {model or 'gpt-3.5-turbo'}")
                response = client.chat.completions.create(
                    model=model or "gpt-3.5-turbo",
                    messages=messages,
                    temperature=0.7,
                    max_tokens=1500
                )
                
                # Extract response text
                response_text = response.choices[0].message.content
                self.logger.info(f"Received response from OpenAI with length {len(response_text)}")
                
                # Check for command execution
                command_result = None
                python_code = None
                
                # Extract command if present at the beginning
                if response_text.startswith("/"):
                    command_line = response_text.split("\n")[0].strip()
                    command_name = command_line.split()[0][1:]
                    self.logger.info(f"Detected command: {command_name}")
                    
                    command = get_command(command_name)
                    if command:
                        args = command_line.split()[1:] if len(command_line.split()) > 1 else []
                        try:
                            command_result = command.execute(args, self)
                        except Exception as e:
                            self.logger.error(f"Command execution error: {str(e)}")
                            command_result = f"Error executing command: {str(e)}"
                
                # Extract Python code if present
                if "```python" in response_text:
                    code_blocks = response_text.split("```python")[1:]
                    for block in code_blocks:
                        if "```" in block:
                            python_code = block.split("```")[0].strip()
                            break
                
                # Update conversation history with assistant response
                self.update_history("assistant", response_text)
                
                return response_text, command_result, python_code
                
            except ImportError:
                self.logger.error("OpenAI package not installed")
                response_text = "Error: OpenAI package not installed. Please install it using 'pip install openai'."
            except Exception as e:
                self.logger.error(f"OpenAI API error: {str(e)}")
                response_text = f"Error communicating with OpenAI API: {str(e)}"
        
        except Exception as e:
            self.logger.error(f"Error in get_response: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            response_text = f"An error occurred while processing your request: {str(e)}"
        
        # Update history with any error message
        self.update_history("assistant", response_text)
        return response_text, None, None
        
    def prepare_system_prompt(self, model=None, user_input=None, conversation_id=None, knowledge_context=None, use_kb=True):
        """
        Prepare the dynamic system prompt based on the current state and selected model.
        
        Args:
            model (str, optional): Model to use for prompting.
            user_input (str, optional): The message from the user for context enhancement.
            conversation_id (str, optional): ID of the current conversation for context retrieval.
            knowledge_context (str, optional): Knowledge base context to include in the prompt.
            use_kb (bool, optional): Whether to use knowledge base for context enhancement.
        
        Returns:
            str: The final system prompt.
        """
        # Start with the default template
        current_system_prompt = self.default_system_prompt
        
        # Include knowledge context if provided directly - this takes priority
        if knowledge_context:
            self.logger.info(f"Adding provided knowledge context to system prompt (length: {len(knowledge_context)})")
            current_system_prompt += "\n\n" + knowledge_context
            # Return early since we already have the knowledge context
            return current_system_prompt.strip()
        
        # Only try to fetch KB context if not already provided and use_kb is True
        if user_input and conversation_id and use_kb and not knowledge_context:
            try:
                # Try to use the knowledge database directly first
                try:
                    from src.core.knowledge_db import AthenaKnowledgeDB
                    
                    kb = AthenaKnowledgeDB()
                    # Query the knowledge base for relevant documents
                    docs = kb.query_knowledge(user_input, limit=3)
                    
                    if docs and len(docs) > 0:
                        kb_context = "\n\nRelevant information from Knowledge Base:\n\n"
                        
                        for i, doc in enumerate(docs):
                            if isinstance(doc, dict) and 'content' in doc:
                                content = doc['content']
                                metadata = doc.get('metadata', {})
                                title = metadata.get('title', f"Document {i+1}")
                                kb_context += f"---DOCUMENT: {title}---\n{content}\n\n"
                            elif hasattr(doc, 'page_content'):
                                # Support for langchain document format
                                kb_context += f"---DOCUMENT {i+1}---\n{doc.page_content}\n\n"
                            else:
                                # If it's just a string
                                kb_context += f"---DOCUMENT {i+1}---\n{str(doc)}\n\n"
                        
                        kb_context += "\nUse the above information to answer the user's question. If the information doesn't directly answer their question, acknowledge that and provide the most relevant information you can."
                        
                        current_system_prompt += kb_context
                        self.logger.info(f"Enhanced prompt with {len(docs)} knowledge base documents")
                except ImportError:
                    self.logger.warning("Knowledge DB module not available, falling back to document content method")
                    # Fall back to document content method
                    try:
                        from src.core.kb_context import get_document_content
                        
                        # Try to get relevant document for this query
                        doc = get_document_content(conversation_id, user_input)
                        if doc and doc.get("content"):
                            title = doc.get("metadata", {}).get("title", doc.get("title", ""))
                            content = doc["content"]
                            doc_context = (
                                f'\n\nDocument "{title}" content:\n\n'
                                f"---BEGIN DOCUMENT CONTENT---\n{content}\n"
                                f"---END DOCUMENT CONTENT---\n\n"
                                "Use this to answer the user's question."
                            )
                            current_system_prompt += doc_context
                            self.logger.info(f"Enhanced prompt with document '{title}'")
                    except ImportError:
                        self.logger.warning("KB context module not available")
                    except Exception as e:
                        self.logger.error(f"Error retrieving KB context: {e}")
            except Exception as e:
                self.logger.error(f"Error enhancing prompt with KB context: {e}")
                import traceback
                self.logger.error(traceback.format_exc())
        
        return current_system_prompt.strip()

        # Force MCP initialization immediately
        try:
            print("\n=== FORCE INITIALIZING MCP SYSTEM ===\n")
            # Check for required packages
            try:
                import smithery

                print("✓ Successfully imported smithery package")
            except ImportError as e:
                print(f"✗ Failed to import smithery package: {e}")

            try:
                import mcp

                print("✓ Successfully imported mcp package")
            except ImportError as e:
                print(f"✗ Failed to import mcp package: {e}")

            # Database-driven approach to getting the Smithery API key

            # 1. Try getting config from AthenaConfig (which loads from database)
            config = AthenaConfig.load()
            smithery_api_key = config.smithery_api_key

            # 2. If not found in config, try DirectConnections as fallback
            if not smithery_api_key:
                try:
                    from flask import current_app
                    from flask_login import current_user

                    # Try to get user_id from current_user if available
                    user_id = None
                    try:
                        if hasattr(current_user, 'id') and current_user.is_authenticated:
                            user_id = current_user.id
                    except Exception:
                        pass

                    if user_id and current_app:
                        with current_app.app_context():
                            from src.models.direct_connection import DirectConnection

                            # Look for a connection that might have Smithery API key
                            connection = DirectConnection.query.filter_by(
                                user_id=user_id,
                                enabled=True
                            ).filter(DirectConnection.api_key.isnot(None)).first()

                            if connection and connection.api_key:
                                # Note: This is a fallback - Smithery keys should ideally be in Configuration table
                                print("Using API key from DirectConnection as Smithery fallback")
                except Exception as e:
                    print(f"Could not access DirectConnections for Smithery key: {e}")
            
            # 3. As a last resort, try direct database access
            if not smithery_api_key:
                try:
                    # Try direct SQLite access without Flask
                    import sqlite3
                    from pathlib import Path
                    
                    # Find the database file
                    db_path = Path(__file__).resolve().parents[2] / "athena.db"
                    if db_path.exists():
                        conn = sqlite3.connect(str(db_path))
                        cursor = conn.cursor()
                        cursor.execute("SELECT value FROM config_entries WHERE key=? AND user_id IS NULL", ("smithery_api_key",))
                        result = cursor.fetchone()
                        if result and result[0]:
                            smithery_api_key = result[0]
                            print("Retrieved Smithery API key directly from SQLite database")
                        conn.close()
                except Exception as e:
                    print(f"Note: Direct SQLite access for Smithery API key failed: {e}")
                    
            # If we found a key, make sure it's also available in the environment
            if smithery_api_key:
                os.environ["SMITHERY_API_KEY"] = smithery_api_key
            
            if smithery_api_key:
                # Only show the first 6 and last 4 characters of the API key for security
                display_key = f"{smithery_api_key[:6]}...{smithery_api_key[-4:]}"
                # Since we prioritize database values in config.py, determine the source based on config settings
                source = "DB config"
                print(f"Using Smithery API key from {source}: {display_key}")
                registry_url = config.mcp_registry_url
                print(f"Initializing Smithery client with registry URL: {registry_url}")
                self.mcp_client = SmitheryClient(api_key=smithery_api_key, base_url=registry_url)
                print("✓ Created SmitheryClient instance")
                self.mcp_instance = AthenaMCP(self.mcp_client)
                print("✓ Created AthenaMCP instance")
                self.mcp_initialized = True
                print("✓ MCP system successfully initialized and ready to use")
            else:
                print("✗ No Smithery API key found in DB config")
        except Exception as e:
            print(f"✗ Error during forced MCP initialization: {e}")
            import traceback

            traceback.print_exc()

    # ... (all other methods unchanged) ...

    def build_prompt(self, conversation_id, msg, msg_history=None, kb_context=None):
        """
        Build a prompt for the GPT model with knowledge base context integration.
        """
        base_system = (
            "You are Athena, an AI digital assistant, providing helpful and concise information across various topics. "
            "For any questions outside your knowledge base, acknowledge your limitations and advise when professional services would be more appropriate. "
            "Break your responses into clear sections when appropriate. Maintain brevity when possible while still being informative."
        )
        # Include KB context if available
        kb_system = ""
        if kb_context and len(kb_context) > 0:
            self.logger.info(f"Including {len(kb_context)} document chunks in prompt")
            kb_system = "\n\nReference information: \n"
            
            # Track what content is actually included in the prompt
            included_content = []
            
            for doc in kb_context:
                if 'content' in doc and doc['content']:
                    content = doc['content']
                    # Ensure content is a string
                    if not isinstance(content, str):
                        content = str(content)
                    
                    # Truncate very long content to avoid token limits
                    if len(content) > 2000:
                        content = content[:2000] + "...(content truncated)"
                    
                    # Add metadata when available to provide source context
                    metadata_str = ""
                    if 'metadata' in doc and doc['metadata']:
                        metadata = doc['metadata']
                        if isinstance(metadata, dict):
                            if 'source' in metadata:
                                metadata_str = f" (Source: {metadata['source']})"
                            elif 'filename' in metadata:
                                metadata_str = f" (Source: {metadata['filename']})"
                    
                    # Add formatted content to KB context
                    kb_system += f"\n--- Document{metadata_str} ---\n{content}\n"
                    included_content.append(content[:50] + "..." if len(content) > 50 else content)
                    
                    # Log that we're successfully adding this content
                    self.logger.debug(f"Added document to context, metadata: {metadata_str}, content length: {len(content)}")
                    
            # If we didn't get any actual content from the KB context, try fallback approaches
            if not included_content:
                self.logger.warning("KB context was provided but no document content was extracted")
                # Try fallback approach by directly extracting content from various possible keys
                for doc in kb_context:
                    if isinstance(doc, dict):
                        # Try all possible keys that might contain content
                        for key in ['content', 'text', 'document', 'documents', 'page_content']:
                            if key in doc and doc[key]:
                                content = doc[key]
                                if isinstance(content, list) and len(content) > 0:
                                    content = content[0]
                                if content:
                                    kb_system += f"\n--- Document (fallback extraction) ---\n{content}\n"
                                    self.logger.info(f"Added document using fallback method with key: {key}")
                                    included_content.append(content[:50] + "..." if len(content) > 50 else content)
                                    break
            
            # Log what's being included for debugging
            self.logger.debug(f"KB context preview: {kb_system[:200]}...")
            self.logger.info(f"Included document contents: {included_content}")
            
            # Add instruction to use KB context
            kb_system += "\n\nUse the above reference information to answer the user's question when relevant. Cite specific sources when possible."
        else:
            self.logger.info("No KB context available for inclusion in prompt")
        
        system_prompt = base_system + kb_system
        return system_prompt

    # ... (all other methods unchanged) ...
