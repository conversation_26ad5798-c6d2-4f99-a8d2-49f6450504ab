"""
Middleware utilities for Athena Core.

This module provides middleware functions for request processing, authentication,
and authorization throughout the application.
"""

import functools
from flask import request, current_app, g, abort
import jwt
from typing import Callable, Optional, Union, Any, Dict

from src.services.auth_service import AuthenticationService
from src.services.base_service import get_service
from src.utils.exceptions import AuthenticationError, AuthorizationError


def require_auth(f: Callable) -> Callable:
    """
    Decorator to require authentication for a route.
    
    This middleware will check for a valid authentication token in the request
    and attach the authenticated user to the Flask global object.
    
    Args:
        f: The route function to decorate
        
    Returns:
        Decorated function
    """
    @functools.wraps(f)
    def decorated(*args, **kwargs):
        auth_service = get_service(AuthenticationService)
        success, user, error = auth_service.authenticate_request()
        
        if not success or not user:
            raise AuthenticationError(message=error or "Authentication required")
        
        # Attach the user to the Flask global object
        g.user = user
        
        return f(*args, **kwargs)
    
    return decorated


def require_api_key(f: Callable) -> Callable:
    """
    Decorator to require an API key for a route.
    
    This middleware will check for a valid API key in the request
    and attach the authenticated user to the Flask global object.
    
    Args:
        f: The route function to decorate
        
    Returns:
        Decorated function
    """
    @functools.wraps(f)
    def decorated(*args, **kwargs):
        # Get API key from header or query parameter
        api_key = request.headers.get('X-API-Key') or request.args.get('api_key')
        
        if not api_key:
            raise AuthenticationError(message="API key required")
        
        # Verify API key
        auth_service = get_service(AuthenticationService)
        user = auth_service.verify_api_key(api_key)
        
        if not user:
            raise AuthenticationError(message="Invalid API key")
        
        # Attach the user to the Flask global object
        g.user = user
        
        return f(*args, **kwargs)
    
    return decorated


def require_role(*roles: str) -> Callable:
    """
    Decorator to require specific role(s) for a route.
    
    This middleware will check if the authenticated user has the required role(s).
    This must be used in conjunction with require_auth or require_api_key.
    
    Args:
        *roles: Required role(s) for the route
        
    Returns:
        Decorator function
    """
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def decorated(*args, **kwargs):
            # Ensure user is authenticated
            if not hasattr(g, 'user'):
                raise AuthenticationError(message="Authentication required")
            
            # Check if user has required role
            user_role = getattr(g.user, 'role', None)
            
            if not user_role or user_role not in roles:
                # Always allow admin role
                if user_role != 'admin':
                    raise AuthorizationError(
                        message=f"Role {user_role} is not authorized. Required: {', '.join(roles)}"
                    )
            
            return f(*args, **kwargs)
        
        return decorated
    
    return decorator


def api_route(bp, *args, **kwargs):
    """
    Decorator for API routes that handles standardized API responses.
    
    This decorator wraps a route function to ensure that it returns a standardized
    API response format, even if the route function doesn't return the expected format.
    
    Args:
        bp: Blueprint to register the route with
        *args: Arguments to pass to the blueprint route decorator
        **kwargs: Keyword arguments to pass to the blueprint route decorator
        
    Returns:
        Decorator function
    """
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def decorated(*args, **kwargs):
            # Call the route function
            response = f(*args, **kwargs)
            
            # If the response is already a tuple with status code, return as is
            if isinstance(response, tuple) and len(response) == 2:
                return response
            
            # If the response is already a Response object, return as is
            from flask import Response
            if isinstance(response, Response):
                return response
            
            # Otherwise, wrap the response in a standard format
            from src.utils.api_response import success_response
            
            if isinstance(response, dict) and 'data' in response:
                # If the response is already in our standard format, just return it
                return success_response(**response)
            
            # Wrap the response in our standard format
            return success_response(data=response)
        
        # Register the decorated function with the blueprint
        endpoint = kwargs.pop('endpoint', None)
        bp.route(*args, **kwargs, endpoint=endpoint)(decorated)
        
        return decorated
    
    return decorator
