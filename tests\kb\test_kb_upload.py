#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
test_kb_upload.py - Test script for directly adding documents to the knowledge base
"""

import os
import sys
import json
import logging
import requests
import traceback
from datetime import datetime

# Add the project directory to path to enable imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('kb_test')

def mask_api_key(key):
    """Mask API key for logging."""
    if not key:
        return None
    return key[:4] + "***" + key[-4:] if len(key) > 10 else "***"

def get_api_key():
    """Get the OpenAI API key from available sources."""
    # First from command line
    if len(sys.argv) > 1:
        key = sys.argv[1].strip()
        logger.info(f"Using API key from command line: {mask_api_key(key)}")
        return key
    
    # Next try from config.json
    try:
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "AthenaCore", "config.json")
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = json.load(f)
                key = config.get("openai_api_key")
                if key:
                    logger.info(f"Using API key from config.json: {mask_api_key(key)}")
                    return key
    except Exception as e:
        logger.error(f"Error reading config.json: {e}")
    
    # Try from environment variables
    key = os.environ.get("OPENAI_API_KEY")
    if key:
        logger.info(f"Using API key from environment: {mask_api_key(key)}")
        return key
    
    logger.error("No API key found. Please provide an API key as command line argument.")
    return None

def get_embedding(text, api_key):
    """Generate embedding directly via OpenAI API."""
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    payload = {
        "input": text[:8000],  # Limit to 8K tokens for safety
        "model": "text-embedding-ada-002"  # Use stable model
    }
    
    logger.info("Generating embedding via direct API call...")
    try:
        response = requests.post(
            "https://api.openai.com/v1/embeddings",
            headers=headers,
            data=json.dumps(payload),
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            embedding = data["data"][0]["embedding"]
            logger.info(f"Successfully generated embedding of length {len(embedding)}")
            return embedding
        else:
            logger.error(f"API error: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        logger.error(f"Error generating embedding: {e}")
        logger.error(traceback.format_exc())
        return None

def test_chromadb_direct():
    """Test ChromaDB directly."""
    try:
        import chromadb
        from chromadb.config import Settings
        
        logger.info("Creating ChromaDB client...")
        
        # Use same settings as in the KnowledgeDatabase
        persist_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "AthenaCore", "data", "chroma")
        client = chromadb.PersistentClient(path=persist_dir)
        
        logger.info(f"ChromaDB version: {getattr(chromadb, '__version__', 'unknown')}")
        logger.info(f"Using persistence directory: {persist_dir}")
        
        # Get or create collection
        collection_name = "athena_knowledge_base"
        logger.info(f"Getting or creating collection: {collection_name}")
        
        # Create metadata
        meta = {
            "description": "Athena knowledge base",
            "created_at": datetime.utcnow().isoformat(),
            "hnsw:space": "cosine",
        }
        
        try:
            # Try to get existing collection first
            collection = client.get_collection(collection_name)
            logger.info(f"Found existing collection: {collection_name}")
            count = collection.count()
            logger.info(f"Collection has {count} documents")
            return collection
        except Exception:
            # Collection doesn't exist, create it
            logger.info(f"Creating new collection: {collection_name}")
            collection = client.create_collection(name=collection_name, metadata=meta)
            logger.info(f"Created new collection: {collection_name}")
            return collection
    except Exception as e:
        logger.error(f"Error working with ChromaDB: {e}")
        logger.error(traceback.format_exc())
        return None

def add_test_document(collection, api_key):
    """Add a test document to the collection."""
    if not collection:
        logger.error("No collection available")
        return False
    
    # Create a test document
    test_content = """# Test Document
    
This is a test document to verify the knowledge base functionality.
It contains some basic text content that will be used to generate embeddings.

- Item 1
- Item 2
- Item 3

The current date and time is: {datetime}
""".format(datetime=datetime.now().isoformat())

    # Create metadata
    metadata = {
        "title": "Test Document",
        "tags": ["test", "verification"],
        "type": "text",
        "source": "test_script",
        "timestamp": datetime.utcnow().isoformat()
    }
    
    # Generate ID
    import uuid
    doc_id = str(uuid.uuid4())
    
    # Generate embedding
    embedding = get_embedding(test_content, api_key)
    if not embedding:
        logger.error("Failed to generate embedding")
        return False
    
    # Try to add the document with various methods
    try:
        # Try upsert with pre-generated embedding first
        logger.info("Trying upsert method with pre-generated embedding")
        collection.upsert(
            documents=[test_content],
            metadatas=[metadata],
            ids=[doc_id],
            embeddings=[embedding]
        )
        logger.info("Successfully added document with upsert method")
        return True
    except Exception as e1:
        logger.warning(f"Upsert method failed: {e1}")
        
        # Try standard add method
        try:
            logger.info("Trying standard add method")
            collection.add(
                documents=[test_content],
                metadatas=[metadata],
                ids=[doc_id]
            )
            logger.info("Successfully added document with standard add method")
            return True
        except Exception as e2:
            logger.warning(f"Standard add method failed: {e2}")
            
            # Try with minimal metadata
            try:
                logger.info("Trying with minimal metadata")
                minimal_metadata = {
                    "title": "Test Document",
                    "timestamp": datetime.utcnow().isoformat()
                }
                collection.add(
                    documents=[test_content],
                    metadatas=[minimal_metadata],
                    ids=[doc_id]
                )
                logger.info("Successfully added document with minimal metadata")
                return True
            except Exception as e3:
                logger.error(f"All methods failed: {e3}")
                logger.error(traceback.format_exc())
                return False

def main():
    """Main function."""
    print("=" * 80)
    print("Athena Knowledge Base - Upload Test")
    print("=" * 80)
    
    # Get API key
    api_key = get_api_key()
    if not api_key:
        print("❌ No API key available. Please provide an API key.")
        return False
    
    # Test embedding generation
    print("\nTesting embedding generation...")
    sample_text = "This is a test of the embedding functionality."
    embedding = get_embedding(sample_text, api_key)
    if embedding:
        print(f"✅ Successfully generated embedding of length {len(embedding)}")
    else:
        print("❌ Failed to generate embedding")
        return False
    
    # Test ChromaDB
    print("\nTesting ChromaDB access...")
    collection = test_chromadb_direct()
    if collection:
        print(f"✅ Successfully connected to ChromaDB collection")
    else:
        print("❌ Failed to connect to ChromaDB")
        return False
    
    # Add test document
    print("\nTesting document addition...")
    success = add_test_document(collection, api_key)
    if success:
        print("✅ Successfully added test document to knowledge base")
    else:
        print("❌ Failed to add test document")
        return False
    
    print("\n" + "=" * 80)
    print("Test completed successfully!")
    print("=" * 80)
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("Operation cancelled by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Unhandled exception: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)
