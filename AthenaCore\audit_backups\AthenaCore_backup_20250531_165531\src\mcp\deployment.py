# Athena MCP Deployment Module

import logging
import os
import json
import base64
import asyncio
import tempfile
import zipfile
from typing import Dict, List, Any, Optional, Tuple

import httpx
from mcp.server.fastmcp import FastMCP

from src.mcp.server_creation import AthenaMCPServerFactory
from src.mcp.smithery_client import SmitheryClient

logger = logging.getLogger(__name__)


class MCPDeploymentManager:
    """Manages deployment of Athena MCP servers to Smithery."""
    
    def __init__(self, smithery_client: SmitheryClient):
        self.smithery_client = smithery_client
        self.deployment_status: Dict[str, Dict[str, Any]] = {}
    
    async def register_server(self, server: FastMCP) -> Dict[str, Any]:
        """Register a new MCP server with Smithery Registry."""
        try:
            # Prepare server metadata
            metadata = {
                "name": server.name,
                "description": server.description or "An Athena-generated MCP server",
                "version": server.version or "0.1.0",
                "dependencies": server.dependencies or [],
                "tags": ["athena", "ai-assistant"],
                "homepage": "https://github.com/OneLostHero/AthenaAgent_Core"
            }
            
            # Register server with Smithery
            response = await self.smithery_client.register_server(metadata)
            
            logger.info(f"Registered MCP server with Smithery: {server.name} - {response.get('qualified_name')}")
            return response
            
        except Exception as e:
            logger.error(f"Failed to register server with Smithery: {str(e)}")
            raise
    
    async def prepare_deployment_package(self, server: FastMCP) -> str:
        """Prepare deployment package for a server."""
        try:
            # Create temporary directory
            with tempfile.TemporaryDirectory() as temp_dir:
                # Create server.py
                server_path = os.path.join(temp_dir, "server.py")
                with open(server_path, "w") as f:
                    f.write(self._generate_server_code(server))
                
                # Create requirements.txt
                requirements_path = os.path.join(temp_dir, "requirements.txt")
                with open(requirements_path, "w") as f:
                    f.write("\n".join(server.dependencies))
                
                # Create README.md
                readme_path = os.path.join(temp_dir, "README.md")
                with open(readme_path, "w") as f:
                    f.write(f"# {server.name}\n\n{server.description}\n\n"
                             "Generated by Athena MCP Server Creator\n")
                
                # Create smithery.json
                smithery_path = os.path.join(temp_dir, "smithery.json")
                with open(smithery_path, "w") as f:
                    smithery_config = {
                        "name": server.name,
                        "version": server.version,
                        "build": {
                            "entrypoint": "server.py"
                        }
                    }
                    json.dump(smithery_config, f, indent=2)
                
                # Create zip file
                zip_path = os.path.join(temp_dir, "deployment.zip")
                with zipfile.ZipFile(zip_path, "w") as zipf:
                    for filename in ["server.py", "requirements.txt", "README.md", "smithery.json"]:
                        zipf.write(os.path.join(temp_dir, filename), filename)
                
                # Read zip file contents
                with open(zip_path, "rb") as f:
                    zip_contents = f.read()
                
                # Return base64-encoded zip
                return base64.b64encode(zip_contents).decode("utf-8")
                
        except Exception as e:
            logger.error(f"Failed to prepare deployment package: {str(e)}")
            raise
    
    def _generate_server_code(self, server: FastMCP) -> str:
        """Generate server.py code for the deployment package."""
        # This is a simplified version - actual implementation would be more complex
        # and would need to recreate all the resource/tool definitions
        
        code = [
            "# Athena-generated MCP Server\n",
            "import json\n",
            "import logging\n",
            "import asyncio\n",
            "from typing import Dict, List, Any, Optional\n\n",
            "from mcp.server.fastmcp import FastMCP, Context\n\n",
            f"# Create server\nserver = FastMCP(\"{server.name}\", description=\"{server.description}\", version=\"{server.version}\")\n\n"
        ]
        
        # Add tool definitions
        for tool in server.tools:
            tool_params = ", ".join([f"{p.name}: {p.annotation.__name__}" for p in tool.parameters])
            return_type = tool.return_annotation.__name__
            
            code.append(f"@server.tool()\n")
            code.append(f"async def {tool.name}({tool_params}, ctx: Context) -> {return_type}:\n")
            code.append(f"    \"\"{tool.description or ''}\"\"\n")
            # Add simplified implementation
            code.append(f"    # Implementation generated by Athena\n")
            code.append(f"    # Add your custom implementation here\n")
            code.append(f"    return \"Response from {tool.name}\"\n\n")
        
        # Add resource definitions
        for resource in server.resources:
            code.append(f"@server.resource(\"{resource.uri_template}\")\n")
            code.append(f"async def resource_{resource.name}(ctx: Context) -> Dict[str, Any]:\n")
            code.append(f"    \"\"{resource.description or ''}\"\"\n")
            # Add simplified implementation
            code.append(f"    # Implementation generated by Athena\n")
            code.append(f"    return {{\"data\": \"Resource data from {resource.uri_template}\"}}\n\n")
        
        # Add main block
        code.append("if __name__ == \"__main__\":\n")
        code.append("    server.run()\n")
        
        return "".join(code)
    
    async def deploy_to_smithery(self, server: FastMCP, qualified_name: str) -> Dict[str, Any]:
        """Deploy an MCP server to Smithery."""
        try:
            # Prepare deployment package
            deployment_package = await self.prepare_deployment_package(server)
            
            # Submit deployment to Smithery
            response = await self.smithery_client.deploy_server(
                qualified_name=qualified_name,
                deployment_package=deployment_package
            )
            
            # Store deployment status
            self.deployment_status[server.name] = {
                "qualified_name": qualified_name,
                "deployment_id": response.get("deployment_id"),
                "status": response.get("status"),
                "url": response.get("url")
            }
            
            logger.info(f"Deployed MCP server to Smithery: {server.name} - {qualified_name}")
            return response
            
        except Exception as e:
            logger.error(f"Failed to deploy server to Smithery: {str(e)}")
            raise
    
    async def check_deployment_status(self, server_name: str) -> Dict[str, Any]:
        """Check deployment status for a server."""
        if server_name not in self.deployment_status:
            logger.warning(f"No deployment information found for server: {server_name}")
            return {"status": "unknown"}
        
        deployment_info = self.deployment_status[server_name]
        qualified_name = deployment_info.get("qualified_name")
        
        try:
            # Get server status from Smithery
            status = await self.smithery_client.get_server_status(qualified_name)
            
            # Update deployment status
            self.deployment_status[server_name]["status"] = status.get("status")
            
            return status
            
        except Exception as e:
            logger.error(f"Failed to check deployment status: {str(e)}")
            return {"status": "error", "message": str(e)}


async def deploy_server(server: FastMCP, smithery_client: SmitheryClient) -> Tuple[bool, str]:
    """Deploy an MCP server to Smithery (convenience function)."""
    deployment_manager = MCPDeploymentManager(smithery_client)
    
    try:
        # Register server with Smithery
        registration = await deployment_manager.register_server(server)
        qualified_name = registration.get("qualified_name")
        
        if not qualified_name:
            return False, "Failed to register server: No qualified name returned"
        
        # Deploy server
        deployment = await deployment_manager.deploy_to_smithery(server, qualified_name)
        
        # Return success with URL
        if "url" in deployment:
            return True, f"Successfully deployed server to {deployment['url']}"
        else:
            return True, f"Successfully deployed server with ID {deployment.get('deployment_id')}"
            
    except Exception as e:
        logger.error(f"Server deployment failed: {str(e)}")
        return False, f"Deployment failed: {str(e)}"
