"""
Knowledge Base Routes for Athena
Handles document management and retrieval for the knowledge base
"""

import os
import threading, uuid
from datetime import datetime
import traceback
from flask import Blueprint, render_template, request, jsonify, current_app
from werkzeug.utils import secure_filename
from flask_login import login_required, current_user
from src.core.knowledge_db import kb
from src.utils.config import AthenaConfig

knowledge_bp = Blueprint('knowledge', __name__)

@knowledge_bp.route('/knowledge')
@login_required
def knowledge_home():
    """Render the knowledge base homepage"""
    return render_template('knowledge.html')

def get_documents_api(filters=None):
    """Utility function to get documents from the knowledge base
    
    Args:
        filters (dict, optional): Filters to apply to the document search
        
    Returns:
        list: List of document objects
    """
    try:
        # Use provided filters or empty dict
        search_filters = filters or {}
        
        # Fetch documents
        documents = kb.search_documents(search_filters)
        
        # Also check in-memory storage if ChromaDB fails
        if not documents and hasattr(kb, '_documents') and kb._documents:
            if search_filters:
                # Apply filters to in-memory documents
                filtered_docs = []
                for doc in kb._documents:
                    matches = True
                    for key, value in search_filters.items():
                        if key == 'tags' and isinstance(value, str):
                            if value not in doc.get('tags', []):
                                matches = False
                                break
                        elif doc.get(key) != value:
                            matches = False
                            break
                    if matches:
                        filtered_docs.append(doc)
                documents = filtered_docs
            else:
                # No filters, return all documents
                documents = kb._documents
        
        # Log the response for debugging
        if hasattr(current_app, 'logger'):
            current_app.logger.info(f"Retrieved {len(documents)} documents from knowledge base")
        else:
            print(f"Retrieved {len(documents)} documents from knowledge base")
            
        return documents
    except Exception as e:
        if hasattr(current_app, 'logger'):
            current_app.logger.error(f"Error retrieving documents: {e}")
        else:
            print(f"Error retrieving documents: {e}")
        return []

@knowledge_bp.route('/api/knowledge/documents', methods=['GET'])
@login_required
def get_documents():
    """Get all documents from the knowledge base"""
    try:
        # Check if user is an admin (role-based check)
        is_admin = hasattr(current_user, 'role') and current_user.role == 'admin'
        
        # Determine whether to include system documents
        include_system = request.args.get('include_system', 'false').lower() == 'true'
        if is_admin:
            include_system = True  # Admins always see system documents
        
        # Start with base filters
        if is_admin and request.args.get('all_users', 'false').lower() == 'true':
            # Admin requesting all documents across users
            filters = {}
        else:
            # Filter by current user's ID
            filters = {'user_id': current_user.id}
            
            # If including system documents, we'll merge them later
            if include_system:
                # Note: We'll fetch system documents separately
                pass
                
        # Add any additional filters from query parameters
        if request.args.get('type'):
            filters['type'] = request.args.get('type')
        if request.args.get('tag'):
            filters['tags'] = request.args.get('tag')
        
        # Get the user's documents
        user_documents = get_documents_api(filters)
        
        # Get system documents if needed
        system_documents = []
        if include_system and not is_admin:
            system_filters = {'user_id': 'system'}
            if request.args.get('type'):
                system_filters['type'] = request.args.get('type')
            if request.args.get('tag'):
                system_filters['tags'] = request.args.get('tag')
            system_documents = get_documents_api(system_filters)
        
        # Combine documents
        documents = user_documents + system_documents
        
        # Log the response for debugging
        current_app.logger.info(f"Returning {len(documents)} documents (user: {len(user_documents)}, system: {len(system_documents)})")
        
        return jsonify({
            'success': True,
            'documents': documents,
            'is_admin': is_admin
        })
    except Exception as e:
        current_app.logger.error(f"Error retrieving documents: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@knowledge_bp.route('/api/knowledge/diagnostics', methods=['GET', 'POST'])
def run_diagnostics():
    """Run diagnostics on the knowledge base and fix any issues found
    
    GET: Returns diagnostic information about the knowledge base
    POST: Attempts to repair issues with the knowledge base
    """
    try:
        # Gather diagnostic information
        diagnostics = {
            "timestamp": datetime.now().isoformat(),
            "chromadb": {},
            "is_persistent": getattr(kb, 'is_persistent', False),
            "status": "healthy"
        }
        
        # Check ChromaDB configuration
        config = AthenaConfig.load()
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        chroma_persist_dir = os.path.join(base_dir, config.CHROMA_PERSIST_DIR)
        diagnostics["chromadb"]["persist_dir"] = chroma_persist_dir
        diagnostics["chromadb"]["dir_exists"] = os.path.exists(chroma_persist_dir)
        diagnostics["chromadb"]["is_writable"] = os.access(chroma_persist_dir, os.W_OK) if os.path.exists(chroma_persist_dir) else False
        diagnostics["chromadb"]["api_implementation"] = config.CHROMA_API_IMPL
        
        # Check for Chroma content
        if os.path.exists(chroma_persist_dir):
            chroma_files = os.listdir(chroma_persist_dir)
            diagnostics["chromadb"]["files"] = chroma_files
            diagnostics["chromadb"]["has_data_files"] = any(f.endswith('.sqlite3') for f in chroma_files)
        else:
            diagnostics["chromadb"]["files"] = []
            diagnostics["chromadb"]["has_data_files"] = False
            diagnostics["status"] = "dir_missing"
        
        # Check collection status
        diagnostics["collection"] = {
            "initialized": kb.collection is not None,
            "name": getattr(kb.collection, "name", None)
        }
        
        if kb.collection:
            try:
                count = kb.collection.count()
                diagnostics["collection"]["document_count"] = count
                if count == 0:
                    diagnostics["status"] = "empty_collection"
            except Exception as e:
                diagnostics["collection"]["error"] = str(e)
                diagnostics["collection"]["document_count"] = 0
                diagnostics["status"] = "collection_error"
        else:
            diagnostics["status"] = "no_collection"
        
        # Check in-memory storage
        in_memory_docs = []
        if hasattr(kb, '_documents'):
            in_memory_docs = kb._documents
        diagnostics["memory_storage"] = {
            "initialized": hasattr(kb, '_documents'),
            "document_count": len(in_memory_docs)
        }
        
        # Check if using memory-only mode accidentally
        if not diagnostics["is_persistent"] and diagnostics["collection"]["initialized"]:
            diagnostics["status"] = "memory_only"
            current_app.logger.warning("Knowledge base is running in memory-only mode - documents will not persist!")
        
        # If this is a POST request, attempt repairs based on the diagnostics
        if request.method == 'POST':
            repair_actions = []
            
            # Create directory if missing
            if not os.path.exists(chroma_persist_dir):
                try:
                    os.makedirs(chroma_persist_dir, exist_ok=True)
                    repair_actions.append(f"Created directory: {chroma_persist_dir}")
                except Exception as dir_err:
                    repair_actions.append(f"Failed to create directory: {str(dir_err)}") 
            
            # Check config and recommend changes
            config_recommendations = []
            if config.CHROMA_API_IMPL != "persistent":
                config_recommendations.append("Change CHROMA_API_IMPL to 'persistent' in config.json")
            
            # Fix Persistence Issue
            if not diagnostics.get("is_persistent", False):
                repair_actions.append("Attempting to fix persistence issue...")
                
                # Use the existing global 'kb' instance
                if not kb.is_persistent:
                    try:
                        # Re-attempt initialization logic within the existing instance if possible
                        # (This part might need a dedicated reinit method in KnowledgeDatabase)
                        # For now, we just log that we would attempt re-init on 'kb'
                        kb.logger.warning("Diagnostics: Would attempt re-initialization on existing kb instance if a method was available.")
                        # We can't easily replicate the complex init here. 
                        # Let's assume the initial load should have worked or needs manual config fix.
                        repair_actions.append("⚠️ Persistence fix requires manual check or restart. Diagnostics cannot automatically re-init the global instance safely.")

                    except Exception as e:
                        repair_actions.append(f"❌ Error attempting to fix persistence: {str(e)}")
                else:
                     repair_actions.append("✅ Knowledge base instance reports it is already persistent.")


            # Fix Collection Initialization
            if not diagnostics.get("collection", {}).get("initialized", False):
                repair_actions.append("Attempting to fix collection initialization...")
                # Use the existing global 'kb' instance
                try:
                    if not kb.collection or not hasattr(kb.collection, 'name'):
                         # Attempt to get/create collection using the existing client and embedding function
                         kb.collection = kb._get_collection() # Use the internal method to re-attempt
                         if kb.collection and hasattr(kb.collection, 'name'):
                             repair_actions.append("✅ Successfully re-initialized collection on existing instance.")
                         else:
                             repair_actions.append("❌ Failed to re-initialize collection on existing instance.")
                    else:
                        repair_actions.append("✅ Collection appears to be initialized.")
                        
                    # Refresh diagnostics for collection
                    if kb.collection:
                       diagnostics["collection"]["initialized"] = True
                       diagnostics["collection"]["name"] = kb.collection.name
                       try:
                           count = kb.collection.count()
                           diagnostics["collection"]["document_count"] = count
                       except Exception as e:
                           diagnostics["collection"]["error"] = f"Count failed after re-init: {str(e)}"
                    else:
                       diagnostics["collection"]["initialized"] = False
                       diagnostics["collection"]["error"] = "Failed to get collection after fix attempt."
                       
                except Exception as e:
                    repair_actions.append(f"❌ Error attempting to fix collection: {str(e)}")

            # Re-fetch diagnostics after attempting fixes
            diagnostics = kb.run_diagnostics() # Assuming run_diagnostics is a method on kb
            diagnostics["repair_actions"] = repair_actions # Add repair actions to output

            return jsonify(diagnostics)
    except Exception as e:
        current_app.logger.error(f"Error running knowledge base diagnostics: {e}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc()
        }), 500

@knowledge_bp.route('/api/knowledge/documents', methods=['POST'])
@login_required
def add_document():
    """Add a document to the knowledge base"""
    try:
        current_app.logger.info(f"Request data: {request.form}, Files: {request.files}")
        
        # Handle file upload
        if 'file' in request.files:
            file = request.files['file']
            current_app.logger.info(f"Processing file upload: {file.filename if file else 'No filename'}")
            if file and file.filename:
                # Get metadata
                title = request.form.get('title', file.filename)
                tags = request.form.get('tags', '').split(',') if request.form.get('tags') else []
                
                # Determine file type and secure the filename
                filename = secure_filename(file.filename)
                
                # Create a temporary file to save the uploaded file
                import tempfile
                with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                    file.save(temp_file.name)
                    temp_filepath = temp_file.name
                
                try:
                    # Get the document type
                    from src.utils.document_processor import get_document_type
                    doc_type = get_document_type(filename)
                    
                    # Initialize basic metadata
                    metadata = {
                        'title': title,
                        'tags': tags,
                        'type': doc_type,
                        'source': 'file',
                        'filename': filename,
                        'file_path': temp_filepath,
                        'user_id': current_user.id  # Add user ID for proper user separation
                    }
                    
                    # For PDFs and binary files, store a placeholder and keep the binary content
                    if doc_type in ['pdf', 'binary', 'docx', 'doc']:
                        current_app.logger.info(f"Processing {doc_type} file: {filename}")
                        
                        # For PDFs, try to handle them specially
                        if doc_type == 'pdf':
                            try:
                                from src.utils.document_processor import extract_text_from_pdf
                                content = extract_text_from_pdf(temp_filepath)
                                current_app.logger.info(f"Extracted full PDF text from {filename}, length {len(content)} chars")
                            except Exception as pdf_err:
                                current_app.logger.error(f"Error extracting text from PDF: {pdf_err}")
                                current_app.logger.error(traceback.format_exc())
                                content = f"[PDF file: {filename} - Could not extract text: {str(pdf_err)}]"
                        else:
                            # For non-PDF binary files
                            content = f"[Binary file: {filename}]"
                    else:
                        # For text files, read the content normally
                        file.seek(0)
                        content = file.read().decode('utf-8', errors='replace')
                        
                except Exception as e:
                    current_app.logger.error(f"Error processing document: {str(e)}")
                    # Fallback to binary representation if processing fails
                    file.seek(0)
                    content = f"[Binary file content: {filename}]"
                    metadata = {
                        'title': title,
                        'tags': tags,
                        'type': 'binary',
                        'source': 'file',
                        'filename': filename,
                        'file_path': temp_filepath,
                        'error': str(e),
                        'user_id': current_user.id  # Add user ID for proper user separation
                    }
                
                # Assign ID and enqueue ingestion in background
                doc_id = uuid.uuid4().hex
                metadata['id'] = doc_id
                current_app.logger.info(f"Enqueuing document ingestion: {doc_id}")
                def ingest():
                    try:
                        kb.add_document(content, metadata, doc_id)
                    except Exception as e:
                        current_app.logger.error(f"Background ingest failed for {doc_id}: {e}")
                threading.Thread(target=ingest, daemon=True).start()
                return jsonify({'success': True, 'doc_id': doc_id, 'document': metadata})
        
        # Handle website URL
        elif request.is_json and request.json.get('url'):
            url = request.json.get('url')
            title = request.json.get('title', url)
            tags = request.json.get('tags', '').split(',') if request.json.get('tags') else []
            
            # Create metadata
            metadata = {
                'title': title,
                'tags': tags,
                'type': 'url',
                'source': 'web',
                'url': url,
                'user_id': current_user.id  # Add user ID for proper user separation
            }
            
            # Attempt to fetch URL content
            try:
                import requests
                from bs4 import BeautifulSoup
                
                # Log the URL retrieval attempt
                current_app.logger.info(f"Fetching content from URL: {url}")
                
                # Fetch the URL with a timeout
                response = requests.get(url, timeout=10)
                response.raise_for_status()  # Raise an exception for 4XX/5XX responses
                
                # Parse HTML content
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Extract text content
                for script in soup(["script", "style"]):
                    script.extract()
                
                # Get text
                text = soup.get_text(separator='\n', strip=True)
                
                # Use a reasonable chunk size
                if len(text) > 100000:
                    text = text[:100000] + "... [content truncated due to length]"
                
                # Log successful content extraction
                current_app.logger.info(f"Successfully extracted {len(text)} characters from URL")
                
                # Add document with the extracted text
                success = kb.add_document(text, metadata)
                
                if success:
                    return jsonify({
                        'success': True,
                        'document': metadata
                    })
                else:
                    return jsonify({
                        'success': False,
                        'error': 'Failed to add document to knowledge base'
                    }), 500
                    
            except Exception as e:
                current_app.logger.error(f"Error fetching URL content: {e}")
                return jsonify({
                    'success': False,
                    'error': f'Error fetching URL content: {str(e)}'
                }), 500
                
        # Handle direct text input
        elif request.is_json and request.json.get('text'):
            text = request.json.get('text')
            title = request.json.get('title', 'Text Document')
            tags = request.json.get('tags', [])
            
            # Ensure tags is a list
            if isinstance(tags, str):
                tags = tags.split(',') if tags else []
            
            # Create metadata
            metadata = {
                'title': title,
                'tags': tags,
                'type': 'text',
                'source': 'manual',
                'user_id': current_user.id  # Add user ID for proper user separation
            }
            
            # Add document
            current_app.logger.info(f"Adding text document: {metadata}, content length: {len(text)}")
            success = kb.add_document(text, metadata)
            
            if success:
                return jsonify({
                    'success': True,
                    'document': metadata
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'Failed to add document'
                }), 500
        
        else:
            return jsonify({
                'success': False,
                'error': 'No document data provided'
            }), 400
            
    except Exception as e:
        current_app.logger.error(f"Error adding document: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@knowledge_bp.route('/api/knowledge/documents/<document_id>', methods=['GET'])
@login_required
def get_document(document_id):
    """Get a specific document from the knowledge base"""
    try:
        document = kb.get_document(document_id)
        if document is None:
            return jsonify({
                'success': False,
                'error': 'Document not found'
            }), 404
            
        # Check if user is an admin (role-based check)
        is_admin = hasattr(current_user, 'role') and current_user.role == 'admin'
        
        # Check user access - users can only access their own documents or system documents if admin
        if not is_admin and document.get('metadata', {}).get('user_id') != current_user.id and document.get('metadata', {}).get('user_id') != 'system':
            return jsonify({
                'success': False,
                'error': 'Access denied'
            }), 403
            
        return jsonify({
            'success': True,
            'document': document
        })
    except Exception as e:
        current_app.logger.error(f"Error retrieving document {document_id}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@knowledge_bp.route('/api/knowledge/documents/<document_id>/assign', methods=['POST'])
@login_required
def assign_document(document_id):
    """Assign a document to a specific user (admin only)"""
    try:
        # Check if user is an admin
        is_admin = hasattr(current_user, 'role') and current_user.role == 'admin'
        if not is_admin:
            return jsonify({
                'success': False,
                'error': 'Only administrators can assign documents to users'
            }), 403
            
        # Get the user ID from the request
        data = request.json
        if not data or 'user_id' not in data:
            return jsonify({
                'success': False,
                'error': 'User ID is required'
            }), 400
            
        user_id = data['user_id']
        
        # Check if the document exists
        document = kb.get_document(document_id)
        if document is None:
            return jsonify({
                'success': False,
                'error': 'Document not found'
            }), 404
            
        # Get the knowledge base catalog
        from src.core.kb_catalog import get_catalog_instance
        catalog = get_catalog_instance()
        
        # Assign the document to the user
        success = catalog.assign_document_to_user(document_id, user_id)
        if success:
            return jsonify({
                'success': True,
                'message': f'Document assigned to user {user_id}'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to assign document'
            }), 500
    except Exception as e:
        current_app.logger.error(f"Error assigning document {document_id}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@knowledge_bp.route('/api/knowledge/documents/<document_id>', methods=['PUT'])
def update_document(document_id):
    """Update a document in the knowledge base"""
    try:
        data = request.json
        document = kb.get_document(document_id)
        
        if not document:
            return jsonify({'success': False, 'error': 'Document not found'}), 404
            
        # Update document fields
        if 'title' in data:
            document['title'] = data['title']
        if 'content' in data:
            document['content'] = data['content']
        if 'tags' in data:
            document['tags'] = data['tags'].split(',') if isinstance(data['tags'], str) else data['tags']
            
        success = kb.update_document(document_id, document)
        if success:
            return jsonify({'success': True, 'document': document})
        else:
            return jsonify({'success': False, 'error': 'Failed to update document'}), 500
            
    except Exception as e:
        current_app.logger.error(f"Error updating document {document_id}: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@knowledge_bp.route('/api/knowledge/documents/<document_id>', methods=['DELETE'])
def delete_document(document_id):
    """Delete a document from the knowledge base"""
    try:
        # Log the document ID we're trying to delete
        current_app.logger.info(f"Attempting to delete document with ID: {document_id}")
        
        # Ensure the document ID is properly formatted
        doc_id = document_id.strip()
        
        success = kb.delete_document(doc_id)
        if success:
            current_app.logger.info(f"Successfully deleted document {doc_id}")
            return jsonify({'success': True})
        else:
            current_app.logger.error(f"Failed to delete document {doc_id}")
            return jsonify({'success': False, 'error': 'Failed to delete document'}), 500
    except Exception as e:
        current_app.logger.error(f"Error deleting document {document_id}: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@knowledge_bp.route('/api/knowledge/stats', methods=['GET'])
def get_stats():
    """Get statistics about the knowledge base"""
    try:
        total_documents = kb.get_document_count()
        
        # Get document counts by type
        types = {}
        documents = []
        try:
            documents = kb.search_documents({})
        except Exception as e:
            current_app.logger.error(f"Error fetching documents for stats: {e}")
            
        for doc in documents:
            doc_type = doc.get('type', 'unknown')
            types[doc_type] = types.get(doc_type, 0) + 1
            
        # Get all unique tags
        tags = set()
        for doc in documents:
            for tag in doc.get('tags', []):
                if tag and tag.strip():
                    tags.add(tag.strip())
        
        stats = {
            'total_documents': total_documents,
            'document_types': types,
            'tags': list(tags)
        }
        
        return jsonify({'success': True, 'stats': stats})
    except Exception as e:
        current_app.logger.error(f"Error retrieving knowledge base stats: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@knowledge_bp.route('/api/kb/raw', methods=['GET'])
def raw_kb():
    """
    Return raw ChromaDB contents (ids, documents, metadatas) for debugging
    """
    try:
        # Default get returns ids, documents, metadatas, embeddings, distances
        res = kb.collection.get()
        keys = list(res.keys())
        lengths = {k: len(v) for k, v in res.items() if isinstance(v, (list, tuple))}
        return jsonify({'keys': keys, 'lengths': lengths})
    except Exception as e:
        current_app.logger.error(f"raw_kb failed: {e}")
        return jsonify({'error': str(e)}), 500

@knowledge_bp.route('/api/knowledge/search', methods=['GET'])
@login_required
def semantic_search():
    """Semantic lookup across all chunks; full=true returns stitched full text."""
    q = request.args.get('query')
    if not q:
        return jsonify(success=False, error="Missing 'query' parameter"), 400

    try:
        limit = int(request.args.get('limit', 5))
    except ValueError:
        limit = 5

    full = request.args.get('full', 'false').lower() == 'true'

    # Add user_id filter for proper user separation
    user_filter = {"user_id": current_user.id}
    
    if full:
        from src.core.knowledge_db import query_knowledge
        docs = query_knowledge(q, limit, filters=user_filter)
        return jsonify(success=True, results=docs)
    else:
        snippets = kb.get_documents_from_search(q, limit, filters=user_filter)
        return jsonify(success=True, results=snippets)

@knowledge_bp.route('/api/knowledge/documents/<document_id>/chunks', methods=['GET'])
@login_required
def get_document_chunks(document_id):
    """Get chunks for a specific document"""
    try:
        # Filter by both document ID and user ID for proper user separation
        # For ChromaDB, we need to use the $and operator for multiple conditions
        current_app.logger.debug(f"Querying chunks for document {document_id} for user {current_user.id}")
        
        # Check if user is admin to decide on filter
        is_admin = hasattr(current_user, 'is_admin') and current_user.is_admin
        
        # Admins can see all chunks for a document, regular users only see their own
        if is_admin:
            # Admin can see all document chunks
            where_filter = {"original_id": document_id}
            current_app.logger.debug(f"Admin user, using filter: {where_filter}")
        else:
            # Use $or to match either the user's documents or system documents
            where_filter = {
                "$and": [
                    {"original_id": document_id},
                    {
                        "$or": [
                            {"user_id": str(current_user.id)},
                            {"user_id": "system"}
                        ]
                    }
                ]
            }
            current_app.logger.debug(f"Regular user, using filter: {where_filter}")
        
        res = kb.collection.get(
            where=where_filter,
            include=["documents", "metadatas"]
        )
        docs = res.get("documents", [])
        metas = res.get("metadatas", [])
        chunks = []
        
        # --- DEBUG LOG START ---
        current_app.logger.debug(f"Document ID: {document_id}")
        for meta, content in zip(metas, docs):
            idx = meta.get("chunk_index", 0)
            preview = content

            # --- DEBUG LOG START ---
            if idx < 5: # Log first 5 chunks only to avoid excessive output
                current_app.logger.debug(f"Chunk {idx} RAW CONTENT (first 300 chars): {content[:300] if isinstance(content, str) else '[Non-str content]'}")
            # --- DEBUG LOG END ---

            # generate improved preview for long strings
            if isinstance(content, str) and len(content) > 200:
                start_offset = 0
                first_sentence_end = -1
                min_search_len = min(50, len(content))
                for terminator in ['. ', '? ', '! ', '\n', '\n\n']:
                    pos = content.find(terminator, 0, min_search_len)
                    if pos != -1:
                        if first_sentence_end == -1 or pos < first_sentence_end:
                            first_sentence_end = pos + len(terminator)
                
                # If we found an early sentence end, start the preview after it
                if first_sentence_end != -1 and first_sentence_end < 50:
                    start_offset = first_sentence_end
                    prefix = "... " 
                else:
                    prefix = ""

                preview_len = 200
                end_offset = min(start_offset + preview_len + 50, len(content))
                snippet = content[start_offset:end_offset]

                # Find the last space within the target preview length *from the start of the snippet*
                target_end_in_snippet = min(preview_len, len(snippet))
                last_space = snippet.rfind(' ', 0, target_end_in_snippet)
                
                # Truncate at the last space if found
                if last_space > 0: # Ensure space is not at the very beginning
                    final_snippet = snippet[:last_space]
                elif len(snippet) > preview_len: # No space found, hard truncate if needed
                    final_snippet = snippet[:preview_len]
                else:
                    final_snippet = snippet # Use the snippet as is if it's short enough
                
                # Combine prefix, snippet, and suffix ellipsis if needed
                suffix = "..." if (start_offset + len(final_snippet)) < len(content) else ""
                preview = prefix + final_snippet.strip() + suffix

            # Fallback for non-string or very short content
            elif not isinstance(content, str):
                 preview = "[Non-text content]"
            # else preview remains the original short content

            chunks.append({"chunk_index": idx, "preview": preview})

            # --- DEBUG LOG START ---
            if idx < 5: # Log first 5 chunks only
                current_app.logger.debug(f"Chunk {idx} FINAL PREVIEW: {preview}")
            # --- DEBUG LOG END ---

        chunks = sorted(chunks, key=lambda c: c["chunk_index"])
        return jsonify({"success": True, "chunks": chunks})
    except Exception as e:
        current_app.logger.error(f"Error retrieving chunks for document {document_id}: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

# Endpoint for client to poll ingestion progress
@knowledge_bp.route('/api/knowledge/progress/<doc_id>', methods=['GET'])
def get_ingest_progress(doc_id):
    from src.core.knowledge_db import processing_progress
    progress = processing_progress.get(doc_id)
    if progress is None:
        return jsonify({'success': False, 'error': 'Document not found'}), 404
    return jsonify({'success': True, 'progress': progress})
