<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Athena AI Assistant</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <!-- Solo Leveling Theme -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/solo-leveling-theme.css') }}">
    <!-- Improved Message Formatting -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/improved-formatting.css') }}">
    <!-- Task Tracking System -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/task-tracker.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Markdown Parser -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- Socket.IO Client -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <!-- Favicon -->
    <link rel="icon" href="{{ url_for('static', filename='img/AthenaLogoHQ.png') }}" type="image/png">
</head>

<body>
    <script>
        window.USER_AVATAR_URL = "{{ url_for('static', filename=current_user.profile_picture or 'uploads/profile_pics/placeholder-profile.png') }}";
        // Set API base URL to use relative paths
        window.API_BASE_URL = "";  // Empty string means use relative URLs
        // Set current user ID for Socket.IO room joining
        window.CURRENT_USER_ID = "{{ current_user.id }}"
    </script>

    <!-- Solo Leveling Theme Elements -->
    <!-- system-status div removed to avoid duplication with the header indicator -->
    <div class="shadow-soldier" id="shadow-soldier"></div>
    <div class="glow-cursor" id="glow-cursor"></div>

    {% include 'partials/emulation_banner.html' %}
    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo" style="display: flex; flex-direction: column; gap: 8px;">
                    <div class="logo-header"
                        style="display: flex; align-items: center; gap: 0.5rem; color: var(--primary-color); font-weight: 600; font-size: 2rem;">
                        <img src="{{ url_for('static', filename='img/AthenaLogoHQ.png') }}" alt="Athena Logo"
                            style="width: 24px; height: 24px; border-radius: 50%;">
                        <span>Athena</span>
                    </div>
                    <div class="model-dropdown">
                        <label for="chat-model-select"
                            style="font-size: 0.8rem; color: var(--text-secondary);">Model:</label>
                        <select id="chat-model-select" style="max-width: 180px; font-size: 0.85rem;">
                            <!-- Populated by main.js on load -->
                        </select>
                    </div>
                </div>
                <button class="new-chat">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 4v16m-8-8h16"></path>
                    </svg>
                    New Chat
                </button>
            </div>

            <div class="conversation-list">
                <div class="conversation-group">
                    <h3>Today</h3>
                </div>
            </div>

            <nav class="menu">
                <a href="/" class="menu-item active">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2">
                        <path d="M12 2L2 12h3v9h14v-9h3L12 2z"></path>
                    </svg>
                    <span>Chat</span>
                </a>
                <a href="/knowledge" class="menu-item">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.3-4.3"></path>
                    </svg>
                    <span>Knowledge</span>
                </a>
                <a href="/settings" class="menu-item">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2">
                        <path d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"></path>
                        <path
                            d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1Z">
                        </path>
                    </svg>
                    <span>Settings</span>
                </a>
            </nav>

            <div class="sidebar-footer">
                <!-- Solo Leveling System Rank -->
                <div class="solo-leveling-status">
                    <div class="system-rank">
                        <div class="rank-label">SYSTEM RANK</div>
                        <div class="rank-value">S</div>
                    </div>
                    <div class="system-status-sidebar">
                        <div class="status-indicator online"></div>
                        <span>ONLINE</span>
                    </div>
                </div>

                <!-- Preserve existing user info and logout -->
                <div class="user-info">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10" />
                        <path d="M12 16v-4m0-4h.01" />
                    </svg>
                    <span>
                        {% if current_user.is_authenticated %}
                        {{ current_user.username }}
                        {% else %}
                        Guest
                        {% endif %}
                    </span>
                </div>

                <div class="theme-toggle-wrapper">
                    <svg class="theme-icon sun-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-width="2">
                        <circle cx="12" cy="12" r="5" />
                        <path d="M12 1v2m0 18v2M4.22 4.22l1.42 1.42
                                 m12.72 12.72l1.42 1.42M1 12h2m18 0h2
                                 M4.22 19.78l1.42-1.42
                                 M18.36 5.64l1.42-1.42" />
                    </svg>
                    <div class="theme-toggle" id="theme-toggle" role="button" tabindex="0"></div>
                    <svg class="theme-icon moon-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-width="2">
                        <path d="M21 12.79A9 9 0 1111.21 3
                                 7 7 0 0021 12.79z" />
                    </svg>
                </div>

                <a href="{{ url_for('auth.logout') }}" class="logout-button" id="logout-button" aria-label="Logout">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2">
                        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                        <polyline points="16 17 21 12 16 7"></polyline>
                        <line x1="21" y1="12" x2="9" y2="12"></line>
                    </svg>
                </a>
            </div>
        </aside>

        <main class="main-content">
            <!-- System status indicator in header -->
            <div class="system-status-header">
                <!-- Notification Bell -->
                <div id="notification-bell" class="notification-bell">
                    <i class="fa fa-bell"></i>
                    <span class="notification-badge" id="notification-count">0</span>
                </div>
                <div class="status-indicator online"></div>
                <span class="status-text">SYSTEM ACTIVE</span>
            </div>

            <!-- Main content area -->
            {% block content %}
            <div class="chat-container">
                <div class="messages" id="messages">
                    <div class="message system">
                        <div class="message-content">
                            <p>Hello! I'm Athena, your AI assistant. I can help you with system commands,
                                Python code, and general tasks. How can I assist you today?</p>
                        </div>
                    </div>
                </div>

                <div class="input-container">
                    <div class="input-wrapper">
                        <textarea id="user-input" placeholder="Message Athena..." rows="1" autofocus></textarea>
                        <button id="kb-toggle-button" class="kb-toggle-button" title="Toggle KB Mode"
                            style="margin-left:8px;">KB</button>
                        <button id="demo-task-button" class="demo-task-button" title="Run Demo Task"
                            style="margin-left:8px;"><i class="fa fa-tasks"></i></button>
                        <button class="send-button" id="send-button">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                stroke-width="2">
                                <path d="M22 2L11 13m0 0L2 9m9 4l-7 9 18-20"></path>
                            </svg>
                        </button>
                    </div>
                    <div id="token-count-preview"></div>
                </div>
            </div>
            {% endblock %}
        </main>

        <!-- Task Panel -->
        <div id="task-panel" class="task-panel">
            <div class="task-panel-header">
                <h3>Background Tasks</h3>
                <div class="task-filter">
                    <button class="active" data-filter="active">Active</button>
                    <button data-filter="completed">Completed</button>
                </div>
                <button class="close-panel">&times;</button>
            </div>
            <div class="task-list" id="task-list">
                <!-- Task items will be populated here -->
            </div>
        </div>

        <!-- Toast Container for Notifications -->
        <div id="toast-container" class="toast-container">
            <!-- Toast notifications will be dynamically added here -->
        </div>
    </div>
    <!-- Emergency chat display fix -->
    <script>
        // Direct DOM manipulation to fix chat display issues
        window.fixChat = function () {
            const container = document.getElementById('messages');

            if (!container) {
                console.error('Cannot find messages container');
                return;
            }

            // Save current scroll position
            const scrollPos = container.scrollTop;
            const wasAtBottom = (container.scrollHeight - container.clientHeight <= container.scrollTop + 10);

            // Force visibility
            container.style.display = 'flex';
            container.style.flexDirection = 'column';
            container.style.visibility = 'visible';
            container.style.opacity = '1';

            // Force all messages to be visible
            const allMessages = container.querySelectorAll('.message');
            allMessages.forEach(msg => {
                msg.style.display = 'flex';
                msg.style.visibility = 'visible';
                msg.style.opacity = '1';
            });

            // Restore scroll position
            if (wasAtBottom) {
                container.scrollTop = container.scrollHeight;
            } else {
                container.scrollTop = scrollPos;
            }

            // Removed excessive console logging for better performance
        };

        // Apply fix only when DOM loads and when messages are added
        document.addEventListener('DOMContentLoaded', function () {
            console.log('DOM ready, applying initial fix');
            // Initial fix
            setTimeout(window.fixChat, 500);

            // Add event listener for new messages instead of constant interval
            const messagesContainer = document.getElementById('messages');
            if (messagesContainer) {
                // Use a debounce approach to limit how often the fix runs
                let fixTimer = null;
                const debounceTime = 2000; // Only run at most once every 2 seconds

                // Monitor for DOM changes in the messages container
                const observer = new MutationObserver(function (mutations) {
                    // Check if any of the mutations are relevant (adding/removing messages)
                    const relevantChange = mutations.some(mutation => {
                        return mutation.type === 'childList' &&
                            (mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0);
                    });

                    if (relevantChange && !fixTimer) {
                        // Run the fix and set a timer to prevent multiple runs
                        window.fixChat();

                        // Set a timer to prevent running the fix again too soon
                        fixTimer = setTimeout(() => {
                            fixTimer = null;
                        }, debounceTime);
                    }
                });

                // Start observing the messages container for changes (only for childList changes)
                observer.observe(messagesContainer, { childList: true });
            }
        });
    </script>

    <script src="{{ url_for('static', filename='js/conversations.js') }}"></script>
    <script src="{{ url_for('static', filename='js/token_count.js') }}" type="module"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/solo-leveling-system.js') }}"></script>
    <!-- Content formatter for better message display -->
    <script src="{{ url_for('static', filename='js/content-formatter.js') }}"></script>
    <!-- Task tracking system -->
    <script src="{{ url_for('static', filename='js/task-tracker.js') }}"></script>
</body>

</html>