# src/api/commands.py

"""
API endpoints for command management in the Cross-Device API System.

This file implements:
- Command submission from one device to another
- Command status updates and result reporting
- Command querying and filtering
- Command history tracking
"""

import json
import logging
import uuid
from datetime import datetime, timedelta

from flask import Blueprint, jsonify, request, current_app
from flask_login import current_user, login_required
from functools import wraps
from sqlalchemy import desc

from src.models import db, User
# Import device models directly instead of using the deprecated module
from src.models.device import (
    Device, 
    Command, 
    CommandLog, 
    create_command, 
    add_command_log,
    get_device_or_404
)

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger("commands_api")

# Create blueprint
commands_bp = Blueprint("commands", __name__, url_prefix="/api/commands")


# Helper Functions

def get_command_or_404(command_uuid, user_id=None):
    """
    Get a command by UUID with optional user ownership check.
    
    Args:
        command_uuid (str): The UUID of the command
        user_id (int, optional): User ID to check ownership against
        
    Returns:
        Command object or 404 error
    """
    query = Command.query.filter_by(command_uuid=command_uuid)
    
    if user_id is not None:
        query = query.filter_by(user_id=user_id)
        
    command = query.first_or_404(
        description=f"Command with UUID {command_uuid} not found"
    )
    return command


def require_device_auth(f):
    """
    Decorator to require device authentication.
    This checks for a valid device token in the authorization header.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Allow access if user is already authenticated through Flask-Login
        if (
            current_user
            and hasattr(current_user, "is_authenticated")
            and current_user.is_authenticated
        ):
            # If device_uuid is in kwargs, verify ownership
            if "device_uuid" in kwargs:
                device = get_device_or_404(kwargs["device_uuid"], current_user.id)
                # Add device to kwargs for convenience
                kwargs["device"] = device
            
            # If command_uuid is in kwargs, verify ownership
            if "command_uuid" in kwargs:
                command = get_command_or_404(kwargs["command_uuid"], current_user.id)
                # Add command to kwargs for convenience
                kwargs["command"] = command
            
            return f(*args, **kwargs)
            
        # TODO: For API clients, implement JWT token verification
        # For now, just return 401
        return jsonify({"error": "Device authentication required"}), 401
        
    return decorated_function


# API Endpoints

@commands_bp.route("", methods=["GET"])
@login_required
def list_commands():
    """
    Get a list of commands with filtering options.
    
    Query Parameters:
        status (str): Filter by status (e.g., "pending", "completed")
        source_device (str): Filter by source device UUID
        target_device (str): Filter by target device UUID
        limit (int): Maximum number of commands to return (default: 20)
        offset (int): Offset for pagination (default: 0)
        
    Returns:
        JSON response with list of commands
    """
    # Get filter parameters
    status = request.args.get("status")
    source_device = request.args.get("source_device")
    target_device = request.args.get("target_device")
    limit = request.args.get("limit", 20, type=int)
    offset = request.args.get("offset", 0, type=int)
    
    # Start with base query for user's commands
    query = Command.query.filter_by(user_id=current_user.id)
    
    # Apply filters
    if status:
        query = query.filter_by(status=status)
        
    if source_device:
        # Find the device ID from UUID
        device = Device.query.filter_by(
            device_uuid=source_device, 
            user_id=current_user.id
        ).first()
        if device:
            query = query.filter_by(source_device_id=device.id)
        else:
            # If device not found, return empty result
            return jsonify({
                "commands": [],
                "total": 0,
                "limit": limit,
                "offset": offset
            })
            
    if target_device:
        # Find the device ID from UUID
        device = Device.query.filter_by(
            device_uuid=target_device, 
            user_id=current_user.id
        ).first()
        if device:
            query = query.filter_by(target_device_id=device.id)
        else:
            # If device not found, return empty result
            return jsonify({
                "commands": [],
                "total": 0,
                "limit": limit,
                "offset": offset
            })
    
    # Get total count before pagination
    total = query.count()
    
    # Apply sorting and pagination
    commands = (
        query.order_by(desc(Command.created_at))
        .limit(limit)
        .offset(offset)
        .all()
    )
    
    # Return the results
    return jsonify({
        "commands": [cmd.to_dict() for cmd in commands],
        "total": total,
        "limit": limit,
        "offset": offset
    })


@commands_bp.route("", methods=["POST"])
@login_required
def create_new_command():
    """
    Create a new command to be executed on a target device.
    
    Request Body:
        target_device (str): UUID of the target device
        source_device (str, optional): UUID of the source device
        capability (str): The capability to invoke on the target device
        parameters (dict): Parameters for the capability
        priority (str, optional): Command priority ("immediate", "normal", "background")
        expires_in_hours (int, optional): Hours until the command expires
        
    Returns:
        JSON response with the created command
    """
    data = request.json or {}
    
    # Validate required fields
    required_fields = ["target_device", "capability", "parameters"]
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        return jsonify({
            "error": f"Missing required fields: {', '.join(missing_fields)}"
        }), 400
    
    # Get target device
    target_device = Device.query.filter_by(
        device_uuid=data["target_device"],
        user_id=current_user.id
    ).first()
    
    if not target_device:
        return jsonify({
            "error": f"Target device not found: {data['target_device']}"
        }), 404
    
    # Check if target device is active
    if not target_device.is_active:
        return jsonify({
            "error": f"Target device is not active: {data['target_device']}"
        }), 400
    
    # Check if target device has the requested capability
    capability_exists = any(
        cap.capability_name == data["capability"] 
        for cap in target_device.capabilities
    )
    
    if not capability_exists:
        return jsonify({
            "error": f"Target device does not support capability: {data['capability']}"
        }), 400
    
    # Get source device if provided
    source_device_id = None
    if "source_device" in data:
        source_device = Device.query.filter_by(
            device_uuid=data["source_device"],
            user_id=current_user.id
        ).first()
        
        if not source_device:
            return jsonify({
                "error": f"Source device not found: {data['source_device']}"
            }), 404
            
        source_device_id = source_device.id
    
    # Get optional parameters
    priority = data.get("priority", "normal")
    if priority not in ["immediate", "normal", "background"]:
        return jsonify({
            "error": f"Invalid priority: {priority}. Must be one of: immediate, normal, background."
        }), 400
        
    expires_in_hours = data.get("expires_in_hours", 24)
    try:
        expires_in_hours = int(expires_in_hours)
    except (ValueError, TypeError):
        return jsonify({
            "error": f"Invalid expires_in_hours: {expires_in_hours}. Must be an integer."
        }), 400
    
    # Create the command
    command = create_command(
        user_id=current_user.id,
        target_device_id=target_device.id,
        capability_name=data["capability"],
        parameters=data["parameters"],
        source_device_id=source_device_id,
        priority=priority,
        expires_in_hours=expires_in_hours
    )
    
    db.session.commit()
    
    logger.info(
        f"Created command {command.command_uuid} for target device {target_device.device_uuid} "
        f"(capability: {command.capability_name})"
    )
    
    return jsonify({
        "command": command.to_dict(),
        "message": "Command created successfully"
    }), 201


@commands_bp.route("/<string:command_uuid>", methods=["GET"])
@login_required
def get_command(command_uuid):
    """
    Get details for a specific command.
    
    Path Parameters:
        command_uuid (str): UUID of the command to retrieve
        
    Returns:
        JSON response with command details
    """
    command = get_command_or_404(command_uuid, current_user.id)
    
    # Get source and target device UUIDs
    source_device_uuid = None
    if command.source_device_id:
        source_device = Device.query.get(command.source_device_id)
        if source_device:
            source_device_uuid = source_device.device_uuid
    
    target_device = Device.query.get(command.target_device_id)
    target_device_uuid = target_device.device_uuid if target_device else None
    
    # Get command logs
    logs = [log.to_dict() for log in command.logs]
    
    # Build response
    response = command.to_dict()
    response.update({
        "source_device_uuid": source_device_uuid,
        "target_device_uuid": target_device_uuid,
        "logs": logs
    })
    
    return jsonify({
        "command": response
    })


@commands_bp.route("/pending", methods=["GET"])
@login_required
def get_pending_commands():
    """
    Get pending commands for a device.
    
    Query Parameters:
        device_uuid (str): UUID of the device to get pending commands for
        limit (int): Maximum number of commands to return (default: 10)
        
    Returns:
        JSON response with list of pending commands
    """
    # Get device UUID from query parameters
    device_uuid = request.args.get("device_uuid")
    if not device_uuid:
        return jsonify({
            "error": "Missing required parameter: device_uuid"
        }), 400
    
    # Get device
    device = Device.query.filter_by(
        device_uuid=device_uuid,
        user_id=current_user.id
    ).first()
    
    if not device:
        return jsonify({
            "error": f"Device not found: {device_uuid}"
        }), 404
    
    # Update device's last active timestamp
    device.last_active = datetime.utcnow()
    db.session.commit()
    
    # Get limit parameter
    limit = request.args.get("limit", 10, type=int)
    
    # Get pending commands for the device
    # Pending commands are those with status "pending" or "delivered"
    # Also filter out expired commands
    now = datetime.utcnow()
    commands = (
        Command.query
        .filter_by(target_device_id=device.id)
        .filter(Command.status.in_(["pending", "delivered"]))
        .filter((Command.expires_at.is_(None)) | (Command.expires_at > now))
        .order_by(Command.priority.desc(), Command.created_at.asc())
        .limit(limit)
        .all()
    )
    
    # Update status to "delivered" for pending commands
    for command in commands:
        if command.status == "pending":
            command.status = "delivered"
            command.delivered_at = datetime.utcnow()
            
            # Add command log
            add_command_log(
                command=command,
                status="delivered",
                message=f"Command delivered to device {device_uuid}"
            )
    
    db.session.commit()
    
    return jsonify({
        "commands": [cmd.to_dict() for cmd in commands],
        "count": len(commands)
    })


@commands_bp.route("/<string:command_uuid>/status", methods=["POST"])
@login_required
def update_command_status(command_uuid):
    """
    Update the status of a command.
    
    Path Parameters:
        command_uuid (str): UUID of the command to update
        
    Request Body:
        status (str): New status for the command
        message (str, optional): Message to log with the status update
        log_data (dict, optional): Additional data to log
        
    Returns:
        JSON response confirming the update
    """
    command = get_command_or_404(command_uuid, current_user.id)
    
    data = request.json or {}
    
    # Validate status
    if "status" not in data:
        return jsonify({
            "error": "Missing required field: status"
        }), 400
    
    status = data["status"]
    if status not in ["pending", "delivered", "executing", "completed", "failed"]:
        return jsonify({
            "error": f"Invalid status: {status}. Must be one of: pending, delivered, executing, completed, failed."
        }), 400
    
    # Get optional fields
    message = data.get("message")
    log_data = data.get("log_data")
    
    # Update command status
    old_status = command.status
    command.status = status
    
    # Update timestamps based on status
    now = datetime.utcnow()
    if status == "delivered" and not command.delivered_at:
        command.delivered_at = now
    elif status in ["completed", "failed"] and not command.completed_at:
        command.completed_at = now
    
    # Add command log
    log = add_command_log(
        command=command,
        status=status,
        message=message,
        log_data=log_data
    )
    
    # If status is "failed", set error message
    if status == "failed" and "error_message" in data:
        command.error_message = data["error_message"]
    
    # If status is "completed", set result
    if status == "completed" and "result" in data:
        result = data["result"]
        if isinstance(result, dict):
            result = json.dumps(result)
        command.result = result
    
    db.session.commit()
    
    logger.info(
        f"Updated command {command_uuid} status from {old_status} to {status}"
    )
    
    return jsonify({
        "message": f"Command status updated to: {status}",
        "command": command.to_dict()
    })


@commands_bp.route("/<string:command_uuid>/result", methods=["POST"])
@login_required
def submit_command_result(command_uuid):
    """
    Submit the result of a command execution.
    
    Path Parameters:
        command_uuid (str): UUID of the command
        
    Request Body:
        result (dict): Result of the command execution
        error_message (str, optional): Error message if the command failed
        
    Returns:
        JSON response confirming the update
    """
    command = get_command_or_404(command_uuid, current_user.id)
    
    data = request.json or {}
    
    # Check if command can receive results
    if command.status in ["completed", "failed"]:
        return jsonify({
            "error": f"Cannot submit result for command with status: {command.status}"
        }), 400
    
    # Set result and update status
    if "result" in data:
        result = data["result"]
        if isinstance(result, dict):
            result = json.dumps(result)
        command.result = result
        
        # Update status to completed
        old_status = command.status
        command.status = "completed"
        command.completed_at = datetime.utcnow()
        
        # Add command log
        add_command_log(
            command=command,
            status="completed",
            message="Command execution completed with result",
            log_data=data.get("log_data")
        )
        
        logger.info(
            f"Command {command_uuid} completed (previous status: {old_status})"
        )
    elif "error_message" in data:
        # Set error message and update status to failed
        command.error_message = data["error_message"]
        old_status = command.status
        command.status = "failed"
        command.completed_at = datetime.utcnow()
        
        # Add command log
        add_command_log(
            command=command,
            status="failed",
            message=data["error_message"],
            log_data=data.get("log_data")
        )
        
        logger.info(
            f"Command {command_uuid} failed: {data['error_message']} (previous status: {old_status})"
        )
    else:
        return jsonify({
            "error": "Missing required field: result or error_message"
        }), 400
    
    db.session.commit()
    
    return jsonify({
        "message": f"Command result submitted (status: {command.status})",
        "command": command.to_dict()
    })


@commands_bp.route("/<string:command_uuid>/cancel", methods=["POST"])
@login_required
def cancel_command(command_uuid):
    """
    Cancel a pending or delivered command.
    
    Path Parameters:
        command_uuid (str): UUID of the command to cancel
        
    Returns:
        JSON response confirming the cancellation
    """
    command = get_command_or_404(command_uuid, current_user.id)
    
    # Check if command can be canceled
    if command.status not in ["pending", "delivered"]:
        return jsonify({
            "error": f"Cannot cancel command with status: {command.status}"
        }), 400
    
    # Update status to failed
    old_status = command.status
    command.status = "failed"
    command.completed_at = datetime.utcnow()
    command.error_message = "Command cancelled by user"
    
    # Add command log
    add_command_log(
        command=command,
        status="failed",
        message="Command cancelled by user"
    )
    
    db.session.commit()
    
    logger.info(
        f"Command {command_uuid} cancelled (previous status: {old_status})"
    )
    
    return jsonify({
        "message": "Command cancelled successfully",
        "command": command.to_dict()
    })
