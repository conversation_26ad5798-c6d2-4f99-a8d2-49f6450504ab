"""
Pytest configuration file for Athena test suite.

This file contains pytest fixtures and configuration used across multiple test files.
"""

import os
import sys
import pytest
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

# Add the parent directory to sys.path to ensure imports work correctly
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.models import db as _db

@pytest.fixture(scope='session')
def app():
    """Create a Flask app for testing."""
    app = Flask(__name__)
    app.config['TESTING'] = True
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = 'test-key'
    
    return app

@pytest.fixture(scope='session')
def db(app):
    """Create a database for testing."""
    with app.app_context():
        _db.init_app(app)
        _db.create_all()
        
        yield _db
        
        _db.session.remove()
        _db.drop_all()

@pytest.fixture(scope='function')
def session(db):
    """Create a new database session for each test."""
    connection = db.engine.connect()
    transaction = connection.begin()
    
    session = db.session
    
    yield session
    
    # Rollback the transaction after the test
    session.close()
    transaction.rollback()
    connection.close()

@pytest.fixture(scope='function')
def client(app):
    """Create a test client for the app."""
    with app.test_client() as client:
        with app.app_context():
            yield client
