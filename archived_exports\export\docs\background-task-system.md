# Background Task Tracking System

## Overview
The Background Task Tracking System enables <PERSON> to execute long-running operations in the background while keeping the user interface responsive. It provides real-time progress updates, notifications, and a comprehensive task management panel.

## Features
- Real-time task status and progress updates via WebSockets
- Visual notification system with counter for active tasks
- Expandable task details with timestamped logs
- Automatic task categorization (active vs. completed)
- Progress tracking with visual indicators
- Toast notifications for completed or failed tasks

## Components

### Server-Side Components
1. **Socket Service** (`src/services/socket.py`)
   - Manages WebSocket connections
   - Handles real-time event emission
   - Creates user-specific rooms for notifications

2. **Task Executor** (`src/services/task_executor.py`)
   - Executes background tasks in separate threads
   - Manages task lifecycle (start, update, complete)
   - Provides progress tracking and status updates
   - Maintains task queues and priorities

3. **Task API** (`src/api/tasks.py`)
   - Provides endpoints for task management
   - Retrieves task lists, logs, and details
   - Supports filtering by status

4. **Demo API** (`src/api/demo.py`)
   - Implements example background tasks for testing
   - Simulates long-running operations with progress updates

### Client-Side Components
1. **Task Tracker** (`static/js/task-tracker.js`)
   - Connects to WebSocket server
   - Manages task collections (active/completed)
   - Updates UI based on real-time events
   - Handles notification display and animation

2. **Task Panel UI** (`templates/index.html`, `static/css/task-tracker.css`)
   - Provides notification bell with counter
   - Implements expandable task panel
   - Displays task details and logs
   - Supports filtering between active and completed tasks

## Usage

### Starting a Background Task
```python
from src.login.device_models import Command
from src.services.task_executor import task_executor_service

# Create a command
command = Command(
    command_uuid=str(uuid.uuid4()),
    user_id=current_user.id,
    source_device_id=device_id,
    target_device_id=device_id,
    capability_name="example_task",
    parameters=json.dumps({"param1": "value1"}),
    priority="normal",
    status="pending",
    is_background=True,
    priority_level=5,
    max_runtime=300,  # 5 minutes
    progress=0
)
db.session.add(command)
db.session.commit()

# Add to task executor
task_executor_service.add_task(command)
```

### Updating Task Progress
```python
from src.services.socket import emit_task_progress

# Update progress
command.progress = 50
db.session.commit()

# Emit progress update event
emit_task_progress(command, "Processing data...", 50)
```

### Completing a Task
```python
from src.services.socket import emit_task_update

# Update status
command.status = "completed"
command.progress = 100
command.completed_at = datetime.utcnow()
db.session.commit()

# Emit task update event
emit_task_update(command)
```

## Dependencies
- Flask-SocketIO: WebSocket server implementation
- Socket.IO Client: JavaScript client library

## Configuration
No special configuration is required beyond ensuring Flask-SocketIO is properly installed and initialized in the main application.

## Troubleshooting
1. **Tasks not showing up in the UI**
   - Check WebSocket connection in browser console
   - Verify user room subscription is correct
   - Check task executor service is running

2. **Progress updates not displaying**
   - Ensure progress is updated in the database
   - Verify emit_task_progress is called with correct parameters

3. **Notification bell not clickable**
   - Check CSS z-index and pointer-events properties
   - Inspect browser console for JavaScript errors

4. **Tasks stuck in "Running" state**
   - Verify proper status updates in the database
   - Check for missing emit_task_update calls
   - Ensure task completion logic is executed

## API Reference

### WebSocket Events
- **connect**: Client connects to server
- **join**: Client joins user-specific room
- **task_update**: Server sends task status updates
- **task_progress**: Server sends task progress updates 
- **notification**: Server sends toast notifications

### REST Endpoints
- **GET /api/tasks/background**: Get background tasks by status
- **GET /api/tasks/{id}/logs**: Get logs for a specific task
- **POST /api/demo/task**: Create a demo background task

## Implementing Custom Background Tasks
1. Create a new function that performs the task operations
2. Update progress at regular intervals
3. Handle exceptions properly
4. Update status on completion or failure
5. Emit appropriate events for UI updates
