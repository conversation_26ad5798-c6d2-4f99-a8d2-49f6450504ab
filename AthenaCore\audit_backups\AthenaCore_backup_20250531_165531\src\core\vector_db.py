# ────────────────────────────────────────────────────────────
#  src/core/vector_db.py        build 2025-04-23 c
# ────────────────────────────────────────────────────────────
"""
Per-user long-term memory store for <PERSON>, using Chroma.
"""

from __future__ import annotations

import logging
import time
import traceback
from pathlib import Path
from typing import Dict, List, Optional

import chromadb
from chromadb.utils.embedding_functions import (
    OpenAIEmbeddingFunction,
    SentenceTransformerEmbeddingFunction,
)
from logging.handlers import RotatingFileHandler

from src.utils.config import AthenaConfig


# ╭──────────────────── logger helper ─────────────────────╮
def _setup_logger() -> logging.Logger:
    lg = logging.getLogger("AthenaVectorDB")
    lg.setLevel(logging.INFO)
    if not lg.handlers:
        log_dir = Path(__file__).resolve().parents[2] / "logs"
        log_dir.mkdir(parents=True, exist_ok=True)
        fh = RotatingFileHandler(
            log_dir / "athena_vector_db.log",
            maxBytes=5_242_880,
            backupCount=5,
            encoding="utf-8",
        )
        fh.setFormatter(logging.Formatter("%(asctime)s [%(levelname)s] %(message)s"))
        fh.setLevel(logging.DEBUG)
        lg.addHandler(fh)
        lg.propagate = False
    return lg


# ╭──────────────────────── main class ─────────────────────╮
class VectorDatabase:
    """Chroma collection `athena_memory` with per-user embeddings."""

    # ────────────────────────────────────────────────────────
    # initialisation
    # ────────────────────────────────────────────────────────
    def __init__(self) -> None:
        self.logger = _setup_logger()
        cfg = AthenaConfig.load()
        
        # Default to memory-only mode until we confirm persistence works
        self.is_persistent = False

        # Normalize the persistence directory path
        persist_dir = (
            Path(cfg.CHROMA_PERSIST_DIR).expanduser()
            if Path(cfg.CHROMA_PERSIST_DIR).is_absolute()
            else Path(__file__).resolve().parents[2] / cfg.CHROMA_PERSIST_DIR
        )
        
        # Ensure the directory exists
        try:
            persist_dir.mkdir(parents=True, exist_ok=True)
            self.persist_dir = str(persist_dir)
            self.logger.info(f"Chroma persist dir -> {self.persist_dir}")
        except Exception as e:
            self.logger.error(f"Failed to create persist directory: {e}")
            self.persist_dir = str(persist_dir)
            self.logger.warning(f"Using directory without confirming it's writable: {self.persist_dir}")

        try:
            # Determine client type and initialize appropriately
            if cfg.CHROMA_API_IMPL == "rest":
                # Remote server mode
                self.logger.info("Initializing ChromaDB HttpClient")
                self.client = chromadb.HttpClient(
                    host=cfg.CHROMA_SERVER_HOST,
                    port=int(cfg.CHROMA_SERVER_HTTP_PORT),
                )
                # Remote server is persistent by design
                self.is_persistent = True
                self.logger.info(f"Using ChromaDB REST API at {cfg.CHROMA_SERVER_HOST}:{cfg.CHROMA_SERVER_HTTP_PORT}")
            else:
                # Local persistent mode
                self.logger.info(f"Initializing ChromaDB PersistentClient at {self.persist_dir}")
                # Ensure directory exists and is writable
                try:
                    Path(self.persist_dir).mkdir(parents=True, exist_ok=True)
                    # Test file creation to confirm write access
                    test_file = Path(self.persist_dir) / "write_test"
                    test_file.write_text("test")
                    test_file.unlink()  # Remove test file
                    self.logger.info("Successfully verified write access to persist directory")
                except Exception as write_err:
                    self.logger.error(f"Directory permissions error: {write_err}")
                    raise
                    
                # Create the persistent client
                self.client = chromadb.PersistentClient(path=self.persist_dir)
                self.is_persistent = True
                self.logger.info(f"Initialized PersistentClient at {self.persist_dir}")
        except Exception as e:
            self.logger.error(f"ChromaDB client initialization failed: {e}")
            self.logger.error(traceback.format_exc())
            self.logger.warning("Falling back to in-memory Chroma client (no persistence).")
            self.client = chromadb.Client()
            self.is_persistent = False
            self.logger.warning("Using in-memory ChromaDB client - data will NOT be persisted between restarts!")

        self.embedding_function = self._select_embeddings(cfg)
        self.collection = self._get_collection()

    # ────────────────────────────────────────────────────────
    # helpers
    # ────────────────────────────────────────────────────────
    def _select_embeddings(self, cfg):
        if cfg.openai_api_key:
            try:
                return OpenAIEmbeddingFunction(
                    api_key=cfg.openai_api_key, model_name=cfg.EMBEDDING_MODEL
                )
            except Exception:
                pass
        try:
            import sentence_transformers  # noqa: F401
            return SentenceTransformerEmbeddingFunction(model_name="all-MiniLM-L6-v2")
        except Exception:
            from chromadb.utils import embedding_functions
            return embedding_functions.DefaultEmbeddingFunction()

    def _get_collection(self):
        try:
            collection_name = "athena_memory"
            self.logger.info(f"Getting or creating collection: {collection_name}")
            
            # Explicit version check for ChromaDB
            chromadb_version = getattr(chromadb, "__version__", "unknown")
            self.logger.info(f"ChromaDB version: {chromadb_version}")
            
            try:  # ≤0.4 signature
                self.logger.info(f"Attempting to create collection '{collection_name}' using ≤0.4 API")
                collection = self.client.get_or_create_collection(
                    collection_name, embedding_function=self.embedding_function
                )
                self.logger.info("Using ChromaDB ≤0.4 API successfully")
                return collection
            except TypeError:  # ≥0.5
                self.logger.info(f"Creating collection '{collection_name}' using ≥0.5 API")
                col = self.client.get_or_create_collection(collection_name)
                self.logger.info("Modifying collection to add embedding function")
                col.modify(embedding_function=self.embedding_function)
                return col
        except Exception as e:
            self.logger.critical(f"Collection error: {e}")
            self.logger.critical(traceback.format_exc())
            return None

    def _persist(self):
        # For ChromaDB version < 0.4.6, explicit persist() is needed
        # For >= 0.4.6 with PersistentClient, persistence is automatic
        if not self.is_persistent:
            self.logger.warning("Not persisting data - running in memory-only mode")
            return
            
        if hasattr(self.client, "persist"):
            try:
                self.client.persist()
                self.logger.debug("Explicitly called persist() on ChromaDB client")
            except Exception as e:
                self.logger.warning(f"Failed to persist ChromaDB: {e}")
                self.logger.warning(traceback.format_exc())

    # ────────────────────────────────────────────────────────
    # public API
    # ────────────────────────────────────────────────────────
    def add_memory(
        self, user_id: str, content: str, metadata: Optional[Dict] = None
    ) -> None:
        if not self.collection:
            self.logger.error("Cannot add memory: collection is not initialized")
            return
            
        metadata = metadata or {}
        metadata["user_id"] = user_id
        memory_id = f"{user_id}_{int(time.time())}"
        
        # Log attempt details
        self.logger.info(f"Attempting to add memory for user {user_id[:8]}...")
        
        try:
            # Debug logging
            self.logger.debug(f"Content length: {len(content) if content else 0} chars")
            self.logger.debug(f"Metadata: {metadata}")
            
            # Add the memory
            self.collection.add(documents=[content], metadatas=[metadata], ids=[memory_id])
            
            # Explicitly persist if needed
            self._persist()
            
            # Log success
            self.logger.info(f"Memory +1 for {user_id[:8]}… (persistence: {'enabled' if self.is_persistent else 'disabled'})")
        except Exception as e:
            self.logger.error(f"add_memory failed: {e}")
            self.logger.error(traceback.format_exc())

    def query_memory(
        self,
        query: str,
        user_id: str,
        limit: int = 5,
        threshold: float | None = None,
    ) -> List[Dict]:
        if not self.collection:
            return []
        try:
            res = self.collection.query(
                query_texts=[query], n_results=limit, where={"user_id": user_id}
            )
            out: List[Dict] = []
            dists = res["distances"][0] if res.get("distances") else []
            for i, (doc, meta) in enumerate(zip(res["documents"][0], res["metadatas"][0])):
                dist = float(dists[i]) if i < len(dists) else None
                if threshold is None or (dist is not None and dist <= threshold):
                    out.append({"content": doc, "metadata": meta, "distance": dist})
            return out
        except Exception as e:
            self.logger.error(f"query_memory failed: {e}")
            return []

    def delete_memory(self, memory_id: str) -> None:
        if not self.collection:
            return
        try:
            self.collection.delete(ids=[memory_id])
            self._persist()
            self.logger.info(f"Memory -1 {memory_id}")
        except Exception as e:
            self.logger.error(f"delete_memory failed: {e}")

    def clear_memories(self) -> None:
        if not self.collection:
            return
        try:
            self.collection.delete(where={})
            self._persist()
            self.logger.info("All memories cleared.")
        except Exception as e:
            self.logger.error(f"clear_memories failed: {e}")

    def get_session_messages(self, session_id: str) -> List[Dict]:
        if not self.collection:
            return []
        try:
            res = self.collection.get(
                where={"session_id": session_id}, include=["documents", "metadatas"]
            )
            return [
                {"content": doc, "metadata": meta}
                for doc, meta in zip(res["documents"], res["metadatas"])
            ]
        except Exception as e:
            self.logger.error(f"get_session_messages failed: {e}")
            return []
