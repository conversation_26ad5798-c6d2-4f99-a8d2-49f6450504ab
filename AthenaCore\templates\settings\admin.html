<!-- Admin Settings Page -->
<div class="settings-section"
     data-section="admin"
     style="display: none;"
     id="admin-section">

<style>
  /* Admin-specific styles */
  .admin-container {
    max-width: 1000px;
    margin: 0 auto;
  }

  .user-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    background-color: var(--card-bg, #1e1e2e);
    border-radius: 8px;
    overflow: hidden;
  }

  .user-table th,
  .user-table td {
    border: 1px solid var(--border-color, #313244);
    padding: 10px 15px;
    text-align: left;
  }

  .user-table th {
    background-color: var(--card-secondary-bg, #181825);
    color: var(--text-color, #cdd6f4);
    font-weight: 600;
  }

  .user-table tr:nth-child(even) {
    background-color: rgba(49, 50, 68, 0.1);
  }

  .action-btn {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 0.9rem;
    margin-right: 5px;
  }

  .edit-btn {
    background-color: var(--primary-color, #89b4fa);
    color: white;
  }

  .delete-btn {
    background-color: var(--error-color, #f38ba8);
    color: white;
  }

  .role-admin {
    color: var(--error-color, #f38ba8);
    font-weight: bold;
  }

  .role-user {
    color: var(--text-color, #cdd6f4);
  }

  .add-user-form {
    background-color: var(--card-bg, #1e1e2e);
    border: 1px solid var(--border-color, #313244);
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
  }

  .form-row {
    margin-bottom: 15px;
  }

  .form-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
  }

  .form-row input,
  .form-row select {
    width: 100%;
    padding: 8px;
    border: 1px solid var(--border-color, #313244);
    border-radius: 4px;
    background-color: var(--input-bg, #181825);
    color: var(--text-color, #cdd6f4);
  }

  .submit-btn {
    background-color: var(--primary-color, #89b4fa);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
  }

  .alert {
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 15px;
  }

  .alert-success {
    background-color: rgba(166, 227, 161, 0.2);
    border: 1px solid #a6e3a1;
    color: #a6e3a1;
  }

  .alert-error {
    background-color: rgba(243, 139, 168, 0.2);
    border: 1px solid #f38ba8;
    color: #f38ba8;
  }
</style>

  <h2>Admin Panel: User Management</h2>
  <p>
    <a href="{{ url_for('core.home') }}">Return to Main Chat</a>
  </p>

  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ category }}">
          {{ message }}
        </div>
      {% endfor %}
    {% endif %}
  {% endwith %}

  <div class="admin-container">
    <!-- CREATE NEW USER FORM -->
    <div class="create-user-form">
      <h3>Create a New User</h3>
      <form action="/settings/admin/create_user"
            method="POST"
            style="margin-bottom: 2rem;">
        <label for="new_username">Username</label>
        <input type="text" id="new_username" name="new_username" required>

        <label for="new_email">Email</label>
        <input type="email" id="new_email" name="new_email" required>

        <label for="new_password">Password</label>
        <input type="password" id="new_password" name="new_password" required>

        <button type="submit">Create User</button>
      </form>
    </div>
    
    <!-- LIST ALL USERS -->
    <table>
      <thead>
        <tr>
          <th>ID</th>
          <th>Username</th>
          <th>Email</th>
          <th>Role</th>
          <th>Emulate</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {% for user in users %}
        <tr>
          <td>{{ user.id }}</td>
          <td>{{ user.username }}</td>
          <td>{{ user.email }}</td>
          <td>{{ user.role if user.role else 'None' }}</td>
          <td>
            {% if current_user.id != user.id %}
              <!-- Emulate button for admin to log in as this user -->
              <form action="/auth/emulate/{{ user.id }}"
                    method="POST"
                    style="display:inline;">
                <button type="submit">Emulate</button>
              </form>
            {% else %}
              N/A
            {% endif %}
          </td>
          <td>
            <!-- Update Role Form -->
            <form action="/auth/update_role"
                  method="POST"
                  style="display:inline;">
              <input type="hidden" name="user_id" value="{{ user.id }}">
              <select name="role" onchange="this.form.submit()">
                <option value="pending" {% if user.role == 'pending' %}selected{% endif %}>Pending</option>
                <option value="user" {% if user.role == 'user' %}selected{% endif %}>User</option>
                <option value="admin" {% if user.role == 'admin' %}selected{% endif %}>Admin</option>
                <option value="suspend" {% if user.role == 'suspend' %}selected{% endif %}>Suspend</option>
              </select>
            </form>

            <!-- Reset Password Form -->
            <form action="/auth/reset_user_password"
                  method="POST"
                  style="display:inline;">
              <input type="hidden" name="user_id" value="{{ user.id }}">
              <input type="password" name="new_password" placeholder="New password" required>
              <button type="submit">Reset PW</button>
            </form>

            <!-- Delete User Form -->
            {% if current_user.id != user.id %}
            <form action="/auth/delete_user"
                  method="POST"
                  style="display:inline;"
                  onsubmit="return confirm('Are you sure you want to delete this account?');">
              <input type="hidden" name="user_id" value="{{ user.id }}">
              <button type="submit" class="btn-danger">Delete</button>
            </form>
            {% endif %}
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>
