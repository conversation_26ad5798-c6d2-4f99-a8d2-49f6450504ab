import threading
import logging
from typing import Optional, Dict, List
from src.core.vector_db import VectorDatabase
from flask_login import current_user


class StateManager:
    """
    An improved StateManager that manages conversation history and memory.
    It prevents duplicate memories from being added by using a similarity threshold.
    """

    def __init__(self, user_id: str = None):
        self.latest_text = ""
        self.latest_text_lock = threading.Lock()

        self.silent_mode_state = False
        self.silent_mode_lock = threading.Lock()

        # Initialize the vector database for memory storage
        self.vector_db = VectorDatabase()
        self.vector_db_lock = threading.Lock()

        # Associate memories with a user - use passed user_id or default to system
        self.user_id = user_id or "system"

        # Cache of recently added memory entries to prevent redundancy
        self.memory_cache = set()
        self.max_cache_size = 100  # Limit cache size to prevent memory leaks

        # Optional threading manager if needed later
        self.threading_manager = None

    def set_threading_manager(self, threading_manager):
        """Store a reference to the threading manager."""
        self.threading_manager = threading_manager

    def set_latest_text(self, text: str):
        """Set the latest recognized text in a thread-safe way."""
        with self.latest_text_lock:
            self.latest_text = text

    def get_latest_text(self) -> str:
        """
        Get and clear the latest recognized text in a thread-safe way.
        """
        with self.latest_text_lock:
            text = self.latest_text
            self.latest_text = ""
        return text

    def get_effective_user_id(self) -> str:
        """
        Get the effective user ID, either from Flask's current_user if available,
        or fallback to the stored user_id.
        """
        try:
            if current_user and current_user.is_authenticated:
                return str(current_user.id)
        except Exception as e:
            logging.warning(f"Could not get current_user, using default: {e}")
        return self.user_id

    def _is_in_cache(self, entry: str) -> bool:
        """Check if an entry is in the memory cache."""
        # Simple string normalization for better matching
        normalized_entry = ' '.join(entry.lower().split())
        return normalized_entry in self.memory_cache

    def _add_to_cache(self, entry: str):
        """Add an entry to the memory cache with size limiting."""
        # Normalize the entry
        normalized_entry = ' '.join(entry.lower().split())

        # If cache is at capacity, remove oldest entries
        if len(self.memory_cache) >= self.max_cache_size:
            # Remove some older entries (20% of max)
            entries_to_remove = list(self.memory_cache)[:int(self.max_cache_size * 0.2)]
            for old_entry in entries_to_remove:
                self.memory_cache.remove(old_entry)

        self.memory_cache.add(normalized_entry)

    def add_memory(self, user_input: str, ai_response: str, threshold: float = 0.2) -> bool:
        """
        Adds a memory entry (user input combined with AI response) to the vector database
        only if there is no duplicate memory (determined by a similarity threshold).

        Args:
            user_input (str): The user's message.
            ai_response (str): The AI's response.
            threshold (float): The similarity threshold below which a memory is considered a duplicate.

        Returns:
            bool: True if the memory was added, False if a duplicate was found.
        """
        combined_entry = f"{user_input} | {ai_response}"
        user_id = self.get_effective_user_id()

        # First check the in-memory cache to avoid unnecessary DB queries
        if self._is_in_cache(combined_entry):
            logging.info("Duplicate memory detected in cache. Skipping addition.")
            return False

        with self.vector_db_lock:
            # Then check vector database for similar entries
            duplicates = self.vector_db.query_memory(
                combined_entry, user_id=user_id, limit=1, threshold=threshold
            )
            if duplicates:
                distance = duplicates[0].get("distance")
                logging.info(
                    f"Duplicate memory detected (distance: {distance}). Skipping addition."
                )
                return False

            # Only add to database if not a duplicate
            self.vector_db.add_memory(user_id, combined_entry)
            # Add to cache to prevent future duplicates
            self._add_to_cache(combined_entry)
            logging.info(f"Memory added successfully for user {user_id}.")
            return True

    def remember_memory(self, content: str, metadata: Optional[Dict] = None) -> str:
        """
        Stores arbitrary memory content along with optional metadata.
        """
        user_id = self.get_effective_user_id()

        # Check cache for duplicates
        if self._is_in_cache(content):
            logging.info("Duplicate content detected in cache. Skipping addition.")
            return "Memory already exists."

        with self.vector_db_lock:
            # Check for similar content in vector DB
            duplicates = self.vector_db.query_memory(
                content, user_id=user_id, limit=1, threshold=0.1  # Stricter threshold
            )
            if duplicates:
                logging.info("Duplicate content detected in vector DB. Skipping addition.")
                return "Memory already exists."

            self.vector_db.add_memory(user_id, content, metadata)
            self._add_to_cache(content)

        return "Memory stored successfully."

    def retrieve_memory(self, query: str, top_k: int = 5) -> List[Dict]:
        """
        Retrieves relevant memories based on a query.

        Args:
            query (str): The query string.
            top_k (int): Maximum number of memories to return.

        Returns:
            List[Dict]: A list of memory entries.
        """
        user_id = self.get_effective_user_id()
        with self.vector_db_lock:
            return self.vector_db.query_memory(query, user_id=user_id, limit=top_k)

    def forget_memory(self, memory_id: str) -> str:
        """
        Removes a specific memory by its ID, but only if it belongs to the current user.

        Args:
            memory_id (str): The ID of the memory to remove.

        Returns:
            str: Confirmation message.
        """
        user_id = self.get_effective_user_id()

        # Extract user_id from memory_id (assuming format: user_id_timestamp)
        try:
            memory_owner_id = memory_id.split('_')[0]

            # Security check: Only allow users to delete their own memories
            if memory_owner_id != user_id:
                logging.warning(f"Security: User {user_id} attempted to delete memory {memory_id} belonging to user {memory_owner_id}")
                return "Error: You can only delete your own memories."
        except Exception as e:
            logging.error(f"Error parsing memory_id {memory_id}: {e}")
            return "Error: Invalid memory ID format."

        with self.vector_db_lock:
            self.vector_db.delete_memory(memory_id)

        return f"Memory with ID {memory_id} has been deleted successfully."

    def close_memory(self):
        """
        Closes the memory connection (if needed).
        """
        with self.vector_db_lock:
            logging.info("VectorDatabase does not require explicit close.")
