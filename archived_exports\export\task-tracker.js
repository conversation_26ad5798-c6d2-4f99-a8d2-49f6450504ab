// task-tracker.js - Background task tracking system for Athena

document.addEventListener("DOMContentLoaded", () => {
    // DOM Elements
    const notificationBell = document.getElementById("notification-bell");
    const notificationCount = document.getElementById("notification-count");
    const taskPanel = document.getElementById("task-panel");
    const taskList = document.getElementById("task-list");
    const taskFilterButtons = document.querySelectorAll(".task-filter button");
    const closePanelButton = document.querySelector(".task-panel .close-panel");
    const toastContainer = document.getElementById("toast-container");
    
    // Current active filter
    let currentFilter = "active";
    
    // Tasks storage
    const tasks = {
        active: new Map(),
        completed: new Map()
    };
    
    // Connect to Socket.IO server
    const socket = io();
    
    // SocketIO event handlers
    socket.on("connect", () => {
        console.log("Connected to WebSocket server");
        // Join user-specific room if the current_user ID is available
        socket.emit("join", { room: `user_${window.CURRENT_USER_ID || 1}` });
    });
    
    socket.on("disconnect", () => {
        console.log("Disconnected from WebSocket server");
    });
    
    socket.on("active_tasks", (data) => {
        console.log("Received active tasks:", data);
        // Update tasks count
        updateNotificationCount(data.count);
        
        // Clear active tasks and add the received ones
        tasks.active.clear();
        data.tasks.forEach(task => {
            tasks.active.set(task.command_uuid, task);
        });
        
        // Update the task list view if active tasks is the current filter
        if (currentFilter === "active") {
            renderTaskList();
        }
    });
    
    socket.on("task_update", (task) => {
        console.log("Task update:", task);
        
        // Determine if the task is active or completed
        if (["completed", "failed", "cancelled", "expired"].includes(task.status)) {
            // Move from active to completed
            if (tasks.active.has(task.command_uuid)) {
                tasks.active.delete(task.command_uuid);
                tasks.completed.set(task.command_uuid, task);
            } else {
                // Update in completed if already there
                tasks.completed.set(task.command_uuid, task);
            }
        } else {
            // Update in active tasks
            tasks.active.set(task.command_uuid, task);
        }
        
        // Update notification count
        updateNotificationCount(tasks.active.size);
        
        // Update the task list view
        renderTaskList();
        
        // Animate bell for new tasks
        if (task.status === "running" && task.progress === 0) {
            animateNotificationBell();
        }
    });
    
    socket.on("task_progress", (data) => {
        console.log("Task progress:", data);
        
        // Update task progress in the corresponding collection
        const task = tasks.active.get(data.command_uuid);
        if (task) {
            task.progress = data.progress || task.progress;
            
            // If progress is 100%, mark as completed after a short delay
            if (task.progress === 100) {
                setTimeout(() => {
                    // Double check that the task wasn't already moved to completed
                    if (tasks.active.has(data.command_uuid)) {
                        // Move to completed collection
                        const completedTask = {...task, status: "completed"};
                        tasks.active.delete(data.command_uuid);
                        tasks.completed.set(data.command_uuid, completedTask);
                        
                        // Update notification count and re-render
                        updateNotificationCount(tasks.active.size);
                        renderTaskList();
                    }
                }, 1000);
            }
            
            updateTaskProgressInUI(data.command_uuid, data.progress, data.message);
        }
    });
    
    socket.on("notification", (data) => {
        console.log("Notification:", data);
        showToastNotification(data.title, data.message, data.type);
    });
    
    // Notification bell click handler
    notificationBell.addEventListener("click", () => {
        toggleTaskPanel();
    });
    
    // Close panel button click handler
    closePanelButton.addEventListener("click", () => {
        toggleTaskPanel(false);
    });
    
    // Task filter buttons click handlers
    taskFilterButtons.forEach(button => {
        button.addEventListener("click", () => {
            // Update active button
            taskFilterButtons.forEach(btn => btn.classList.remove("active"));
            button.classList.add("active");
            
            // Update filter
            currentFilter = button.dataset.filter;
            
            // Render tasks with new filter
            renderTaskList();
        });
    });
    
    // Task list event delegation for expandable items
    taskList.addEventListener("click", (e) => {
        // Find the closest task header or expand button
        const taskHeader = e.target.closest(".task-header");
        const expandButton = e.target.closest(".expand-task");
        
        if (taskHeader || expandButton) {
            const taskItem = (taskHeader || expandButton).closest(".task-item");
            toggleTaskDetails(taskItem);
        }
    });
    
    // Demo task button handler
    const demoTaskButton = document.getElementById("demo-task-button");
    if (demoTaskButton) {
        demoTaskButton.addEventListener("click", () => {
            // Create a demo task that runs for 30 seconds and succeeds
            fetch("/api/demo/task", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    duration: 30,
                    task_type: "success"
                })
            })
            .then(response => response.json())
            .then(data => {
                // Show notification
                showToastNotification("Demo Task Started", "A background task has been started. Check the notification bell for updates.", "info");
                
                // Open task panel
                toggleTaskPanel(true);
            })
            .catch(error => {
                console.error("Error creating demo task:", error);
                showToastNotification("Error", "Failed to start demo task.", "error");
            });
        });
    }
    
    // Load initial tasks data
    loadTasks();
    
    /* ---- Helper functions ---- */
    
    // Load tasks data from the API
    function loadTasks() {
        // Fetch active tasks
        fetch("/api/tasks/background?status=pending,running")
            .then(response => response.json())
            .then(data => {
                updateNotificationCount(data.tasks.length);
                
                // Store tasks in active map
                tasks.active.clear();
                data.tasks.forEach(task => {
                    tasks.active.set(task.command_uuid, task);
                });
                
                // Render active tasks if that's the current filter
                if (currentFilter === "active") {
                    renderTaskList();
                }
            })
            .catch(error => {
                console.error("Error fetching active tasks:", error);
            });
        
        // Fetch completed tasks (limit to last 10)
        fetch("/api/tasks/background?status=completed,failed,cancelled,expired&limit=10")
            .then(response => response.json())
            .then(data => {
                // Store tasks in completed map
                tasks.completed.clear();
                data.tasks.forEach(task => {
                    tasks.completed.set(task.command_uuid, task);
                });
                
                // Render completed tasks if that's the current filter
                if (currentFilter === "completed") {
                    renderTaskList();
                }
            })
            .catch(error => {
                console.error("Error fetching completed tasks:", error);
            });
    }
    
    // Update the notification count badge
    function updateNotificationCount(count) {
        notificationCount.textContent = count;
        
        if (count > 0) {
            notificationCount.classList.add("active");
        } else {
            notificationCount.classList.remove("active");
        }
    }
    
    // Animate notification bell
    function animateNotificationBell() {
        notificationBell.classList.add("notification-new");
        
        // Remove animation class after animation completes
        setTimeout(() => {
            notificationBell.classList.remove("notification-new");
        }, 1000);
    }
    
    // Toggle the task panel
    function toggleTaskPanel(show) {
        if (show === undefined) {
            taskPanel.classList.toggle("open");
        } else if (show) {
            taskPanel.classList.add("open");
        } else {
            taskPanel.classList.remove("open");
        }
    }
    
    // Render the task list based on the current filter
    function renderTaskList() {
        // Clear current tasks
        taskList.innerHTML = "";
        
        // Get tasks based on filter
        const tasksToRender = (currentFilter === "active" ? tasks.active : tasks.completed);
        
        // If no tasks, show a message
        if (tasksToRender.size === 0) {
            const emptyMessage = document.createElement("div");
            emptyMessage.className = "empty-tasks";
            emptyMessage.textContent = `No ${currentFilter} tasks found.`;
            taskList.appendChild(emptyMessage);
            return;
        }
        
        // Convert to array and sort by creation time (newest first)
        const taskArray = Array.from(tasksToRender.values())
            .sort((a, b) => b.created_at - a.created_at);
        
        // Render each task
        taskArray.forEach(task => {
            renderTaskItem(task);
        });
    }
    
    // Render a single task item
    function renderTaskItem(task) {
        // Create task item container
        const taskItem = document.createElement("div");
        taskItem.className = "task-item";
        taskItem.dataset.id = task.command_uuid;
        
        // Create task header
        const taskHeader = document.createElement("div");
        taskHeader.className = "task-header";
        
        // Create task name
        const taskName = document.createElement("div");
        taskName.className = "task-name";
        taskName.textContent = task.capability_name;
        
        // Create task status
        const taskStatus = document.createElement("div");
        taskStatus.className = `task-status ${task.status}`;
        taskStatus.textContent = capitalizeFirstLetter(task.status);
        
        // Create task progress bar
        const taskProgress = document.createElement("div");
        taskProgress.className = "task-progress";
        
        const progressBar = document.createElement("div");
        progressBar.className = "progress-bar";
        progressBar.style.width = `${task.progress}%`;
        
        taskProgress.appendChild(progressBar);
        
        // Create expand button
        const expandButton = document.createElement("button");
        expandButton.className = "expand-task";
        expandButton.innerHTML = "↓";
        
        // Add elements to task header
        taskHeader.appendChild(taskName);
        taskHeader.appendChild(taskStatus);
        taskHeader.appendChild(taskProgress);
        taskHeader.appendChild(expandButton);
        
        // Create task details section
        const taskDetails = document.createElement("div");
        taskDetails.className = "task-details";
        
        // Create task logs section
        const taskLogs = document.createElement("div");
        taskLogs.className = "task-logs";
        
        // Add a placeholder for logs that will be loaded when expanded
        taskLogs.innerHTML = `<div class="loading-logs">Click to load task logs...</div>`;
        
        // Add logs section to details
        taskDetails.appendChild(taskLogs);
        
        // Add all sections to task item
        taskItem.appendChild(taskHeader);
        taskItem.appendChild(taskDetails);
        
        // Add task item to list
        taskList.appendChild(taskItem);
    }
    
    // Toggle task details view
    function toggleTaskDetails(taskItem) {
        // Toggle expanded class
        taskItem.classList.toggle("expanded");
        
        // If expanded and logs haven't been loaded, load them
        if (taskItem.classList.contains("expanded")) {
            const taskLogsElement = taskItem.querySelector(".task-logs");
            const loadingElement = taskLogsElement.querySelector(".loading-logs");
            
            if (loadingElement) {
                const taskId = taskItem.dataset.id;
                
                // Load logs for this task
                fetch(`/api/tasks/background/${taskId}`)
                    .then(response => response.json())
                    .then(data => {
                        // Clear loading message
                        taskLogsElement.innerHTML = "";
                        
                        // If no logs, show a message
                        if (!data.logs || data.logs.length === 0) {
                            taskLogsElement.innerHTML = `<div class="no-logs">No logs available for this task.</div>`;
                            return;
                        }
                        
                        // Add each log entry
                        data.logs.forEach(log => {
                            const logEntry = document.createElement("div");
                            logEntry.className = "log-entry";
                            
                            const logTime = document.createElement("span");
                            logTime.className = "log-time";
                            
                            // Format timestamp
                            const date = new Date(log.timestamp * 1000);
                            logTime.textContent = `${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`;
                            
                            const logMessage = document.createElement("span");
                            logMessage.className = "log-message";
                            logMessage.textContent = log.message;
                            
                            logEntry.appendChild(logTime);
                            logEntry.appendChild(logMessage);
                            
                            taskLogsElement.appendChild(logEntry);
                        });
                    })
                    .catch(error => {
                        console.error("Error loading task logs:", error);
                        taskLogsElement.innerHTML = `<div class="error">Error loading logs: ${error.message}</div>`;
                    });
            }
        }
    }
    
    // Update task progress in UI
    function updateTaskProgressInUI(taskId, progress, message) {
        // Find task item in the DOM
        const taskItem = document.querySelector(`.task-item[data-id="${taskId}"]`);
        if (!taskItem) return;
        
        // Update progress bar
        const progressBar = taskItem.querySelector(".progress-bar");
        if (progressBar && progress !== undefined) {
            progressBar.style.width = `${progress}%`;
        }
        
        // If task is expanded and has a new message, add it to logs
        if (taskItem.classList.contains("expanded") && message) {
            const taskLogs = taskItem.querySelector(".task-logs");
            
            // Create new log entry
            const logEntry = document.createElement("div");
            logEntry.className = "log-entry";
            
            const logTime = document.createElement("span");
            logTime.className = "log-time";
            
            // Format current time
            const date = new Date();
            logTime.textContent = `${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`;
            
            const logMessage = document.createElement("span");
            logMessage.className = "log-message";
            logMessage.textContent = message;
            
            logEntry.appendChild(logTime);
            logEntry.appendChild(logMessage);
            
            taskLogs.appendChild(logEntry);
            
            // Scroll to bottom of logs
            taskLogs.scrollTop = taskLogs.scrollHeight;
        }
    }
    
    // Show toast notification
    function showToastNotification(title, message, type = "info") {
        // Create toast element
        const toast = document.createElement("div");
        toast.className = `toast ${type}`;
        
        // Add icon based on type
        let iconClass = "fa-info-circle";
        if (type === "success") iconClass = "fa-check-circle";
        if (type === "warning") iconClass = "fa-exclamation-triangle";
        if (type === "error") iconClass = "fa-exclamation-circle";
        
        // Create toast content
        toast.innerHTML = `
            <i class="fa ${iconClass} toast-icon"></i>
            <div class="toast-content">
                <div class="toast-title">${title}</div>
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close">&times;</button>
        `;
        
        // Add to container
        toastContainer.appendChild(toast);
        
        // Animate in
        setTimeout(() => {
            toast.classList.add("show");
        }, 10);
        
        // Set up auto-dismiss
        const dismissTimeout = setTimeout(() => {
            dismissToast(toast);
        }, 5000);
        
        // Add click handler for close button
        const closeButton = toast.querySelector(".toast-close");
        closeButton.addEventListener("click", () => {
            clearTimeout(dismissTimeout);
            dismissToast(toast);
        });
    }
    
    // Dismiss a toast notification
    function dismissToast(toast) {
        // Animate out
        toast.classList.remove("show");
        
        // Remove after animation completes
        setTimeout(() => {
            toast.remove();
        }, 300);
    }
    
    // Helper function to capitalize first letter of a string
    function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
    }
    
    // Global expose for debugging and manual calls
    window.taskTracker = {
        showNotification: showToastNotification,
        toggleTaskPanel,
        loadTasks
    };
});
