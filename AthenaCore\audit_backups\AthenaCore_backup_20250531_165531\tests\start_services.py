# tests/start_services.py

"""
A startup script that initializes the Cross-Device API services.

This script should be run before executing tests to ensure that all services
are properly started and the database is prepared for testing.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.services.task_executor import task_executor_service
from src.services.task_scheduler import task_scheduler_service
from main import app

def start_services():
    """
    Start the Cross-Device API services.
    """
    print("Starting services for testing...")
    
    with app.app_context():
        # Start the task executor service
        task_executor_service.start()
        print("Task executor service started")
        
        # Start the task scheduler service
        task_scheduler_service.start()
        print("Task scheduler service started")
    
    print("All services started successfully")

if __name__ == "__main__":
    start_services()
