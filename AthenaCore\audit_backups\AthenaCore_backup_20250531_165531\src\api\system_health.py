"""
System Health API endpoint for Athena
"""

import os
import time
import psutil
import json
from datetime import datetime
from flask import Blueprint, jsonify, current_app

# Create blueprint
system_health_bp = Blueprint('system_health', __name__)

@system_health_bp.route('/api/system/health', methods=['GET'])
def system_health():
    """Get system health information."""
    try:
        # Get process information
        process = psutil.Process(os.getpid())
        
        # Get memory usage
        memory_info = process.memory_info()
        memory_usage_mb = memory_info.rss / 1024 / 1024
        
        # Get CPU usage
        cpu_percent = process.cpu_percent(interval=0.1)
        
        # Get uptime
        start_time = process.create_time()
        uptime_seconds = time.time() - start_time
        
        # Format uptime nicely
        days, remainder = divmod(uptime_seconds, 86400)
        hours, remainder = divmod(remainder, 3600)
        minutes, seconds = divmod(remainder, 60)
        uptime_formatted = f"{int(days)}d {int(hours)}h {int(minutes)}m {int(seconds)}s"
        
        # Get system load
        load_avg = psutil.getloadavg()
        
        # Get disk usage for current directory
        disk_usage = psutil.disk_usage('/')
        disk_percent = disk_usage.percent
        
        # Determine system health
        system_healthy = True  # If the API responds, system is healthy
        
        # Check KB status if available
        kb_status = "unknown"
        try:
            # Import the SHARED kb instance, not the class
            from src.api.routes import kb
            
            # Use the imported shared instance
            if kb and kb.collection:
                kb_info = kb.get_document_count() # Use a simpler check like count
                kb_status = "active" if kb_info >= 0 else "error" # Check if count is non-negative
            else:
                kb_status = "inactive"
        
        except ImportError:
            # This might happen if src.api.routes hasn't been fully imported yet?
            kb_status = "import_error"
        except Exception as e:
            # Log the specific error for debugging
            current_app.logger.error(f"Health check KB access error: {e}")
            kb_status = "error"
        
        # Build response
        response = {
            'healthy': system_healthy,
            'cpu_percent': round(cpu_percent, 2),
            'memory_usage_mb': round(memory_usage_mb, 2),
            'uptime': uptime_formatted,
            'kb_status': kb_status
        }
        
        return jsonify(response)
    
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500
