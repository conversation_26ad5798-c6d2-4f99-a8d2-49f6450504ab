"""
Admin controller module for the Athena application.

This module contains routes and functions for admin panel functionality.
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, g
from src.utils.middleware import require_auth, require_role, api_route
from src.utils.api_response import success_response, error_response
from src.services.user_service import UserService
from src.services.base_service import get_service

# Create admin blueprint
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/')
@admin_bp.route('/dashboard')
@require_auth
@require_role(['admin'])
def admin_panel():
    """
    Admin dashboard page.
    
    Requires admin role to access.
    """
    # Get the user service
    user_service = get_service(UserService)
    
    # Get all users for the admin panel
    users = user_service.get_all_users()
    
    return render_template(
        'admin/dashboard.html',
        users=users,
        active_tab='dashboard'
    )

@admin_bp.route('/users')
@require_auth
@require_role(['admin'])
def manage_users():
    """
    User management page.
    
    Allows admins to view and manage all users in the system.
    """
    # Get the user service
    user_service = get_service(UserService)
    
    # Get all users
    users = user_service.get_all_users()
    
    return render_template(
        'admin/users.html',
        users=users,
        active_tab='users'
    )

@admin_bp.route('/api/users', methods=['GET'])
@require_auth
@require_role(['admin'])
@api_route
def get_users_api():
    """
    API endpoint to get all users.
    
    Returns:
        JSON response with all users
    """
    # Get the user service
    user_service = get_service(UserService)
    
    # Get all users
    users = user_service.get_all_users()
    
    return success_response(
        data={"users": [user.to_dict() for user in users]},
        message=f"Retrieved {len(users)} users"
    )

@admin_bp.route('/api/users/<user_id>', methods=['GET'])
@require_auth
@require_role(['admin'])
@api_route
def get_user_api(user_id):
    """
    API endpoint to get a specific user.
    
    Args:
        user_id: ID of the user to retrieve
    
    Returns:
        JSON response with user details
    """
    # Get the user service
    user_service = get_service(UserService)
    
    # Get the user
    user = user_service.get_user_by_id(user_id)
    
    if not user:
        return error_response(f"User {user_id} not found", status_code=404)
    
    return success_response(
        data={"user": user.to_dict()},
        message=f"User {user_id} retrieved successfully"
    )

@admin_bp.route('/api/users/<user_id>/roles', methods=['POST'])
@require_auth
@require_role(['admin'])
@api_route
def update_user_role(user_id):
    """
    API endpoint to update a user's role.
    
    Args:
        user_id: ID of the user to update
    
    Request Body:
        role: New role for the user
    
    Returns:
        JSON response with updated user details
    """
    # Get the user service
    user_service = get_service(UserService)
    
    # Get request data
    data = request.get_json()
    if not data or 'role' not in data:
        return error_response("Role is required", status_code=400)
    
    role = data['role']
    
    # Update the user's role
    user = user_service.update_user_role(user_id, role)
    
    if not user:
        return error_response(f"User {user_id} not found", status_code=404)
    
    return success_response(
        data={"user": user.to_dict()},
        message=f"User {user_id} role updated to {role}"
    )

# Register the blueprint
def register_admin_blueprint(app):
    """
    Register the admin blueprint with the Flask app.
    
    Args:
        app: Flask application instance
    """
    app.register_blueprint(admin_bp)
