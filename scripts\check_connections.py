"""
DirectConnection Diagnostic Tool for Athena

This script checks the DirectConnection entries in the database
and verifies their API key format without displaying sensitive information.
"""

import os
import sys

# Add the AthenaCore directory to the Python path
base_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(base_dir, 'AthenaCore'))

# Import database models
try:
    # Add AthenaCore to the path
    sys.path.insert(0, os.path.join(base_dir, 'AthenaCore'))
    
    # Now import with correct paths
    from src.login.models import DirectConnection, db
    from src.core.database import db

    # Get the database path
    from src.utils.config import AthenaConfig
    config = AthenaConfig.load()
    
    # Connect to the database directly
    import sqlite3
    import os
    
    # Determine database path
    db_path = os.path.join(base_dir, 'AthenaCore', 'data', 'athena.db')
    if not os.path.exists(db_path):
        print(f"Error: Database file not found at {db_path}")
        sys.exit(1)
        
    print(f"Connecting to database at: {db_path}")
    
    print("\n=== DirectConnection Diagnostic Results ===\n")
    
    # Check all connections
    connections = DirectConnection.query.all()
    print(f"Found {len(connections)} connection entries in the database")
    
    if len(connections) == 0:
        print("\nNo DirectConnection entries found. You need to add one with a valid OpenAI API key.")
        print("Go to Settings > LLM Connection and add a new connection with your OpenAI API key.")
        sys.exit(1)
    
    for idx, conn in enumerate(connections):
        print(f"\nConnection #{idx+1}")
        print(f"  Name: {conn.name}")
        print(f"  URL: {conn.url}")
        print(f"  Enabled: {'Yes' if conn.enabled else 'No'}")
        
        # Check API key (without displaying it)
        if conn.api_key:
            # Mask the key for display
            if len(conn.api_key) > 8:
                masked_key = f"{conn.api_key[:4]}...{conn.api_key[-4:]}"
            else:
                masked_key = "***"
                
            print(f"  API Key: {masked_key}")
            
            # Verify key format
            if conn.api_key.startswith("sk-"):
                print("  Key Format: Valid OpenAI format (starts with sk-)")
            else:
                print("  Key Format: INVALID - OpenAI keys should start with 'sk-'")
                if conn.enabled:
                    print("  WARNING: This enabled connection has an invalid key format")
        else:
            print("  API Key: Not set")
            if conn.enabled:
                print("  WARNING: This enabled connection has no API key")
        
        print(f"  Models: {conn.model_ids if conn.model_ids else 'None specified'}")
    
    # Print recommendations
    print("\n=== Recommendations ===")
    valid_connections = [c for c in connections if c.api_key and c.api_key.startswith("sk-") and c.enabled]
    
    if not valid_connections:
        print("No valid connections found. Please:")
        print("1. Go to Settings > LLM Connection")
        print("2. Add a new connection with a valid OpenAI API key (starting with 'sk-')")
        print("3. Make sure the connection is enabled")
    else:
        print(f"Found {len(valid_connections)} valid and enabled connections.")
        print("If you're still having issues, please check that:")
        print("1. Your OpenAI API key is valid and has proper permissions")
        print("2. Your API key has not expired")
        print("3. You have billing set up on your OpenAI account")

except Exception as e:
    print(f"Error accessing database: {e}")
    print("Please make sure the Athena server is not running when using this diagnostic tool.")
