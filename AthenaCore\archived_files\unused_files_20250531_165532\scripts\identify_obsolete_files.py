#!/usr/bin/env python
"""
Obsolete Files Identification Script

This script identifies files that have been refactored and are now obsolete.
It creates a report of files that can be marked as deprecated or safely removed.

Usage:
    python scripts/identify_obsolete_files.py

This script will:
1. Scan the codebase for files that have been refactored
2. Check if a compatibility layer exists
3. Generate a report of files that can be deprecated or removed
"""

import os
import sys
from pathlib import Path
import importlib
import importlib.util
import inspect
import datetime

# Define paths to check
ORIGINAL_PATHS = [
    ("src/login/models.py", "src/models"),
    ("src/login/views.py", "src/controllers/auth_controller.py"),
]

# Files that should be marked as deprecated but not removed yet
DEPRECATE_FILES = [
    "src/login/models.py",
    "src/login/views.py",
]

# Files that can be safely removed (no compatibility layer needed)
SAFE_TO_REMOVE = [
    # Add files here that can be safely removed
]

def check_file_exists(file_path):
    """Check if a file exists."""
    return os.path.isfile(file_path)

def check_module_has_compat_layer(module_path):
    """Check if a module has a compatibility layer."""
    try:
        module_name = module_path.replace('/', '.').replace('.py', '')
        module = importlib.import_module(module_name)
        return hasattr(module, '__file__') and 'compat' in module.__file__
    except ImportError:
        return False

def get_imported_modules(file_path):
    """Get a list of modules imported by a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        imports = []
        for line in content.split('\n'):
            if line.startswith('from ') or line.startswith('import '):
                imports.append(line.strip())
        
        return imports
    except Exception as e:
        print(f"Error reading imports from {file_path}: {e}")
        return []

def check_file_usage(file_path, project_root):
    """Check if a file is being used by other files in the project."""
    # Convert file path to import format
    module_path = file_path.replace('\\', '/').replace('/', '.').replace('.py', '')
    if module_path.startswith('.'):
        module_path = module_path[1:]
    
    usage_count = 0
    usage_files = []
    
    for root, dirs, files in os.walk(project_root):
        for file in files:
            if file.endswith('.py') and file != os.path.basename(file_path):
                file_full_path = os.path.join(root, file)
                imports = get_imported_modules(file_full_path)
                
                for imp in imports:
                    if module_path in imp:
                        usage_count += 1
                        rel_path = os.path.relpath(file_full_path, project_root)
                        usage_files.append(rel_path)
                        break
    
    return usage_count, usage_files

def main():
    """Main entry point for the script."""
    print("Obsolete Files Identification Script")
    print("===================================")
    
    # Get the root directory of the project
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    
    # Report header
    report_lines = []
    report_lines.append("# Refactoring Clean-up Report")
    report_lines.append(f"Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append("\n## Files Status Summary")
    report_lines.append("\n| File | Status | Replacement | Usage Count | Has Compat Layer |")
    report_lines.append("| ---- | ------ | ----------- | ----------- | --------------- |")
    
    # Check each original path
    for original, replacement in ORIGINAL_PATHS:
        original_path = os.path.join(project_root, original)
        replacement_path = os.path.join(project_root, replacement)
        
        original_exists = check_file_exists(original_path)
        replacement_exists = check_file_exists(replacement_path) if not replacement.endswith('/') else os.path.isdir(replacement_path)
        
        has_compat = check_module_has_compat_layer(original.replace('.py', ''))
        usage_count, usage_files = check_file_usage(original_path, project_root)
        
        status = "Deprecated" if original in DEPRECATE_FILES else "Safe to remove" if original in SAFE_TO_REMOVE else "Needs review"
        
        report_lines.append(f"| {original} | {status} | {replacement} | {usage_count} | {'Yes' if has_compat else 'No'} |")
    
    report_lines.append("\n## Detailed Usage Information")
    
    # Add detailed usage information
    for original, replacement in ORIGINAL_PATHS:
        original_path = os.path.join(project_root, original)
        usage_count, usage_files = check_file_usage(original_path, project_root)
        
        report_lines.append(f"\n### {original}")
        report_lines.append(f"Usage count: {usage_count}")
        
        if usage_count > 0:
            report_lines.append("\nUsed by:")
            for usage_file in usage_files:
                report_lines.append(f"- {usage_file}")
    
    # Write report to file
    report_path = os.path.join(project_root, "OBSOLETE-FILES-REPORT.md")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))
    
    print(f"\nReport generated at: {report_path}")
    print("Review this report to identify files that can be safely deprecated or removed.")

if __name__ == "__main__":
    main()
