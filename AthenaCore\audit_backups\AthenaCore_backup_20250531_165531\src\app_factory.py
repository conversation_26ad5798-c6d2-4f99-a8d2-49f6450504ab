"""
Application factory module.

This module contains the factory function for creating Flask application instances.
It centralizes configuration loading, extension initialization, and blueprint registration.
"""

import os
from pathlib import Path
from flask import Flask
from flask_migrate import Migrate
from flask_socketio import SocketIO

# Import extensions
from src.models import db
from src.login.extensions import login_manager
from src.config.service import config_service, init_config_service
from src.controllers import register_all_blueprints
from src.db import init_migration_manager

# Create socket instance for real-time notifications
socketio = SocketIO()

def create_app(config_name=None):
    """
    Create and configure the Flask application.
    
    Args:
        config_name: Configuration name to use (development, production, testing)
        
    Returns:
        Configured Flask application
    """
    app = Flask(__name__, 
                template_folder="../templates",
                static_folder="../static")
    
    # Configure the app
    configure_app(app, config_name)
    
    # Initialize extensions
    initialize_extensions(app)
    
    # Register blueprints
    register_all_blueprints(app)
    
    # Register error handlers
    register_error_handlers(app)
    
    return app

def configure_app(app, config_name=None):
    """
    Configure the Flask application with the appropriate settings.
    
    Args:
        app: Flask application instance
        config_name: Configuration name to use
    """
    # If no config name is provided, use the environment variable
    if not config_name:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    # Initialize the configuration service
    init_config_service(app)
    
    # Basic configuration
    app.config['SECRET_KEY'] = config_service.get('SECRET_KEY', os.urandom(24).hex())
    
    # Use absolute path for database to avoid path resolution issues
    instance_path = Path(__file__).resolve().parents[2] / 'instance' / 'athena.db'
    app.config['SQLALCHEMY_DATABASE_URI'] = config_service.get(
        'DATABASE_URI', 
        f'sqlite:///{instance_path}'
    )
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # Additional configuration based on environment
    if config_name == 'development':
        app.config['DEBUG'] = True
        app.config['TESTING'] = False
    elif config_name == 'production':
        app.config['DEBUG'] = False
        app.config['TESTING'] = False
    elif config_name == 'testing':
        app.config['DEBUG'] = True
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
    
    # Load additional configuration from the database
    # This happens after basic configuration to ensure database connection is established
    app.config['APP_NAME'] = config_service.get('APP_NAME', 'Athena')
    app.config['APP_VERSION'] = config_service.get('APP_VERSION', '1.0.0')
    app.config['APP_PORT'] = config_service.get('APP_PORT', 5000, 'int')
    
    # Set configuration in the config service for access from other modules
    config_service.set('APP_NAME', app.config['APP_NAME'])
    config_service.set('APP_VERSION', app.config['APP_VERSION'])
    config_service.set('APP_PORT', app.config['APP_PORT'], 'int')

def initialize_extensions(app):
    """
    Initialize Flask extensions.
    
    Args:
        app: Flask application instance
    """
    # Initialize database
    db.init_app(app)
    
    # Configure SQLAlchemy to handle table redefinitions gracefully during refactoring
    # This ensures that tables can be redefined without errors when imported from multiple places
    with app.app_context():
        # Set extend_existing=True in the metadata to handle table redefinitions
        db.metadata.info['extend_existing'] = True
    
    # Initialize migrations
    Migrate(app, db)
    
    # Initialize custom migration manager
    app.migration_manager = init_migration_manager(app)
    
    # Initialize login manager
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    
    # Initialize SocketIO
    socketio.init_app(app, cors_allowed_origins="*")
    
    # Store SocketIO instance on app for access from other modules
    app.socketio = socketio

def register_error_handlers(app):
    """
    Register error handlers with the Flask application.
    
    Args:
        app: Flask application instance
    """
    # Import and use our standardized error handlers
    from src.utils.error_handlers import register_error_handlers as register_standard_error_handlers
    register_standard_error_handlers(app)
